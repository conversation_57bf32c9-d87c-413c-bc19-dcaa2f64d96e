package com.api.yitouniu.workflow.action;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ryytn.http.HttpClientResult;
import com.ryytn.http.HttpClientUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/7 10:39
 * @Description TODO
 * @Version 1.0
 */

@Path("yitouniu/workflow/action/travelInfoAction")
public class travelInfoAction {

    BaseBean baseBean = new BaseBean();


    /**
     * id与链接的记录表
     */
    public static final String UF_TRAVEL_INFO = "uf_travel_info";
    public static final String UF_SLCCDZ = "uf_slccdz";

    public static final String HEC_API_URL = Util.null2String(new BaseBean().getPropValue("hec","hecApiUrl"));
    public static final String HEC_TOKEN_URL = Util.null2String(new BaseBean().getPropValue("hec","hecTokenUrl"));
    public static final String CLIENT_SECRET = Util.null2String(new BaseBean().getPropValue("hec","clientSecret"));
    public static final String OA_URL = Util.null2String(new BaseBean().getPropValue("travelInfoAction","oaUrl"));
    public static final String FORMMODEID = Util.null2String(new BaseBean().getPropValue("travelInfoAction","dczformmodeid"));




    /**
     * 接收ehr的流程审批数据并调用hec的接口
     * */
    @POST
    @Path("/travelInfo")
    @Produces(MediaType.APPLICATION_JSON)
    public String travelInfoToHec(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {

//        String hecUrl = Util.null2String(new BaseBean().getPropValue())

        BufferedReader br;
        JSONObject json = null;
        String httpResult = null;

        //收到ehr的请求，先用ehr的id判断在两张表是否存在，如果存在，则更新，不存在则插入
        try {
            StringBuilder sb = new StringBuilder();
            br = new BufferedReader(new InputStreamReader((ServletInputStream) request.getInputStream(),"utf-8"));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            json = JSONObject.parseObject(sb.toString());
            baseBean.writeLog("travelInfoToHec:json=" + json);
            String processId = json.getString("processId");
            String processNumber = json.getString("processNumber");
            String processName = json.getString("processName");
            String processType = json.getString("processType");
            String employeeCode = json.getString("employeeCode");
            String description = json.getString("description");
            String processLink = json.getString("processLink");
            String closeFlag = json.getString("close_flag");
            String employeeId = null;
            String employeeName = null;
            String subcompanycode = null;
            RecordSet rs = new RecordSet();
            rs.executeQuery("select a.id, a.lastname, b.gsdm from HrmResource a left join uf_gongsigongchangd b on a.subcompanyid1 = b.gsdmms  where workcode = ? ", employeeCode);
            if(rs.next()){
                employeeId = rs.getString("id");
                employeeName = rs.getString("lastname");
                subcompanycode = rs.getString("gsdm");
            }

            JSONObject jsonReq = new JSONObject();
            jsonReq.put("sourceSystemCode", "OA");
            jsonReq.put("interfaceCode", "APPROVE_REQ");
            JSONObject requestData = new JSONObject();
            JSONArray approveRequests = new JSONArray();
            JSONObject approveRequest = new JSONObject();
            //传给hec的新的流程id
            String newProcessId = employeeId+System.currentTimeMillis();

            boolean b = selectByProcessEhrId(processId);
            boolean c = selectBySpdh(processId);
            //判断商旅出差对账单中是否有这条审批单，没有则插入
            if(!c){
                RecordSet rs2 = new RecordSet();
                String selectEmploySql = "select * from HrmResource where loginid = ?";
                rs2.executeQuery(selectEmploySql,employeeCode);
                String sqrId = "";
                String sqrDepartmentId = "";
                String sqrSubcompanyId = "";
                String nowDate = LocalDate.now().toString();
                if(rs2.next()){
                    sqrId = rs2.getString("id");
                    sqrDepartmentId = rs2.getString("departmentid");
                    sqrSubcompanyId = rs2.getString("subcompanyid1");
                }
                //增加建模数据
                String insertSql = "insert into " + UF_SLCCDZ + " (sqr, szbm, szgs, spdh, sqrq,zdlx, formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values(?,?,?,?,?,?,?,?,?,?,?)";
                Date date = new Date();
                rs2.executeUpdate(insertSql, sqrId, sqrDepartmentId, sqrSubcompanyId, processId, nowDate,processType, FORMMODEID, 1, 0, getNowTimeStr(date, "yyyy-MM-dd"), getNowTimeStr(date, "HH:mm:ss"));

                //修改建模模块权限
                int id = selectIdBySpdh(processId);
                ModeRightInfo ModeRightInfo = new ModeRightInfo();
                ModeRightInfo.setNewRight(true);
                ModeRightInfo.editModeDataShare(1, Integer.parseInt(FORMMODEID),id);
            }
            //判断是否接收过这条审批单，如果接收过就用原来的流程id
            if(b){
                RecordSet rs3 = new RecordSet();
                String selectEmploySql = "select * from "+UF_TRAVEL_INFO+" where process_ehr_id = ?";
                rs3.executeQuery(selectEmploySql,processId);
                if(rs3.next()){
                    newProcessId = rs3.getString("process_id");
                }
            }
            approveRequest.put("processId", newProcessId);
            approveRequest.put("processNumber", processNumber);
            approveRequest.put("processName", processName+"-"+employeeName+"-"+processNumber);
            approveRequest.put("processType", processType);
            approveRequest.put("employeeId", employeeId);
            approveRequest.put("employeeCode", employeeCode);
            approveRequest.put("closeFlag", closeFlag);
//            approveRequest.put("travelLink", OA_URL + "/ryytn/LoginUfSlccdz.jsp#&billid="+selectIdBySpdh(processId));
            approveRequest.put("employeeName", employeeName);
            approveRequest.put("description", description);
            approveRequest.put("processLink", OA_URL + "/ryytn/LoginRedirectEHR.jsp#&processId="+newProcessId);
            approveRequest.put("amount", "");
            approveRequest.put("accEntityCode", subcompanycode);
            approveRequests.add(approveRequest);
            requestData.put("approveRequests", approveRequests);
            jsonReq.put("requestData", requestData);

            //获取token
            String token  = getToken();
            if("".equals(token)){
                token = getToken();
            }
            baseBean.writeLog("travelInfoToHec:请求json=jsonReq=" + jsonReq);
            httpResult = HttpRequest
                    .post(HEC_API_URL)
                    .header("Authorization","Bearer "+ token)
//                    .headerMap(header,false)
                    .body(jsonReq.toJSONString())
                    .execute().body();
            baseBean.writeLog("travelInfoToHec:请求HEC返回的结果=httpResult=" + httpResult);

            JSONObject result = JSONObject.parseObject(httpResult);
            if("S".equals(result.getString("status"))){
                RecordSet rs1 = new RecordSet();
                if(!b){
                    String insertSql = "insert into " + UF_TRAVEL_INFO + " (process_id, process_link, process_ehr_id, process_code, process_name, process_type, employee_code, description, close_flag) values(?,?,?,?,?,?,?,?,?)";
                    rs1.executeUpdate(insertSql, newProcessId, processLink, processId, processNumber, processName+"-"+employeeName+"-"+processNumber, processType, employeeCode, description, closeFlag);
                } else {
                    String updateSql = "update " + UF_TRAVEL_INFO + " set process_link=?,process_name=?, employee_code=?, description=?, close_flag=? where process_ehr_id=?";
                    rs1.executeUpdate(updateSql, processLink, processName+"-"+employeeName+"-"+processNumber, employeeCode, description, closeFlag, processId);
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
        return httpResult;
    }

    private static String getNowTimeStr(Date time, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }



    public String getToken() throws Exception {
        Map<String,String> params = new HashMap<>();
        params.put("client_id","ry1tn-hec");
        params.put("grant_type","client_credentials");
        params.put("client_secret",CLIENT_SECRET);
        HttpClientResult result = HttpClientUtils.doPost(HEC_TOKEN_URL, params);
        new BaseBean().writeLog("travelInfoToHec :result " + result);

        return JSONObject.parseObject(result.getContent()).getString("access_token");
    }

    public Map<String,String> getHeader(String token) {
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json;charset=utf-8");
        headers.put("Authorization","Bearer "+ token);
        new BaseBean().writeLog("travelInfoToHec :result " + headers);

        return headers;
    }

    public boolean selectByProcessEhrId(String processEhrId) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+UF_TRAVEL_INFO+" where process_ehr_id =? ", processEhrId);
        return rs1.next();
    }

    /**
     * 查找该记录的id
     * @param spdh
     * @return
     */
    public int  selectIdBySpdh(String spdh) {
        RecordSet rs = new RecordSet();
        int id = -1;
        String sql = "select * from " + UF_SLCCDZ + " where spdh = ?";
        rs.executeQuery(sql, spdh);
        if(rs.next()){
            id = rs.getInt("id");
        }
        return id;
    }

    public boolean selectBySpdh(String spdh) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+UF_SLCCDZ+" where spdh =? ", spdh);
        return rs1.next();
    }



}

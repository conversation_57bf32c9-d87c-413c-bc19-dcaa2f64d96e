package com.api.yitouniu.workflow.action.okr;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.afternodeoperation.JXPersonalEvaluateArchivePreAction;
import yitouniu.util.ActionUtil;
import yitouniu.util.CreateJxWorkflowUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.UnsupportedEncodingException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/5/22 16:05
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/action/okr/JxRysxAction")
public class JxRysxAction {


    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    /**
     * 触发制定，可勾选，可全部
     * */
    @POST
    @Path("/formulate")
    @Produces(MediaType.TEXT_PLAIN)
    public  String formulate(@Context HttpServletRequest request, @Context HttpServletResponse response){
        baseBean.writeLog("formulate start" + request);
        JSONObject result = new JSONObject();
        try {
            String ids = request.getParameter("ids");
            //0为勾选，1为全部
            int type = Integer.parseInt(request.getParameter("type"));
            //绩效类型 0 季度， 1 月度
            int performanceType = Integer.parseInt(request.getParameter("performanceType"));
            if(StrUtil.isBlank(ids) && type == 0){
                result.put("status", "0");
                result.put("msg","未选择人员，请重试");
                return result.toJSONString();
            }

            rs.executeQuery("select count(DISTINCT cflx) num from uf_ryssxx where id in (?) ", ids);
            int num = rs.getInt("num");
            if(num > 2){
                result.put("status", "0");
                result.put("msg","勾选了季度和月度，请重新勾选");
                return result.toJSONString();
            }
            boolean deleteSql = rs.executeUpdate("delete a from uf_ryssxx a inner join HrmResource b on a.cfry = b.id where b.status >= 4");
            baseBean.writeLog("formulate deleteSql = " + deleteSql);
            if(!deleteSql){
                result.put("status", "0");
                result.put("msg","删除离职人员失败，请重试");
                return result.toJSONString();
            }
            //异步插入
            CompletableFuture.runAsync(() -> {
                formulateProcess(type, ids, performanceType);
            });

        } catch(Exception e){
            result.put("status","0");
            result.put("msg","网络连接异常error");
            baseBean.writeLog(this.getClass().getName(),"出错鸟:"+e);
        }
        baseBean.writeLog("formulate end");
        result.put("status","1");
        result.put("msg","创建成功");
        return result.toJSONString();
    }


    /**
     * 触发复核
     * */
    @POST
    @Path("/doReview")
    @Produces(MediaType.TEXT_PLAIN)
    public  String doReview(@Context HttpServletRequest request, @Context HttpServletResponse response){
        baseBean.writeLog("doReview start");
        JSONObject result = new JSONObject();
        try {
            //数据id
            int billId = Integer.parseInt(request.getParameter("billId"));
            //表单id
            String formId = request.getParameter("formId");
            //绩效类型 0 季度， 1 月度
            int performanceType = Integer.parseInt(request.getParameter("performanceType"));

            String performanceTableName = "";
            rs.executeQuery("select * from workflow_bill where id = ?", formId);
            if(rs.next()){
                performanceTableName = rs.getString("tablename");
            }
//            if(performanceType == 0) {
//                performanceTableName = "uf_bmjdjx";
//            } else if(performanceType == 1){
//                performanceTableName = "uf_bmydjxxx";
//            }
            result = doReviewProcess(billId, formId, performanceTableName, performanceType);

        } catch(Exception e){
            result.put("status","0");
            result.put("msg","网络连接异常error");
            baseBean.writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        baseBean.writeLog("doReview end");
        return result.toJSONString();
    }


    public JSONObject formulateProcess(Integer type, String ids, Integer performanceType){
        JSONObject result = new JSONObject();
        List<Map<String, String>> ryList = new ArrayList<>();
        List<Integer> hdRyList = new ArrayList<>();
        LocalDate nowTime = LocalDate.now();
        //季度选项框  0-3对应1-4
        String jxlxName = "";
        //0：季度  1：月度
        String jxlxValue = "";
        if(performanceType == 0){
            jxlxName = "季度";
            jxlxValue = "0";
            hdRyList = CreateJxWorkflowUtil.selectHdRyList((nowTime.getMonthValue()+2)/3 -1, "jd");
        } else if(performanceType == 1){
            jxlxName = "月度";
            jxlxValue = "1";
            hdRyList = CreateJxWorkflowUtil.selectHdRyList(nowTime.getMonthValue() -1, "yd");
        }
        ryList = getRyList(type, ids, performanceType, hdRyList);
        baseBean.writeLog("formulate.formulateProcess.ryList="+ryList);
        HashSet<String> departmentIdSet = ryList.stream()
                .filter(o -> o != null && o.containsKey("departmentid"))
                .map(o -> o.get("departmentid"))
                .collect(Collectors.toCollection(HashSet::new));
        List<String> managerIdList = ryList.stream().map(o -> o.get("managerid")).distinct().collect(Collectors.toList());
        baseBean.writeLog("formulate.formulateProcess.departmentIdSet="+departmentIdSet);
        baseBean.writeLog("formulate.formulateProcess.managerIdList="+managerIdList);

        if(CollUtil.isEmpty(ryList)){
            result.put("status","0");
            result.put("msg","所选条件没有可制定的人员，请确认");
            return result;
        }
        //获取特殊人员的固定模版
        Map<String, List<Map<String,String>>> tsryListMap = CreateJxWorkflowUtil.getTemplateByName();

        String managerIdString = managerIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        RecordSet gjzgRs = new RecordSet();
        String gjzgSql = "select id,managerid from HrmResource where id in (" + managerIdString + ")";
        gjzgRs.executeQuery(gjzgSql);
        Map<String, String> gjzgMap = new HashMap<>();
        while (gjzgRs.next()) {
            String managerid = gjzgRs.getString("managerid");
            if("276".equals(managerid)){
                managerid = gjzgRs.getString("id");
            }
            gjzgMap.put(gjzgRs.getString("id"), managerid);
        }
        baseBean.writeLog("CreateForJDJXWorkflowCron:获取隔级主管id=" + gjzgMap);
        //获取部门的一级部门id
        Map<String, String> yjbmMap = CreateJxWorkflowUtil.getYjbmMap(departmentIdSet);
        baseBean.writeLog("CreateForJDJXWorkflowCron:获取部门的一级部门id=" + yjbmMap);
        //获取所有杭州一级部门和负责人
        Map<String, String> yjbmfzrMap = CreateJxWorkflowUtil.getYjbmfzrMap();
        baseBean.writeLog("CreateForJDJXWorkflowCron:所有杭州一级部门和负责人=" + yjbmfzrMap);
        CreateJxWorkflowUtil.createWorkflowV1(ryList, gjzgMap, nowTime, yjbmMap, yjbmfzrMap, jxlxValue, jxlxName, tsryListMap);

        result.put("status","1");
        result.put("msg","创建成功");
        return result;
    }


    /**
     * 创建复核流程
     * @param billId
     * @param formId
     * @return
     */
    public JSONObject doReviewProcess(int billId, String formId, String performanceTableName, int performanceType){
        JSONObject result = new JSONObject();
        //如果这个一级部门下没有人需要个人评价了的话，触发创建部门绩效评价流程
        //不再判断，点击按钮时，直接创建
//        boolean queryFlag = query(updateTableName, yjbmmc, nf, time);

        List<Map<String, String>> itemMapList = queryByMainId(performanceTableName, billId);
        Map<String ,String> bmJXMap = new HashMap<>();

        rs.executeQuery("select * from " + performanceTableName + " where id = ? ", billId);

        if (rs.next()){
            //年份
            bmJXMap.put("nf", rs.getString("jxsznf"));
            //绩效所属季度/月度
            bmJXMap.put("time", rs.getString("jxszyf"));
            //一级部门负责人
            bmJXMap.put("yjbmfzr", rs.getString("bmfzr"));
            //一级部门名称id
            bmJXMap.put("yjbmmc", rs.getString("yjbmmc"));
            //部门绩效信息核对表id
            bmJXMap.put("bmjxxxhdbid", String.valueOf(billId));
            //获取一级部门负责人的所属部门
            bmJXMap.put("yjbmfzrDepId", String.valueOf(queryDepId(rs.getString("bmfzr"))));
        }

        baseBean.writeLog("doReviewProcess.一级部门bmJXMap = " + bmJXMap);
        baseBean.writeLog("doReviewProcess.一级部门下的明细itemMapList = " + itemMapList);

        if(itemMapList.size() >0){
            //创建部门绩效评价流程
            boolean flag = CreateJxWorkflowUtil.createWorkflowForFirstDepartmentEvaluate(bmJXMap, itemMapList, String.valueOf(performanceType));
            if(!flag){
                result.put("status","0");
                result.put("msg","创建部门绩效评价流程失败");
                return result;
            }
        } else {
            result.put("status","0");
            result.put("msg","该一级部门下没有个人评价绩效流程明细");
            return result;
        }
        result.put("status","1");
        result.put("msg","创建成功");
        return result;
    }


    public List<Map<String, String>> getRyList(Integer type, String ids, Integer performanceType, List<Integer> hdRyList){
        String rySql = "select a.sftsry,a.okrmb, b.id, b.lastname,b.workcode,b.departmentid,b.jobtitle,b.managerid,b.createdate " +
                " from uf_ryssxx a " +
                " left join HrmResource b on a.cfry = b.id " +
                " where cflx = " + performanceType + " ";
        if(type == 0){
            rySql = rySql + " and a.id in (" + ids + ")";
            rs.executeQuery(rySql);
        } else if(type == 1){
            rs.executeQuery(rySql);
        }
        List<Map<String, String>> ryList = new ArrayList<>();
        rs.executeQuery(rySql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            //当类型为全部的时候，判断已经制定过的不再制定
            if(hdRyList.contains(rs.getInt("id")) && type == 1){
                continue;
            }
            String tsry = rs.getString("sftsry");
            map.put("sftsry", tsry);

            map.put("id", rs.getString("id"));
            map.put("lastname", rs.getString("lastname"));
            map.put("workcode", rs.getString("workcode"));
            map.put("departmentid", rs.getString("departmentid"));
            map.put("jobtitle", rs.getString("jobtitle"));
            map.put("managerid", rs.getString("managerid"));
            map.put("createdate", rs.getString("createdate"));
            map.put("okrmb", rs.getString("okrmb"));
            ryList.add(map);
        }

        return ryList;
    }

    public int queryDepId(String id){
        int depId = -1;
        rs.executeQuery("select departmentid from HrmResource where id = ?", id);
        if(rs.next()){
            depId = rs.getInt("departmentid");
        }
        return depId;
    }

    public List<Map<String, String>> queryByMainId(String updateTableName, int mainId){
        List<Map<String, String>> resultList = new ArrayList<>();
        String sql = "select a.id,a.xm,a.jxpjlc,a.jzgdf,a.okrdf,a.xtjg,a.bm,b.jobtitle,b.lastname from " + updateTableName + "_dt1 a  left join HrmResource b on a.xm = b.id where a.mainid = ?";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, mainId);
        while(rs.next()){
            Map<String, String> map = new HashMap<>();
            //明细id
            map.put("mxid", rs.getString("id"));
            //姓名
            map.put("xm", rs.getString("xm"));
            //绩效评价流程
            map.put("jxpjlc", rs.getString("jxpjlc"));
            //价值观得分
            map.put("jzgdf", rs.getString("jzgdf"));
            //okr得分
            map.put("okrdf", rs.getString("okrdf"));
            //系统结果
            String xtjg = rs.getString("xtjg");
            //为空的话就默认C
            if(StrUtil.isBlank(xtjg)){
                xtjg = "2";
            }
            map.put("xtjg", xtjg);
            //部门
            map.put("bm", rs.getString("bm"));
            //岗位
            map.put("gw", rs.getString("jobtitle"));
            //姓名名称
            map.put("lastname", rs.getString("lastname"));
            resultList.add(map);
        }
        return resultList;
    }






}

package com.api.yitouniu.workflow.action.okr;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.CreateJxWorkflowUtilV1;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/5/22 16:05
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/action/okr/JxRysxActionV11")
public class JxRysxActionV11 {


    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();


    /**
     * 触发复核
     * */
    @POST
    @Path("/doReviewV2")
    @Produces(MediaType.TEXT_PLAIN)
    public  String doReview(@Context HttpServletRequest request, @Context HttpServletResponse response){
        baseBean.writeLog("doReviewV1 start");
        JSONObject result = new JSONObject();
        try {
            //数据id
            int billId = Integer.parseInt(request.getParameter("billId"));
            //表单id
            String formId = request.getParameter("formId");
            //绩效类型 0 季度， 1 月度
            int performanceType = Integer.parseInt(request.getParameter("performanceType"));

            String performanceTableName = "";
            rs.executeQuery("select * from workflow_bill where id = ?", formId);
            if(rs.next()){
                performanceTableName = rs.getString("tablename");
            }
//            if(performanceType == 0) {
//                performanceTableName = "uf_bmjdjx";
//            } else if(performanceType == 1){
//                performanceTableName = "uf_bmydjxxx";
//            }
            result = doReviewProcess(billId, formId, performanceTableName, performanceType);

        } catch(Exception e){
            result.put("status","0");
            result.put("msg","网络连接异常error");
            baseBean.writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        baseBean.writeLog("doReview end");
        return result.toJSONString();
    }


    /**
     * 创建复核流程
     * @param billId
     * @param formId
     * @return
     */
    public JSONObject doReviewProcess(int billId, String formId, String performanceTableName, int performanceType){
        JSONObject result = new JSONObject();
        //如果这个一级部门下没有人需要个人评价了的话，触发创建部门绩效评价流程
        //不再判断，点击按钮时，直接创建
//        boolean queryFlag = query(updateTableName, yjbmmc, nf, time);

        List<Map<String, String>> itemMapList = queryByMainId(performanceTableName, billId);
        Map<String ,String> bmJXMap = new HashMap<>();

        rs.executeQuery("select * from " + performanceTableName + " where id = ? ", billId);

        if (rs.next()){
            //年份
            bmJXMap.put("nf", rs.getString("jxsznf"));
            //绩效所属季度/月度
            bmJXMap.put("time", rs.getString("jxszyf"));
            //一级部门负责人
            bmJXMap.put("yjbmfzr", rs.getString("bmfzr"));
            //一级部门名称id
            bmJXMap.put("yjbmmc", rs.getString("yjbmmc"));
            //部门绩效信息核对表id
            bmJXMap.put("bmjxxxhdbid", String.valueOf(billId));
            //获取一级部门负责人的所属部门
            bmJXMap.put("yjbmfzrDepId", String.valueOf(queryDepId(rs.getString("bmfzr"))));
        }

        baseBean.writeLog("doReviewProcess.一级部门bmJXMap = " + bmJXMap);
        baseBean.writeLog("doReviewProcess.一级部门下的明细itemMapList = " + itemMapList);

        if(itemMapList.size() >0){
            //创建部门绩效评价流程
            boolean flag = CreateJxWorkflowUtilV1.createWorkflowForFirstDepartmentEvaluate(bmJXMap, itemMapList, String.valueOf(performanceType));
            if(!flag){
                result.put("status","0");
                result.put("msg","创建部门绩效评价流程失败");
                return result;
            }
        } else {
            result.put("status","0");
            result.put("msg","该一级部门下没有个人评价绩效流程明细");
            return result;
        }
        result.put("status","1");
        result.put("msg","创建成功");
        return result;
    }




    public int queryDepId(String id){
        int depId = -1;
        rs.executeQuery("select departmentid from HrmResource where id = ?", id);
        if(rs.next()){
            depId = rs.getInt("departmentid");
        }
        return depId;
    }

    public List<Map<String, String>> queryByMainId(String updateTableName, int mainId){
        List<Map<String, String>> resultList = new ArrayList<>();
//        String sql = "select a.id,a.xm,a.jxpjlc,a.jzgdf,a.okrdf,a.xtjg,a.bm,a.khdy,a.cxdd,a.hzgy,a.xfjq,b.jobtitle,b.lastname from " + updateTableName + "_dt1 a  left join HrmResource b on a.xm = b.id where a.mainid = ?";
        String sql = "select " +
                "a.id,a.xm,c.jxsznf,c.jxszyf,a.jxpjlc,a.jzgdf,a.okrdf,a.xtjg,a.bm,a.khdy,a.cxdd,a.hzgy,a.xfjq,b.jobtitle,b.lastname, " +
                "c.id as mainid,d.sfcfpjlc " +
                "from  "+updateTableName+"_dt1 a " +
                "left join "+updateTableName+" c on a.mainid = c.id " +
                "left join HrmResource b on a.xm = b.id " +
                "left join uf_grjxxx d on d.xm = a.xm and d.nf = c.jxsznf and d.jd = c.jxszyf " +
                "where a.mainid = ? and d.sfcfpjlc = 0;";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, mainId);
        while(rs.next()){
            Map<String, String> map = new HashMap<>();
            //明细id
            map.put("mxid", rs.getString("id"));
            //姓名
            map.put("xm", rs.getString("xm"));
            //绩效评价流程
            map.put("jxpjlc", rs.getString("jxpjlc"));
            //价值观得分
            map.put("jzgdf", rs.getString("jzgdf"));
            //okr得分
            map.put("okrdf", rs.getString("okrdf"));
            //系统结果
            String xtjg = rs.getString("xtjg");
            //为空的话就默认C
            if(StrUtil.isBlank(xtjg)){
                xtjg = "2";
            }
            map.put("xtjg", xtjg);
            //部门
            map.put("bm", rs.getString("bm"));
            //岗位
            map.put("gw", rs.getString("jobtitle"));
            //姓名名称
            map.put("lastname", rs.getString("lastname"));

            //客户第一得分
            map.put("khdy", rs.getString("khdy"));
            // 诚信担当
            map.put("cxdd", rs.getString("cxdd"));
            // 合作共赢
            map.put("hzgy", rs.getString("hzgy"));
            // 幸福进取
            map.put("xfjq", rs.getString("xfjq"));

            resultList.add(map);
        }
        return resultList;
    }






}

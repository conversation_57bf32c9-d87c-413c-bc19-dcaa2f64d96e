package com.api.yitouniu.workflow.action;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.fna.domain.Result;
import weaver.general.BaseBean;
import weaver.general.Util;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/29 11:19
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/action/sapAction")
public class sapAction {

    BaseBean baseBean = new BaseBean();

    public static final String MFDDTABLENAME = Util.null2String(new BaseBean().getPropValue("mfddtablename","mfdd"));
    public static final String YGNGTABLENAME = Util.null2String(new BaseBean().getPropValue("mfddtablename","ygng"));


    /**
     * 接收回传的免费订单的快递号
     * */
    @POST
    @Path("/expresscode/save")
    @Produces(MediaType.APPLICATION_JSON)
    public String saveExpresscode(@Context HttpServletRequest request, @Context HttpServletResponse response) throws IOException {
        BufferedReader br;
        JSONObject json = null;
        String httpResult = null;
        String tableName = null;
        JSONObject result = new JSONObject();
        JSONObject ctrl = new JSONObject();
        boolean flag = true;
        boolean updateFlag = true;
        String errMessage = "";

        //收到sap的请求，解析请求json
        try {
            StringBuilder sb = new StringBuilder();
            br = new BufferedReader(new InputStreamReader((ServletInputStream) request.getInputStream(), "utf-8"));
            String line = null;
            while ((line = br.readLine()) != null) {
                sb.append(line);
            }
            json = JSONObject.parseObject(sb.toString());
            baseBean.writeLog("saveExpresscode:接收json=" + json.toJSONString());
            JSONArray dataArray = json.getJSONArray("DATA");
            baseBean.writeLog("saveExpresscode:dataArray=" + dataArray.toJSONString());
            String processNo = ((JSONObject)dataArray.get(0)).getString("VBELN_OA");
            baseBean.writeLog("saveExpresscode:processNo=" + processNo);
            if(!processNo.contains("-")){
                ctrl.put("MSGTY","E");
                ctrl.put("MSAGE","所传oa流程编号不在系统中存在");
                result.put("CTRL",ctrl);
                httpResult = result.toJSONString();
                return httpResult;
            }
            if("MFDD".equals(processNo.substring(0,processNo.indexOf("-")))){
                tableName = MFDDTABLENAME;
            } else {
                tableName = YGNGTABLENAME;
            }
            List<List> list = new ArrayList<>();
            String expresscode = null;
            String oaLcbh = null;
            for (int i = 0;i < dataArray.size();i++){
                JSONObject y=(JSONObject) dataArray.get(i);
                List o = new ArrayList();
                expresscode = y.getString("TRAID");
                o.add(expresscode);
                o.add(expresscode);
                String VBELN_OA = y.getString("VBELN_OA");
                int x = VBELN_OA.lastIndexOf("-");
                oaLcbh = VBELN_OA.substring(0,x)+VBELN_OA.substring(x+1);
                flag = select(oaLcbh, tableName);
                baseBean.writeLog("saveExpresscode:flag=" + flag);
                if(!flag){
                    errMessage = "所传oa流程编号不在系统中存在";
                    break;
                }
                o.add(oaLcbh);
                list.add(o);
            }
            baseBean.writeLog("saveExpresscode:list=" + list);
            baseBean.writeLog("saveExpresscode:expresscode=" + expresscode);
            baseBean.writeLog("saveExpresscode:oaLcbh=" + oaLcbh);
            if(flag){
                updateFlag = update(list, tableName);
                baseBean.writeLog("saveExpresscode:updateFlag=" + updateFlag);
                if(!updateFlag){
                    errMessage = "OA更新失败";
                }
            }
        } catch (Exception e){
            baseBean.writeLog("异常=" + e);
        }
        if(flag && updateFlag){
            ctrl.put("MSGTY","S");
            ctrl.put("MSAGE","");
            result.put("CTRL",ctrl);
            httpResult = result.toJSONString();
        } else {
            ctrl.put("MSGTY","E");
            ctrl.put("MSAGE",errMessage);
            result.put("CTRL",ctrl);
            httpResult = result.toJSONString();
        }

        return httpResult;
    }

    public boolean update(List<List> list, String tableName) {
        RecordSet rs1 = new RecordSet();
        String sql = " UPDATE " + tableName + "_dt1 " +
                " SET expresscode =  CASE WHEN expresscode is null or expresscode= '' THEN ? " +
                " ELSE expresscode + ',' + ? " +
                " END  " +
                " WHERE " +
                " lcbhjxh = ? ";
        return rs1.executeUpdate(sql, list);
    }

    public boolean select(String lcbhjxh, String tableName) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+tableName+"_dt1 where lcbhjxh = ?",lcbhjxh);
        return rs1.next();
    }







}

package com.api.yitouniu.workflow.ajax.SAP;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;
import yitouniu.esb.ReturnMsgToSAP;
import yitouniu.esb.TestEsb;
import yitouniu.util.SAPUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Chen Binlei
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 10:56 2022/2/23
 */
@Path("yitouniu/workflow/ajax/SAP/test")
public class test extends ReturnMsgToSAP {



    /**
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getTest")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getTest(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("开始进行联动 : ");
        new BaseBean().writeLog("Ajax连接sapTest" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
//        Map execute = new HashMap();

        JSONObject jsonObject = new JSONObject();

        JSONObject ctrlObject = new JSONObject();
        ctrlObject.put("SYSID","OA");
        ctrlObject.put("REVID","SAP");
        ctrlObject.put("FUNID","ZINF050");
        ctrlObject.put("INFID","123");
        ctrlObject.put("UNAME","sysadmin");
        ctrlObject.put("DATUM","");
        ctrlObject.put("UZEIT","");
        ctrlObject.put("KEYID","");
        ctrlObject.put("MSGTY","");
        ctrlObject.put("MSAGE","");

        JSONArray dataArr = new JSONArray();

        JSONObject dataJSON = new JSONObject();
        dataJSON.put("NAME_ORG1","裘区萍");
        dataJSON.put("NAME_ORG2","c010123");
        dataJSON.put("BUKRS","7000");
        dataJSON.put("BANKN","6217251400019603366");
        dataJSON.put("BKREF","");
        dataJSON.put("BANKL","************");
        dataArr.add(dataJSON);
        jsonObject.put("CTRL",ctrlObject);
        jsonObject.put("DATA",dataJSON);
//        String execute = SAPUtil.execute(jsonObject.toString());
        TestEsb test = new TestEsb();
//        Map result = test.executeParam(JSONObject.toJavaObject(jsonObject,Map.class));
//        String execute = (String) result.get("msg");
//        String status = (String) result.get("status");

        new BaseBean().writeLog("银行信息维护流程测试execute" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

//        String msgty = (String) execute.get("MSGTY");
//        String result = (String) execute.get("RESULT");

        JSONObject resultObject = new JSONObject();

//        if(!"S".equals(msgty)){
//            resultObject.put("status","error");
//        } else{
//            resultObject.put("status","success");
//        }
        resultObject.put("status","error");
        resultObject.put("msg","execute");

        new BaseBean().writeLog("结束进行联动 : ");


        return resultObject.toJSONString();
    }



}

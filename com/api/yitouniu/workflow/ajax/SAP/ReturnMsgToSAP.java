package com.api.yitouniu.workflow.ajax.SAP;

import com.alibaba.fastjson.JSONObject;
import com.api.yitouniu.workflow.ajax.utils.SAPUtil;
import weaver.general.BaseBean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class ReturnMsgToSAP {

    public Map execute(String params) {
        Map map = new HashMap();
        try {


            String execute = SAPUtil.execute(params); // 调用sap返回结果
            new BaseBean().writeLog("返回的数据: "+execute);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            if (jsonObject.containsKey("DATA")) {

                List<JSONObject> dataObject = (List<JSONObject>) jsonObject.get("DATA"); // 获取响应结果

                boolean isResult = true;  // 判断返回结果是否成功
                if (dataObject.size() > 0) {
                    for (JSONObject result : dataObject) {
                        String msgty = result.getString("MSGTY");
                        if ("S".equals(msgty)) {
                            isResult = true;
                        } else {
                            isResult = false;
                            break;
                        }
                    }
                }
                if (isResult) {
                    map.put("MSGTY", "S");
                    map.put("MSAGE", "成功");
                    map.put("RESULT", execute);
                    map.put("PARAMS", params);
                } else {
                    map.put("MSGTY", "F");
                    map.put("MSAGE", "失败");
                    map.put("RESULT", execute);
                    map.put("PARAMS", params);
                }
                return map;
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "失败");
                map.put("RESULT", execute);
                map.put("PARAMS", params);
                return map;
            }
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "失败");
            map.put("RESULT", e);
            map.put("PARAMS", params);
        }
        return map;
    }

    public String nullChangeString(String value) {

        if (value == null || "".equals(value)) {
            return "";
        } else {
            return value;
        }

    }

}

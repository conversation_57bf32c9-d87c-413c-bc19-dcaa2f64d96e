package com.api.yitouniu.workflow.ajax.OA;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @Date 2022/8/16 11:37
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/ajax/OA/CustomerDataProcess")
public class CustomerDataProcess {

    @GET
    @Path("/CreditByCompany")
    @Produces(MediaType.TEXT_PLAIN)
    public  String CreditByCompany(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("CustomerDataProcess-CreditByCompany——开始");
        JSONObject jsonObject = new JSONObject();
        try {
            String companyDataId = request.getParameter("companyDataId");
            String departmentId = "";
            String firstDepartmentId = "";
            RecordSet rs2 = new RecordSet();
            rs2.executeQuery("select * from HrmDepartment where id = ? ", companyDataId);
            if(rs2.next()){
                departmentId = rs2.getString("departmentid");
            }
            while(true){
                RecordSet rs = new RecordSet();
                rs.executeQuery("select id, supdepid from hrmdepartment where id = ? ", departmentId);
                if(rs.next()){
                    String supdepid = rs.getString("supdepid");
                    String id = rs.getString("id");
                    if("0".equals(supdepid) || "".equals(supdepid)){
                        new BaseBean().writeLog("ContractOperationRecordAction：判断是否为0中departmentId = " + departmentId);
                        firstDepartmentId = id;
                        break;
                    }
                }
            }
            //新零售部门的id
            if("765".equals(firstDepartmentId)){
                jsonObject.put("status","1");
                jsonObject.put("msg","该部门是新零售运营中心下的部门");
            } else {
                jsonObject.put("status","0");
                jsonObject.put("msg","该部门不是新零售运营中心下的部门");
            }
        } catch(Exception e){
            jsonObject.put("status","error");
            new BaseBean().writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        new BaseBean().writeLog("CustomerDataProcess-CreditByCompany——结束");
        return jsonObject.toJSONString();
    }




}

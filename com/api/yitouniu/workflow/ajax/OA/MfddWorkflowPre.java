package com.api.yitouniu.workflow.ajax.OA;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @Date 2023/1/11 9:27
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/ajax/OA/MfddWorkflowPre")
public class MfddWorkflowPre {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @POST
    @Path("/getSapCode")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getSapCode(@Context HttpServletRequest request, @Context HttpServletResponse response){
        baseBean.writeLog("MfddWorkflow getSapCode start");
        JSONObject jsonObject = new JSONObject();
        try {
            String companyId = request.getParameter("companyId");
            String depId = request.getParameter("depId");
            String sapbmbm = "";
            String sapbmms = "";
            rs.executeQuery(" SELECT top 1 sapbmbm,sapbmms FROM formtable_main_306 WHERE oabm = ? AND sfmr = 0 AND fb = ? and sfdj != 0", depId, companyId);
            if(rs.next()){
                sapbmbm = rs.getString("sapbmbm");
                sapbmms = rs.getString("sapbmms");
            }

            if(StringUtils.isNotBlank(sapbmbm)){
                jsonObject.put("status","success");
                jsonObject.put("code",sapbmbm);
                jsonObject.put("msg",sapbmms);
            } else {
                jsonObject.put("status","0");
                jsonObject.put("msg","该部门id"+depId+"没有默认的成本中心");
            }
        } catch(Exception e){
            jsonObject.put("status","error");
            baseBean.writeLog(this.getClass().getName(),"出错鸟:"+e);
        }
        baseBean.writeLog("MfddWorkflow getSapCode end");


        return jsonObject.toJSONString();
    }


}
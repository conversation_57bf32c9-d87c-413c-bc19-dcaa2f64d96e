package com.api.yitouniu.workflow.ajax.OA;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/4/20 9:26
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/ajax/OA/JdythAjax")
public class JdythAjax {

    RecordSet rs = new RecordSet();


    @GET
    @Path("/getSalesStructure")
    @Produces(MediaType.TEXT_PLAIN)
    public String getSalesStructure(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog("getSalesStructure 开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("getSalesStructure value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("getSalesStructure valueArray = " + Arrays.toString(valueArray));
        map = getSalesStructure(valueArray[0], valueArray[1]);

        jsonObject.put("firstEname", map.get("firstEname"));
        jsonObject.put("secondEname", map.get("secondEname"));
        jsonObject.put("thirdEname", map.get("thirdEname"));

        new BaseBean().writeLog("getSalesStructure jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();

    }

    /**
     * 获取销售架构
     * @param sortId
     * @param id
     * @return
     */
    public Map<String, String> getSalesStructure(String sortId, String id){
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        new BaseBean().writeLog("********getSalesStructure: id = " + id);
        new BaseBean().writeLog("********getSalesStructure: sortId = " + sortId);

        List<String> list = new ArrayList<>();
        String searchId = id;
        while (true){
            String sql = " select up_id,bm from uf_xsbmjg where id = ? ";
            rs.executeQuery(sql, searchId);
            if(rs.next()){
                searchId = rs.getString("up_id");
                list.add(rs.getString("bm"));
                if(StringUtils.isBlank(searchId)){
                    break;
                }
            }
        }
        if (list.size() == 3){
            thirdSortEname = list.get(0);
            secondSortEname = list.get(1);
            firstSortEname = list.get(2);
        } else if (list.size() == 2){
            thirdSortEname = "/";
            secondSortEname = list.get(0);
            firstSortEname = list.get(1);
        } else if (list.size() == 1){
            thirdSortEname = "/";
            secondSortEname = "/";
            firstSortEname = list.get(0);
        }

        map.put("firstEname",firstSortEname);
        map.put("secondEname",secondSortEname);
        map.put("thirdEname",thirdSortEname);

        new BaseBean().writeLog("********getSalesStructure: map = " + map);

        return map;
    }


}

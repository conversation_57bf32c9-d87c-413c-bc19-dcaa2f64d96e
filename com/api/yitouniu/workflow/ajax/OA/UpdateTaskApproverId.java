package com.api.yitouniu.workflow.ajax.OA;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.sql.ResultSet;

/**
 * @Author: <PERSON>i
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 14:24 2022/1/7
 */
@Path("yitouniu/workflow/ajax/OA/UpdateTaskApproverId")
public class UpdateTaskApproverId {

    @GET
    @Path("/UpdateApproverId")
    @Produces(MediaType.TEXT_PLAIN)
    public  String UpdateApproverId(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        try {
            String approverId = request.getParameter("approverId");
            String requestId = request.getParameter("requestId");
            String sqlUpdate = "update formtable_main_91 set processUser = ? WHERE requestid = ?";
            RecordSet rs = new RecordSet();
            rs.executeUpdate(sqlUpdate,approverId,requestId);
            jsonObject.put("status","success");
            jsonObject.put("msg",approverId);
            new BaseBean().writeLog("ESB-UpdateTaskApproverId：UpdateTaskApproverId"+JSONObject.toJSONString(jsonObject));
        } catch(Exception e){
            jsonObject.put("status","error");
            new BaseBean().writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        new BaseBean().writeLog("结束进行联动 : ");

        return jsonObject.toJSONString();
    }


}

package com.api.yitouniu.workflow.ajax.OA;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @Date 2022/8/16 16:58
 * @Description TODO
 * @Version 1.0
 */
@Path("yitouniu/workflow/ajax/OA/CustomerFreeOrder")
public class CustomerFreeOrder {

    @POST
    @Path("/queryByMateriel")
    @Produces(MediaType.TEXT_PLAIN)
    public  String queryByMateriel(@Context HttpServletRequest request, @Context HttpServletResponse response) throws UnsupportedEncodingException {
        new BaseBean().writeLog("CustomerFreeOrder-queryByMateriel——开始");
        JSONObject jsonObject = new JSONObject();
        new BaseBean().writeLog("CustomerFreeOrder-queryByMateriel——开始"+request);
        try {
            String materielId = request.getParameter("MATNRValue");
            String materielName = request.getParameter("TXZ01Value");
            String MAKTX = "";
            RecordSet rs = new RecordSet();
            rs.executeQuery("select MAKTX from uf_wlzsj where MATNR = ? ", materielId);
            if(rs.next()){
                MAKTX = rs.getString("MAKTX");
            }
            new BaseBean().writeLog("materielName = ", materielId);
            new BaseBean().writeLog("materielName = ", materielName);
            new BaseBean().writeLog("MAKTX = " + MAKTX);

            if(!materielName.equals(MAKTX)){
                jsonObject.put("status", "0");
                jsonObject.put("msg","物料名称错误，请修正");
            } else {
                jsonObject.put("status", "1");
                jsonObject.put("msg","物料名称正确");
            }
        } catch(Exception e){
            jsonObject.put("status","error");
            jsonObject.put("msg","网络连接异常error");
            new BaseBean().writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        new BaseBean().writeLog("CustomerFreeOrder-queryByMateriel——结束");
        return jsonObject.toJSONString();
    }
}

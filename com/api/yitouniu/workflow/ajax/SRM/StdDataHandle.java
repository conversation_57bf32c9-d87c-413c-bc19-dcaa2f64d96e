package com.api.yitouniu.workflow.ajax.SRM;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.StdUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-06-20 16:25
 **/
@Path("yitouniu/workflow/ajax/SRM/StdDataHandle")
public class StdDataHandle {
    /**
     * 本地缓存，一小时过期
     */
    private static long EXPIRE_TIME;
    private static String IFRAME_COOKIE;
    /**
     * 获取新cookie的std登录接口
     */
    private static final String STD_IFRAME_LOGIN = "/crm-mdm/v1/external/std/fixedAccountLogin";


    RecordSet rs = new RecordSet();

    @GET
    @Path("/getStdSalesStructure")
    @Produces(MediaType.TEXT_PLAIN)
    public String getStdSalesStructure(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog("getSalesStructure 开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("getSalesStructure value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("getSalesStructure valueArray = " + Arrays.toString(valueArray));
        map = getStdSalesStructure(valueArray[0], valueArray[1]);

        jsonObject.put("firstSortEname", map.get("firstSortEname"));
        jsonObject.put("secondSortEname", map.get("secondSortEname"));
        jsonObject.put("thirdSortEname", map.get("thirdSortEname"));
//        jsonObject.put("forthSortEname",map.get("forthSortEname"));
//        jsonObject.put("fifthSortEname",map.get("fifthSortEname"));
//        jsonObject.put("sixthSortEname",map.get("sixthSortEname"));

        jsonObject.put("firstSortEcode", map.get("firstSortEcode"));
        jsonObject.put("secondSortEcode", map.get("secondSortEcode"));
        jsonObject.put("thirdSortEcode", map.get("thirdSortEcode"));
//        jsonObject.put("forthSortEcode", map.get("forthSortEcode"));
//        jsonObject.put("fifthSortEcode", map.get("fifthSortEcode"));
//        jsonObject.put("sixthSortEcode", map.get("sixthSortEcode"));

        new BaseBean().writeLog("getSalesStructure jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();

    }

    /**
     * 多级分类
     *
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getStdSort")
    @Produces(MediaType.TEXT_PLAIN)
    public String getStdSort(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog("MaterialMasterData——getStdSort，开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("MaterialMasterData——getSort，value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("MaterialMasterData——getSort，valueArray = " + Arrays.toString(valueArray));
        map = getStdAllSort(valueArray[0], valueArray[1]);

        jsonObject.put("firstSortEname", map.get("firstSortEname"));
        jsonObject.put("secondSortEname", map.get("secondSortEname"));
        jsonObject.put("thirdSortEname", map.get("thirdSortEname"));
        jsonObject.put("fourthSortEname", map.get("fourthSortEname"));
        jsonObject.put("fifthSortEname", map.get("fifthSortEname"));

        jsonObject.put("firstSortEcode", map.get("firstSortEcode"));
        jsonObject.put("secondSortEcode", map.get("secondSortEcode"));
        jsonObject.put("thirdSortEcode", map.get("thirdSortEcode"));
        jsonObject.put("fourthSortEcode", map.get("fourthSortEcode"));
        jsonObject.put("fifthSortEcode", map.get("fifthSortEcode"));


        jsonObject.put("ZLJID", map.get("firstProductItemCode"));
        jsonObject.put("ZYJID", map.get("secondProductItemCode"));
        jsonObject.put("ZEJID", map.get("thirdProductItemCode"));
        jsonObject.put("ZSJID", map.get("forthProductItemCode"));
        jsonObject.put("ZZSJID", map.get("fifthProductItemCode"));


        new BaseBean().writeLog("MaterialMasterData——getSort，jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();
    }

    /**
     * 获取std销售架构
     * @param sortId
     * @param orgCode
     * @return
     */
    public Map<String, String> getStdSalesStructure(String sortId, String orgCode){
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        String forthSortEname = "";
        String fifthSortEname = "";
        String sixthSortEname = "";

        String firstSortEcode = "";
        String secondSortEcode = "";
        String thirdSortEcode = "";
        String forthSortEcode = "";
        String fifthSortEcode = "";
        String sixthSortEcode = "";
        new BaseBean().writeLog("********getSalesStructure: orgCode = " + orgCode);
        new BaseBean().writeLog("********getSalesStructure: sortId = " + sortId);

        List<String> enamelist = new ArrayList<>();
        List<String> ecodelist = new ArrayList<>();
        String searchId = orgCode;
        while (true){
            String sql = "select orgCode,orgName,parentCode from uf_yxzzcj where orgCode = ? ";
            rs.executeQuery(sql, searchId);
            if(rs.next()){
                searchId = rs.getString("parentCode");
                new BaseBean().writeLog("StdSalesSearchId：{}", searchId);
                enamelist.add(rs.getString("orgName"));
                ecodelist.add(rs.getString("orgCode"));
                if(StringUtils.isBlank(searchId)){
                    new BaseBean().writeLog("StdSales跳出当前循环：{}", searchId);
                    break;
                }
            }
        }
        if (enamelist.size() == 6){
            sixthSortEname = enamelist.get(0);
            fifthSortEname = enamelist.get(1);
            forthSortEname = enamelist.get(2);
            thirdSortEname = enamelist.get(3);
            secondSortEname = enamelist.get(4);
            firstSortEname = enamelist.get(5);

            sixthSortEcode = ecodelist.get(0);
            fifthSortEcode = ecodelist.get(1);
            forthSortEcode = ecodelist.get(2);
            thirdSortEcode = ecodelist.get(3);
            secondSortEcode = ecodelist.get(4);
            firstSortEcode = ecodelist.get(5);
        }else if (enamelist.size() == 5){
            sixthSortEname = "/";
            fifthSortEname = enamelist.get(0);
            forthSortEname = enamelist.get(1);
            thirdSortEname = enamelist.get(2);
            secondSortEname = enamelist.get(3);
            firstSortEname = enamelist.get(4);

            sixthSortEcode = "/";
            fifthSortEcode = ecodelist.get(0);
            forthSortEcode = ecodelist.get(1);
            thirdSortEcode = ecodelist.get(2);
            secondSortEcode = ecodelist.get(3);
            firstSortEcode = ecodelist.get(4);
        } else if (enamelist.size() == 4) {
            sixthSortEname = "/";
            fifthSortEname = "/";
            forthSortEname = enamelist.get(0);
            thirdSortEname = enamelist.get(1);
            secondSortEname = enamelist.get(2);
            firstSortEname = enamelist.get(3);

            sixthSortEcode = "/";
            fifthSortEcode = "/";
            forthSortEcode = ecodelist.get(0);
            thirdSortEcode = ecodelist.get(1);
            secondSortEcode = ecodelist.get(2);
            firstSortEcode = ecodelist.get(3);
        } else if (enamelist.size() == 3){
            sixthSortEname = "/";
            fifthSortEname = "/";
            forthSortEname = "/";
            thirdSortEname = enamelist.get(0);
            secondSortEname = enamelist.get(1);
            firstSortEname = enamelist.get(2);

            sixthSortEcode = "/";
            fifthSortEcode = "/";
            forthSortEcode = "/";
            thirdSortEcode = ecodelist.get(0);
            secondSortEcode = ecodelist.get(1);
            firstSortEcode = ecodelist.get(2);
        } else if (enamelist.size() == 2){
            sixthSortEname = "/";
            fifthSortEname = "/";
            forthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = enamelist.get(0);
            firstSortEname = enamelist.get(1);

            sixthSortEcode = "/";
            fifthSortEcode = "/";
            forthSortEcode = "/";
            thirdSortEcode = "/";
            secondSortEcode = ecodelist.get(0);
            firstSortEcode = ecodelist.get(1);
        } else if (enamelist.size() == 1){
            sixthSortEname = "/";
            fifthSortEname = "/";
            forthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = "/";
            firstSortEname = enamelist.get(0);

            sixthSortEcode = "/";
            fifthSortEcode = "/";
            forthSortEcode = "/";
            thirdSortEcode = "/";
            secondSortEcode = "/";
            firstSortEcode = ecodelist.get(0);
        }

        map.put("firstSortEname",firstSortEname);
        map.put("secondSortEname",secondSortEname);
        map.put("thirdSortEname",thirdSortEname);
        map.put("forthSortEname",forthSortEname);
        map.put("fifthSortEname",fifthSortEname);
        map.put("sixthSortEname",sixthSortEname);

        map.put("sixthSortEcode",sixthSortEcode);
        map.put("fifthSortEcode",fifthSortEcode);
        map.put("forthSortEcode",forthSortEcode);
        map.put("thirdSortEcode",thirdSortEcode);
        map.put("secondSortEcode",secondSortEcode);
        map.put("firstSortEcode",firstSortEcode);

        new BaseBean().writeLog("********getSalesStructure: map = " + map);

        return map;
    }

    /**
     * 获取std所有分类
     *
     * @param sortId
     * @param productLevelCode
     * @return
     */
    public Map<String, String> getStdAllSort(String sortId, String productLevelCode) {
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        String fourthSortEname = "";
        String fifthSortEname = "";

        String firstSortEcode = "";
        String secondSortEcode = "";
        String thirdSortEcode = "";
        String fourthSortEcode = "";
        String fifthSortEcode = "";

        String firstProductItemCode = "";
        String secondProductItemCode = "";
        String thirdProductItemCode = "";
        String forthProductItemCode = "";
        String fifthProductItemCode = "";

        new BaseBean().writeLog("********getStdAllSort: productLevelCode = " + productLevelCode);
        new BaseBean().writeLog("********getStdAllSort: sortId = " + sortId);

        List<String> eNamelist = new ArrayList<>();
        List<String> eCodelist = new ArrayList<>();
        List<String> eItemCodeList = new ArrayList<>();
        String searchId = productLevelCode;
        while (true) {
            String sql = "select productLevelName,parentCode,productLevelCode,productItemCode from uf_cpck where productLevelCode = ? ";
            rs.executeQuery(sql, searchId);
            if (rs.next()) {
                searchId = rs.getString("parentCode");
                eNamelist.add(rs.getString("productLevelName"));
                eCodelist.add(rs.getString("productLevelCode"));
                eItemCodeList.add(rs.getString("productItemCode"));
                if (StringUtils.isBlank(searchId)) {
                    new BaseBean().writeLog("StdAllSort跳出当前循环：{}", searchId);
                    break;
                }
            }
        }
        if (eNamelist.size() == 5) {
            fifthSortEname = eNamelist.get(0);
            fourthSortEname = eNamelist.get(1);
            thirdSortEname = eNamelist.get(2);
            secondSortEname = eNamelist.get(3);
            firstSortEname = eNamelist.get(4);

            fifthSortEcode = eCodelist.get(0);
            fourthSortEcode = eCodelist.get(1);
            thirdSortEcode = eCodelist.get(2);
            secondSortEcode = eCodelist.get(3);
            firstSortEcode = eCodelist.get(4);

            fifthProductItemCode = eItemCodeList.get(0);
            forthProductItemCode = eItemCodeList.get(1);
            thirdProductItemCode = eItemCodeList.get(2);
            secondProductItemCode = eItemCodeList.get(3);
            firstProductItemCode = eItemCodeList.get(4);
        }else if (eNamelist.size() == 4) {
            fifthSortEname = "/";
            fourthSortEname = eNamelist.get(0);
            thirdSortEname = eNamelist.get(1);
            secondSortEname = eNamelist.get(2);
            firstSortEname = eNamelist.get(3);

            fifthSortEcode = "/";
            fourthSortEcode = eCodelist.get(0);
            thirdSortEcode = eCodelist.get(1);
            secondSortEcode = eCodelist.get(2);
            firstSortEcode = eCodelist.get(3);

            fifthProductItemCode = "/";
            forthProductItemCode = eItemCodeList.get(0);
            thirdProductItemCode = eItemCodeList.get(1);
            secondProductItemCode = eItemCodeList.get(2);
            firstProductItemCode = eItemCodeList.get(3);
        }
        else if (eNamelist.size() == 3) {
            fifthSortEname = "/";
            fourthSortEname = "/";
            thirdSortEname = eNamelist.get(0);
            secondSortEname = eNamelist.get(1);
            firstSortEname = eNamelist.get(2);

            fifthSortEcode = "/";
            fourthSortEcode = "/";
            thirdSortEcode = eCodelist.get(0);
            secondSortEcode = eCodelist.get(1);
            firstSortEcode = eCodelist.get(2);

            fifthProductItemCode = "/";
            forthProductItemCode = "/";
            thirdProductItemCode = eItemCodeList.get(0);
            secondProductItemCode = eItemCodeList.get(1);
            firstProductItemCode = eItemCodeList.get(2);
        }
        else if (eNamelist.size() == 2) {
            fifthSortEname = "/";
            fourthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = eNamelist.get(0);
            firstSortEname = eNamelist.get(1);

            fifthSortEcode = "/";
            fourthSortEcode = "/";
            thirdSortEcode = "/";
            secondSortEcode = eCodelist.get(0);
            firstSortEcode = eCodelist.get(1);

            fifthProductItemCode = "/";
            forthProductItemCode = "/";
            thirdProductItemCode = "/";
            secondProductItemCode = eItemCodeList.get(0);
            firstProductItemCode = eItemCodeList.get(1);
        }
        else if (eNamelist.size() == 1) {
            fifthSortEname = "/";
            fourthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = "/";
            firstSortEname = eNamelist.get(0);

            fifthSortEcode = "/";
            fourthSortEcode = "/";
            thirdSortEcode = "/";
            secondSortEcode = "/";
            firstSortEcode = eCodelist.get(0);

            fifthProductItemCode = "/";
            forthProductItemCode = "/";
            thirdProductItemCode = "/";
            secondProductItemCode = "/";
            firstProductItemCode = eItemCodeList.get(0);
        }

        map.put("firstSortEname", firstSortEname);
        map.put("secondSortEname", secondSortEname);
        map.put("thirdSortEname", thirdSortEname);
        map.put("fourthSortEname", fourthSortEname);
        map.put("fifthSortEname", fifthSortEname);


        map.put("fifthSortEcode", fifthSortEcode);
        map.put("fourthSortEcode", fourthSortEcode);
        map.put("thirdSortEcode", thirdSortEcode);
        map.put("secondSortEcode", secondSortEcode);
        map.put("firstSortEcode", firstSortEcode);

        map.put("fifthProductItemCode", fifthProductItemCode);
        map.put("forthProductItemCode", forthProductItemCode);
        map.put("thirdProductItemCode", thirdProductItemCode);
        map.put("secondProductItemCode", secondProductItemCode);
        map.put("firstProductItemCode", firstProductItemCode);

        new BaseBean().writeLog("********getAllSortNew: maps = " + map);

        return map;
    }


    @GET
    @Path("/genIframeCookie")
    @Produces(MediaType.TEXT_PLAIN)
    public String genIframeCookie(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog("genIframeCookie 开始获取cookie");

        if (StringUtils.isEmpty(IFRAME_COOKIE)
                || System.currentTimeMillis() > EXPIRE_TIME) {
            new BaseBean().writeLog("genIframeCookie 调用登录接口获取 cookie");
            IFRAME_COOKIE = StdUtil.executeGet(STD_IFRAME_LOGIN, null);
            /*他们10小时过期，建议我们一小时*/
            EXPIRE_TIME = System.currentTimeMillis() + 1000 * 60 * 60;
        }

        return IFRAME_COOKIE;
    }

}

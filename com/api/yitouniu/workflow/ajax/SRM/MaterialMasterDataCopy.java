package com.api.yitouniu.workflow.ajax.SRM;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * @Author: Moss
 * @Description: 物料主数据使用ajax自定义联动数据。
 * @Date: Create in 11:22 2021/11/12
 * @Modified By:
 */

@Path("yitouniu/workflow/ajax/SRM/MaterialMasterDataCopy")
public class MaterialMasterDataCopy {

    //srm物料主数据表
    public static final String SRM_WL = "uf_srm_wl";

    //供应商设计号表
    public static final String supplierDesign = "uf_supplier_design";

    RecordSet rs = new RecordSet();

    /**
     * 返回物料主数据
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getMaterialData")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getMaterialData(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        try {
            String wlid = request.getParameter("wlid");
            jsonObject= getMaterialData(wlid);
            jsonObject.put("status","success");
            new BaseBean().writeLog("********getMaterialData: "+JSONObject.toJSONString(jsonObject));
        } catch(Exception e){
            jsonObject.put("status","error");
            new BaseBean().writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        new BaseBean().writeLog("结束进行联动 : ");

        return jsonObject.toJSONString();
    }

    /**
     * 返回物料的状态
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/returnSelectedMaterial")
    @Produces(MediaType.TEXT_PLAIN)
    public String returnSelectedMaterial(@Context HttpServletRequest request, @Context HttpServletResponse response) throws UnsupportedEncodingException {
        new BaseBean().writeLog("SRM-MaterialMasterData-returnSelectedMaterial : start");
        JSONObject jsonObject = new JSONObject();
        String materialDescription = request.getParameter("MAKTX");
        boolean flag = false;
        try {
            RecordSet rs = new RecordSet();
            String sql = "select * from " + SRM_WL +" where cinvname = ?";
            rs.executeQuery(sql, materialDescription);
            if(rs.next()){
                flag = true;
            }
            new BaseBean().writeLog("SRM-MaterialMasterData-returnSelectedMaterial : materialDescription = " + materialDescription);
            if(flag){
                jsonObject.put("status","error");
                jsonObject.put("msg","SRM-MaterialMasterData-returnSelectedMaterial:"+materialDescription+"，该物料描述已存在，不可新增");
            } else {
                jsonObject.put("status","success");
                jsonObject.put("msg","创建成功");
            }
        } catch (Exception e){
            jsonObject.put("status","error");
            jsonObject.put("msg","接口异常，联系相关负责人");
        }

        new BaseBean().writeLog("SRM-MaterialMasterData-returnSelectedMaterial : end");

        return jsonObject.toString();
    }

    /**
     * 查询供应商设计号
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/querySupplierDesignNo")
    @Produces(MediaType.TEXT_PLAIN)
    public String querySupplierDesignNo(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        new BaseBean().writeLog(">>>querySupplierDesignNo : start");
        JSONObject jsonObject = new JSONObject();
        String supplierDesignNo = request.getParameter("supplierDesignNo");
        boolean flag = false;
        try {
            RecordSet rs = new RecordSet();
            String sql = "select * from " + supplierDesign +" where supplier_design_no = ?";
            rs.executeQuery(sql, supplierDesignNo);
            if(rs.next()){
                flag = true;
            }
            new BaseBean().writeLog("querySupplierDesignNo : supplierDesignNo = " + supplierDesignNo);
            if(flag){
                jsonObject.put("status","error");
                jsonObject.put("msg","{"+supplierDesignNo+"},该设计号已存在,不可新增,请重新输入");
            } else {
                jsonObject.put("status","success");
                jsonObject.put("msg","{"+supplierDesignNo+"},该设计号不存在,可使用");
            }
        } catch (Exception e){
            jsonObject.put("status","error");
            jsonObject.put("msg","接口异常，联系相关负责人");
        }

        new BaseBean().writeLog(">>>querySupplierDesignNo : end");

        return jsonObject.toString();
    }

    /**
     * 多级分类
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getSort")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getSort(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("MaterialMasterData——getSort，开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("MaterialMasterData——getSort，value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("MaterialMasterData——getSort，valueArray = " + Arrays.toString(valueArray));
        map = getAllSort(valueArray[0], valueArray[1]);

        jsonObject.put("firstSortEname", map.get("firstSortEname"));
        jsonObject.put("secondSortEname", map.get("secondSortEname"));
        jsonObject.put("thirdSortEname", map.get("thirdSortEname"));

        new BaseBean().writeLog("MaterialMasterData——getSort，jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();
    }

    /**
     * 多级分类
     * @param request
     * @param response
     * @return
     */
    @GET
    @Path("/getSortNew")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getSortNew(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("MaterialMasterData——getSort，开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("MaterialMasterData——getSort，value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("MaterialMasterData——getSort，valueArray = " + Arrays.toString(valueArray));
        map = getAllSortNew(valueArray[0], valueArray[1]);

        jsonObject.put("firstSortEname", map.get("firstSortEname"));
        jsonObject.put("secondSortEname", map.get("secondSortEname"));
        jsonObject.put("thirdSortEname", map.get("thirdSortEname"));
        jsonObject.put("fourthSortEname", map.get("fourthSortEname"));

        new BaseBean().writeLog("MaterialMasterData——getSort，jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();
    }

    /**
     * 查询物料数据
     * @param wlid
     * @return
     */
    private JSONObject getMaterialData(String wlid){
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_KCXGWLZSJSY  where id=?";
        rs.executeQuery(sql,wlid);
        JSONObject data = new JSONObject();
        if(rs.next()){
            data.put("MAKTX",rs.getString("MAKTX"));
            data.put("MEINS",rs.getString("MEINS"));
            data.put("MTARTID",rs.getString("MTARTID"));
            data.put("MATKL",rs.getString("MATKL"));
            data.put("SPARTID",rs.getString("SPARTID"));
            data.put("BRGEW",rs.getString("BRGEW"));
            data.put("NTGEW",rs.getString("NTGEW"));
            data.put("BCODE",rs.getString("BCODE"));
            data.put("BRAND",rs.getString("BRAND"));
            data.put("BRNAM",rs.getString("BRNAM"));
            data.put("MODEL",rs.getString("MODEL"));
            data.put("BOXNO",rs.getString("BOXNO"));
            data.put("TASTE",rs.getString("TASTE"));
            data.put("PUPSE",rs.getString("PUPSE"));
            data.put("ZLENGTH",rs.getString("ZLENGTH"));
            data.put("ZWIDTH",rs.getString("ZWIDTH"));
            data.put("ZHIGH",rs.getString("ZHIGH"));
            data.put("ZVOLUME",rs.getString("ZVOLUME"));
            data.put("VNDOR",rs.getString("VNDOR"));
            data.put("VNDNM",rs.getString("VNDNM"));
            data.put("PRMODID",rs.getString("PRMODID"));
            data.put("VRKME",rs.getString("VRKME"));
            data.put("TAXM1",rs.getString("TAXM1"));
            data.put("KTGRMID",rs.getString("KTGRMID"));
            data.put("MVGR1ID",rs.getString("MVGR1ID"));
            data.put("PLIFZ",rs.getString("PLIFZ"));
            data.put("LGFSB",rs.getString("LGFSB"));
            data.put("BSTME",rs.getString("BSTME"));
            data.put("ZCGRKID",rs.getString("ZCGRKID"));
            data.put("ZXSRKID",rs.getString("ZXSRKID"));
            data.put("ZZKJID",rs.getString("ZZKJID"));
            data.put("ZSCRKID",rs.getString("ZSCRKID"));
            data.put("ZYZDJID",rs.getString("ZYZDJID"));
            data.put("MAXLZ",rs.getString("MAXLZ"));
            data.put("MHDRZ",rs.getString("MHDRZ"));
            data.put("INSMKID",rs.getString("INSMKID"));
            data.put("XCHPFID",rs.getString("XCHPFID"));
            data.put("DISPOID",rs.getString("DISPOID"));
            data.put("MINBE",rs.getString("MINBE"));
            data.put("BSTMI",rs.getString("BSTMI"));
            data.put("BSTMA",rs.getString("BSTMA"));
            data.put("MABST",rs.getString("MABST"));
            data.put("BSTRF",rs.getString("BSTRF"));
            data.put("EISBE",rs.getString("EISBE"));
            data.put("VPRSVID",rs.getString("VPRSVID"));
            data.put("STPRS",rs.getString("STPRS"));
            data.put("PEINH",rs.getString("PEINH"));
            data.put("ZMATNR_JLB",rs.getString("ZMATNR_JLB"));
            data.put("ZMAKTX_JLB",rs.getString("ZMAKTX_JLB"));
            data.put("ZBZXQID",rs.getString("ZBZXQID"));
            data.put("ZDBZL",rs.getString("ZDBZL"));
            data.put("ZGBXQID",rs.getString("ZGBXQID"));
            data.put("ZIQCID",rs.getString("ZIQCID"));

            data.put("BKLAS",rs.getString("BKLAS"));
            data.put("EKGRP",rs.getString("EKGRP"));

            data.put("UEBTO",rs.getString("UEBTO"));
            data.put("UNTTO",rs.getString("UNTTO"));
            data.put("MSTAE",rs.getString("MSTAE"));
            data.put("GEWEI",rs.getString("GEWEI"));
            data.put("CZ_WERKS",rs.getString("WERKS"));
            data.put("MATNR",rs.getString("MATNR"));
            data.put("WERKS",rs.getString("WERKS"));

            data.put("ZJJXX",rs.getString("ZJJXX"));
            data.put("ZJJSX",rs.getString("ZJJSX"));
            data.put("ZYJFL",rs.getString("ZYJFL"));
            data.put("ZEJFL",rs.getString("ZEJFL"));
            data.put("ZSJFL",rs.getString("ZSJFL"));
            data.put("ZZSJFL",rs.getString("ZZSJFL"));
            data.put("ZCPSJ_CW",rs.getString("ZCPSJ_CW"));
            data.put("ZCPJC_CW",rs.getString("ZCPJC_CW"));
            data.put("ZCPSJ_JH",rs.getString("ZCPSJ_JH"));
            data.put("ZCPJS_JH",rs.getString("ZCPJS_JH"));
            data.put("ZSSFL",rs.getString("ZSSFL"));
            data.put("ZCW_UNIT",rs.getString("ZCW_UNIT"));
            data.put("ZGUIGE",rs.getString("ZGUIGE"));
            data.put("ZGUIGE2",rs.getString("ZGUIGE2"));

        }
        RecordSet rs1 = new RecordSet();
        String sqlWERKS = "select NAME1 from uf_gcb where WERKS = ?";
        rs1.executeQuery(sqlWERKS,data.getString("WERKS"));
        if(rs1.next()){
            data.put("WERKSNAME",rs1.getString("NAME1"));
        }
        RecordSet rs2 = new RecordSet();
        String sqlMATKL = "select * from uf_wlzb where MATKL = ?";
        rs2.executeQuery(sqlMATKL,data.getString("MATKL"));
        if(rs2.next()){
            data.put("MATKLMS",rs2.getString("wgbez"));
        }
        RecordSet rs3 = new RecordSet();
        String sqlBKLAS = "select * from uf_WLZSJPGL where code = ?";
        rs3.executeQuery(sqlBKLAS,data.getString("BKLAS"));
        if(rs3.next()){
            data.put("BKLASMS",rs3.getString("description"));
            data.put("BKLASID",rs3.getString("id"));
        }
        RecordSet rs4 = new RecordSet();
        String sqlEKGRP = "select * from uf_CGSGWLZSJCGZ where code = ?";
        rs4.executeQuery(sqlEKGRP,data.getString("EKGRP"));
        if(rs4.next()){
            data.put("EKGRPMS",rs4.getString("description"));
            data.put("EKGRPID",rs4.getString("id"));
        }
        RecordSet rs5 = new RecordSet();
        String sqlZCW_UNIT = "select * from uf_dw where dwid = ?";
        rs5.executeQuery(sqlZCW_UNIT,data.getString("ZCW_UNIT"));
        if(rs5.next()){
            data.put("ZCW_UNITMS",rs5.getString("dw"));
            data.put("ZCW_UNITID",rs5.getString("id"));
        }


        return data;
    }


    /**
     * 获取所有分类
     * @param sortId
     * @param id
     * @return
     */
    public Map<String, String> getAllSortNew(String sortId, String id){
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        String fourthSortEname = "";
        new BaseBean().writeLog("********getAllSortNew: id = " + id);
        new BaseBean().writeLog("********getAllSortNew: sortId = " + sortId);

        List<String> list = new ArrayList<>();
        String searchId = id;
        while (true){
            String sql = " select up_id,flmc from uf_category where id = ? ";
            rs.executeQuery(sql, searchId);
            if(rs.next()){
                searchId = rs.getString("up_id");
                list.add(rs.getString("flmc"));
                if(StringUtils.isBlank(searchId)){
                    break;
                }
            }
        }
        if(list.size() == 4){
            fourthSortEname = list.get(0);
            thirdSortEname = list.get(1);
            secondSortEname = list.get(2);
            firstSortEname = list.get(3);
        } else if (list.size() == 3){
            fourthSortEname = "/";
            thirdSortEname = list.get(0);
            secondSortEname = list.get(1);
            firstSortEname = list.get(2);
        } else if (list.size() == 2){
            fourthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = list.get(0);
            firstSortEname = list.get(1);
        } else if (list.size() == 1){
            fourthSortEname = "/";
            thirdSortEname = "/";
            secondSortEname = "/";
            firstSortEname = list.get(0);
        }

        map.put("firstSortEname",firstSortEname);
        map.put("secondSortEname",secondSortEname);
        map.put("thirdSortEname",thirdSortEname);
        map.put("fourthSortEname",fourthSortEname);

        new BaseBean().writeLog("********getAllSortNew: map = " + map);

        return map;
    }

    /**
     * 获取所有分类
     * @param id
     * @param sortId
     * @return
     */
    public Map<String, String> getAllSort(String id, String sortId){
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        String dyyjflid = "";
        String dyejflid = "";
        new BaseBean().writeLog("********getAllSort: id = " + id);
        new BaseBean().writeLog("********getAllSort: sortId = " + sortId);


        //测试
//        if("3".equals(id)){
//            RecordSet rs1 = new RecordSet();
//            //测试
//            rs1.executeQuery("select plb, dyyjfl from uf_plb where id = ? ", sortId);
//            if(rs1.next()){
//                secondSortEname = rs1.getString("plb");
//                firstSortEname = rs1.getString("dyyjfl");
//            }
//
//        }else if ("4".equals(id)){
//            //测试
//            RecordSet rs1 = new RecordSet();
//            rs1.executeQuery("select ppb, dyejfl from uf_ppb where id = ? ", sortId);
//            if(rs1.next()){
//                thirdSortEname = rs1.getString("ppb");
//                secondSortEname = rs1.getString("dyejfl");
//            }
//            RecordSet rs2 = new RecordSet();
//            rs2.executeQuery("select plb, dyyjfl from uf_plb where plb = ? ", secondSortEname);
//            if(rs2.next()){
//                secondSortEname = rs2.getString("plb");
//                firstSortEname = rs2.getString("dyyjfl");
//            }
//
//        } else {
//            //测试
//            RecordSet rs1 = new RecordSet();
//            rs1.executeQuery("select plflmc from uf_plfl where id = ? ", sortId);
//            if(rs1.next()){
//                firstSortEname = rs1.getString("plflmc");
//            }
//        }
        //正式
        if("5".equals(id)){
            RecordSet rs1 = new RecordSet();
            rs1.executeQuery("select plid, dyyjflid,dyyjfl from uf_plb where id = ? ", sortId);
            if(rs1.next()){
                secondSortEname = rs1.getString("plid");
                dyyjflid = rs1.getString("dyyjflid");
//                firstSortEname = rs1.getString("dyyjfl");
            }

            RecordSet rs2 = new RecordSet();
            rs2.executeQuery("select plflmc from uf_plfl where id = ? ", dyyjflid);
            if(rs2.next()){
                firstSortEname = rs2.getString("plflmc");
            }

        }else if ("6".equals(id)){
            RecordSet rs1 = new RecordSet();
            rs1.executeQuery("select ppb, dyejflid from uf_ppb where id = ? ", sortId);
            if(rs1.next()){
                thirdSortEname = rs1.getString("ppb");
                dyejflid = rs1.getString("dyejflid");
            }

            RecordSet rs2 = new RecordSet();
            rs2.executeQuery("select plid, dyyjflid,dyyjfl from uf_plb where id = ? ", dyejflid);
            if(rs2.next()){
                secondSortEname = rs2.getString("plid");
                dyyjflid = rs2.getString("dyyjflid");
//                firstSortEname = rs2.getString("dyyjfl");
            }

            RecordSet rs3 = new RecordSet();
            rs3.executeQuery("select plflmc from uf_plfl where id = ? ", dyyjflid);
            if(rs3.next()){
                firstSortEname = rs3.getString("plflmc");
            }
        }else if ("2".equals(id)){
            RecordSet rs1 = new RecordSet();
            rs1.executeQuery("select plflmc from uf_plfl where id = ? ", sortId);
            if(rs1.next()){
                firstSortEname = rs1.getString("plflmc");
            }
        }

        new BaseBean().writeLog("********getAllSort: dyyjflid = " + dyyjflid);
        new BaseBean().writeLog("********getAllSort: dyejflid = " + dyejflid);


        if("".equals(thirdSortEname)){
            thirdSortEname = "/";
        }
        if("".equals(secondSortEname)){
            secondSortEname = "/";
        }
        map.put("firstSortEname",firstSortEname);
        map.put("secondSortEname",secondSortEname);
        map.put("thirdSortEname",thirdSortEname);

        new BaseBean().writeLog("********getAllSort: firstSortEname = " + firstSortEname);
        new BaseBean().writeLog("********getAllSort: secondSortEname = " + secondSortEname);
        new BaseBean().writeLog("********getAllSort: thirdSortEname = " + thirdSortEname);

        return map;
    }










}

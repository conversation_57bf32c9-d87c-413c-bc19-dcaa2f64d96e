package com.api.yitouniu.workflow.ajax.SRM;

import com.alibaba.fastjson.JSONObject;
import com.api.yitouniu.workflow.ajax.SAP.ReturnMsgToSAP;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Moss
 * @Description: 采购申购SRM表单避免选择框引起别的bug使用这样的方式去联动数据
 * @Date: Create in 19:07 2021/11/26
 * @Modified By:
 */

@Path("yitouniu/workflow/ajax/SRM/PurchaseSubscriptionSrm")
public class PurchaseSubscriptionSrm extends ReturnMsgToSAP {

    @GET
    @Path("/getPurchaseSubscription")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getPurchaseSubscription(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map execute = new HashMap();
        try {
            String materielNo = request.getParameter("materielNo");
            String factory = request.getParameter("factory");
            jsonObject= querySrmWlData(materielNo,factory);

            JSONObject reqJson = inventoryRequest(materielNo,factory);
            execute = super.execute(reqJson.toJSONString());
            new BaseBean().writeLog("********getPurchaseSubscription: 物料号="+materielNo+".工厂="+factory+",获取库存接口的返回值"+execute);
            String result = (String) execute.get("RESULT");
            JSONObject resultObject = JSONObject.parseObject(result);
            List<JSONObject> list = (List<JSONObject>) resultObject.get("DATA");
            JSONObject listJson = list.get(0);
            jsonObject.put("LABST",listJson.getString("LABST"));
            jsonObject.put("INSME",listJson.getString("INSME"));
            jsonObject.put("ZMATUP",listJson.getString("ZMATUP"));
            jsonObject.put("ZMRPJSYL",listJson.getString("ZMRPJSYL"));

            jsonObject.put("status","success");
            new BaseBean().writeLog("********getPurchaseSubscription: "+JSONObject.toJSONString(jsonObject));
        } catch(Exception e){
            jsonObject.put("status","error");
            new BaseBean().writeLog(this.getClass().getName(),"出错鸟:"+e);
            e.printStackTrace();
        }
        new BaseBean().writeLog("结束进行联动 : ");

        return jsonObject.toJSONString();
    }

    /**
     * 查询物料信息
     * @param materielNo
     * @return
     */
    private JSONObject querySrmWlData(String materielNo, String factory){
        RecordSet rs = new RecordSet();
//        //正式
        String sql = "select * from uf_srm_wl  where csrminvcode=? and cpasturecode = ?";
//        测试
//        String sql = "select * from uf_srm_wl  where csrminvcode=? and cpasturecode = ?";
        rs.executeQuery(sql,materielNo,factory);
        JSONObject data = new JSONObject();
        if(rs.next()){
            //SAP物料号
            data.put("cinvcode",rs.getString("cinvcode"));
            //SRM物料号
            data.put("csrminvcode",rs.getString("csrminvcode"));
            //规格
            data.put("cinvmodel",rs.getString("cinvmodel"));
            //供应商品牌
            data.put("cbrand",rs.getString("cbrand"));
            //采购单位
            data.put("cunit",rs.getString("cunit"));
            //物料组编码
            data.put("csapinvgroup",rs.getString("csapinvgroup"));
            //采购组编码
            data.put("cpurchases",rs.getString("cpurchases"));
            //最小批量
            data.put("iminbatch",rs.getString("iminbatch"));
            //类别
            data.put("ccategory",rs.getString("ccategory"));
            //分类编号
            data.put("cclasscode",rs.getString("cclasscode"));
            //分类名称
            data.put("cclassname",rs.getString("cclassname"));
            //预估单价（参考单价）
            data.put("iprice",rs.getString("iprice"));
        }

        return data;
    }

    /**
     * 获取请求sap库存接口的req
     * @param materielNo
     * @param factory
     * @return
     */
    private JSONObject inventoryRequest(String materielNo, String factory){
        //获取当前时间戳 (毫秒)
        Long currentTime = System.currentTimeMillis();
        Date date = new Date(currentTime);
        System.out.println("当前时间戳转换成时间为： " + date);
        // 定义格式化时间输出格式
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH:mm:ss");
        String currentSimpleDateFormat = simpleDateFormat.format(date);
        String currentSimpleDateFormat2 = simpleDateFormat2.format(date);
        JSONObject request = new JSONObject();
        JSONObject ctrlObject = new JSONObject();
        JSONObject dataObject = new JSONObject();
        ctrlObject.put("SYSID","OA");
        ctrlObject.put("REVID","SAP");
        ctrlObject.put("FUNID","ZINF058");
        ctrlObject.put("INFID",currentTime);
        ctrlObject.put("UNAME","sysadmin");
        ctrlObject.put("DATUM",currentSimpleDateFormat);
        ctrlObject.put("UZEIT",currentSimpleDateFormat2);
        ctrlObject.put("MSGTY","\"\"");
        ctrlObject.put("MSAGE","\"\"");

        dataObject.put("MATNR",materielNo);
        dataObject.put("WERKS",factory);
        request.put("CTRL",ctrlObject);
        request.put("DATA",dataObject);
        return request;
    }
}

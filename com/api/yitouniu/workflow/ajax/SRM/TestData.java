package com.api.yitouniu.workflow.ajax.SRM;

import com.alibaba.fastjson.JSONObject;
import org.docx4j.wml.R;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/6/29 18:32
 * @Description TODO
 * @Version 1.0
 */

@Path("yitouniu/workflow/ajax/SRM/TestData")
public class TestData {



    @GET
    @Path("/getSort")
    @Produces(MediaType.TEXT_PLAIN)
    public  String getSort(@Context HttpServletRequest request, @Context HttpServletResponse response){
        new BaseBean().writeLog("TestData——getSort，开始进行联动 : ");
        JSONObject jsonObject = new JSONObject();
        Map map = new HashMap();
        String value = request.getParameter("value");
        new BaseBean().writeLog("TestData——getSort，value = " + value);
        String[] valueArray = value.split("_");
        new BaseBean().writeLog("TestData——getSort，valueArray = " + Arrays.toString(valueArray));
        map = getAllSort(valueArray[0], valueArray[1]);

        jsonObject.put("firstSortEname", map.get("firstSortEname"));
        jsonObject.put("secondSortEname", map.get("secondSortEname"));
        jsonObject.put("thirdSortEname", map.get("thirdSortEname"));

        new BaseBean().writeLog("TestData——getSort，jsonObject = " + jsonObject.toJSONString());

        return jsonObject.toJSONString();
    }


    public Map<String, String> getAllSort(String id, String sortId){
        Map map = new HashMap();
        String firstSortEname = "";
        String secondSortEname = "";
        String thirdSortEname = "";
        String dyyjflid = "";
        String dyejflid = "";
        new BaseBean().writeLog("********getAllSort: id = " + id);
        new BaseBean().writeLog("********getAllSort: sortId = " + sortId);


        if("5".equals(id)){
            RecordSet rs1 = new RecordSet();
            rs1.executeQuery("select plid, dyyjflid from uf_plb where id = ? ", sortId);
            if(rs1.next()){
                secondSortEname = rs1.getString("plid");
                dyyjflid = rs1.getString("dyyjflid");
            }

            RecordSet rs2 = new RecordSet();
            rs2.executeQuery("select plflmc from uf_plfl where id = ? ", dyyjflid);
            if(rs2.next()){
                firstSortEname = rs2.getString("plflmc");
            }

        }else if ("6".equals(id)){
            RecordSet rs1 = new RecordSet();
            rs1.executeQuery("select ppb, dyejflid from uf_ppb where id = ? ", sortId);
            if(rs1.next()){
                thirdSortEname = rs1.getString("ppb");
                dyejflid = rs1.getString("dyejflid");
            }

            RecordSet rs2 = new RecordSet();
            rs2.executeQuery("select plid, dyyjflid from uf_plb where plb = ? ", dyejflid);
            if(rs2.next()){
                secondSortEname = rs2.getString("plid");
                dyyjflid = rs1.getString("dyyjflid");
            }
            RecordSet rs3 = new RecordSet();
            rs3.executeQuery("select plflmc from uf_plfl where id = ? ", dyyjflid);
            if(rs3.next()){
                firstSortEname = rs1.getString("plflmc");
            }
        }

        if("".equals(thirdSortEname)){
            thirdSortEname = "/";
        }
        map.put("firstSortEname",firstSortEname);
        map.put("secondSortEname",secondSortEname);
        map.put("thirdSortEname",thirdSortEname);

        new BaseBean().writeLog("********getAllSort: firstSortEname = " + firstSortEname);
        new BaseBean().writeLog("********getAllSort: secondSortEname = " + secondSortEname);
        new BaseBean().writeLog("********getAllSort: thirdSortEname = " + thirdSortEname);

        return map;
    }













}

package com.api.yitouniu.workflow.ajax.utils;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import com.api.yitouniu.workflow.ajax.SAP.ReturnMsgToSAP;

import java.util.HashMap;
import java.util.Map;

/**
 * 凭证推送到SAP
 */
public class SAPUtil {
    private static RecordSet rs = new RecordSet();

    public static String execute(String param) {
        new  BaseBean().writeLog("sap获取数据开始");
        /*String functionname = "ZSYS_MAIN_FUNCTION_PROCESS";//SAP RFC函数的名称//更改为客户对应的数据
        String datasourceId = "1";//数据源id//更改为客户对应的数据
        JCO.Function bapi = null;
        //----------------数据配置----------------------------
        SAPInterationOutUtil sapUtil = new SAPInterationOutUtil();
        JCO.Client myConnection = (JCO.Client) sapUtil.getConnection(datasourceId, new LogInfo());
        myConnection.connect();
        try {
            JCO.Repository myRepository1 = new JCO.Repository("myRepositroy", myConnection);
            IFunctionTemplate ft1 = myRepository1.getFunctionTemplate(functionname);
            bapi = ft1.getFunction();
            long time = new Date().getTime();
            //处理输入--------------------------------------------------
            JCO.ParameterList imList = bapi.getImportParameterList();
            //输入参数
            imList.setValue(param, "IN_JSON");
            *//*String imStructName = "";//输入结构名//更改为客户对应的数据
            JCO.Structure instr = imList.getStructure(imStructName);
            instr.setValue("值", "SAP字段名");//更改为客户对应的数据
            //输入表
            String imTableName = "table1";//输入表明
            JCO.Table imTable = bapi.getTableParameterList().getTable(imTableName);
            for (int i = 0; i < 10; i++) {
                imTable.appendRow();
                imTable.setValue("值1", "SAP字段名1");//更改为客户对应的数据
            }*//*
            //执行bapi函数
            myConnection.execute(bapi);
            //处理输出--------------------------------------------------
            JCO.ParameterList exList = bapi.getExportParameterList();
            //处理输出参数
            String out_json = exList.getString("OUT_JSON");//更改为客户对应的数据

//处理输出结构
            *//*String sapStruct = "";//输出结构名//更改为客户对应的数据
            JCO.Structure exportStruct = exList.getStructure(sapStruct);
            String esValue1 = exportStruct.getString("SAP字段名1");//更改为客户对应的数据
            //处理输出表
            JCO.ParameterList exTable = bapi.getTableParameterList();
            String tableName = "IS_RESULT";//sap输出表的名称//更改为客户对应的数据
            JCO.Table exTab = exTable.getTable(tableName);
            int size = exTab.getNumRows();//获取输出表的行数
            for (int i = 0; i < size; i++) {
                exTab.setRow(i);//获取第i行
                exTab.getString("SAP字段名");//更改为客户对应的数据
            }*//*
            //处理表结束
            return out_json;

        } catch (Exception e) {
            rs.executeUpdate("insert into uf_sap (qqcs,cwxx,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                    param,e,"17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
            WorkflowUtil.ModeDataShare("uf_sap");
            new BaseBean().writeLog("数据异常信息:"+e);
        } finally {
            if (null != myConnection) {
                JCO.releaseClient(myConnection);//重要！！！！！必须要有
            }
        }*/

        //Map<String,String> sapParams = getSAPParams();
        //String type = sapParams.get("hjlx");
        ConnectPooled connectPooled = ConnectPooled.getInstance();

        String result = connectPooled.consumeABAPFM(param);
        return result;
    }

    /**
     * 获取连接参数
     *
     * @return
     */
    public static Map getSAPParams() {

         Map map = new HashMap();


            rs.executeQuery("select * from uf_SAPhuanjing ");
            String id = "";
            String saphjlx = "";

            if (rs.next()) {
                id = rs.getString("id");
                saphjlx = rs.getString("saphjlx");
            }

            rs.executeQuery("select * from uf_SAPhuanjing_dt1 where mainid = ? and hjlx = ? ", id, saphjlx);
            if (rs.next()) {
                map.put("USER", rs.getString("USER1"));
                map.put("PASSWD", rs.getString("PASSWD"));
                map.put("MSHOST", rs.getString("MSHOST"));
                map.put("MSSERV", rs.getString("MSSERV"));
                map.put("GROUP", rs.getString("GROUP1"));
                map.put("R3NAME", rs.getString("R3NAME"));
                map.put("CLIENT", rs.getString("CLIENT"));
                map.put("POOL_CAPACITY", rs.getString("POOL_CAPACITY"));
                map.put("PEAK_LIMIT", rs.getString("PEAK_LIMIT"));
                map.put("ASHOST", rs.getString("ASHOST"));
                map.put("SYSNR", rs.getString("SYSNR"));
                map.put("ROUTER", rs.getString("ROUTER"));
                map.put("hjlx", rs.getString("hjlx"));
                map.put("LANG", rs.getString("LANG"));
            }
            return map;

    }
}


package com.ryytn;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.UnknownServiceException;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.alibaba.fastjson.JSONObject;
import com.ryytn.http.HttpUtil;

import weaver.common.DateUtil;

/**
 * EHR集成服务类
 * <AUTHOR>
 *
 */
public class EHRService {
	
	
	//private String ip = "http://adoptacow-test.51hrc.cn";
	//private String AppSecrect = "adoptacow_testBY&3Vx6u";
	
	private String ip = "http://adoptacow.51hrc.cn";
	private String AppSecrect = "adoptacow_prodXK2g0e*9";
	
	private HttpUtil http  = new HttpUtil();
	private Log log = LogFactory.getLog(EHRService.class); 
	 
	/**
	 * 获取指定用户Token
	 * @param loginId
	 * @return
	 */
	public String getToken(String loginId) {
		String url = "/RedseaPlatform/vwork/third/api/sso.mob?method=createtoken";
		MD5Helper md5Helper = new MD5Helper();
		String nowTime = DateUtil.getNowDateTimeStr();
		String Token = "";
		
		String sign = md5Helper.encrypt32(AppSecrect+"&"+loginId+"&"+nowTime);
		try {
			log.error(ip+url+"&loginId="+loginId+"&loginIdType=BASECODE&timestamp="+nowTime+"&sign="+sign);
			String retStr = http.sendGet(ip+url+"&loginId="+loginId+"&loginIdType=BASECODE&timestamp="+nowTime+"&sign="+sign);
			log.error(retStr);
			JSONObject ret = JSONObject.parseObject(retStr);
			if("1".equals(ret.getString("state")))  {
				Token= ret.getString("result");
			}
		} catch (MalformedURLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (UnknownServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		return Token;
	}
	
	
	public String loginEHR(String loginId,String gotourl) {
		String ret = "";
		try {
			String token = this.getToken(loginId);
			if(!"".equals(token)) {
				String url = "/RedseaPlatform/vwork/third/api/sso.mob?method=oauthLogin";
				
				 Map head = new HashMap<String, String>();
				 head.put("Content-Type","application/json");
				 
				 Map content = new HashMap<String, String>();
				 content.put("token", token);
				 content.put("client", "pc");
				 content.put("action", "login");
				 content.put("gotourl", gotourl);
				 
				 log.error(content.toString());
				ret = http.sendPost(ip+url, content.toString(), head);
				
				
				
			}
		
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			ret = "";
		}
		
		return ret;
		
	}

}

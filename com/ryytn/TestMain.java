package com.ryytn;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.UnknownServiceException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;

import com.ryytn.http.HttpUtil;
import com.weaver.general.MD5;

import weaver.common.DateUtil;
import weaver.general.xcommon.MD5Utils;


public class TestMain {
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		String url = "http://adoptacow-test.51hrc.cn/RedseaPlatform/vwork/third/api/sso.mob?method=createtoken";
		
		
		 HttpUtil http  = new HttpUtil();
		 Map head = new HashMap<String, String>();
		 head.put("Content-Type","application/json");
		 
		 SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		 MD5Helper md5Helper = new MD5Helper();
		 
		 
		 
		 String nowTime = DateUtil.getNowDateTimeStr();
		 
		 String AppSecrect = "adoptacow_testBY&3Vx6u";
		 String sign = md5Helper.encrypt32(AppSecrect+"&c020065&"+nowTime);
		 
		 try {
			String retStr = http.sendGet(url+"&loginId=c020065&loginIdType=BASECODE&timestamp="+nowTime+"&sign="+sign);
			System.out.println(retStr);
		 } catch (MalformedURLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (UnknownServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		 
		 String sql = "update formtable_main_39 set  sqr = 1, sqbm = null, sqgs = null, lxdh = '', jfkh = '', yf = '', sapsdfbm = '', tpmhtqyfbh = '', khxz = null, hytjksrq = '', hytjjsrq = '', hylx = null, sffbzj = null, jxszzshb = '', htxselx = null, hyydje = null, hyydje2 = null, hyydje3 = null, hyydje4 = null, hyydje5 = null, hyydje6 = null, hyydje7 = null, hyydje8 = null, hyydje9 = null, hyydje10 = null, hyydje11 = null, hyydje12 = null, hyydjehj = null, nbglwszqje1 = null, nbglwszqje2 = null, nbglwszqje3 = null, nbglwszqje4 = null, nbglwszqje5 = null, nbglwszqje6 = null, nbglwszqje7 = null, nbglwszqje8 = null, nbglwszqje9 = null, nbglwszqje10 = null, nbglwszqje11 = null, nbglwszqje12 = null, nbglwszqjehj = null, jgzlxq = null, jgzlxh = null, hyfktjq = '', chdzksrqq = '', chdzksrqh = '', dzrq = '', dzrh = '', khlprq = '', khlprh = '', khhkrq = '', khhkrh = '', chdzjsrqq = '', chdzjsrqh = '', e1sqpzzblq = null, e1sqpzzblh = null, e1htzkflq = null, e1htzkflh = null, e1htzktcq = null, e1htzktch = null, e1jahhtzktcq = null, e1jahhtzktch = null, e2htflyfq = null, e2htflyfh = null, e2clfsq = null, e2clfsh = 1, e2japlq = null, e2japlh = 0, e2zffsq = null, e2zffsh = null, e2ljblq = null, e2ljblh = null, e2jablq = null, e2jablh = null, e3lclfsq = null, e3lclfsh = 1, e3ljaplq = null, e3ljaplh = 0, e3lzffsq = null, e3lzffsh = null, e3lljbl1q = null, e3lljbl1h = null, e3ljabl1q = null, e3ljabl1h = null, e3lljbl2q = null, e3lljbl2h = null, e3ljabl2q = null, e3ljabl2h = null, e3lljbl3q = null, e3lljbl3h = null, e3ljabl3q = null, e3ljabl3h = null, e3lljbl4q = null, e3lljbl4h = null, e3ljabl4q = null, e3ljabl4h = null, e3eclfsq = null, e3eclfsh = 1, e3ejaplq = null, e3ejaplh = 0, e3ezffsq = null, e3ezffsh = null, e3eljbl1q = null, e3eljbl1h = null, e3ejabl1q = null, e3ejabl1h = null, e3eljbl2q = null, e3eljbl2h = null, e3ejabl2q = null, e3ejabl2h = null, e3eljbl3q = null, e3eljbl3h = null, e3ejabl3q = null, e3ejabl3h = null, e3eljbl4q = null, e3eljbl4h = null, e3ejabl4q = null, e3ejabl4h = null, e4htflyfq = null, e4htflyfh = null, e4clfsq = null, e4clfsh = 1, e4japlq = null, e4japlh = 3, e4zffsq = null, e4zffsh = null, e4ljblq = null, e4ljblh = null, e4jablq = null, e4jablh = null, e5lclfsq = null, e5lclfsh = 1, e5ljaplq = null, e5ljaplh = 3, e5lzffsq = null, e5lzffsh = null, e5lljbl1q = null, e5lljbl1h = null, e5ljabl1q = null, e5ljabl1h = null, e5lljbl2q = null, e5lljbl2h = null, e5ljabl2q = null, e5ljabl2h = null, e5lljbl3q = null, e5lljbl3h = null, e5ljabl3q = null, e5ljabl3h = null, e5lljbl4q = null, e5lljbl4h = null, e5ljabl4q = null, e5ljabl4h = null, e5eclfsq = null, e5eclfsh = 1, e5ejaplq = null, e5ejaplh = 3, e5ezffsq = null, e5ezffsh = null, e5eljbl1q = null, e5eljbl1h = null, e5ejabl1q = null, e5ejabl1h = null, e5eljbl2q = null, e5eljbl2h = null, e5ejabl2q = null, e5ejabl2h = null, e5eljbl3q = null, e5eljbl3h = null, e5ejabl3q = null, e5ejabl3h = null, e5eljbl4q = null, e5eljbl4h = null, e5ejabl4q = null, e5ejabl4h = null, e6lclfsq = null, e6lclfsh = 1, e6ljaplq = null, e6ljaplh = 1, e6lzffsq = null, e6lzffsh = null, e6lljbl1q = null, e6lljbl1h = null, e6ljabl1q = null, e6ljabl1h = null, e6lljbl2q = null, e6lljbl2h = null, e6ljabl2q = null, e6ljabl2h = null, e6lljbl3q = null, e6lljbl3h = null, e6ljabl3q = null, e6ljabl3h = null, e6lljbl4q = null, e6lljbl4h = null, e6ljabl4q = null, e6ljabl4h = null, e6eclfsq = null, e6eclfsh = 1, e6ejaplq = null, e6ejaplh = 1, e6ezffsq = null, e6ezffsh = null, e6eljbl1q = null, e6eljbl1h = null, e6ejabl1q = null, e6ejabl1h = null, e6eljbl2q = null, e6eljbl2h = null, e6ejabl2q = null, e6ejabl2h = null, e6eljbl3q = null, e6eljbl3h = null, e6ejabl3q = null, e6ejabl3h = null, e6eljbl4q = null, e6eljbl4h = null, e6ejabl4q = null, e6ejabl4h = null, e7htfljlfq = null, e7htfljlfh = null, e7fyqjq = '', e7fyqjh = '', e7clfsq = null, e7clfsh = 1, e7japlq = null, e7japlh = 0, e7zffsq = null, e7zffsh = null, e7ljbl1q = null, e7ljbl1h = null, e7jabl1q = null, e7jabl1h = null, e7ljbl2q = null, e7ljbl2h = null, e7jabl2q = null, e7jabl2h = null, e7ljbl3q = null, e7ljbl3h = null, e7jabl3q = null, e7jabl3h = null, e7ljbl4q = null, e7ljbl4h = null, e7jabl4q = null, e7jabl4h = null, e8htflytjdpbzq = null, e8htflytjdpbzh = null, e8fyqjq = '', e8fyqjh = '', e8clfsq = null, e8clfsh = 1, e8japlq = null, e8japlh = 0, e8zffsq = null, e8zffsh = null, e8ljbl1q = null, e8ljbl1h = null, e8jabl1q = null, e8jabl1h = null, e8ljbl2q = null, e8ljbl2h = null, e8jabl2q = null, e8jabl2h = null, e8ljbl3q = null, e8ljbl3h = null, e8jabl3q = null, e8jabl3h = null, e8ljbl4q = null, e8ljbl4h = null, e8jabl4q = null, e8jabl4h = null, e9clfsq = null, e9clfsh = 2, e9zffsq = null, e9zffsh = null, e9gdjeq = null, e9gdjeh = null, e9qndmq = '', e9qndmh = '', e10clfsq = null, e10clfsh = 2, e10zffsq = null, e10zffsh = null, e10gdjeq = null, e10gdjeh = null, e10qnclq = '', e10qnclh = '', e11clfsq = null, e11clfsh = 2, e11zffsq = null, e11zffsh = null, e11gdjeq = null, e11gdjeh = null, e11qnjqq = '', e11qnjqh = '', e12clfsq = null, e12clfsh = 2, e12zffsq = null, e12zffsh = null, e12gdjeq = null, e12gdjeh = null, e12xdq = null, e12xdh = null, e12ldfxq = null, e12ldfxh = null, e13clfsq = null, e13clfsh = 2, e13zffsq = null, e13zffsh = null, e13gdjeq = null, e13gdjeh = null, e13xpsjskusq = null, e13xpsjskush = null, e14lclfsq = null, e14lclfsh = 0, e14lzffsq = null, e14lzffsh = null, e14lgdjeq = null, e14lgdjeh = null, e14eclfsq = null, e14eclfsh = 2, e14ezffsq = null, e14ezffsh = null, e14eytblq = null, e14eytblh = null, e15lclfsq = null, e15lclfsh = 1, e15ljaplq = null, e15ljaplh = null, e15lzffsq = null, e15lzffsh = null, e15lljbl1q = null, e15lljbl1h = null, e15ljabl1q = null, e15ljabl1h = null, e15lljbl2q = null, e15lljbl2h = null, e15ljabl2q = null, e15ljabl2h = null, e15lljbl3q = null, e15lljbl3h = null, e15ljabl3q = null, e15ljabl3h = null, e15lljbl4q = null, e15lljbl4h = null, e15ljabl4q = null, e15ljabl4h = null, e15eclfsq = null, e15eclfsh = 2, e15ezffsq = null, e15ezffsh = null, e15egdjeq = null, e15egdjeh = null, e16clfsq = null, e16clfsh = 1, e16japlq = null, e16japlh = 0, e16zffsq = null, e16zffsh = null, e16ljbl1q = null,"
		 		+ " e16ljbl1h = null, e16jabl1q = null, e16jabl1h = null, e16ljbl2q = null, e16ljbl2h = null, e16jabl2q = null, e16jabl2h = null, e16ljbl3q = null, e16ljbl3h = null, e16jabl3q = null, e16jabl3h = null, e16ljbl4q = null, e16ljbl4h = null, e16jabl4q = null, e16jabl4h = null, e17clfsq = null, e17clfsh = 1, e17japlq = null, e17japlh = 0, e17zffsq = null, e17zffsh = null, e17ljbl1q = null, e17ljbl1h = null, e17jabl1q = null, e17jabl1h = null, e17ljbl2q = null, e17ljbl2h = null, e17jabl2q = null, e17jabl2h = null, e17ljbl3q = null, e17ljbl3h = null, e17jabl3q = null, e17jabl3h = null, e17ljbl4q = null, e17ljbl4h = null, e17jabl4q = null, e17jabl4h = null, e18lclfsq = null, e18lclfsh = 0, e18lzffsq = null, e18lzffsh = null, e18lytblq = null, e18lytblh = null, e18eclfsq = null, e18eclfsh = 2, e18ezffsq = null, e18ezffsh = null, e18egdjeq = null, e18egdjeh = null, e19lclfsq = null, e19lclfsh = 0, e19lzffsq = null, e19lzffsh = null, e19lytblq = null, e19lytblh = null, e19eclfsq = null, e19eclfsh = 2, e19ezffsq = null, e19ezffsh = null, e19egdjeq = null, e19egdjeh = null, e20eclfsq = null, e20eclfsh = 2, e20ezffsq = null, e20ezffsh = null, e20egdjeq = null, e20egdjeh = null, e20pzdgrsq = null, e20pzdgrsh = null, e21clfsq = null, e21clfsh = 1, e21japlq = null, e21japlh = 3, e21zffsq = null, e21zffsh = null, e21ljbl1q = null, e21ljbl1h = null, e21jabl1q = null, e21jabl1h = null, e21ljbl2q = null, e21ljbl2h = null, e21jabl2q = null, e21jabl2h = null, e21ljbl3q = null, e21ljbl3h = null, e21jabl3q = null, e21jabl3h = null, e21ljbl4q = null, e21ljbl4h = null, e21jabl4q = null, e21jabl4h = null, e22clfsq = null, e22clfsh = 2, e22zffsq = null, e22zffsh = null, e22gdjeq = null, e22gdjeh = null, e23sfybthhtk = null, hytk = '', e1sqpz = '', e2htflyf = '', e3htflytjyf = '', e4htflnf = '', e5htflytjnf = '', e6htflytjjdf = '', e7htfljlf = '', e8htfl = '', e9httkdmf = '', e10httkclf = '', e11httknjf = '', e12httkxdfldfxf = '', e13httkxpsjf = '', e14httkzzf = '', e15httkbthbt = '', e16httkkahtfybzjxzy = '', e17ttkkqyfbzjxzy = '', e18httkxxf = '', e19httkzjf = '', e20dgglf = '', e21ysftczyzy = '', e22tpqt = '', e23 = '', e3jffs = null, e5jffs = null, e6jffs = null, e15jffs = null, e14jffs = null, e18jffs = null, e19jffs = null, e22jffs = null, czzy = 11910, e22lclfsq = null, e22lclfsh = 0, e22lzffsq = null, e22lzffsh = null, e22lytblq = null, e22lytblh = null, pxdsqpzlsfyz = null, yysmc = '', sqlx = null, hyydjehjh = null, hyydjeh = null, hyydje2h = null, hyydje3h = null, hyydje4h = null, hyydje5h = null, hyydje6h = null, hyydje7h = null, hyydje8h = null, hyydje9h = null, hyydje10h = null, hyydje11h = null, hyydje12h = null, nbglwszqjehjh = null, nbglwszqjeh = null, nbglwszqje2h = null, nbglwszqje3h = null, nbglwszqje4h = null, nbglwszqje5h = null, nbglwszqje6h = null, nbglwszqje7h = null, nbglwszqje8h = null, nbglwszqje9h = null, nbglwszqje10h = null, nbglwszqje11h = null, nbglwszqje12h = null, e23sfybthhtkh = null, sbtmzg = null, tpmhtmc = '', fktjms = '', sapzqts = '18.00', sapzqtsh = '18.00', fktjmsh = '非整月月结30~29当月（10日付款', fktjdmh = '3010', sapzqdmq = '3010', sapzqdmh = '3010', saphtfktjq = '', saphtfktjh = '', qyfmc = '' where id=31";
		 System.out.print(sql.length());
	}

}

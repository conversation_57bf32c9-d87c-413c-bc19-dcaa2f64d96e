package com.ryytn.http;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import javax.naming.ServiceUnavailableException;
import javax.net.ssl.*;
import java.io.IOException;
import java.net.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.Set;

/**
 * http协议get方式接口规范。
 *
 * <AUTHOR>
 * @date 2016-3-14
 * @version 1.0
 */
public abstract class AbstractHttp{
	private Log log = LogFactory.getLog(HttpGet.class); 
	public final static String HEAD_CONTENT_TYPE = "Content-Type";
	protected final static String HEAD_COOKIE = "Cookie";

	protected String charsetName = "UTF-8";
	protected final static int connectTime = 3000;
	protected final static int readTimeout = 30000;

	protected static String sessionid = "";

	/**
	 * http 消息请求
	 * @param url 请求地址。当地址不正确可能抛出异常
	 * @param content 请求参数。请对参数进行编码。<b>该值传的是编码之后的值</b>见{@link URLEncoder}
	 * @param httphead 请求头
	 * @return
	 * @throws ServiceUnavailableException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2016-9-14
	 */
	public String send(String url, String content, Map<String, String> httphead) throws UnknownServiceException,IOException{
		return send(url, content, httphead, connectTime, readTimeout, charsetName);
	}

	/**
	 * http 消息请求
	 * @param url 请求地址。当地址不正确可能抛出异常
	 * @param content 请求参数。请对参数进行编码。<b>该值传的是编码之后的值</b>见{@link URLEncoder}
	 * @param httphead 请求头
	 * @param charsetName 编码
	 * @return
	 * @throws ServiceUnavailableException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2016-9-14
	 */
	public String send(String url, String content, Map<String, String> httphead, String charsetName) throws UnknownServiceException,IOException{
		return send(url, content, httphead, connectTime, readTimeout, charsetName);
	}

	/**
	 * http 消息请求
	 * @param url 请求地址。当地址不正确可能抛出异常
	 * @param content 请求参数。请对参数进行编码。<b>该值传的是编码之后的值</b>见{@link URLEncoder}
	 * @param httphead 请求头
	 * @param connTime 连接超时时间 单位毫秒
	 * @param readTime 请求超时时间 单位毫秒
	 * @param charsetName 编码。 见{@link IConstant#Encode_utf8} / {@link IConstant#Encode_gbk}
	 * @return
	 * @throws ServiceUnavailableException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2016-9-14
	 */
	public abstract String send(String url, String content, Map<String, String> httphead, int connTime, int readTime, String charsetName) throws MalformedURLException,UnknownServiceException,IOException;

	/**
	 * 为http 请求增加头信息
	 * @param conn
	 * @param httphead
	 * <AUTHOR>
	 * @data 2016-9-14
	 */
	protected void addHead(URLConnection conn, Map<String, String> httphead){
		if(httphead != null && httphead.size()>0){
			Set<Map.Entry<String, String>> set = httphead.entrySet();
			for (Map.Entry<String, String> entry : set) {
				addHead(conn, entry.getKey(), entry.getValue());
			}
		}
	}

	/**
	 * 为http 请求增加头信息
	 * @param conn
	 * @param key
	 * @param value
	 * <AUTHOR>
	 * @data 2017-9-8
	 */
	protected void addHead(URLConnection conn, String key, String value){
		if(key != null && value != null){
			conn.setRequestProperty(key, value); 
		}
	}


	/**
	 * 根据url获取连接
	 * @param url
	 * <AUTHOR>
	 * @throws IOException
	 * @data 2016-9-18
	 */
	protected URLConnection getConn(String url, int connTime, int readTime) throws IOException{
		if(StringUtils.isBlank(url)){
			throw new MalformedURLException("HTTP 请求地址不能为空");
		}else if(url.startsWith("http") == false){
			throw new MalformedURLException("错误的HTTP地址 ，URL="+url);
		}

		//是否是HTTPS 通道
		boolean  isHttps = false;
		if(url.startsWith("https")){
			isHttps = true;
		}

		URL httpUrl = new URL(url);
		HttpsURLConnection.setDefaultHostnameVerifier(ignoreHostnameVerifier);
		URLConnection conn;
		/*
		if(PropertyEnum.Http_proxy_enable.isTrue()){
			Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(PropertyEnum.Http_proxy_host.getValue(), PropertyEnum.Http_proxy_host.getIntValue()));
			conn = httpUrl.openConnection(proxy);
		}else{
			
		}*/
		conn = httpUrl.openConnection();
		//设置默认5秒超时
		conn.setConnectTimeout(connTime);	//请求超时时间
		conn.setReadTimeout(readTime);		//响应超时时间

		//设置证书信息
		if(isHttps){
			try {

				TrustManager[] tm = { ignoreCertificationTrustManger };
				SSLContext sslContext = SSLContext.getInstance("SSL", "SunJSSE");
				sslContext.init(null, tm, new java.security.SecureRandom());

				SSLSocketFactory ssf = sslContext.getSocketFactory();
				((HttpsURLConnection)conn).setSSLSocketFactory(ssf);
			} catch (Exception e) {
				log.error("请求地址【{"+url+"}】设置证书异常"+e);
			}
		}
		return conn;
	}


	protected HostnameVerifier ignoreHostnameVerifier = new HostnameVerifier() {
		@Override
        public boolean verify(String s, SSLSession sslsession) {
			return true;
		}
	};

	protected TrustManager ignoreCertificationTrustManger = new X509TrustManager() {

		private X509Certificate[] certificates;

		@Override
		public void checkClientTrusted(X509Certificate certificates[],
				String authType) throws CertificateException {
			if (this.certificates == null) {
				this.certificates = certificates;
			}
		}


		@Override
		public void checkServerTrusted(X509Certificate[] ax509certificate,
				String s) throws CertificateException {
			if (this.certificates == null) {
				this.certificates = ax509certificate;
			}
		}


		@Override
		public X509Certificate[] getAcceptedIssuers() {
			return null;
		}
	};
}

package com.ryytn.http;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * http协议get方式接口规范。
 * 
 * <AUTHOR>
 * @date 2016-3-14
 * @version 1.0
 */
public class HttpGet extends AbstractHttp{
	private Log log = LogFactory.getLog(HttpGet.class); 

	/**
	 * 根据参数 url 和 params 组装完整的Url地址
	 * @param url 访问地址
	 * @param params 访问参数<b>该值传的是编码之后的值</b>见{@link URLEncoder}
	 * @return 返回完整的url地址
	 * <AUTHOR>
	 * @data 2016-3-15
	 */
	protected String buildGetUrl(String url, String params) throws MalformedURLException{
		if(StringUtils.isBlank(url)){
			throw new MalformedURLException("url can not be null");
		}
		if(StringUtils.isBlank(params)){
			return url;
		}
		if(url.indexOf('?')>0){
			url = url+"&";
		}else{
			url = url+"?";
		}
		return url + params;
	}
	
	
	@Override
	public String send(String url, String params,
			Map<String, String> httphead, int connTime, int readTime, String charset)
			throws IOException {
		String result = "";
		long start = System.currentTimeMillis();
		try{
			url = buildGetUrl(url, params);
			//if(logger.isDebugEnabled()){
				log.debug("http get url is"+url);
			//}

			HttpURLConnection conn = (HttpURLConnection)getConn(url,connTime, readTime);
			
			//增加http头
			if(StringUtils.isNotBlank(sessionid)){
				addHead(conn, HEAD_COOKIE, sessionid);
			}
			addHead(conn, httphead);
			
			InputStream in = null;
			try {
				boolean isok = conn.getResponseCode()== HttpURLConnection.HTTP_OK;
				if(isok){
					in = conn.getInputStream();
				}else{
					in = conn.getErrorStream();
				}
				
				if(in != null){
					if(StringUtils.isBlank(charset)){
						result = IOUtils.toString(in);
					}else{
						result = IOUtils.toString(in, charset);
					}
				}
				
				//获取sessionid
				String session_value = conn.getHeaderField("Set-Cookie");
				if(StringUtils.isNotBlank(session_value)){
					String[] sessionId = session_value.split(";");
					sessionid = sessionId[0];
				}
				
				if(isok == false){
					log.error("访问地址【{"+url+"}】返回错误【code={"+conn.getResponseCode()+"}】,错误信息【{"+result+"}】" );
					result = "http_"+conn.getResponseCode();
				}
			}finally{
				IOUtils.closeQuietly(in);
				if(conn != null){
					conn.disconnect();
				}
			}
		}finally{
			int usetime = (int)(System.currentTimeMillis() - start);
			if(usetime > AbstractHttp.connectTime){
				log.error("【接口调用耗时长】调用第三方接口{"+url+"}耗时{"+usetime+"}毫秒");
			}
		}
		return result;
	}

}

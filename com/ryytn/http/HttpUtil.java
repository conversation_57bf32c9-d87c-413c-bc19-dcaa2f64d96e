package com.ryytn.http;


import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


import java.io.IOException;
import java.net.MalformedURLException;
import java.net.UnknownServiceException;
import java.util.Map;


/**
 * HTTP简单调用工具类。
 * 自动分辨是否是ssl通道。
 * <AUTHOR>
 * @date 2016-4-26
 * @version 1.0
 */
public class HttpUtil {
	private static AbstractHttp httpGet = new HttpGet();
	private static AbstractHttp httpPost = new HttpPost();

	private Log log = LogFactory.getLog(HttpUtil.class); 

	/**
	 * post发送HTTP 消息
	 * @param url	       请求地址。可以是http 或者 https
	 * @param content  请求内容
	 * @return
	 * <AUTHOR>
	 * @throws IOException
	 * @throws UnknownServiceException
	 * @throws MalformedURLException
	 * @data 2016-9-18
	 */
	public static String sendPost(String url, String content) throws MalformedURLException, UnknownServiceException, IOException{
		return httpPost.send(url, content, null);
	}

	/**
	 * post发送HTTP 消息
	 * @param url	       请求地址。可以是http 或者 https
	 * @param content  请求内容
	 * @param charsetName 编码
	 * @return
	 * <AUTHOR>
	 * @throws IOException
	 * @throws UnknownServiceException
	 * @throws MalformedURLException
	 * @data 2016-9-18
	 */
	public static String sendPost(String url, String content, String charsetName) throws MalformedURLException, UnknownServiceException, IOException{
		 return httpPost.send(url, content, null, charsetName);
	}

	/**
	 * post发送HTTP 消息
	 * @param url 		请求地址。可以是http 或者 https
	 * @param content	请求内容
	 * @param httphead	请求头需添加内容
	 * @return
	 * @throws MalformedURLException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2017年11月6日
	 */
	public static String sendPost(String url, String content, Map<String, String> httphead)  throws MalformedURLException, UnknownServiceException, IOException{
		return httpPost.send(url, content, httphead);
	}

	/**
	 * post发送HTTP 消息
	 * @param url 		请求地址。可以是http 或者 https
	 * @param content	请求内容
	 * @param httphead	请求头需添加内容
	 * @param charsetName 编码
	 * @return
	 * @throws MalformedURLException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2017年11月6日
	 */
	public static String sendPost(String url, String content, Map<String, String> httphead, String charsetName)  throws MalformedURLException, UnknownServiceException, IOException{
		return httpPost.send(url, content, httphead, charsetName);
	}

	/**
	 * get发送HTTP 消息
	 * @param url	       请求地址。可以是http 或者 https
	 * @return
	 * <AUTHOR>
	 * @throws IOException
	 * @throws UnknownServiceException
	 * @throws MalformedURLException
	 * @data 2016-9-18
	 */
	public static String sendGet(String url) throws MalformedURLException, UnknownServiceException, IOException{
		return httpGet.send(url, null, null);
	}

	/**
	 * get发送HTTP 消息
	 * @param url	       请求地址。可以是http 或者 https
	 * @param charsetName 编码
	 * @return
	 * <AUTHOR>
	 * @throws IOException
	 * @throws UnknownServiceException
	 * @throws MalformedURLException
	 * @data 2016-9-18
	 */
	public static String sendGet(String url, String charsetName) throws MalformedURLException, UnknownServiceException, IOException{
		 return httpGet.send(url, null, null, charsetName);
	}

	/**
	 * get发送HTTP 消息
	 * @param url		请求地址。可以是http 或者 https
	 * @param httphead	请求头需添加内容
	 * @return
	 * @throws MalformedURLException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2017年11月6日
	 */
	public static String sendGet (String url, Map<String, String> httphead) throws MalformedURLException, UnknownServiceException, IOException{
		return httpGet.send(url, null, httphead);
	}

	/**
	 * get发送HTTP 消息
	 * @param url		请求地址。可以是http 或者 https
	 * @param httphead	请求头需添加内容
	 * @param charsetName 编码
	 * @return
	 * @throws MalformedURLException
	 * @throws UnknownServiceException
	 * @throws IOException
	 * <AUTHOR>
	 * @data 2017年11月6日
	 */
	public static String sendGet (String url, Map<String, String> httphead, String charsetName) throws MalformedURLException, UnknownServiceException, IOException{
		 return httpGet.send(url, null, httphead, charsetName);
	}


}


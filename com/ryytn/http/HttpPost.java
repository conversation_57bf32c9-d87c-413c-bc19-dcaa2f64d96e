package com.ryytn.http;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.util.Map;

/**
 * http协议get方式接口规范。
 * 
 * <AUTHOR>
 * @date 2016-3-14
 * @version 1.0
 */
public class HttpPost extends AbstractHttp{
	private Log log = LogFactory.getLog(HttpPost.class); 

	
	@Override
	public String send(String url, String content,
			Map<String, String> httphead, int connTime, int readTime, String charset)
			throws  IOException {
		String result = "";
		long start = System.currentTimeMillis();
		int code = 999;
		try {
		
			log.debug("http get url is:"+url+";post httpbody content is:"+content);
		
			
			HttpURLConnection conn = (HttpURLConnection)getConn(url, connTime, readTime);
			//增加http头
			if(StringUtils.isNotBlank(sessionid)){
				addHead(conn, HEAD_COOKIE, sessionid);
			}
			addHead(conn, httphead);
			
			if(content==null){
				content = "";
			}
			conn.setDoOutput(true);
			byte[] ob;
			if(StringUtils.isBlank(charset)){
				ob = content.getBytes();
			}else{
				ob = content.getBytes(charset);
			}
			conn.getOutputStream().write(ob);
			
			InputStream in = null;
			try {
				code = conn.getResponseCode();
				boolean isok = (code== HttpURLConnection.HTTP_OK);
				System.out.println("isok"+isok);
				if(isok){
					in = conn.getInputStream();
				}else{
					in = conn.getErrorStream();
				}
				
				if(in != null){
					if(StringUtils.isBlank(charset)){
						result = IOUtils.toString(in);
					}else{
						result = IOUtils.toString(in, charset);
					}
				}
				
				//获取sessionid
				String session_value = conn.getHeaderField("Set-Cookie");
				if(StringUtils.isNotBlank(session_value)){
					String[] sessionId = session_value.split(";");
					sessionid = sessionId[0];
				}
				
				if(isok == false){
					log.error("访问地址【{"+url+"}】返回错误【code={"+conn.getResponseCode()+"}】,错误信息【{"+result+"}】" );
					result = "http_"+conn.getResponseCode();
				}
			}finally{
				IOUtils.closeQuietly((Closeable) in);
				if(conn != null){
					conn.disconnect();
				}
			}
		}finally{
			int usetime = (int)(System.currentTimeMillis() - start);
			if(usetime > AbstractHttp.connectTime){
				log.error("【接口调用耗时长】调用第三方接口{"+url+"}耗时{"+usetime+"}毫秒");
			}
		}
		return result;
	}
	
}

package com.ryytn;

import biweekly.util.org.apache.commons.codec.binary.Base64;
import yitouniu.util.HttpClientUtils;
import yitouniu.util.HttpUtil;
import yitouniu.util.RsaEncrypt;

import javax.crypto.Cipher;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

/**
 * @program: oa
 * @description: 测试登录接口
 * @author: haiyang
 * @create: 2023-10-27 15:42
 **/
public class TestLogin {

    private static final String public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBEUdyxHCHAxGB5oqq8dfuFPWNm9xdbDc6azMzVih8/zht770/bFcr6PibI6iQLeUPSPb/+I4ignPHvblX1FftrFtZ5obWyWccQsoVcK2eGbVLgIKl0KYDFOqitJ40HdYOo6SX+OaXb7NC/GAweOqbRhZQBB7IiTg+z+svptE8NwIDAQAB";

    public static void main(String[] args) throws Exception {
        String ip = "http://************:8086";
        String url = "/api/system/sso/oaLogin?info=";
        long l = System.currentTimeMillis();
        String info = "{\"code\":\"SuiTZM\",\"loginName\":\"c011959\",\"requestSource\":\"oa\",\"ts\":"+ l +"}";
        String token = encryptByPublicKey(public_key, info);
        String requestUrl = ip + url + token;
        System.out.println(token);
        String s = HttpClientUtils.doGet(requestUrl);
        System.out.println("request result: " + s);
    }


    private static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }
}

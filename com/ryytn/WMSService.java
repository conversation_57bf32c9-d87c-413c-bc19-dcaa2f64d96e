package com.ryytn;

import com.alibaba.fastjson.JSONObject;
import com.ryytn.http.HttpClientResult;
import com.ryytn.http.HttpClientUtils;
import com.ryytn.http.HttpUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.common.DateUtil;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.UnknownServiceException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Chen <PERSON>i
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 10:47 2022/6/2
 */
public class WMSService {
    private String ip = "http://**************:9090/g3-2";
    private String AppKey = "oainterfaces";
    private String AppSecrect = "51c6ff8aed904c1e937f466c251ec285";
    private HttpUtil http = new HttpUtil();
    private Log log = LogFactory.getLog(WMSService.class);

    public WMSService() {
    }

    public String getToken(String loginId) {
        String url = "/login/api/getToken";
        MD5Helper md5Helper = new MD5Helper();
        String nowTime = getNowTimeStr(new Date(), "yyyyMMddHHmmss");
        String Token = "";
        String stringA = "appkey="+AppKey+"&time="+nowTime+"&username="+loginId;
        String sign = md5Helper.encrypt32(stringA+"&appsecret="+AppSecrect).toUpperCase();

        try {
            Map content = new HashMap();
            content.put("appkey", AppKey);
            content.put("time", nowTime);
            content.put("sign", sign);
            content.put("username", loginId);
            String reqUrl = this.ip + url;
            this.log.error(content);
            this.log.error(reqUrl);
//            String retStr = HttpUtil.sendPost(reqUrl, content.toString());
            HttpClientResult retStr = HttpClientUtils.doPost(reqUrl, content);
            this.log.error(retStr);
            String code = String.valueOf(getValue(retStr.getContent()).get("code"));
            this.log.error(code);
            if ("200".equals(code)) {
                Token = (String) getValue(String.valueOf(getValue(retStr.getContent()).get("data"))).get("token");
                this.log.error(Token);
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (UnknownServiceException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Token;
    }

    public String loginEHR(String loginId, String gotourl) {
        String ret = "";

        try {
            String token = this.getToken(loginId);
            if (!"".equals(token)) {
                String url = "/login/api?";
                this.log.error(this.ip + url+"token="+token+"&username="+loginId);
                ret = HttpUtil.sendGet(this.ip + url+"token="+token+"&username="+loginId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = "";
        }

        return ret;
    }

    private static String getNowTimeStr(Date time, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }

    public static Map<String, Object> getValue(String param) {
        Map<String, Object> map = new HashMap<>();
        String str = "";
        String key = "";
        Object value = "";
        char[] charList = param.toCharArray();
        boolean valueBegin = false;
        for (int i = 0; i < charList.length; i++) {
            char c = charList[i];
            if (c == '{') {
                if (valueBegin == true) {
                    value = getValue(param.substring(i, param.length()));
                    i = param.indexOf('}', i) + 1;
                    map.put(key, value);
                }
            } else if (c == '=') {
                valueBegin = true;
                key = str;
                str = "";
            } else if (c == ',') {
                valueBegin = false;
                value = str;
                str = "";
                map.put(key, value);
            } else if (c == '}') {
                if (str != "") {
                    value = str;
                }
                map.put(key, value);
                return map;
            } else if (c != ' ') {
                str += c;
            }
        }
        return map;
    }




}

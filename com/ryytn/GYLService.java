package com.ryytn;

import ecode.security.rsa.Base64;

import javax.crypto.Cipher;
import java.net.URLEncoder;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @Date 2023/11/27
 */
public class GYLService {

    private static final String public_key = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBEUdyxHCHAxGB5oqq8dfuFPWNm9xdbDc6azMzVih8/zht770/bFcr6PibI6iQLeUPSPb/+I4ignPHvblX1FftrFtZ5obWyWccQsoVcK2eGbVLgIKl0KYDFOqitJ40HdYOo6SX+OaXb7NC/GAweOqbRhZQBB7IiTg+z+svptE8NwIDAQAB";
    //    private static final String url = "https://gyl.ryytngroup.com/api/system/sso/oaLogin?info=";
    private static final String url = "https://scp-test.ryytngroup.com/api/system/sso/oaLogin?info=";

    public String login(String loginName) throws Exception {

        System.out.println("供应链登录 loginName：" + loginName);

        String info = "{\"code\":\"SuiTZM\",\"loginName\":\"{loginName}\",\"requestSource\":\"oa\",\"ts\":"+ System.currentTimeMillis() +"}";

        info = info.replace("{loginName}", loginName);

        String token = encryptByPublicKey(info);

        return url + token;
    }


    private static String encryptByPublicKey(String text) throws Exception {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(public_key));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return URLEncoder.encode(Base64.encodeBase64String(result));
    }
}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.esb.ListMaterialMasterDataV2;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 18:13 2021/9/9
 * @Modified By:
 */
public class ListMaterialMasterDataCronV4 extends Base<PERSON>ronJob {

    @Override
    public void execute(){
        ListMaterialMasterDataV2 listMaterialMasterDataV2 = new ListMaterialMasterDataV2();
        Map param = new HashMap();
        JSONObject ctrlObject = new JSONObject();
        List<JSONObject> dataListObject = new ArrayList<JSONObject>();
        JSONObject dataObject = new JSONObject();
        String DATUM = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String UZEIT = new SimpleDateFormat("HH:mm:ss").format(new Date());
        String INFID = String.valueOf(System.currentTimeMillis());
        //ctrl头
        ctrlObject.put("SYSID","OA");
        ctrlObject.put("REVID","SAP");
        ctrlObject.put("FUNID","ZWMSINF029");
        ctrlObject.put("INFID",INFID);
        ctrlObject.put("UNAME","sysadmin");
        ctrlObject.put("DATUM",DATUM);
        ctrlObject.put("UZEIT",UZEIT);
        ctrlObject.put("KEYID","");
        ctrlObject.put("MSGTY","");
        ctrlObject.put("MSAGE","");

        //data头 ERSDA:创建日期    LAEDA:最后更改日期
        dataObject.put("MATNR","");
        dataObject.put("WERKS","");
        RecordSet rs = new RecordSet();
        rs.executeQuery("select top 1 * from uf_KCXGWLZSJSY");
        RecordSet rsERSDA = new RecordSet();
        RecordSet rsLAEDA = new RecordSet();
        String ERSDA = null;
        String LAEDA = null;
        rsERSDA.executeQuery("select MAX(ERSDA) as ERSDA from uf_KCXGWLZSJSY");
        rsLAEDA.executeQuery("select MAX(LAEDA) as LAEDA from uf_KCXGWLZSJSY");
        if(rsERSDA.next()){
            ERSDA = rsERSDA.getString("ERSDA");
        }
        if(rsLAEDA.next()){
            LAEDA = rsLAEDA.getString("LAEDA");
        }
        //没数据时输入空，得到全量数据，后续增量
        if(rs.next()){
            dataObject.put("ERSDA","");
//            dataObject.put("ERSDA",ERSDA.replaceAll( "-",""));
            dataObject.put("LAEDA",LAEDA.replaceAll("-", ""));
            new BaseBean().writeLog("有数据了。");
        } else{
            dataObject.put("ERSDA","");
            dataObject.put("LAEDA","");
        }

        dataListObject.add(dataObject);

        param.put("CTRL",ctrlObject);
        param.put("DATA",dataListObject);

        String codes = "1110,1210,1310,1410,1510,2110,5010,5110,5210,6010,6110,6210,7010,7110,8010,8110,8210,8220,8310,8410,8510,8610,8710,8810,8910,9010,9110,9210,3010";
        String[] split = codes.split(",");

        for (String s : split) {
            dataObject.put("WERKS", s);
            Map resultMap = listMaterialMasterDataV2.executeParam(param);
            new BaseBean().writeLog("定时读取物料主数据ListMaterialMasterDataCron：" + resultMap);
        }

//        new BaseBean().writeLog("定时读取物料主数据ListMaterialMasterDataCron："+resultMap.get("MSGTY"));

    }
}

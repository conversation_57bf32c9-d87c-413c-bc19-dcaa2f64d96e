package yitouniu.planningtasks;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
//职级合并
public class JobtitleMergeCron extends BaseCronJob {
    @Override
    public void execute() {
        RecordSet rs = new RecordSet();
        String sql = "select * from cus_fielddata where scopeid = '1' and scope = 'HrmCustomFieldByInfoType' ";
        rs.executeQuery(sql);
        while(rs.next()){
            String id = rs.getString("id");
            String field3 = rs.getString("field3");
            RecordSet rs2 = new RecordSet();
            String sql2 = "select * from cus_fielddata where id = ? and scopeid = '-1' and scope = 'HrmCustomFieldByInfoType' ";
            rs2.executeQuery(sql2,id);
            if(rs2.next()){
                RecordSet rs3 = new RecordSet();
                String sql3 = "update cus_fielddata set field3 = '"+field3+"' where id = '"+id+"' and scopeid = '-1' and scope = 'HrmCustomFieldByInfoType' ";
                rs3.executeUpdate(sql3);
                new BaseBean().writeLog("测试日志" + sql3);
                String sql4 = "update cus_fielddata set field0 = '"+rs2.getString("field0")+"' where id = '"+id+"' and scopeid = '1' and scope = 'HrmCustomFieldByInfoType' ";
                rs3.executeUpdate(sql4);
                new BaseBean().writeLog("测试日志" + sql4);
            }
        }
    }
}

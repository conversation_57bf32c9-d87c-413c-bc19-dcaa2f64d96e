package yitouniu.planningtasks;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.StdUtil;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-05-22 09:57
 **/
public class StdProductSyncCronV22 extends BaseCronJob {
    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    private static final int REQUESTS_PER_SECOND = 20;
    private static final RateLimiter rateLimiter = RateLimiter.create(REQUESTS_PER_SECOND);

    private static final int PAGE_SIZE = 100;

    @Override
    public void execute() {
//        String gatewayUrl = "https://std-test.ryytngroup.com";
//        String qryApi = "/crm-mdm/v1/external/std/findOrgByConditions";

        String gatewayUrl = StdUtil.GATEWAY_URL;
        String qryApi = StdUtil.STD_PRODUCT_LEVEL_API;

        int page = 1;
        int totalPages = 1;
        while (page <= totalPages) {
            rateLimiter.acquire();
            long l = System.currentTimeMillis();
            String apiSign = StdUtil.getApiSign(String.valueOf(l));
            Map<String, Object> params = assembleQryParam(page);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("ak", StdUtil.STD_AK);
            headers.put("timestamp", String.valueOf(l));
            headers.put("token", apiSign);
            String responseBody = HttpRequest.get(gatewayUrl + qryApi).form(params).addHeaders(headers).execute().body();
            if (StringUtils.isEmpty(responseBody)) {
                baseBean.writeLog("stdItfResponseBodyEmpty");
                break;
            }
            JSONObject responseJson = (JSONObject) JSON.parse(responseBody);
            if (responseJson.getString("code").equals("200")) {
                StdResponse stdResponse = JSON.parseObject(responseJson.getJSONObject("result").toJSONString(), StdResponse.class);
                totalPages = stdResponse.getPages();
                page++;
                baseBean.writeLog("StdProductSyncCron.totalPages:" + totalPages + ",page:" + page);
                if (CollectionUtils.isNotEmpty(stdResponse.getRecords()) && stdResponse.getRecords().size() > 0) {
                    List<BusinessRecord> data = stdResponse.getRecords();
                    // data过滤 productLevelCategory = SAP
                    data = data.stream().filter(e -> Objects.equals(e.getProductLevelCategory(), "SAP")).collect(Collectors.toList());
                    // 处理更新的数据并返回需要插入的数据
                    List<BusinessRecord> insertData = handleUpdateData(data);
                    if (CollectionUtils.isNotEmpty(insertData)) {
                        List<List> batchInsertData = Lists.newArrayList();
                        for (BusinessRecord dataBean : insertData) {
                            batchInsertData.add(
                                    Lists.newArrayList(
                                            dataBean.getProductLevelCode(),
                                            dataBean.getProductLevelName(),
                                            dataBean.getProductLevelType(),
                                            dataBean.getProductLevelCategory(),
                                            dataBean.getLevelNum(),
                                            dataBean.getParentCode(),
                                            dataBean.getParentCode(),
                                            dataBean.getRuleCode(),
                                            // 测试209 正式187
                                            209,
                                            1,
                                            1,
                                            dataBean.getEnableStatus().equals("009")?"已启用":"已禁用",
                                            dataBean.getDelFlag().equals("009")?"正常":"已删除",
                                            dataBean.getProductItemCode(),
                                            StringUtils.isNotEmpty(dataBean.getAvailableStatus())? dataBean.getAvailableStatus().equals("Y")? 1 : 2 : ""
                                    )
                            );
                        }
                        boolean insertSuccess = rs.executeBatchSql("insert into uf_cpck(productLevelCode,productLevelName,productLevelType,productLevelCategory,levelNum,parentCode,parentName,ruleCode,formmodeid,modedatacreater,modedatacreatertype,enableStatus,delFlag,productItemCode,sfsx) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", batchInsertData);
                        baseBean.writeLog("page:" + page + "插入执行结果:" + insertSuccess);
                    }
                }
            } else {
                baseBean.writeLog("请求std接口报错:" + responseBody);
                break;
            }
        }
        baseBean.writeLog("执行拉取std产品层级任务结束，totalPages:" + totalPages);
    }

    private List<BusinessRecord> handleUpdateData(List<BusinessRecord> data) {
        RecordSet rsq = new RecordSet();
        List<List> updateAllData = Lists.newArrayList();
        Map<String, BusinessRecord> businessRecordMap = data.stream().collect(Collectors.toMap(BusinessRecord::getProductLevelCode, Function.identity(), (v1, v2) -> v1));
        String productLevelCodeListStr = data.stream().map(e -> "'" + e.getProductLevelCode() + "'").collect(Collectors.joining(","));
        rsq.execute("select * from uf_cpck where productLevelCode in ("+productLevelCodeListStr+")");
        List<String> productLevelCodeList = Lists.newArrayList();
        while (rsq.next()) {
            String productLevelCode = rsq.getString("productLevelCode");
            BusinessRecord businessRecord = businessRecordMap.get(productLevelCode);
            if (Objects.nonNull(businessRecord)) {
                productLevelCodeList.add(businessRecord.getProductLevelCode());
                updateAllData.add(Lists.newArrayList(
                                businessRecord.getProductLevelName(),
                                businessRecord.getProductLevelType(),
                                businessRecord.getProductLevelCategory(),
                                businessRecord.getLevelNum(),
                                businessRecord.getParentCode(),
                                businessRecord.getParentCode(),
                                businessRecord.getRuleCode(),
                                businessRecord.getEnableStatus().equals("009")?"已启用":"已禁用",
                                businessRecord.getDelFlag().equals("009")?"正常":"已删除",
                                businessRecord.getProductItemCode(),
                                StringUtils.isNotEmpty(businessRecord.getAvailableStatus())? businessRecord.getAvailableStatus().equals("Y")? 1 : 2 : "",
                                productLevelCode
                        )
                );
            }
        }
        if (CollectionUtils.isNotEmpty(updateAllData)) {
            rs.executeBatchSql("update uf_cpck set productLevelName=?,productLevelType=?,productLevelCategory=?,levelNum=?,parentCode=?,parentName=?,ruleCode=?,enableStatus=?,delFlag=?,productItemCode=?,sfsx=? where productLevelCode=?", updateAllData);
        }
        return data.stream().filter(e -> !productLevelCodeList.contains(e.getProductLevelCode())).collect(Collectors.toList());
    }

    private static Map<String, Object> assembleQryParam(int page) {
        Map<String, Object> params = Maps.newHashMap();
//        params.put("enableStatus", );
//        params.put("delFlag", );
//        params.put("orgCode", );
//        params.put("orgName", );
//        params.put("codeOrName", );
//        params.put("orgTypeList", );
//        params.put("levelNum", );
        params.put("page", page);
        params.put("size", PAGE_SIZE);

        return params;
    }



    public static class StdResponse {
        private int total;
        private int size;
        private int current;
        private boolean optimizeCountSql;
        private boolean hitCount;
        private boolean searchCount;
        private int pages;
        private List<BusinessRecord> records;


        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public boolean isOptimizeCountSql() {
            return optimizeCountSql;
        }

        public void setOptimizeCountSql(boolean optimizeCountSql) {
            this.optimizeCountSql = optimizeCountSql;
        }

        public boolean isHitCount() {
            return hitCount;
        }

        public void setHitCount(boolean hitCount) {
            this.hitCount = hitCount;
        }

        public boolean isSearchCount() {
            return searchCount;
        }

        public void setSearchCount(boolean searchCount) {
            this.searchCount = searchCount;
        }

        public int getPages() {
            return pages;
        }

        public void setPages(int pages) {
            this.pages = pages;
        }

        public List<BusinessRecord> getRecords() {
            return records;
        }

        public void setRecords(List<BusinessRecord> records) {
            this.records = records;
        }
    }


    public static class BusinessRecord extends BaseStdRecord {

        private String productLevelCode;
        private String productLevelName;
        private String productLevelType;
        private String productLevelCategory;
        private Integer levelNum;
        private String parentCode;
        private String parentName;
        private String ruleCode;

        private String productItemCode;



        // 是否生效 Y生效 N不生效
        private String availableStatus;


        public String getAvailableStatus() {
            return availableStatus;
        }

        public void setAvailableStatus(String availableStatus) {
            this.availableStatus = availableStatus;
        }


        public String getProductItemCode() {
            return productItemCode;
        }

        public void setProductItemCode(String productItemCode) {
            this.productItemCode = productItemCode;
        }




        // Getter methods
        public String getProductLevelCode() {
            return productLevelCode;
        }

        public String getProductLevelName() {
            return productLevelName;
        }

        public String getProductLevelType() {
            return productLevelType;
        }

        public String getProductLevelCategory() {
            return productLevelCategory;
        }

        public Integer getLevelNum() {
            return levelNum;
        }

        public String getParentCode() {
            return parentCode;
        }

        public String getParentName() {
            return parentName;
        }

        public String getRuleCode() {
            return ruleCode;
        }

        // Setter methods
        public void setProductLevelCode(String productLevelCode) {
            this.productLevelCode = productLevelCode;
        }

        public void setProductLevelName(String productLevelName) {
            this.productLevelName = productLevelName;
        }

        public void setProductLevelType(String productLevelType) {
            this.productLevelType = productLevelType;
        }

        public void setProductLevelCategory(String productLevelCategory) {
            this.productLevelCategory = productLevelCategory;
        }

        public void setLevelNum(Integer levelNum) {
            this.levelNum = levelNum;
        }

        public void setParentCode(String parentCode) {
            this.parentCode = parentCode;
        }

        public void setParentName(String parentName) {
            this.parentName = parentName;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

    }

    public static class BaseStdRecord {
        private String id;
        private String createAccount;
        private String createName;
        private Date createTime;
        private String modifyAccount;
        private Date modifyTime;
        private String modifyName;
        private String remark;
        private String tenantCode;
        /**
         * 启禁用
         * 009 启用
         * 003 禁用
         */
        private String enableStatus;
        private String delFlag;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getCreateAccount() {
            return createAccount;
        }

        public void setCreateAccount(String createAccount) {
            this.createAccount = createAccount;
        }

        public String getCreateName() {
            return createName;
        }

        public void setCreateName(String createName) {
            this.createName = createName;
        }

        public Date getCreateTime() {
            return createTime;
        }

        public void setCreateTime(Date createTime) {
            this.createTime = createTime;
        }

        public String getModifyAccount() {
            return modifyAccount;
        }

        public void setModifyAccount(String modifyAccount) {
            this.modifyAccount = modifyAccount;
        }

        public Date getModifyTime() {
            return modifyTime;
        }

        public void setModifyTime(Date modifyTime) {
            this.modifyTime = modifyTime;
        }

        public String getModifyName() {
            return modifyName;
        }

        public void setModifyName(String modifyName) {
            this.modifyName = modifyName;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getTenantCode() {
            return tenantCode;
        }

        public void setTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
        }

        public String getEnableStatus() {
            return enableStatus;
        }

        public void setEnableStatus(String enableStatus) {
            this.enableStatus = enableStatus;
        }

        public String getDelFlag() {
            return delFlag;
        }

        public void setDelFlag(String delFlag) {
            this.delFlag = delFlag;
        }
    }

}

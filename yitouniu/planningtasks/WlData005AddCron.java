package yitouniu.planningtasks;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.SAPUtil;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 物料主数据获取
 */
public class WlData005AddCron extends BaseCronJob {


    private String FUNID = "ZINF005";
    private RecordSet rs = new RecordSet();

    private String BEGDAT;

    public void setBEGDAT(String BEGDAT) {
        this.BEGDAT = BEGDAT;
    }

    @Override
    public void execute() {
        long time = new Date().getTime();
        String currDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        if (StringUtils.isBlank(BEGDAT)) {
            DateTime lastTime = DateUtil.offsetDay(new Date(), -7);
            BEGDAT = new SimpleDateFormat("yyyy-MM-dd").format(lastTime);
        }

        String currTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        String params = "{\n" +
                "    \"CTRL\":{\n" +
                "        \"SYSID\":\"OA\",\n" +
                "        \"REVID\":\"SAP\",\n" +
                "        \"FUNID\":\""+FUNID+"\",\n" +
                "        \"INFID\":\""+time+"\",\n" +
                "        \"UNAME\":\"\",\n" +
                "        \"DATUM\":\""+currDate+"\",\n" +
                "        \"UZEIT\":\""+currTime+"\",\n" +
                "        \"KEYID\":\"\",\n" +
                "        \"TABIX\":0,\n" +
                "        \"MSGTY\":\"\",\n" +
                "        \"MSAGE\":\"\"\n" +
                "    },\n" +
                "    \"DATA\":{\n" +
                "        \"MATNR\":\"\",\n" +
                "        \"MTART\":\"\",\n" +
                "        \"BEGDAT\":\"" + BEGDAT + "\",\n" +
                "        \"ENDDAT\":\""+currDate+"\"\n" +
                "    }\n" +
                "}";
        // 一次拉7天的数据
        String execute = SAPUtil.execute(params);
        new BaseBean().writeLog("WlData005Cron.param" + execute);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
        String update = "";

        for (JSONObject datum : data) {
            String MATNR = datum.getString("MATNR");
            String MAKTX = datum.getString("MAKTX");
            String MTART = datum.getString("MTART");
            String MEINS = datum.getString("MEINS");
            String ZBZXSJ = datum.getString("ZBZXSJ");
            String VTEXT = datum.getString("VTEXT");

            boolean b = selectCb(MATNR);
            // 只做更新
            if(b){
                update +="更新:"+ updateCb(MATNR, MAKTX, MTART, MEINS, ZBZXSJ, VTEXT)+",";
            }
//            else {
//                update += "新增:"+insertCb(MATNR, MAKTX, MTART, MEINS, ZBZXSJ, VTEXT)+",";
//            }
        }
        rs.executeUpdate("insert into uf_sap (qqcs,xycs,gxjg,qqsj,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                params,execute,update,currDate,"17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_sap");
    }




    // 修改
    public boolean updateCb(String MATNR, String MAKTX, String MTART,String MEINS, String ZBZXSJ,String VTEXT) {
        boolean b = rs.executeUpdate("update uf_wlzsj set MATNR =?,MAKTX=?,MTART=?,MEINS=?,bzsj=?,sl=? where MATNR = ? and BWKEY in('6010','5010')",
                MATNR, MAKTX, MTART, MEINS, ZBZXSJ, VTEXT,MATNR);
        return b;
    }


    // 新增
    public boolean insertCb(String MATNR, String MAKTX, String MTART,String MEINS, String ZBZXSJ,String VTEXT) {
        boolean b = rs.executeUpdate("insert into uf_wlzsj ( MATNR, MAKTX, MTART, MEINS,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME,bzsj,sl) values (?,?,?,?,?,?,?,?,?,?,?) ",
                MATNR, MAKTX, MTART, MEINS, "16", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()), ZBZXSJ, VTEXT);
        WorkflowUtil.ModeDataShare("uf_wlzsj");


        return b;
    }

    // 查询
    public boolean selectCb( String MATNR) {
         RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  uf_wlzsj where MATNR =? and BWKEY in('6010','5010')",MATNR);


        return rs1.next();
    }


}



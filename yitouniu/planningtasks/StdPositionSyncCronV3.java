package yitouniu.planningtasks;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.StdUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 岗位信息同步-定时任务
 *
 * <AUTHOR>
 * @since 2024-08-22 11:30
 */
public class StdPositionSyncCronV3 extends BaseCronJob {
    private final Log LOG = LogFactory.getLog(StdPositionSyncCronV3.class);

    /*岗位级别分页查询*/
    private static final String POSITION_LEVEL_GET_URL = "/crm-mdm/v1/external/std/findPositionLevelByConditions";
    /*岗位人员分页查询*/
    private static final String POSITION_USER_GET_URL = "/crm-mdm/v1/external/std/findPositionByConditions";

    private static final Object DISABLE_CODE = "003";
    private static final int PAGE_SIZE = 1000;
    private static final String CONCATE_STR = "/";


    /*默认值是生产环境，因为配置到文件的话，需要重启*/
    /*「测试环境：uf_STDJSZJ-岗位：formmodeid：220 uf_STDGWXX-岗位人员：formmodeid：221」*/
    /*「正式环境：uf_STDJSZJ-岗位：formmodeid：199 uf_STDGWXX-岗位人员：formmodeid：200」*/
    private static final String POSITION_LEVEL_FORMMODEID =
            StrUtil.blankToDefault(new BaseBean().getPropValue("std_configuration", "position_level_formmodeid"),
                    "199");
    private static final String POSITION_USER_FORMMODEID =
            StrUtil.blankToDefault(new BaseBean().getPropValue("std_configuration", "position_user_formmodeid"),
                    "200");

    @Override
    public void execute() {
        LOG.info("StdPositionSyncCron.execute，开始-执行岗位信息同步");
        long startTime = System.currentTimeMillis();

        int levelCount = positionLevelHandler();

        int positionCount = positionUserHandler();

        int totalCount = levelCount + positionCount;
        LOG.info("StdPositionSyncCron.execute，完成-执行岗位信息同步，耗时:" + (System.currentTimeMillis() - startTime)
                + ",影响行数:" + totalCount);
    }


    /**
     * 处理岗位级别信息
     *
     * @return 影响行数
     */
    private int positionLevelHandler() {
        long startTime = System.currentTimeMillis();

        Map<String, Object> paramMap = new HashMap<>();
        int enable = handlerByPage(POSITION_LEVEL_GET_URL, paramMap, StdPositionLevel.class, (positionLevels, curPage) -> {
            String codeList = positionLevels.stream().map(StdPositionLevel::getPositionLevelCode)
                    .map(code -> "'" + code + "'").collect(Collectors.joining(","));
            String selectSql = "SELECT * FROM uf_STDJSZJ WHERE positionLevelCode IN (" + codeList + ");";
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(selectSql);

            List<StdPositionLevelEntity> entityList = new ArrayList<>();
            while (recordSet.next()) {
                StdPositionLevelEntity entity = new StdPositionLevelEntity();
                entity.setId(recordSet.getInt("id"));
                entity.setPositionLevelCode(recordSet.getString("positionLevelCode"));
                entity.setPositionLevelName(recordSet.getString("positionLevelName"));
                entity.setRoleCode(recordSet.getString("roleCode"));
                entity.setRoleName(recordSet.getString("roleName"));
                entity.setEnableStatus(recordSet.getString("enableStatus"));
                entity.setDelFlag(recordSet.getString("delFlag"));
                entityList.add(entity);
            }

            List<List> insertList = new ArrayList<>();
            List<List> updateList = new ArrayList<>();
            Map<String, StdPositionLevelEntity> entityCodeMap = entityList.stream()
                    .collect(Collectors.toMap(StdPositionLevelEntity::getPositionLevelCode, Function.identity(), (a, b) -> a));

            for (StdPositionLevel positionLevel : positionLevels) {
                StdPositionLevelEntity entity = entityCodeMap.get(positionLevel.getPositionLevelCode());
                if (Objects.isNull(entity)) {
                    /*新增*/
                    insertList.add(Lists.newArrayList(
                            positionLevel.getPositionLevelCode(),
                            positionLevel.getPositionLevelName(),
                            positionLevel.getRoleCode(),
                            positionLevel.getRoleName(),
                            positionLevel.getEnableStatus(),
                            positionLevel.getDelFlag(),
                            DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                            DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                            1, 1, POSITION_LEVEL_FORMMODEID
                    ));
                } else {
                    /*修改*/
                    if (!ObjectUtil.equal(entity.getPositionLevelCode(), positionLevel.getPositionLevelCode())
                            || !ObjectUtil.equal(entity.getPositionLevelName(), positionLevel.getPositionLevelName())
                            || !ObjectUtil.equal(entity.getRoleCode(), positionLevel.getRoleCode())
                            || !ObjectUtil.equal(entity.getRoleName(), positionLevel.getRoleName())
                            || !ObjectUtil.equal(entity.getEnableStatus(), positionLevel.getEnableStatus())
                            || !ObjectUtil.equal(entity.getDelFlag(), positionLevel.getDelFlag())) {
                        updateList.add(Lists.newArrayList(
                                positionLevel.getPositionLevelCode(),
                                positionLevel.getPositionLevelName(),
                                positionLevel.getRoleCode(),
                                positionLevel.getRoleName(),
                                positionLevel.getEnableStatus(),
                                positionLevel.getDelFlag(),
                                DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                                entity.getId()
                        ));
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                recordSet.executeBatchSql("INSERT INTO uf_STDJSZJ(positionLevelCode, positionLevelName, roleCode, roleName, enableStatus, delFlag, cjsj, gxsj, modedatacreater, modedatacreatertype, formmodeid) VALUES (?,?,?,?,?,?,?,?,?,?,?)",
                        insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                recordSet.executeBatchSql("UPDATE uf_STDJSZJ SET positionLevelCode=?,positionLevelName=?,roleCode=?,roleName=?,enableStatus=?,delFlag=?,gxsj=? WHERE id=?",
                        updateList);
            }

            LOG.info("StdPositionSyncCron.execute，岗位级别信息同步执行完成-可用，耗时:" + (System.currentTimeMillis() - startTime)
                    + ",新增行数:" + insertList.size() + ",修改行数:" + updateList.size()
                    + ",当前执行页:" + curPage + ",处理数量：" + positionLevels.size());
            return updateList.size() + insertList.size();
        });

        paramMap.clear();
        paramMap.put("delFlag", DISABLE_CODE);
        int disable = handlerByPage(POSITION_LEVEL_GET_URL, paramMap, StdPositionLevel.class, (positionLevels, curPage) -> {
            String codeList = positionLevels.stream().map(StdPositionLevel::getPositionLevelCode)
                    .map(code -> "'" + code + "'").collect(Collectors.joining(","));
            /*直接将当前库里面的数据更新为删除状态就好了，多查询到的被std删除的数据不需要新增到OA*/
            String selectSql = "SELECT * FROM uf_STDJSZJ WHERE delFlag='009' AND positionLevelCode IN (" + codeList + ");";
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(selectSql);

            List<List> updateList = new ArrayList<>();
            while (recordSet.next()) {
                updateList.add(Lists.newArrayList(
                        DISABLE_CODE,
                        DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                        recordSet.getInt("id")));
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                recordSet.executeBatchSql("UPDATE uf_STDJSZJ SET delFlag=?,gxsj=? WHERE id=?",
                        updateList);
            }

            LOG.info("StdPositionSyncCron.execute，岗位级别信息同步执行完成-删除，耗时:" + (System.currentTimeMillis() - startTime)
                    + ",修改行数:" + updateList.size() + ",当前执行页:"
                    + curPage + ",处理数量：" + positionLevels.size());
            return updateList.size();
        });

        int totalCount = enable + disable;
        LOG.info("StdPositionSyncCron.execute，岗位级别信息同步执行完成-总，耗时:" + (System.currentTimeMillis() - startTime)
                + ",影响行数:" + totalCount);
        return totalCount;
    }


    /**
     * 处理岗位人员信息
     *
     * @return 影响行数
     */
    private int positionUserHandler() {
        long startTime = System.currentTimeMillis();

        Map<String, Object> paramMap = new HashMap<>();
        int enable = handlerByPage(POSITION_USER_GET_URL, paramMap, StdPositionUser.class, (positionUsers, curPage) -> {
            String codeList = positionUsers.stream().map(StdPositionUser::getPositionCode)
                    .map(code -> "'" + code + "'").collect(Collectors.joining(","));
            String selectSql = "SELECT * FROM uf_STDGWXX WHERE positionCode IN (" + codeList + ");";
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(selectSql);

            List<StdPositionUserEntity> entityList = new ArrayList<>();
            while (recordSet.next()) {
                StdPositionUserEntity entity = new StdPositionUserEntity();
                entity.setId(recordSet.getInt("id"));
                entity.setPositionCode(recordSet.getString("positionCode"));
                entity.setPositionName(recordSet.getString("positionName"));
                entity.setOrgCode(recordSet.getString("orgCode"));
                entity.setOrgName(recordSet.getString("orgName"));
                entity.setPositionLevelCode(recordSet.getString("positionLevelCode"));
                entity.setPositionLevelName(recordSet.getString("positionLevelName"));
                entity.setFullName(recordSet.getString("fullName"));
                entity.setUserName(recordSet.getString("userName"));
                entity.setPrimaryFlag(recordSet.getString("primaryFlag"));
                entity.setEnableStatus(recordSet.getString("enableStatus"));
                entity.setDelFlag(recordSet.getString("delFlag"));
                entityList.add(entity);
            }

            List<List> insertList = new ArrayList<>();
            List<List> updateList = new ArrayList<>();
            Map<String, StdPositionUserEntity> userEntityMap = entityList.stream()
                    .collect(Collectors.toMap(StdPositionUserEntity::getPositionCode, Function.identity(), (a, b) -> a));

            for (StdPositionUser positionUser : positionUsers) {
                StdPositionUserEntity entity = userEntityMap.get(positionUser.getPositionCode());
                if (Objects.isNull(entity)) {
                    /*新增*/
                    insertList.add(Lists.newArrayList(
                            positionUser.getPositionCode(),
                            positionUser.getPositionName(),
                            positionUser.getOrgCode(),
                            positionUser.getOrgName(),
                            positionUser.getPositionLevelCode(),
                            positionUser.getPositionLevelName(),
                            positionUser.getFullName(),
                            positionUser.getUserName(),
                            positionUser.getPrimaryFlag(),
                            positionUser.getEnableStatus(),
                            positionUser.getDelFlag(),
                            DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                            DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                            1, 1, POSITION_USER_FORMMODEID,
                            positionUser.getOrgName() + CONCATE_STR + positionUser.getPositionName() + CONCATE_STR + positionUser.getFullName()
                    ));
                } else {
                    /*修改*/
                    if (!ObjectUtil.equal(positionUser.getPositionCode(), entity.getPositionCode())
                            || !ObjectUtil.equal(positionUser.getPositionName(), entity.getPositionName())
                            || !ObjectUtil.equal(positionUser.getOrgCode(), entity.getOrgCode())
                            || !ObjectUtil.equal(positionUser.getOrgName(), entity.getOrgName())
                            || !ObjectUtil.equal(positionUser.getPositionLevelCode(), entity.getPositionLevelCode())
                            || !ObjectUtil.equal(positionUser.getPositionLevelName(), entity.getPositionLevelName())
                            || !ObjectUtil.equal(positionUser.getFullName(), entity.getFullName())
                            || !ObjectUtil.equal(positionUser.getUserName(), entity.getUserName())
                            || !ObjectUtil.equal(positionUser.getPrimaryFlag(), entity.getPrimaryFlag())
                            || !ObjectUtil.equal(positionUser.getEnableStatus(), entity.getEnableStatus())
                            || !ObjectUtil.equal(positionUser.getDelFlag(), entity.getDelFlag())) {
                        updateList.add(Lists.newArrayList(
                                positionUser.getPositionCode(),
                                positionUser.getPositionName(),
                                positionUser.getOrgCode(),
                                positionUser.getOrgName(),
                                positionUser.getPositionLevelCode(),
                                positionUser.getPositionLevelName(),
                                positionUser.getFullName(),
                                positionUser.getUserName(),
                                positionUser.getPrimaryFlag(),
                                positionUser.getEnableStatus(),
                                positionUser.getDelFlag(),
                                DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                                positionUser.getOrgName() + CONCATE_STR + positionUser.getPositionName() + CONCATE_STR + positionUser.getFullName(),
                                entity.getId()
                        ));
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                recordSet.executeBatchSql("INSERT INTO uf_STDGWXX(positionCode,positionName,orgCode,orgName, positionLevelCode, positionLevelName, fullName, userName,primaryFlag, enableStatus, delFlag, cjsj, gxsj, modedatacreater, modedatacreatertype, formmodeid,zjsjmc) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
                        insertList);
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                recordSet.executeBatchSql("UPDATE uf_STDGWXX SET positionCode=?, positionName=?, orgCode=?, orgName=?, positionLevelCode=?, positionLevelName=?, fullName=?, userName=?,primaryFlag=?, enableStatus=?, delFlag=?, gxsj=?, zjsjmc=? WHERE id=?",
                        updateList);
            }

            LOG.info("StdPositionSyncCron.execute，岗位人员信息同步执行完成-可用，耗时:" + (System.currentTimeMillis() - startTime)
                    + ",新增行数:" + insertList.size() + ",修改行数:"
                    + updateList.size() + ",当前执行页:" + curPage + ",处理数量：" + positionUsers.size());
            return updateList.size() + insertList.size();
        });

        paramMap.clear();
        paramMap.put("delFlag", DISABLE_CODE);
        int disable = handlerByPage(POSITION_USER_GET_URL, paramMap, StdPositionUser.class, (positionUsers, curPage) -> {
            String codeList = positionUsers.stream().map(StdPositionUser::getPositionCode)
                    .map(code -> "'" + code + "'").collect(Collectors.joining(","));
            /*直接将当前库里面的数据更新为删除状态就好了，多查询到的被std删除的数据不需要新增到OA*/
            String selectSql = "SELECT * FROM uf_STDGWXX WHERE delFlag='009' AND positionCode IN (" + codeList + ");";
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(selectSql);

            List<List> updateList = new ArrayList<>();
            while (recordSet.next()) {
                updateList.add(Lists.newArrayList(
                        DISABLE_CODE,
                        DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN),
                        recordSet.getInt("id")));
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                recordSet.executeBatchSql("UPDATE uf_STDGWXX SET delFlag=?,gxsj=? WHERE id=?",
                        updateList);
            }

            LOG.info("StdPositionSyncCron.execute，岗位人员信息同步执行完成-删除，耗时:" + (System.currentTimeMillis() - startTime)
                    + ",修改行数:" + updateList.size() + ",当前执行页:"
                    + curPage + ",处理数量：" + positionUsers.size());
            return updateList.size();
        });

        int totalCount = enable + disable;
        LOG.info("StdPositionSyncCron.execute，岗位人员信息同步执行完成-总，耗时:" + (System.currentTimeMillis() - startTime)
                + ",影响行数:" + totalCount);
        return totalCount;
    }


    /**
     * 逐页处理
     *
     * @param url      请求地址
     * @param paramMap 参数
     * @param type     返回类型
     * @param function 处理方法
     * @return 影响行数
     */
    private <T> int handlerByPage(String url, Map<String, Object> paramMap,
                                  Class<T> type, BiFunction<List<T>, Integer, Integer> function) {
        int ret = 0;
        int curPage = 1;
        int totalPage = 1;
        /*paramMap.put("delFlag", DISABLE_CODE);*/
        paramMap.put("size", PAGE_SIZE);
        while (curPage <= totalPage) {
            paramMap.put("page", curPage);
            String firstPageResult = StdUtil.executeGet(url, paramMap);
            StdPageResult<T> pageResult = JSON.parseObject(firstPageResult, new TypeReference<StdPageResult<T>>(type) {
            });
            if (Objects.isNull(pageResult) || CollectionUtil.isEmpty(pageResult.getRecords())) {
                LOG.info("执行结束，返回结果为空，请求地址：" + url + ",入参:" + paramMap);
                break;
            }

            try {
                ret += function.apply(pageResult.getRecords(), curPage);
            } catch (Exception e) {
                LOG.warn("执行异常，请求地址：" + url + ",入参:" + paramMap, e);
            }

            if (curPage == 1) {
                totalPage = pageResult.getPages();
            }
            curPage++;
        }
        return ret;
    }


    /**
     * 分页结果对象
     */
    @Data
    public static class StdPageResult<T> {
        /**
         * 数据集合
         */
        private List<T> records;

        /**
         * 当前页
         */
        private Integer current;
        /**
         * 总页数
         */
        private Integer pages;
        /**
         * 每页大小
         */
        private Integer size;
        /**
         * 总条数
         */
        private Integer total;
//        /**
//         * 自动优化 COUNT SQL
//         */
//        private Boolean optimizeCountSql;
//
//        /**
//         * 是否命中count缓存
//         */
//        private Boolean hitCount;
//        /**
//         * 是否进行count查询
//         */
//        private Boolean searchCount;
    }

    /**
     * 岗位级别查询结果对象
     */
    @Data
    public static class StdPositionLevel {
//        /**
//         * ID主键
//         */
//        private String id;
//
//        /**
//         * 创建人账号
//         */
//        private String createAccount;
//        /**
//         * 创建人名称
//         */
//        private String createName;
//        /**
//         * 创建时间 -格式yyyy-MM-dd HH:mm:ss
//         */
//        private String createTime;
        /**
         * 删除标记 - 009-正常 003-删除
         */
        private String delFlag;
        /**
         * 启禁用 - 009-启用 003-禁用
         */
        private String enableStatus;
//        /**
//         * 更新|修改人账号
//         */
//        private String modifyAccount;
//        /**
//         * 更新|修改人名称
//         */
//        private String modifyName;
//        /**
//         * 更新|修改时间 格式yyyy-MM-dd HH:mm:ss
//         */
//        private String modifyTime;
//        /**
//         * 备注
//         */
//        private String remark;
//        /**
//         * 租户编号
//         */
//        private String tenantCode;

        /**
         * 岗位级别编码
         */
        private String positionLevelCode;

        /**
         * 岗位级别名称
         */
        private String positionLevelName;

        /**
         * 角色编码
         */
        private String roleCode;
        /**
         * 角色名称
         */
        private String roleName;
    }


    /**
     * 岗位级别实体
     */
    @Data
    public static class StdPositionLevelEntity {
        /**
         * ID主键
         */
        private Integer id;
        private Integer formmodeid;
//        private Integer requestId;
        //        private Integer modedatacreater;
//        private Integer modedatacreatertype;
//        private String modedatacreatedate;
//        private String modedatacreatetime;
//        private Integer modedatamodifier;
//        private String modedatamodifydatetime;
//        private String form_biz_id;
//        private String MODEUUID;

        private String cjsj;
        private String gxsj;
        /**
         * 删除标记 - 009-正常 003-删除
         */
        private String delFlag;
        /**
         * 启禁用 - 009-启用 003-禁用
         */
        private String enableStatus;
        /**
         * 岗位级别编码
         */
        private String positionLevelCode;

        /**
         * 岗位级别名称
         */
        private String positionLevelName;

        /**
         * 角色编码
         */
        private String roleCode;
        /**
         * 角色名称
         */
        private String roleName;
    }


    /**
     * 岗位用户查询结果对象
     */
    @Data
    public static class StdPositionUser {
//        /**
//         * ID主键
//         */
//        private String id;
//
//        /**
//         * 创建人账号
//         */
//        private String createAccount;
//        /**
//         * 创建人名称
//         */
//        private String createName;
//        /**
//         * 创建时间 -格式yyyy-MM-dd HH:mm:ss
//         */
//        private String createTime;
        /**
         * 删除标记 - 009-正常 003-删除
         */
        private String delFlag;
        /**
         * 启禁用 - 009-启用 003-禁用
         */
        private String enableStatus;
//        /**
//         * 更新|修改人账号
//         */
//        private String modifyAccount;
//        /**
//         * 更新|修改人名称
//         */
//        private String modifyName;
//        /**
//         * 更新|修改时间 格式yyyy-MM-dd HH:mm:ss
//         */
//        private String modifyTime;
//        /**
//         * 备注
//         */
//        private String remark;
//        /**
//         * 租户编号
//         */
//        private String tenantCode;


        /**
         * 岗位编码
         */
        private String positionCode;

        /**
         * 岗位名称
         */
        private String positionName;

        /**
         * 组织编码
         */
        private String orgCode;
        /**
         * 组织名称
         */
        private String orgName;

        /**
         * 岗位级别编码
         */
        private String positionLevelCode;

        /**
         * 岗位级别名称
         */
        private String positionLevelName;

        /**
         * 角色编码
         */
        private String fullName;
        /**
         * 角色名称
         */
        private String userName;
        /**
         * 是否主职位
         */
        private String primaryFlag;
    }


    /**
     * 岗位用户实体
     */
    @Data
    public static class StdPositionUserEntity {
        /**
         * ID主键
         */
        private Integer id;
        private Integer formmodeid;

//        private Integer requestId;
//        private Integer modedatacreater;
//        private Integer modedatacreatertype;
//        private String modedatacreatedate;
//        private String modedatacreatetime;
//        private Integer modedatamodifier;
//        private String modedatamodifydatetime;
//        private String form_biz_id;
//        private String MODEUUID;

        private String cjsj;
        private String gxsj;
        private String zjsjmc;

        /**
         * 删除标记 - 009-正常 003-删除
         */
        private String delFlag;
        /**
         * 启禁用 - 009-启用 003-禁用
         */
        private String enableStatus;

        /**
         * 岗位编码
         */
        private String positionCode;

        /**
         * 岗位名称
         */
        private String positionName;

        /**
         * 组织编码
         */
        private String orgCode;
        /**
         * 组织名称
         */
        private String orgName;

        /**
         * 岗位级别编码
         */
        private String positionLevelCode;

        /**
         * 岗位级别名称
         */
        private String positionLevelName;

        /**
         * 角色编码
         */
        private String fullName;
        /**
         * 角色名称
         */
        private String userName;
        /**
         * 是否主职位
         */
        private String primaryFlag;
    }

}

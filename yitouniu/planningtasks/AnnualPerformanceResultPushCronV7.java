package yitouniu.planningtasks;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponseBody;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.DingTalkRobotUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description: 年度绩效结果推送定时任务
 * @author: haiyang
 * @create: 2024-02-01 15:17
 **/
public class AnnualPerformanceResultPushCronV7 extends BaseCronJob {

    RecordSet rs = new RecordSet();
    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        String token = DingTalkRobotUtil.getToken(DingTalkRobotUtil.OA_ROBOT_APPKEY, DingTalkRobotUtil.OA_ROBOT_APPSECRET);
        List<Map<String, String>> resultList = queryResult();
        baseBean.writeLog("AnnualPerformanceResultPushCron.result: " + resultList);
        for(Map<String, String> o : resultList){
            String name = o.get("lastname");
            String xm = o.get("xm");
            String year = o.getOrDefault("nf", "2023");
            String performanceResult = o.get("ndjxjg");
            String mobile = o.get("sjh");
            baseBean.writeLog("开始执行："+ name +"年度结果推送");
            StringBuilder sb = new StringBuilder();
            sb.append("亲爱的").append(name).append("小伙伴，您").append(year).append("年度绩效结果为：").append(performanceResult).append("。")
                    .append("感谢您这一年来在工作岗位上倾情投入与付出，持争赢之志攀高峰，怀敢为之心勇担当。")
                    .append("期待2025年我们持续坚持“犟”精神、追求“犟目标”，勠力同心，使命必达！");
//                    .append("若对绩效评价结果有异议，须在2个工作日内填写《认养一头牛绩效考核申诉表》向邮箱：<EMAIL> 提出绩效申诉。");
            List<String> userIds = new ArrayList<>();
            String id = DingTalkRobotUtil.userGetByMobile(mobile, token);
            if(StringUtils.isNotBlank(id)){
                userIds.add(id);
            } else {
                baseBean.writeLog("年度绩效推送手机号为"+mobile+"的在钉钉找不到该用户，请确认");
                continue;
            }
            JSONObject msgParam = new JSONObject();
            String title = year + "年度绩效结果反馈";
            msgParam.put("title", title);
            msgParam.put("text", sb);

            BatchSendOTOResponseBody responseBody = DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OA_ROBOT_APPKEY, userIds, "sampleMarkdown", JSON.toJSONString(msgParam), token);

            if(StrUtil.isBlank(responseBody.getProcessQueryKey())){
                baseBean.writeLog("年度结果推送失败");
            } else {
                rs.executeUpdate("update uf_ndjxtz set sftscg = 1 where xm = ? ", xm);
            }

        }
    }



    /**
     * 通过id获取id和评价流程
     * @return
     */
    public List<Map<String, String>> queryResult(){
        List<Map<String, String>> list = new ArrayList<>();
        String sql = "select a.nf,a.xm,b.mobile,a.ndjxjg,b.lastname from uf_ndjxtz a, hrmresource b where a.xm = b.id and a.sftscg = 0";
        rs.executeQuery(sql);
        while(rs.next()){
            Map<String,String> map = new HashMap<>();
            map.put("nf", rs.getString("nf"));
            map.put("xm", rs.getString("xm"));
            map.put("lastname", rs.getString("lastname"));
            map.put("sjh", rs.getString("mobile"));
            map.put("ndjxjg", rs.getString("ndjxjg"));
            list.add(map);
        }
        return list;
    }
}

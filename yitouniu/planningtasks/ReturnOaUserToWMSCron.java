package yitouniu.planningtasks;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.HttpUtil;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 17:24 2021/9/16
 * @Modified By:
 */
public class ReturnOaUserToWMSCron extends BaseCronJob {

    //测试IP
//    private static String URL = "http://***********:9090/interfaces-web/api?";
    //正式IP
    private static String URL = "http://**************:9090/interfaces-web/api?";
    private static String APP_KEY = "23202400";
    private static String METHOD = "taobao.qimen.user.register";
    private static String TIMESTAMP;
    private static String FORMAT = "xml";
    private static String V = "2.0";
    private static String SIGN = "";
    private static String SIGN_METHOD = "md5";
    //测试
//    private static String CUSTOMERID = "246";
    //正式
    private static String CUSTOMERID = "646";


    @Override
    public void execute(){
        new BaseBean().writeLog("ReturnOaUserToWMSCron测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        RecordSet rs = new RecordSet();

        String sql = "select HR.lastname name, HR.mobile phone\n" +
                "from HrmResource HR\n" +
                "where HR.status <=3 and HR.subcompanyid1 in (17,19,20,32)";
        rs.executeQuery(sql);

        StringBuilder sb = new StringBuilder();
        sb.append("<request>\n");
        sb.append("    <userList>\n");

        while (rs.next()){
            sb.append("        <user>\n");
            sb.append("            <name>"+rs.getString("name")+"</name>\n");
            sb.append("            <phone>"+rs.getString("phone")+"</phone>\n");
            sb.append("        </user>\n");
        }
        sb.append("    </userList>\n");
        sb.append("</request>\n");

        try {
            String xmlDataResponse = HttpUtil.httpXmlPost(getUrl(),sb.toString());
            new BaseBean().writeLog("定时推送人员信息到WMS小邮局ReturnOaUserToWMSCron："+xmlDataResponse);
        } catch (IOException e) {
            e.printStackTrace();
        }


    }

    public static String getUrl(){
        TIMESTAMP = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String url =URL+"app_key=&method="+METHOD+"&format="+FORMAT+"&sign=&customerId="+CUSTOMERID;
        return url;
    }

}

package yitouniu.planningtasks;
import weaver.conn.RecordSet;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;
//工号自动同步SAP账号
public class SAPFieldSyncCron extends BaseCronJob {
    @Override
    public void execute() {
        RecordSet rs = new RecordSet();
        boolean b = rs.executeUpdate("delete from cus_fielddata where scopeid = '-1' ");
        if(b){
            RecordSet rs2 = new RecordSet();
            boolean b2 = rs2.execute(
                    "insert into cus_fielddata (scope,scopeid,id,field0) "+
                    "select 'HrmCustomFieldByInfoType','-1',id,loginid from Hrmresource"
            );
            if(b2){
                System.out.println("同步成功");
            }else{
                System.out.println("同步失败");
            }
        }else{
            System.out.println("同步失败");
        }
    }
}

package yitouniu.planningtasks;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.CreateJxWorkflowUtil;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/23 10:41
 * @Description 季度评价流程创建
 * @Version 1.0
 */
public class EvaluateForJDJXWorkflowCronV2 extends BaseCronJob {

    RecordSet rs = new RecordSet();
    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //季度选项框  0-3对应1-4

        String jxlxName = "季度";
        //0：季度  1：月度
        String jxlxValue = "0";

//        String sql = "select * from uf_jxglpzb where jxlx = "+jxlxValue;
//        rs.executeQuery(sql);
//
//        String zxrq = "";
//        if(rs.next()){
//            zxrq = rs.getString("zxrq");
//        }
//        int zxrqInt = Integer.parseInt(zxrq);
        LocalDate nowTime = LocalDate.now();
        int nowMonth = nowTime.getMonthValue();
        int nowDay = nowTime.getDayOfMonth();
//        if(StringUtils.isNotBlank(zxrq) && nowMonth%3 == 0 && zxrqInt == nowDay){
        //计算本季度
        int quarter = ((nowMonth+2)/3)-1;
        //删除核对表和个人基础表 离职人员
//        boolean deleteSql = CreateJxWorkflowUtil.deleteResignPeoples("uf_bmjdjx_dt1", "0");
//        if(!deleteSql){
//            baseBean.writeLog("EvaluateForJDJXWorkflowCron.execute 删除离职人员失败");
//        }
        //从个人绩效基础表中取出当月或当季度没有触发评价流程的信息
        List<Map<String, String>> grjxxxList = new ArrayList<>();
        Map<String, List<Map<String, String>>> itemListMap = new HashMap<>();
        String grjxxxSql = " select a.*,b.lastname as lastname " +
                " from uf_grjxxx a " +
                " left join HrmResource b on a.xm=b.id " +
                " where a.jd = ? and a.sfcfpjlc is null and b.status in (0,1,2,3)";
        rs.executeQuery(grjxxxSql, quarter);
        while (rs.next()){
            Map<String, String> map = new HashMap<>();
            //id
            map.put("mainId", rs.getString("id"));
            //季度
            map.put("jd", rs.getString("jd"));
            //隔级主管
            String gjzg = rs.getString("gjzg");
            map.put("gjzg", gjzg);
            //年份
            map.put("nf", rs.getString("nf"));
            //姓名
            map.put("xm", rs.getString("xm"));
            //lastname姓名
            map.put("lastname", rs.getString("lastname"));
            //工号
            map.put("gh", rs.getString("gh"));
            //部门
            map.put("bm", rs.getString("bm"));
            //岗位
            map.put("gw", rs.getString("gw"));
            //入职时间（原当前岗位任职时间）
            map.put("dqgwrzsj", rs.getString("dqgwrzsj"));
            //直接上级
            String zjsj = rs.getString("zjsj");
            map.put("zjsj", zjsj);
            //绩效类型
            map.put("jxlx", rs.getString("jxlx"));
            //所属一级部门
            map.put("szyjbm", rs.getString("szyjbm"));
            //制定流程
            map.put("zdlc", rs.getString("zdlc"));
            //一级部门负责人
            String yjbmfzr = rs.getString("yjbmfzr");
            map.put("yjbmfzr", yjbmfzr);
            //直接与隔级是否相同
            if(gjzg.equals(zjsj)){
                map.put("zjygjsfxt", "0");
            } else {
                map.put("zjygjsfxt", "1");
            }
            grjxxxList.add(map);
        }
        baseBean.writeLog("季度EvaluateForJDJXWorkflowCron:获取grjxxxList="+grjxxxList);
        if(grjxxxList.size() == 0){
            return;
        }
        for(Map<String,String> o : grjxxxList){
            String mainId = o.get("mainId");
            String itemSql = "select * from uf_grjxxx_dt1 where mainid = ? ";
            rs.executeQuery(itemSql, mainId);
            List<Map<String, String>> itemList = new ArrayList<>();
            while (rs.next()){
                Map<String, String> map = new HashMap<>();
                //目标id
                map.put("itemId", rs.getString("id"));
                //目标序号
                map.put("mbxh", rs.getString("mbxh"));
                //目标(Objective)
                map.put("mbobjective", rs.getString("mbobjective"));
                //关键结果（Key?Results）
                map.put("gjjgkeyresults", rs.getString("gjjgkeyresults"));
                //权重（百分比）
                map.put("qzbfb", rs.getString("qzbfb"));
                //使命必达目标（3.5分）
                map.put("smbdmb", rs.getString("smbdmb"));
                itemList.add(map);
            }
            itemListMap.put(mainId, itemList);
        }


        baseBean.writeLog("季度EvaluateForJDJXWorkflowCron:获取itemListMap="+itemListMap);

        CreateJxWorkflowUtil.createWorkflowForPersonalEvaluate(grjxxxList, itemListMap, jxlxValue);

//    }





    }

}

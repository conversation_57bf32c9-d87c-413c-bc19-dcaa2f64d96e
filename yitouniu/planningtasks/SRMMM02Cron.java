package yitouniu.planningtasks;

import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import yitouniu.esb.SRMMM02;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class SRMMM02<PERSON>ron extends BaseCronJob {   //定时读取SRM物料分类主数据

    @Override
    public void execute() {
        SRMMM02 srmmm02 = new SRMMM02();

        Map param = new HashMap();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("access_token","8vn1fjb9zefbtj79rh");
        jsonObject.put("functionid","SRMMM02");

        JSONObject jsonObject2 = new JSONObject();
        String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
//        jsonObject2.put("ddate",formatDate.replaceAll( "-",""));
        jsonObject2.put("ddate","");

        RecordSet rs1 = new RecordSet();
        //生产
        rs1.executeQuery("select MAX(dmodifytime) as dmodifytime from uf_srm_wl_category");
        //测试
//        rs1.executeQuery("select MAX(dmodifytime) as dmodifytime from srm_wl_category");
        if(rs1.next()){
            jsonObject2.put("dlastdate",rs1.getString("dmodifytime"));
            new BaseBean().writeLog("有以往日期数据"+rs1.getString("dmodifytime"));
        }else{
            jsonObject2.put("dlastdate","");
        }

        param.put("CTRL",jsonObject);
        param.put("DATA",jsonObject2);

        Map resultmap = srmmm02.executeParam(param);
        new BaseBean().writeLog("定时读取物料分类SRMMM02Cron："+resultmap.get("MSGTY"));
    }

}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;
import yitouniu.util.CreateJxWorkflowUtil;
import yitouniu.util.TimeUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/14 15:06
 * @Description TODO
 * @Version 1.0
 */
public class CreateForYDJXWorkflowCronV8 extends BaseCronJob {


    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();
    public static final String WORKFLOWID_ARCHIVE = "1588";
//    public static final String WORKFLOWID_ARCHIVE = "967";

    public static final String YDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","ydjxformmodeid"));


    @Override
    public void execute() {


        try {
            String jxlxName = "月度";
            String jxlxValue = "1";
            //获取需要创建月度绩效流程的人员
            List<Map<String,String>> ryList = new ArrayList<>();
            List<String> managerIdList = new ArrayList<>();
            HashSet<String> departmentIdSet = new HashSet<>();


            String rySql = "select a.sftsry,a.yd,a.nf, b.id, b.lastname,b.workcode,b.departmentid,b.jobtitle,b.managerid,b.createdate " +
                    " from uf_ryssxx a " +
                    " left join HrmResource b on a.cfry = b.id " +
                    " where cflx = " +jxlxValue;
            rs.executeQuery(rySql);
            while (rs.next()){

                String yd = rs.getString("yd");
                if (StringUtils.isEmpty(yd)) {
                    baseBean.writeLog("CreateForYDJXWorkflowCron:" +rs.getString("lastname")+"没有月度信息，跳过");
                    continue;
                }

                Map<String,String> map = new HashMap<>();
                //已经制定过的不再制定
                if(hasCreated(Integer.parseInt(yd), rs.getString("id"))){
                    continue;
                }

                String tsry = rs.getString("sftsry");
                map.put("sftsry", tsry);
                map.put("id",rs.getString("id"));
                map.put("lastname",rs.getString("lastname"));
                map.put("workcode",rs.getString("workcode"));
                String departmentId = rs.getString("departmentid");
                departmentIdSet.add(departmentId);
                map.put("departmentid",departmentId);
                map.put("jobtitle",rs.getString("jobtitle"));
                String managerId = rs.getString("managerid");
                managerIdList.add(managerId);
                map.put("managerid",managerId);
                map.put("createdate", rs.getString("createdate"));
                map.put("year", rs.getString("nf"));
                map.put("month", rs.getString("yd"));
                ryList.add(map);
            }
            baseBean.writeLog("CreateForYDJXWorkflowCron:获取ryList="+ryList);

            //获取特殊人员的固定模版
//            List<Map<String, String>> tsryList = CreateJxWorkflowUtil.getTemplateByName("新零售固定模板");

            RecordSet gjzgRs = new RecordSet();
            String managerIdString = managerIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            String gjzgSql = "select id,managerid from HrmResource where id in ("+managerIdString+")";
            gjzgRs.executeQuery(gjzgSql);
            Map<String,String> gjzgMap = new HashMap<>();
            while (gjzgRs.next()){
                gjzgMap.put(gjzgRs.getString("id"),gjzgRs.getString("managerid"));
            }
            baseBean.writeLog("CreateForYDJXWorkflowCron:获取隔级主管id="+gjzgMap);
            //获取部门的一级部门id
            Map<String,String> yjbmMap = CreateJxWorkflowUtil.getYjbmMap(departmentIdSet);
            baseBean.writeLog("CreateForYDJXWorkflowCron:获取部门的一级部门id="+yjbmMap);
            //获取所有杭州一级部门和负责人
            Map<String,String> yjbmfzrMap = CreateJxWorkflowUtil.getYjbmfzrMap();
            baseBean.writeLog("CreateForYDJXWorkflowCron:所有杭州一级部门和负责人="+yjbmfzrMap);
            //创建流程
            createWorkflow(ryList, gjzgMap, LocalDate.now(), yjbmMap, yjbmfzrMap, jxlxValue, jxlxName);
//        }
        } catch (Exception e) {
            baseBean.writeLog("CreateForYDJXWorkflowCron:异常=",e);
        }

    }


    public boolean hasCreated(int month, String xm) {
        boolean flag = false;
        RecordSet rs1 = new RecordSet();
        String sql = "select xm from uf_jxzdhdb where yd = ? and xm = ? and sfycj = 0";
        rs1.executeQuery(sql, month, xm);
        while (rs1.next()){
            String xm1 = rs1.getString("xm");
            if (StringUtils.isNotEmpty(xm1)) {
                flag = true;
            }
        }
        return flag;
    }


    /**
     * 创建绩效制定流程
     * @param ryList
     * @param gjzgMap
     * @param nowTime
     * @param yjbmMap
     * @param yjbmfzrMap
     * @param jxlxValue
     * @param jxlxName
     */
    public static void createWorkflow(List<Map<String,String>> ryList,
                                      Map<String,String> gjzgMap,
                                      LocalDate nowTime,
                                      Map<String,String> yjbmMap,
                                      Map<String,String> yjbmfzrMap,
                                      String jxlxValue,
                                      String jxlxName){
        RecordSet rs = new RecordSet();
        for(Map<String,String> o :ryList){
            try {
                JSONObject result = new JSONObject();

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0]  = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqrq"); //申请日期
                wrti[0].setFieldValue(nowTime.toString());
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1]  = new WorkflowRequestTableField();
                wrti[1].setFieldName("gjzg"); //隔级主管
                wrti[1].setFieldValue(gjzgMap.get(o.get("managerid"))) ;
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("szyjbm"); //所属一级部门
                wrti[2].setFieldValue(yjbmMap.get(o.get("departmentid")));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("szyjbmfzr"); //所属一级部门负责人
                wrti[3].setFieldValue(yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("jxlx"); //绩效类型
                wrti[4].setFieldValue(jxlxValue);
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                String requestName = "";
                String year = o.get("year");
                String nowMonthly = o.get("month");
                wrti[5]  = new WorkflowRequestTableField();
                wrti[5].setFieldName("yd"); //月度
                wrti[5].setFieldValue(nowMonthly);
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                requestName = "认养一头牛"+year+"年"+(Integer.parseInt(nowMonthly) + 1)+"月员工绩效目标制定流程-" + o.get("lastname");


                wrti[6]  = new WorkflowRequestTableField();
                wrti[6].setFieldName("nf"); //年份
                wrti[6].setFieldValue(String.valueOf(year));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7]  = new WorkflowRequestTableField();
                wrti[7].setFieldName("xm"); //姓名
                wrti[7].setFieldValue(o.get("id"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8]  = new WorkflowRequestTableField();
                wrti[8].setFieldName("gh"); //工号
                wrti[8].setFieldValue(o.get("workcode"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9]  = new WorkflowRequestTableField();
                wrti[9].setFieldName("bm"); //部门
                wrti[9].setFieldValue(o.get("departmentid"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                wrti[10]  = new WorkflowRequestTableField();
                wrti[10].setFieldName("gw"); //岗位
                wrti[10].setFieldValue(o.get("jobtitle"));
                wrti[10].setView(true);
                wrti[10].setEdit(true);

                wrti[11]  = new WorkflowRequestTableField();
                wrti[11].setFieldName("zjsj"); //直接上级
                wrti[11].setFieldValue(o.get("managerid"));
                wrti[11].setView(true);
                wrti[11].setEdit(true);

                wrti[12]  = new WorkflowRequestTableField();
                wrti[12].setFieldName("dqgwrzsj"); //入职时间
                wrti[12].setFieldValue(o.get("createdate"));
                wrti[12].setView(true);
                wrti[12].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);


                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID_ARCHIVE);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(o.get("id"));//创建人id
                wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName(requestName);//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("id"))));
                if(!requestid.isEmpty()){
                    if(Integer.parseInt(requestid) >0){
                        String sql = "";
                        sql = "insert into uf_jxzdhdb (xm, yd, sfycj) values(?,?,?)";
                        boolean flag = rs.executeUpdate(sql, o.get("id"), nowMonthly, 0);

                        result.put("status", "0");
                        result.put("requestid", requestid);
                        if(!flag){
                            new BaseBean().writeLog("createWorkflow:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果成功但是插入核对表失败，请手动插入，数据为绩效为类型=" + jxlxValue+", 月度or季度=" + nowMonthly + ", 姓名=" + o.get("id"));
                        }
                        //制定成功后直接加入信息表
                        String insertTableName = "uf_bmydjxxx";
                        String insertMainSql = "";
                        insertMainSql = "insert into " + insertTableName + " (yjbmmc, bmfzr, jxsznf, jxszyf, formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values(?,?,?,?,?,?,?,?,?)  ";
                        Date date = new Date();
                        int mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), year, nowMonthly);
                        if(mainId < 0){
                            // 插入主表数据
                            boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), year, nowMonthly, YDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                            if(insertFlag){
                                mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), year, nowMonthly);
                                //修改建模模块权限
                                ModeRightInfo modeRightInfo = new ModeRightInfo();
                                modeRightInfo.setNewRight(true);
                                modeRightInfo.editModeDataShare(1, Integer.parseInt(YDJXFORMMODEID),mainId);
                            } else {
                                new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");

                            }
                        }
                        //不管是否存在，先将原来的删除了
                        rs.executeUpdate("delete from " + insertTableName + "_dt1 where mainid = ? and xm = ?", mainId, o.get("id"));
                        //插入明细数据
                        String insertDetailSql = "insert into " + insertTableName + "_dt1  (mainid, xm, jxzdlc, bm, sfzdwc, gh) values(?,?,?,?,?,?)";
                        boolean insertDetailFlag = rs.executeUpdate(insertDetailSql, mainId, o.get("id"), requestid, o.get("departmentid"), 1, o.get("workcode"));
                        new BaseBean().writeLog("CreateJxWorkflowUtil insertDetailFlag = " + insertDetailFlag);

                    }else{
                        result.put("status", "1");
                        result.put("requestid", requestid);
                    }
                }
                new BaseBean().writeLog("CreateJxWorkflowUtil:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果"+result.toJSONString());
            } catch (Exception e) {
                new BaseBean().writeLog("createWorkflow : 异常错误" + e);
                new BaseBean().writeLog(e);
            }
        }

    }

}

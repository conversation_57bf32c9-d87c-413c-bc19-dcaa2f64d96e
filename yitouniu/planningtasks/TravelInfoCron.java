package yitouniu.planningtasks;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.esb.TravelBillSettementBtripTrain;
import yitouniu.esb.TravelBillSettementFlight;
import yitouniu.esb.TravelBillSettementHotel;
import yitouniu.util.HecUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/7 9:07
 * @Description TODO
 * @Version 1.0
 */
public class TravelInfoCron extends BaseCronJob {

    public static final String UF_TRAVEL_INFO = "uf_travel_info";
    public static final String UF_SLCCDZ = "uf_slccdz";

    BaseBean baseBean = new BaseBean();

    //机票账单
    private final String dataBaseNameItemFlight = "uf_slccdz_dt1";
    //酒店账单
    private final String dataBaseNameItemHotel = "uf_slccdz_dt2";
    //火车票账单
    private final String dataBaseNameItemBtripTrain = "uf_slccdz_dt3";

    public static final String HEC_API_URL = Util.null2String(new BaseBean().getPropValue("hec","hecApiUrl"));
    public static final String HEC_TOKEN_URL = Util.null2String(new BaseBean().getPropValue("hec","hecTokenUrl"));
    public static final String CLIENT_SECRET = Util.null2String(new BaseBean().getPropValue("hec","clientSecret"));
    public static final String OA_URL = Util.null2String(new BaseBean().getPropValue("travelInfoAction","oaUrl"));
    public static final String FORMMODEID = Util.null2String(new BaseBean().getPropValue("travelInfoAction","dczformmodeid"));

    @Override
    public void execute() {

        //查询有账单的且未推送到hec的审批单
        RecordSet rs = new RecordSet();
        RecordSet rs2 = new RecordSet();
        RecordSet rs3 = new RecordSet();
        RecordSet rs4 = new RecordSet();
        try {
            String sql = "select * from "+UF_SLCCDZ+" where sftsfk is null";
            rs.executeQuery(sql);
            baseBean.writeLog("TravelBillSettementCron sql="+sql);
            while (rs.next()) {
                //审批单号
                String spdh = rs.getString("spdh");
                if(selectBillSettement(spdh,dataBaseNameItemFlight) || selectBillSettement(spdh,dataBaseNameItemHotel) ||selectBillSettement(spdh,dataBaseNameItemBtripTrain)) {
                    String spdhSql = "select * from " + UF_TRAVEL_INFO + " where process_ehr_id = ? ";
                    rs2.executeQuery(spdhSql,spdh);
                    if (rs2.next()) {
                        String processId = rs2.getString("process_id");
                        String processNumber = rs2.getString("process_code");
                        String processName = rs2.getString("process_name");
                        String processType = rs2.getString("process_type");
                        String employeeCode = rs2.getString("employee_code");
                        String description = rs2.getString("description");
                        String closeFlag = rs2.getString("close_flag");

                        String httpResult = null;
                        String employeeId = null;
                        String employeeName = null;
                        String subcompanycode = null;
                        rs.executeQuery("select a.id, a.lastname, b.gsdm from HrmResource a left join uf_gongsigongchangd b on a.subcompanyid1 = b.gsdmms  where workcode = ? ", employeeCode);
                        if(rs3.next()){
                            employeeId = rs.getString("id");
                            employeeName = rs.getString("lastname");
                            subcompanycode = rs.getString("gsdm");
                        }

                        JSONObject jsonReq = new JSONObject();
                        jsonReq.put("sourceSystemCode", "OA");
                        jsonReq.put("interfaceCode", "APPROVE_REQ");

                        JSONObject requestData = new JSONObject();
                        JSONArray approveRequests = new JSONArray();
                        JSONObject approveRequest = new JSONObject();

                        approveRequest.put("processId", processId);
                        approveRequest.put("processNumber", processNumber);
                        approveRequest.put("processName", processName);
                        approveRequest.put("processType", processType);
                        approveRequest.put("employeeId", employeeId);
                        approveRequest.put("employeeCode", employeeCode);
                        approveRequest.put("closeFlag", closeFlag);
                        approveRequest.put("travelLink", OA_URL + "/ryytn/LoginUfSlccdz.jsp#&billid="+selectIdBySpdh(spdh));
                        approveRequest.put("employeeName", employeeName);
                        approveRequest.put("description", description);
                        approveRequest.put("processLink", OA_URL + "/ryytn/LoginRedirectEHR.jsp#&processId="+processId);
                        approveRequest.put("amount", "");
                        approveRequest.put("accEntityCode", subcompanycode);
                        approveRequests.add(approveRequest);
                        requestData.put("approveRequests", approveRequests);
                        jsonReq.put("requestData", requestData);

                        //获取token
                        String token  = HecUtils.getToken();
                        if("".equals(token)){
                            token = HecUtils.getToken();
                        }
                        baseBean.writeLog("travelInfoCron:请求json=jsonReq=" + jsonReq);
                        httpResult = HttpRequest
                                .post(HEC_API_URL)
                                .header("Authorization","Bearer "+ token)
                                .body(jsonReq.toJSONString())
                                .execute().body();
                        baseBean.writeLog("travelInfoCron:请求HEC返回的结果=httpResult=" + httpResult);
                        JSONObject result = JSONObject.parseObject(httpResult);
                        if("S".equals(result.getString("status"))){
                            String updatesftsfkSql = "update " + UF_SLCCDZ + " set sftsfk = 1 where spdh = ?";
                            rs4.executeUpdate(updatesftsfkSql, spdh);
                        }
                    }
                }
            }
        } catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 判断商旅对账单明细是否有数据
     * @param spdh
     * @param dataName
     * @return
     */
    public boolean selectBillSettement(String spdh, String dataName) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+dataName+" where spdh =? ", spdh);
        return rs1.next();
    }

    /**
     * 查找该记录的id
     * @param spdh
     * @return
     */
    public int  selectIdBySpdh(String spdh) {
        RecordSet rs = new RecordSet();
        int id = -1;
        String sql = "select * from " + UF_SLCCDZ + " where spdh = ?";
        rs.executeQuery(sql, spdh);
        if(rs.next()){
            id = rs.getInt("id");
        }
        return id;
    }


}

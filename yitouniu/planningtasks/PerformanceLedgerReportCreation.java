package yitouniu.planningtasks;

import cn.hutool.core.map.MapUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/7/9 10:58
 * @Description
 */
public class PerformanceLedgerReportCreation extends BaseCronJob {

    RecordSet rs = new RecordSet();
    BaseBean baseBean = new BaseBean();


    @Override
    public void execute() {
        Map<String, Map<String, String>> queryResult = queryResult();
        baseBean.writeLog("PerformanceLedgerReportCreation.queryResult: " + queryResult);
        if (MapUtil.isEmpty(queryResult)) {
            return;
        }
        Map<String, Map<String, String>> existResult = queryExistResult();
        baseBean.writeLog("PerformanceLedgerReportCreation.existResult: " + existResult);
        for (Map.Entry<String, Map<String, String>> entry : queryResult.entrySet()) {
            String key = entry.getKey();
            Map<String, String> entryValue = entry.getValue();
            if (!existResult.containsKey(key)) {
                //新增
                insertSql(entryValue.get("yjbmmc"), entryValue.get("jxsznf"), entryValue.get("jxszyf"), entryValue.get("xm"),
                        entryValue.get("bm"), entryValue.get("gh"), entryValue.get("okrdf"), entryValue.get("jzgdf"),
                        entryValue.get("zzfhjg"), entryValue.get("khdy"), entryValue.get("cxdd"), entryValue.get("hzgy"),
                        entryValue.get("xfjq"), entryValue.get("departmentid"));
            } else {
                //更新
                Map<String, String> existMap = existResult.get(key);
                updateSql(existMap.get("id"), entryValue.get("yjbmmc"), entryValue.get("bm"), entryValue.get("gh"),
                        entryValue.get("okrdf"), entryValue.get("jzgdf"), entryValue.get("zzfhjg"), entryValue.get("khdy"),
                        entryValue.get("cxdd"), entryValue.get("hzgy"), entryValue.get("xfjq"), entryValue.get("departmentid"));
            }
        }

    }

    public Map<String, Map<String, String>> queryResult() {
        Map<String, Map<String, String>> resultMap = new HashMap<>();
        String sql = "SELECT\n" +
                "\ta.yjbmmc,a.jxsznf,a.jxszyf,b.xm,c.departmentid,b.bm,b.gh,b.okrdf,b.jzgdf,b.zzfhjg,b.khdy,b.cxdd,b.hzgy,b.xfjq\n" +
                "FROM\n" +
                "\tuf_bmjdjx_dt1 b\n" +
                "\tLEFT JOIN uf_bmjdjx a ON a.id = b.mainid" +
                "\tLEFT JOIN HrmResource c ON c.id = b.xm";
        rs.executeQuery(sql);
        while (rs.next()) {
            String jxsznf = rs.getString("jxsznf");
            String jxszyf = rs.getString("jxszyf");
            String xm = rs.getString("xm");
            String key = jxsznf + ":" + jxszyf + ":" + xm;
            Map<String, String> map = new HashMap<>();
            map.put("yjbmmc", StringUtils.isEmpty(rs.getString("yjbmmc")) ? null : rs.getString("yjbmmc"));
            map.put("jxsznf", StringUtils.isEmpty(jxsznf) ? null : jxsznf);
            map.put("jxszyf", StringUtils.isEmpty(jxszyf) ? null : jxszyf);
            map.put("xm", StringUtils.isEmpty(xm) ? null : xm);
            map.put("bm", StringUtils.isEmpty(rs.getString("bm")) ? null : rs.getString("bm"));
            map.put("departmentid", StringUtils.isEmpty(rs.getString("departmentid")) ? null : rs.getString("departmentid"));
            map.put("gh", StringUtils.isEmpty(rs.getString("gh")) ? null : rs.getString("gh"));
            map.put("okrdf", StringUtils.isEmpty(rs.getString("okrdf")) ? null : rs.getString("okrdf"));
            map.put("jzgdf", StringUtils.isEmpty(rs.getString("jzgdf")) ? null : rs.getString("jzgdf"));
            map.put("zzfhjg", StringUtils.isEmpty(rs.getString("zzfhjg")) ? null : rs.getString("zzfhjg"));
            map.put("khdy", StringUtils.isEmpty(rs.getString("khdy")) ? null : rs.getString("khdy"));
            map.put("cxdd", StringUtils.isEmpty(rs.getString("cxdd")) ? null : rs.getString("cxdd"));
            map.put("hzgy", StringUtils.isEmpty(rs.getString("hzgy")) ? null : rs.getString("hzgy"));
            map.put("xfjq", StringUtils.isEmpty(rs.getString("xfjq")) ? null : rs.getString("xfjq"));
            resultMap.put(key, map);
        }
        return resultMap;
    }

    public Map<String, Map<String, String>> queryExistResult() {
        Map<String, Map<String, String>> resultMap = new HashMap<>();
        String sql = "SELECT id,nd,jd,xm FROM uf_jdjxxxtzb";
        rs.executeQuery(sql);
        while (rs.next()) {
            String nd = rs.getString("nd");
            String jd = rs.getString("jd");
            String xm = rs.getString("xm");
            String key = nd + ":" + jd + ":" + xm;
            Map<String, String> map = new HashMap<>();
            map.put("id", rs.getString("id"));
            resultMap.put(key, map);
        }
        return resultMap;
    }

    public void updateSql(String id, String yjbmmc, String bm, String gh, String okrdf, String jzgdf, String zzfhjg,
                          String khdy, String cxdd, String hzgy, String xfjq, String dqszbm) {
        String sql = "update uf_jdjxxxtzb set yjbm = ?,szbm = ?, gh = ?, yjdf = ?,jzgzhdf = ?, " +
                "jxjg = ?, khdy = ?, cxdd = ?, hzgy = ?, xfjq = ?, dqszbm = ? where id = ?";
        rs.executeUpdate(sql, yjbmmc, bm, gh, okrdf, jzgdf, zzfhjg, khdy, cxdd, hzgy, xfjq, dqszbm, id);
    }

    public void insertSql(String yjbmmc, String jxsznf, String jxszyf, String xm, String bm, String gh, String okrdf,
                          String jzgdf, String zzfhjg, String khdy, String cxdd, String hzgy, String xfjq, String dqszbm) {
        String insertSql = "insert into uf_jdjxxxtzb (yjbm, nd, jd, xm, szbm,gh, yjdf,jzgzhdf,jxjg,khdy,cxdd,hzgy,xfjq,dqszbm," +
                "formmodeid,modedatacreater,modedatacreatertype,crfs) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,192,1,1,0)";
        rs.executeUpdate(insertSql, yjbmmc, jxsznf, jxszyf, xm, bm, gh, okrdf, jzgdf, zzfhjg, khdy, cxdd, hzgy, xfjq, dqszbm);
    }


}

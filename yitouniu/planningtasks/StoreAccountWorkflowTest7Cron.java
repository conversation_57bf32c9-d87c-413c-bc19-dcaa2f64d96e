package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import com.huawei.shade.com.alibaba.fastjson.JSON;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StoreAccountWorkflowTest7Cron extends BaseCronJob {

//    public static final String WORKFLOWID_STORE = "1144";
    public static final String WORKFLOWID_STORE = "2059";

    public static final String ADMIN_ID = "6565";

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        boolean flag = selectDepartingPerson();

        //存在离职人员
        if(flag){
            List<Map<String,String>> mapList = new ArrayList<>();
            String sql = "select a.* from uf_rydpzzhhzb a left join Hrmresource b on a.sqr = b.id where b.status > 4 and (a.sfycflc <> 0 or a.sfycflc is null)";
            rs.executeQuery(sql);
            while (rs.next()){
                Map<String,String> map = new HashMap<>();
                map.put("id", rs.getString("id"));
                map.put("sqr", rs.getString("sqr"));
                map.put("sqbm", rs.getString("sqbm"));
                map.put("sqgs", rs.getString("sqgs"));
                map.put("sqrq", rs.getString("sqrq"));
                map.put("sqyy", rs.getString("sqyy"));
                map.put("pt", rs.getString("pt"));
                map.put("dp", rs.getString("dp"));
                map.put("gw", rs.getString("gw"));
                map.put("gwzxx", rs.getString("gwzxx"));
                map.put("dpyyfzr", rs.getString("dpyyfzr"));
                map.put("zhlx", rs.getString("zhlx"));
                map.put("gjpt", rs.getString("gjpt"));
                mapList.add(map);
            }
            baseBean.writeLog("execute:mapList = "+mapList);
            //创建流程
            createWorkflow(mapList);

        }



    }

    /**
     * 判断是否存在离职人员
     * @return
     */
    public boolean selectDepartingPerson(){
        String sql = "select * from uf_rydpzzhhzb a left join Hrmresource b on a.sqr = b.id where b.status > 4 and (a.sfycflc <> 0 or a.sfycflc is null) ";
        rs.executeQuery(sql);
        return rs.next();
    }

    public void createWorkflow(List<Map<String,String>> mapList){
        LocalDate now = LocalDate.now();

        for(Map<String,String> o : mapList){
            JSONObject result = new JSONObject();

            WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
            //赋值方式
            wrti[0]  = new WorkflowRequestTableField();
            wrti[0].setFieldName("sqr"); //申请人
            wrti[0].setFieldValue(o.get("sqr"));
            wrti[0].setView(true);
            wrti[0].setEdit(true);

            wrti[1]  = new WorkflowRequestTableField();
            wrti[1].setFieldName("sqbm"); //申请部门
            wrti[1].setFieldValue(o.get("sqbm")) ;
            wrti[1].setView(true);
            wrti[1].setEdit(true);

            wrti[2]  = new WorkflowRequestTableField();
            wrti[2].setFieldName("sqgs"); //申请公司
            wrti[2].setFieldValue(o.get("sqgs"));
            wrti[2].setView(true);
            wrti[2].setEdit(true);

            wrti[3]  = new WorkflowRequestTableField();
            wrti[3].setFieldName("sqyy"); //申请原因
            wrti[3].setFieldValue(o.get("sqyy"));
            wrti[3].setView(true);
            wrti[3].setEdit(true);

            wrti[4]  = new WorkflowRequestTableField();
            wrti[4].setFieldName("pt"); //平台
            wrti[4].setFieldValue(o.get("pt"));
            wrti[4].setView(true);
            wrti[4].setEdit(true);

            wrti[5]  = new WorkflowRequestTableField();
            wrti[5].setFieldName("dp"); //店铺
            wrti[5].setFieldValue(o.get("dp"));
            wrti[5].setView(true);
            wrti[5].setEdit(true);

            wrti[6]  = new WorkflowRequestTableField();
            wrti[6].setFieldName("gwx"); //岗位（新）
            wrti[6].setFieldValue(o.get("gw"));
            wrti[6].setView(true);
            wrti[6].setEdit(true);

            wrti[7]  = new WorkflowRequestTableField();
            wrti[7].setFieldName("gwzxx"); //岗位子选项
            wrti[7].setFieldValue(o.get("gwzxx"));
            wrti[7].setView(true);
            wrti[7].setEdit(true);

            wrti[8]  = new WorkflowRequestTableField();
            wrti[8].setFieldName("sqlx"); //申请类型
            wrti[8].setFieldValue("2");
            wrti[8].setView(true);
            wrti[8].setEdit(true);

            wrti[9]  = new WorkflowRequestTableField();
            wrti[9].setFieldName("sqrq"); //申请日期
            wrti[9].setFieldValue(now.toString());
            wrti[9].setView(true);
            wrti[9].setEdit(true);

            wrti[10]  = new WorkflowRequestTableField();
            wrti[10].setFieldName("dpgly"); //申请日期
            wrti[10].setFieldValue(o.get("dpyyfzr"));
            wrti[10].setView(true);
            wrti[10].setEdit(true);

            wrti[11]  = new WorkflowRequestTableField();
            wrti[11].setFieldName("zhlx"); //账号类型
            wrti[11].setFieldValue(o.get("zhlx"));
            wrti[11].setView(true);
            wrti[11].setEdit(true);

            wrti[12]  = new WorkflowRequestTableField();
            wrti[12].setFieldName("gjpt"); //工具平台
            wrti[12].setFieldValue(o.get("gjpt"));
            wrti[12].setView(true);
            wrti[12].setEdit(true);

            WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
            wrtri[0] = new WorkflowRequestTableRecord();
            wrtri[0].setWorkflowRequestTableFields(wrti);
            WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
            wmi.setRequestRecords(wrtri);

            //流程基本信息设置
            WorkflowBaseInfo wbi = new WorkflowBaseInfo();
            wbi.setWorkflowId(WORKFLOWID_STORE);//流程请求id
            WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
            wri.setCreatorId(ADMIN_ID);//创建人id
            wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
            wri.setRequestLevel("0");//0 正常，1重要，2紧急

            wri.setRequestName("店铺子账号权限管理流程-店铺账号流程管理员-" + now);//流程标题
            //添加主字段数据
            wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
//            wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
            wri.setWorkflowBaseInfo(wbi);
            WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
            baseBean.writeLog("execute:wri = " + JSON.toJSONString(wri));
            String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(ADMIN_ID)));
            if(!"".equals(requestid)){
                if(Integer.parseInt(requestid) >0){
                    result.put("status", "0");
                    result.put("requestid", requestid);
                    rs.executeUpdate("update uf_rydpzzhhzb set sfycflc = 0 where id = " + o.get("id"));

                }else{
                    result.put("status", "1");
                    result.put("requestid", requestid);

                }
            }
            new BaseBean().writeLog("店铺子账号流程创建结果"+result.toJSONString());




        }

    }


}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.TeambitionBaseConfig;
import yitouniu.util.TeambitionHttpToken;
import yitouniu.util.TeambitionUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * @program: oa
 * @description: tb任务同步本地-报表用
 * @author: haiyang
 * @create: 2023-08-25 10:47
 **/
public class QueryChangedTbTaskCron extends BaseCronJob {

    public static final String UF_TB_TASK = "uf_tb_task";

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public void execute() {

        String taskSqlTest = "select \n" +
                "a.taskId, \n" +
                "a.lcmcxsz, \n" +
                "case b.xtlx\n" +
                "when '0' then 'OA'\n" +
                "when '1' then '费控' end as xltxmc,\n" +
                "b.bm, d.departmentname, \n" +
                "b.sqr, c.lastname as sqrmc\n" +
                "from formtable_main_716_dt1 a \n" +
                "LEFT JOIN formtable_main_716 b on a.mainid = b.id\n" +
                "LEFT JOIN HrmResource c on b.sqr = c.id\n" +
                "LEFT JOIN HrmDepartment d on b.bm = d.id \n" +
                "where a.taskId is not null OR a.taskId <> ''\n" +
                "order by a.id desc";

        String taskSql = "select \n" +
                "a.taskId, \n" +
                "a.lcmcxsz, \n" +
                "case b.xtlx\n" +
                "when '0' then 'OA'\n" +
                "when '1' then '费控' end as xltxmc,\n" +
                "b.bm, d.departmentname, \n" +
                "b.sqr, c.lastname as sqrmc\n" +
                "from formtable_main_94_dt1 a \n" +
                "LEFT JOIN formtable_main_94 b on a.mainid = b.id\n" +
                "LEFT JOIN HrmResource c on b.sqr = c.id\n" +
                "LEFT JOIN HrmDepartment d on b.bm = d.id \n" +
                "where a.taskId is not null OR a.taskId <> ''\n" +
                "order by a.id desc";
//        rs.executeQuery(taskSqlTest);
        rs.executeQuery(taskSql);
        Map<String, Map<String, String>> taskMaps = new HashMap<>();
        List<String> taskIds = new ArrayList<>();
        while (rs.next()) {
            Map<String, String> o = new HashMap<>();
            String taskId = rs.getString("taskId");
            if (StringUtils.isBlank(taskId)){
                continue;
            }
            o.put("taskId", taskId);
            o.put("lcmcxsz", rs.getString("lcmcxsz"));
            o.put("xltxmc", rs.getString("xltxmc"));
            o.put("bm", rs.getString("bm"));
            o.put("departmentname", rs.getString("departmentname"));
            taskIds.add(taskId);
            taskMaps.put(taskId, o);
        }
        new BaseBean().writeLog("execute：需要查询的taskIds = " + taskIds);

        String queryTaskIds = String.join(",", taskIds);
        //获取token
        String token = null;
        try {
            token = TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID, TeambitionBaseConfig.APP_SECRET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //非创建更新使用的headers
        Map<String, String> headers = TeambitionHttpToken.returnHeaders(token);
        //获取OA/费控项目下，对应的自定义id对应name的Map
        List<String> projectIdList = returnProjectIdList();
        String projectIds = String.join(",", projectIdList);

        //获取任务详情
        JSONArray resultArray = TeambitionUtils.returnTaskInfos(queryTaskIds, headers);
        try {

            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();

            new BaseBean().writeLog("execute：本次拉取的数量为：" + resultArray.size());
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject o = resultArray.getJSONObject(i);
                List insertData = new ArrayList<>();
                List updateData = new ArrayList<>();
                String taskId = o.getString("taskId");
                List dataList = new ArrayList<>();
                new BaseBean().writeLog("execute：当前为第"+(i+1)+"个，taskId = " + taskId);
                dataList.add(o.getString("content"));
                dataList.add(o.getString("content"));
                dataList.add(o.getString("note"));
                String projectId = o.getString("projectId");
                if(!projectIds.contains(projectId)){
                    continue;
                }
                dataList.add(projectId);
                String tfsId = o.getString("tfsId");
                dataList.add(tfsId);
                String gzlzt = TeambitionUtils.returnTaskflowStatusNameIdV3(tfsId, projectId, headers);
                dataList.add(gzlzt);
                dataList.add(o.getString("tasklistId"));
                String stageId = o.getString("stageId");
                dataList.add(stageId);
                dataList.add(o.getString("tagIds"));
                dataList.add(o.getString("creatorId"));
                dataList.add(o.getString("executorId"));
                dataList.add(o.getString("involveMembers"));
                dataList.add(o.getString("storyPoint"));
                dataList.add(o.getString("isDone"));
                dataList.add(o.getString("isArchived"));
                dataList.add(o.getString("visible"));
                dataList.add(o.getString("uniqueId"));
                dataList.add(o.getString("startDate"));
                dataList.add(o.getString("dueDate"));
                dataList.add(o.getString("accomplishTime"));
                dataList.add(o.getString("created"));
                dataList.add(o.getString("updated"));
                dataList.add(o.getString("sfcId"));
                // 需求类型
                String xqlx = "流程变更类";
                // 需求方
                String xqf = "";
                // 需求部门
                String xqbm = taskMaps.get(taskId).get("departmentname");
                // 需求对接人
                String xqdjr = "";
                // 需求申请时间
                String xqsqsj = o.getString("created");
                // 期望解决时间
                String qwjjsj = "";
                // 预估人天
                String ygrt = "";
                // 任务优先级
                String rwyxj = "P2";


                dataList.add(xqlx);
                dataList.add(xqf);
                dataList.add(xqbm);
                dataList.add(xqdjr);
                dataList.add(xqsqsj);
                dataList.add(qwjjsj);
                dataList.add(ygrt);
                dataList.add(rwyxj);
                dataList.add(taskMaps.get(taskId).get("xltxmc"));
                String stageName = TeambitionUtils.returnStageNameV3(projectId, stageId, headers);
                dataList.add(stageName);

                int id = selectTbTask(taskId);
                if (id > 0) {
                    CollectionUtils.addAll(updateData, new Object[dataList.size()]);
                    Collections.copy(updateData, dataList);
                    updateData.add(id);
                    updateAllData.add(updateData);
                } else {
                    CollectionUtils.addAll(insertData, new Object[dataList.size()]);
                    Collections.copy(insertData, dataList);
                    insertData.add(0, taskId);
                    insertAllData.add(insertData);
                }

            }
            if (insertAllData.size() > 0) {
                baseBean.writeLog("QueryChangedTbTaskCron: insertAllData新增：" + insertAllData.size());
                flag += insertTbTask(insertAllData) + ",";
            }

            if (updateAllData.size() > 0) {
                baseBean.writeLog("QueryChangedTbTaskCron: updateAllData更新：" + updateAllData.size());
                flag += updateTbTask(updateAllData) + ",";
            }
            if (flag.contains("false")) {
                baseBean.writeLog("处理数据失败");
            } else {
                baseBean.writeLog(" 处理数据成功");
            }
        } catch (Exception e) {
            baseBean.writeLog("异常错误",e);
        }

    }


    public int selectTbTask(String taskId) {
        RecordSet rs1 = new RecordSet();
        int id = -1;
        rs1.executeQuery("select id from " + UF_TB_TASK + " where rwid =?", taskId);
        if (rs1.next()) {
            id = rs1.getInt("id");
        }
        return id;
    }

    public boolean updateTbTask(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("update " + UF_TB_TASK + " set oasrwmc =?,tbsrwmc =?,rwbz =?,xmid =?,rwztid =?,gzlzt =?,rwfzid =?,rwlid =?,bqidjh =?," +
                "cjrid =?,zxrid =?,cyzidjh =?,storypoint =?,sfrwywc =?,sfrwfrhsz =?,rwysx =?,rwszid =?,rwkssjutc =?,rwjzsjutc =?," +
                "rwwcsjutc =?,cjsjutc =?,gxsjutc =?,rwlxid =?,xqlx =?,xqf =?,xqbm =?,xqdjr =?,xqsqsj =?,qwjjsj =?,ygrt=?," +
                "rwyxj =?,szxm =?,rwlmc=? where id = ?", list);
    }

    public boolean insertTbTask(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("insert into " + UF_TB_TASK + " (rwid, oasrwmc, tbsrwmc, rwbz, xmid, rwztid, gzlzt, rwfzid, rwlid, bqidjh, " +
                "cjrid, zxrid, cyzidjh , storypoint, sfrwywc, sfrwfrhsz, rwysx, rwszid, rwkssjutc, rwjzsjutc, " +
                "rwwcsjutc, cjsjutc, gxsjutc, rwlxid, xqlx, xqf, xqbm, xqdjr, xqsqsj, qwjjsj, ygrt, " +
                "rwyxj, szxm, rwlmc) " +
                "values (?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?, " +
                "?,?,?,?,?,?,?,?,?,?,?, " +
                "?,?,?) ", list);
    }

    public List<String> returnProjectIdList(){
        List<String> list = new ArrayList<>();
        list.add("63743a25fa424151c03cab1d");//基础运维项目
        list.add("63760183b722ed7092ff6516");//数智化财务项目

        return list;
    }


}

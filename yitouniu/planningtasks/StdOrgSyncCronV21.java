package yitouniu.planningtasks;

import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import weaver.conn.RecordSet;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.StdUtil;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-05-22 09:57
 **/
public class StdOrgSyncCronV21 extends BaseCronJob {
    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    private static final int REQUESTS_PER_SECOND = 20;
    private static final RateLimiter rateLimiter = RateLimiter.create(REQUESTS_PER_SECOND);

    private static final int PAGE_SIZE = 100;

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public void execute() {
        baseBean.writeLog("进行执行拉取std销售组织层级任务");
//        String gatewayUrl = "https://std-test.ryytngroup.com";
//        String qryApi = "/crm-mdm/v1/external/std/findOrgByConditions";

        String gatewayUrl = StdUtil.GATEWAY_URL;
        String qryApi = StdUtil.STD_ORG_LEVEL_API;
        baseBean.writeLog("stdOrgLevelApi:" + qryApi);
        int page = 1;
        int totalPages = 1;
        while (page <= totalPages) {
            rateLimiter.acquire();
            long l = System.currentTimeMillis();
            String apiSign = StdUtil.getApiSign(String.valueOf(l));
            Map<String, Object> params = assembleQryParam(page);
            Map<String, String> headers = Maps.newHashMap();
            headers.put("ak", StdUtil.STD_AK);
            headers.put("timestamp", String.valueOf(l));
            headers.put("token", apiSign);
            String responseBody = HttpRequest.get(gatewayUrl + qryApi).form(params).addHeaders(headers).execute().body();
            if (StringUtils.isEmpty(responseBody)) {
                baseBean.writeLog("stdItfResponseBodyEmpty");
                break;
            }
            JSONObject responseJson = (JSONObject) JSON.parse(responseBody);
            if (responseJson.getString("code").equals("200")) {
                baseBean.writeLog("responseJson:"+ responseJson.toJSONString());
                StdResponse stdResponse = JSON.parseObject(responseJson.getJSONObject("result").toJSONString(), StdResponse.class);
                totalPages = stdResponse.getPages();
                page++;
                baseBean.writeLog("StdOrgSyncCron.totalPages:" + totalPages + ",page:" + page);
                if (CollectionUtils.isNotEmpty(stdResponse.getRecords()) && stdResponse.getRecords().size() > 0) {
                    List<BusinessRecord> data = stdResponse.getRecords();
                    // 处理更新的数据并返回需要插入的数据
                    List<BusinessRecord> insertData = handleUpdateData(data);
                    if (CollectionUtils.isNotEmpty(insertData)) {
                        List<List> batchInsertData = Lists.newArrayList();
                        for (BusinessRecord dataBean : insertData) {
                            batchInsertData.add(
                                    Lists.newArrayList(
                                            dataBean.getOrgCode(),
                                            dataBean.getOrgName(),
                                            dataBean.getOrgType(),
                                            dataBean.getOrgDesc(),
                                            dataBean.getLevelNum(),
                                            dataBean.getParentCode(),
                                            dataBean.getParentCode(),
                                            dataBean.getRuleCode(),
                                            dataBean.getStartTime() == null? "" : sdf.format(dataBean.getStartTime()),
                                            dataBean.getEndTime() == null? "" : sdf.format(dataBean.getEndTime()),
                                            186,
                                            1,
                                            1,
                                            dataBean.getDataSource(),
                                            dataBean.getEnableStatus().equals("009")?"已启用":"已禁用",
                                            dataBean.getDelFlag().equals("009")?"正常":"已删除",
                                            StringUtils.isNotEmpty(dataBean.getAvailableStatus())? dataBean.getAvailableStatus().equals("Y")? 1 : 2 : ""
                                    )
                            );
                        }
                        boolean insertSuccess = rs.executeBatchSql("insert into uf_yxzzcj(orgCode,orgName,orgType,orgDesc,levelNum,parentCode,parentName,ruleCode,startTime,endTime,formmodeid,modedatacreater,modedatacreatertype,dataSource,enableStatus,delFlag,sfsx) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", batchInsertData);
                        baseBean.writeLog("page:" + page + "插入执行结果:" + insertSuccess);
                    }
                }
            } else {
                baseBean.writeLog("请求std接口报错:" + responseBody);
                break;
            }
        }
        baseBean.writeLog("执行拉取std销售组织层级任务结束，totalPages:" + totalPages);
    }

    private List<BusinessRecord> handleUpdateData(List<BusinessRecord> data) {
        RecordSet rsq = new RecordSet();
        List<List> updateAllData = Lists.newArrayList();
        Map<String, BusinessRecord> businessRecordMap = data.stream().collect(Collectors.toMap(BusinessRecord::getOrgCode, Function.identity(), (v1, v2) -> v1));
        String orgCodeListStr = data.stream().map(e -> "'" + e.getOrgCode() +"'").collect(Collectors.joining(","));

        baseBean.writeLog("orgCodeListStr:"+orgCodeListStr);
        rsq.execute("select * from uf_yxzzcj where orgCode in ("+orgCodeListStr+")");
        List<String> orgCodeList = Lists.newArrayList();
        while (rsq.next()) {
            String orgCode = rsq.getString("orgCode");
            baseBean.writeLog("rsqExecuteOrgCode:"+ orgCode);
            BusinessRecord businessRecord = businessRecordMap.get(orgCode);
            if (Objects.nonNull(businessRecord)) {
                orgCodeList.add(businessRecord.getOrgCode());
                updateAllData.add(Lists.newArrayList(
                                businessRecord.getOrgName(),
                                businessRecord.getOrgType(),
                                businessRecord.getOrgDesc(),
                                businessRecord.getLevelNum(),
                                businessRecord.getParentCode(),
                                businessRecord.getParentCode(),
                                businessRecord.getStartTime() == null? "" : sdf.format(businessRecord.getStartTime()),
                                businessRecord.getEndTime() == null? "" : sdf.format(businessRecord.getEndTime()),
                                businessRecord.getDataSource(),
                                businessRecord.getEnableStatus().equals("009")?"已启用":"已禁用",
                                businessRecord.getDelFlag().equals("009")?"正常":"已删除",
                                StringUtils.isNotEmpty(businessRecord.getAvailableStatus())? businessRecord.getAvailableStatus().equals("Y")? 1 : 2 : "",
                                orgCode
                        )
                );
            }
        }
        if (CollectionUtils.isNotEmpty(updateAllData)) {
            rsq.executeBatchSql("update uf_yxzzcj set orgName=?,orgType=?,orgDesc=?,levelNum=?,parentCode=?,parentName=?,startTime=?,endTime=?,dataSource=?,enableStatus=?,delFlag=?,sfsx=? where orgCode=?", updateAllData);
        }
        return data.stream().filter(e -> !orgCodeList.contains(e.getOrgCode())).collect(Collectors.toList());
    }

    private static Map<String, Object> assembleQryParam(int page) {
        Map<String, Object> params = Maps.newHashMap();
//        params.put("enableStatus", );
//        params.put("delFlag", );
//        params.put("orgCode", );
//        params.put("orgName", );
//        params.put("codeOrName", );
//        params.put("orgTypeList", );
//        params.put("levelNum", );
        params.put("page", page);
        params.put("size", PAGE_SIZE);

        return params;
    }


    public static void main(String[] args) throws NoSuchAlgorithmException {
        long l = System.currentTimeMillis();
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest((l + "GR^&s&EQ*9Chz$Ag" + "uCCx6B#3eFyFgRnJEkd*64gV").getBytes(StandardCharsets.UTF_8));
        String sign = DatatypeConverter.printHexBinary(digest).toLowerCase();
        String gatewayUrl = "https://std-test.ryytngroup.com";
        String qryApi = "/crm-mdm/v1/external/std/findOrgByConditions";

        Map<String, Object> params = Maps.newHashMap();
        params.put("page", 1);
        params.put("size", 100);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("ak", "GR^&s&EQ*9Chz$Ag");
        headers.put("timestamp", String.valueOf(l));
        headers.put("token", sign);
        HttpRequest form = HttpRequest.get(gatewayUrl + qryApi).form(params).addHeaders(headers);
        String responseBody = form.execute().body();
        JSONObject responseJson = (JSONObject) JSON.parse(responseBody);
        if (responseJson.getString("code").equals("200")) {
            StdResponse stdResponse = JSON.parseObject(responseJson.getJSONObject("result").toJSONString(), StdResponse.class);
            if (CollectionUtils.isNotEmpty(stdResponse.getRecords()) && stdResponse.getRecords().size() > 0) {
                List<BusinessRecord> data = stdResponse.getRecords();
                // 处理更新的数据并返回需要插入的数据
                List<List> batchInsertData = Lists.newArrayList();
                for (BusinessRecord dataBean : data) {

                    batchInsertData.add(
                            Lists.newArrayList(
                                    dataBean.getOrgCode(),
                                    dataBean.getOrgName(),
                                    dataBean.getOrgType(),
                                    dataBean.getOrgDesc(),
                                    dataBean.getLevelNum(),
                                    dataBean.getParentCode(),
                                    dataBean.getParentName(),
                                    dataBean.getRuleCode(),
                                    dataBean.getStartTime(),
                                    dataBean.getEndTime()
                            )
                    );
                }
                System.out.println(batchInsertData);
            }

        }

    }

    public static void main1(String[] args) throws NoSuchAlgorithmException {
        long l = System.currentTimeMillis();
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] digest = md5.digest((l + "GR^&s&EQ*9Chz$Ag" + "uCCx6B#3eFyFgRnJEkd*64gV").getBytes(StandardCharsets.UTF_8));
        String sign = DatatypeConverter.printHexBinary(digest).toLowerCase();
        String gatewayUrl = "https://bootstrap-test.ryytngroup.com/std";
        String qryApi = "/crm-mdm/v1/external/std/findProductLevelByConditions";

        Map<String, Object> params = Maps.newHashMap();
        params.put("page", 1);
        params.put("size", 100);
        Map<String, String> headers = Maps.newHashMap();
        headers.put("ak", "GR^&s&EQ*9Chz$Ag");
        headers.put("timestamp", String.valueOf(l));
        headers.put("token", sign);
        HttpRequest form = HttpRequest.get(gatewayUrl + qryApi).form(params).addHeaders(headers);
        String responseBody = form.execute().body();
        System.out.println(responseBody);
    }

    public static class StdResponse {
        private int total;
        private int size;
        private int current;
        private boolean optimizeCountSql;
        private boolean hitCount;
        private boolean searchCount;
        private int pages;
        private List<BusinessRecord> records;


        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public boolean isOptimizeCountSql() {
            return optimizeCountSql;
        }

        public void setOptimizeCountSql(boolean optimizeCountSql) {
            this.optimizeCountSql = optimizeCountSql;
        }

        public boolean isHitCount() {
            return hitCount;
        }

        public void setHitCount(boolean hitCount) {
            this.hitCount = hitCount;
        }

        public boolean isSearchCount() {
            return searchCount;
        }

        public void setSearchCount(boolean searchCount) {
            this.searchCount = searchCount;
        }

        public int getPages() {
            return pages;
        }

        public void setPages(int pages) {
            this.pages = pages;
        }

        public List<BusinessRecord> getRecords() {
            return records;
        }

        public void setRecords(List<BusinessRecord> records) {
            this.records = records;
        }
    }


        public static class BusinessRecord extends BaseStdRecord {
            /**
             * 组织编码
             */
            private String orgCode;
            /**
             * 组织名称
             */
            private String orgName;
            /**
             * 组织类型 数据字典[mdm_org_type]
             */
            private String orgType;
            /**
             * 组织描述
             */
            private String orgDesc;
            /**
             * 组织级别 数据字典[mdm_org_level]
             */
            private Integer levelNum;
            /**
             * 上级组织编码
             */
            private String parentCode;
            /**
             * 上级组织名称
             */
            private String parentName;
            /**
             * 规则编码
             */
            private String ruleCode;
            /**
             * 有效开始时间 yyyy-MM-dd HH:mm:ss
             */
            private Date startTime;
            /**
             * 有效结束时间 yyyy-MM-dd HH:mm:ss
             */
            private Date endTime;
            private String sapOrgCode;
            private String sapProfitOrgCode;
            private String sapCostOrgCode;

            private String dataSource;


            // 是否生效 Y生效 N不生效
            private String availableStatus;

            private BusinessRecord parent;
            private List<BusinessRecord> children;


            public String getAvailableStatus() {
                return availableStatus;
            }

            public void setAvailableStatus(String availableStatus) {
                this.availableStatus = availableStatus;
            }

            public String getOrgCode() {
                return orgCode;
            }

            public void setOrgCode(String orgCode) {
                this.orgCode = orgCode;
            }

            public String getOrgName() {
                return orgName;
            }

            public void setOrgName(String orgName) {
                this.orgName = orgName;
            }

            public String getOrgType() {
                return orgType;
            }

            public void setOrgType(String orgType) {
                this.orgType = orgType;
            }

            public String getOrgDesc() {
                return orgDesc;
            }

            public void setOrgDesc(String orgDesc) {
                this.orgDesc = orgDesc;
            }

            public Integer getLevelNum() {
                return levelNum;
            }

            public void setLevelNum(Integer levelNum) {
                this.levelNum = levelNum;
            }

            public String getParentCode() {
                return parentCode;
            }

            public void setParentCode(String parentCode) {
                this.parentCode = parentCode;
            }

            public String getParentName() {
                return parentName;
            }

            public void setParentName(String parentName) {
                this.parentName = parentName;
            }

            public String getRuleCode() {
                return ruleCode;
            }

            public void setRuleCode(String ruleCode) {
                this.ruleCode = ruleCode;
            }

            public Date getStartTime() {
                return startTime;
            }

            public void setStartTime(Date startTime) {
                this.startTime = startTime;
            }

            public Date getEndTime() {
                return endTime;
            }

            public void setEndTime(Date endTime) {
                this.endTime = endTime;
            }

            public String getSapOrgCode() {
                return sapOrgCode;
            }

            public void setSapOrgCode(String sapOrgCode) {
                this.sapOrgCode = sapOrgCode;
            }

            public String getSapProfitOrgCode() {
                return sapProfitOrgCode;
            }

            public void setSapProfitOrgCode(String sapProfitOrgCode) {
                this.sapProfitOrgCode = sapProfitOrgCode;
            }

            public String getSapCostOrgCode() {
                return sapCostOrgCode;
            }

            public void setSapCostOrgCode(String sapCostOrgCode) {
                this.sapCostOrgCode = sapCostOrgCode;
            }

            public BusinessRecord getParent() {
                return parent;
            }

            public void setParent(BusinessRecord parent) {
                this.parent = parent;
            }

            public List<BusinessRecord> getChildren() {
                return children;
            }

            public void setChildren(List<BusinessRecord> children) {
                this.children = children;
            }

            public String getDataSource() {
                return dataSource;
            }

            public void setDataSource(String dataSource) {
                this.dataSource = dataSource;
            }
        }

        public static class BaseStdRecord {
            private String id;
            private String createAccount;
            private String createName;
            private Date createTime;
            private String modifyAccount;
            private Date modifyTime;
            private String modifyName;
            private String remark;
            private String tenantCode;
            /**
             * 启禁用
             * 009 启用
             * 003 禁用
             */
            private String enableStatus;
            private String delFlag;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getCreateAccount() {
                return createAccount;
            }

            public void setCreateAccount(String createAccount) {
                this.createAccount = createAccount;
            }

            public String getCreateName() {
                return createName;
            }

            public void setCreateName(String createName) {
                this.createName = createName;
            }

            public Date getCreateTime() {
                return createTime;
            }

            public void setCreateTime(Date createTime) {
                this.createTime = createTime;
            }

            public String getModifyAccount() {
                return modifyAccount;
            }

            public void setModifyAccount(String modifyAccount) {
                this.modifyAccount = modifyAccount;
            }

            public Date getModifyTime() {
                return modifyTime;
            }

            public void setModifyTime(Date modifyTime) {
                this.modifyTime = modifyTime;
            }

            public String getModifyName() {
                return modifyName;
            }

            public void setModifyName(String modifyName) {
                this.modifyName = modifyName;
            }

            public String getRemark() {
                return remark;
            }

            public void setRemark(String remark) {
                this.remark = remark;
            }

            public String getTenantCode() {
                return tenantCode;
            }

            public void setTenantCode(String tenantCode) {
                this.tenantCode = tenantCode;
            }

            public String getEnableStatus() {
                return enableStatus;
            }

            public void setEnableStatus(String enableStatus) {
                this.enableStatus = enableStatus;
            }

            public String getDelFlag() {
                return delFlag;
            }

            public void setDelFlag(String delFlag) {
                this.delFlag = delFlag;
            }
        }

}

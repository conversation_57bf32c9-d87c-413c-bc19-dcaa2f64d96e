package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/23 10:41
 * @Description 季度评价流程创建
 * @Version 1.0
 */
public class EvaluateForLastQuarterJDJXWorkflowCronV3 extends BaseCronJob {

    RecordSet rs = new RecordSet();
    BaseBean baseBean = new BaseBean();

    //正式
    public static final String WORKFLOWID_EVALUATE = "1230";

    public static int getCurrentQuarter() {
        Calendar calendar = Calendar.getInstance();
        int month = calendar.get(Calendar.MONTH) + 1; // 月份从0开始，因此要加1
        int quarter = 0;
        if (month >= 1 && month <= 3) {
            quarter = 0;
        } else if (month >= 4 && month <= 6) {
            quarter = 1;
        } else if (month >= 7 && month <= 9) {
            quarter = 2;
        } else if (month >= 10 && month <= 12) {
            quarter = 3;
        }
        return quarter;
    }

    @Override
    public void execute() {
        //季度选项框  0-3对应1-4

        String jxlxName = "季度";
        //0：季度  1：月度
        String jxlxValue = "0";

//        String sql = "select * from uf_jxglpzb where jxlx = "+jxlxValue;
//        rs.executeQuery(sql);
//
//        String zxrq = "";
//        if(rs.next()){
//            zxrq = rs.getString("zxrq");
//        }
//        int zxrqInt = Integer.parseInt(zxrq);
        LocalDate nowTime = LocalDate.now();
        int nowMonth = nowTime.getMonthValue();
        int nowDay = nowTime.getDayOfMonth();
//        if(StringUtils.isNotBlank(zxrq) && nowMonth%3 == 0 && zxrqInt == nowDay){
        //计算本季度
        int quarter = getCurrentQuarter();
        int lastQuarter = quarter - 1;
        int realLastQuarter = lastQuarter < 0? 3 : lastQuarter;
        baseBean.writeLog("lastQuarterIs " + realLastQuarter);
//        //删除核对表和个人基础表 离职人员
//        boolean deleteSql = CreateJxWorkflowUtil.deleteResignPeoples("uf_bmjdjx_dt1", "0");
//        if(!deleteSql){
//            baseBean.writeLog("EvaluateForJDJXWorkflowCron.execute 删除离职人员失败");
//        }
        //从个人绩效基础表中取出当月或当季度没有触发评价流程的信息
        List<Map<String, String>> grjxxxList = new ArrayList<>();
        Map<String, List<Map<String, String>>> itemListMap = new HashMap<>();
        String grjxxxSql = " select a.*,b.lastname as lastname " +
                " from uf_grjxxx a " +
                " left join HrmResource b on a.xm=b.id " +
                " where a.jd = ? and a.sfcfpjlc is null and b.status in (0,1,2,3)";
        rs.executeQuery(grjxxxSql, realLastQuarter);
        while (rs.next()){
            Map<String, String> map = new HashMap<>();
            //id
            map.put("mainId", rs.getString("id"));
            //季度
            map.put("jd", rs.getString("jd"));
            //隔级主管
            String gjzg = rs.getString("gjzg");
            map.put("gjzg", gjzg);
            //年份
            map.put("nf", rs.getString("nf"));
            //姓名
            map.put("xm", rs.getString("xm"));
            //lastname姓名
            map.put("lastname", rs.getString("lastname"));
            //工号
            map.put("gh", rs.getString("gh"));
            //部门
            map.put("bm", rs.getString("bm"));
            //岗位
            map.put("gw", rs.getString("gw"));
            //入职时间（原当前岗位任职时间）
            map.put("dqgwrzsj", rs.getString("dqgwrzsj"));
            //直接上级
            String zjsj = rs.getString("zjsj");
            map.put("zjsj", zjsj);
            //绩效类型
            map.put("jxlx", rs.getString("jxlx"));
            //所属一级部门
            map.put("szyjbm", rs.getString("szyjbm"));
            //制定流程
            map.put("zdlc", rs.getString("zdlc"));
            //一级部门负责人
            String yjbmfzr = rs.getString("yjbmfzr");
            map.put("yjbmfzr", yjbmfzr);
            //直接与隔级是否相同
            if(gjzg.equals(zjsj)){
                map.put("zjygjsfxt", "0");
            } else {
                map.put("zjygjsfxt", "1");
            }
            grjxxxList.add(map);
        }
        baseBean.writeLog("季度EvaluateForJDJXWorkflowCron:获取grjxxxList="+grjxxxList);
        if(grjxxxList.size() == 0){
            return;
        }
        for(Map<String,String> o : grjxxxList){
            String mainId = o.get("mainId");
            String itemSql = "select * from uf_grjxxx_dt1 where mainid = ? ";
            rs.executeQuery(itemSql, mainId);
            List<Map<String, String>> itemList = new ArrayList<>();
            while (rs.next()){
                Map<String, String> map = new HashMap<>();
                //目标id
                map.put("itemId", rs.getString("id"));
                //目标序号
                map.put("mbxh", rs.getString("mbxh"));
                //目标(Objective)
                map.put("mbobjective", rs.getString("mbobjective"));
                //关键结果（Key?Results）
                map.put("gjjgkeyresults", rs.getString("gjjgkeyresults"));
                //权重（百分比）
                map.put("qzbfb", rs.getString("qzbfb"));
                //使命必达目标（3.5分）
                map.put("smbdmb", rs.getString("smbdmb"));
                itemList.add(map);
            }
            itemListMap.put(mainId, itemList);
        }


        baseBean.writeLog("季度EvaluateForJDJXWorkflowCron:获取itemListMap="+itemListMap);

        createWorkflowForPersonalEvaluate(grjxxxList, itemListMap, jxlxValue);

//    }





    }



    /**
     * 创建个人绩效评价流程
     * @param grjxxxList
     * @param itemListMap
     * @param jxlxValue
     */
    public static void createWorkflowForPersonalEvaluate(List<Map<String,String>> grjxxxList, Map<String, List<Map<String, String>>> itemListMap, String jxlxValue) {
        RecordSet rs = new RecordSet();
        LocalDate nowDate = LocalDate.now();
        for (Map<String, String> o : grjxxxList) {
            JSONObject result = new JSONObject();

            String id = o.get("mainId");
            WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
            //赋值方式
            wrti[0] = new WorkflowRequestTableField();
            wrti[0].setFieldName("jxlx"); //绩效类型
            wrti[0].setFieldValue(jxlxValue);
            wrti[0].setView(true);
            wrti[0].setEdit(true);

            wrti[1] = new WorkflowRequestTableField();
            wrti[1].setFieldName("yjbmfzr"); //一级部门负责人
            wrti[1].setFieldValue(o.get("yjbmfzr"));
            wrti[1].setView(true);
            wrti[1].setEdit(true);

            wrti[2] = new WorkflowRequestTableField();
            wrti[2].setFieldName("gjzg"); //隔级主管
            wrti[2].setFieldValue(o.get("gjzg"));
            wrti[2].setView(true);
            wrti[2].setEdit(true);

            wrti[3] = new WorkflowRequestTableField();
            wrti[3].setFieldName("nf"); //年份
            wrti[3].setFieldValue(o.get("nf"));
            wrti[3].setView(true);
            wrti[3].setEdit(true);

            if ("0".equals(jxlxValue)) {
                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("jd"); //季度
                wrti[4].setFieldValue(o.get("jd"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);
            } else {
                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("yd"); //月度
                wrti[4].setFieldValue(o.get("yd"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);
            }

            wrti[5] = new WorkflowRequestTableField();
            wrti[5].setFieldName("xm"); //姓名
            wrti[5].setFieldValue(o.get("xm"));
            wrti[5].setView(true);
            wrti[5].setEdit(true);

            wrti[6] = new WorkflowRequestTableField();
            wrti[6].setFieldName("bm"); //部门
            wrti[6].setFieldValue(o.get("bm"));
            wrti[6].setView(true);
            wrti[6].setEdit(true);

            wrti[7] = new WorkflowRequestTableField();
            wrti[7].setFieldName("gw"); //岗位
            wrti[7].setFieldValue(o.get("gw"));
            wrti[7].setView(true);
            wrti[7].setEdit(true);

            wrti[8] = new WorkflowRequestTableField();
            wrti[8].setFieldName("dqgwrzsj"); //入职时间
            wrti[8].setFieldValue(o.get("dqgwrzsj"));
            wrti[8].setView(true);
            wrti[8].setEdit(true);

            wrti[9] = new WorkflowRequestTableField();
            wrti[9].setFieldName("zjsj"); //直接上级
            wrti[9].setFieldValue(o.get("zjsj"));
            wrti[9].setView(true);
            wrti[9].setEdit(true);

            wrti[10] = new WorkflowRequestTableField();
            wrti[10].setFieldName("zjygjsfxt"); //直接与隔级是否相同
            wrti[10].setFieldValue(o.get("zjygjsfxt"));
            wrti[10].setView(true);
            wrti[10].setEdit(true);

            wrti[11] = new WorkflowRequestTableField();
            wrti[11].setFieldName("gh"); //工号
            wrti[11].setFieldValue(o.get("gh"));
            wrti[11].setView(true);
            wrti[11].setEdit(true);

            wrti[12] = new WorkflowRequestTableField();
            wrti[12].setFieldName("zdlc"); //制定流程
            wrti[12].setFieldValue(o.get("zdlc"));
            wrti[12].setView(true);
            wrti[12].setEdit(true);

            wrti[13] = new WorkflowRequestTableField();
            wrti[13].setFieldName("yjbmmc"); //一级部门名称
            wrti[13].setFieldValue(o.get("szyjbm"));
            wrti[13].setView(true);
            wrti[13].setEdit(true);

            wrti[14] = new WorkflowRequestTableField();
            wrti[14].setFieldName("sqrq"); //申请日期
            wrti[14].setFieldValue(nowDate.toString());
            wrti[14].setView(true);
            wrti[14].setEdit(true);

            WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
            wrtri[0] = new WorkflowRequestTableRecord();
            wrtri[0].setWorkflowRequestTableFields(wrti);
            WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
            wmi.setRequestRecords(wrtri);

            //插入明细表
            new BaseBean().writeLog("进入添加明细");
            List<Map<String, String>> itemList = itemListMap.get(id);
            int detailrows = itemList.size();//添加指定条数明细
            //添加明细数据
            wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
            for (int i = 0; i < detailrows; i++) {
                wrti = new WorkflowRequestTableField[21]; //字段个数   **************

                wrti[0] = new WorkflowRequestTableField();
                wrti[0].setFieldName("mbxh"); //目标序号
                wrti[0].setFieldValue(itemList.get(i).get("mbxh"));
                wrti[0].setView(true);//字段是否可见
                wrti[0].setEdit(true);//字段是否可编辑

                wrti[1] = new WorkflowRequestTableField();
                wrti[1].setFieldName("mbobjective"); //目标(Objective)
                wrti[1].setFieldValue(itemList.get(i).get("mbobjective"));
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2] = new WorkflowRequestTableField();
                wrti[2].setFieldName("gjjgkeyresults"); //关键结果（Key?Results）
                wrti[2].setFieldValue(itemList.get(i).get("gjjgkeyresults"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3] = new WorkflowRequestTableField();
                wrti[3].setFieldName("qzbfb"); //权重（百分比）
                wrti[3].setFieldValue(itemList.get(i).get("qzbfb"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("smbdmb"); //使命必达目标（3.5分）
                wrti[4].setFieldValue(itemList.get(i).get("smbdmb"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                wrti[5] = new WorkflowRequestTableField();
                wrti[5].setFieldName("jxmxbid"); //绩效明细id
                wrti[5].setFieldValue(itemList.get(i).get("itemId"));
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                wrtri[i] = new WorkflowRequestTableRecord();
                wrtri[i].setWorkflowRequestTableFields(wrti);
            }

            //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
            WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
            WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
            WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
            //流程基本信息设置
            WorkflowBaseInfo wbi = new WorkflowBaseInfo();
            wbi.setWorkflowId(WORKFLOWID_EVALUATE);//流程请求id
            WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
            wri.setCreatorId(o.get("xm"));//创建人id
            wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
            wri.setRequestLevel("0");//0 正常，1重要，2紧急
            String requestName = "";
            if ("0".equals(jxlxValue)) {
                requestName = "认养一头牛" + nowDate.getYear() + "年Q" + getCurrentQuarter() + "季度员工绩效评价审批流程-" + o.get("lastname");
            } else {
                requestName = "认养一头牛" + nowDate.getYear() + "年" + (nowDate.getMonthValue() - 1) + "月员工绩效评价审批流程-" + o.get("lastname");
            }
            wri.setRequestName(requestName);//流程标题
            //添加主字段数据
            wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
            wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
            wri.setWorkflowBaseInfo(wbi);
            WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
            String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri, Integer.parseInt(o.get("xm"))));
            if (!"".equals(requestid)) {
                if (Integer.parseInt(requestid) > 0) {
                    result.put("status", "0");
                    result.put("requestid", requestid);
                    //修改是否触发评价流程为是
                    rs.executeUpdate("update uf_grjxxx set sfcfpjlc = 0 where id = ? ", o.get("mainId"));
                } else {
                    result.put("status", "1");
                    result.put("requestid", requestid);
                }
            }
            new BaseBean().writeLog("CreateJxWorkflowUtil:" + o.get("gh") + "的" + jxlxValue + "（0：季度，1：月度）绩效评价流程创建结果" + result.toJSONString());
        }
    }
}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.SAPUtil;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
//会计科目主数据获取
public class HJKMData004Cron extends BaseCronJob {

    private String FUNID = "ZINF004";
    private RecordSet rs = new RecordSet();

    @Override
    public void execute() {


        long time = new Date().getTime();
        String currDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String currTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        String params = "{\n" +
                "    \"CTRL\":{\n" +
                "        \"SYSID\":\"OA\",\n" +
                "        \"REVID\":\"SAP\",\n" +
                "        \"FUNID\":\""+FUNID+"\",\n" +
                "        \"INFID\":\""+time+"\",\n" +
                "        \"UNAME\":\"\",\n" +
                "        \"DATUM\":\""+currDate+"\",\n" +
                "        \"UZEIT\":\""+currTime+"\",\n" +
                "        \"KEYID\":\"\",\n" +
                "        \"TABIX\":0,\n" +
                "        \"MSGTY\":\"\",\n" +
                "        \"MSAGE\":\"\",\n" +
                "        \"METHOD\":\"\",\n" +
                "        \"PAGE_SIZE\":0,\n" +
                "        \"PAGE_NO\":0\n" +
                "    },\n" +
                "    \"DATA\":{\n" +
                "        \"TBUKRS\":\"\",\n" +
                "        \"KTOPL\":\"RYJT\",\n" +
                "        \"KTOKS\":\"\"\n" +
                "    }\n" +
                "}";
        String execute = SAPUtil.execute(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
        String update = "";

        for (JSONObject datum : data) { // 主数据
            String KTOPL = datum.getString("KTOPL");
            String KTOKS = datum.getString("KTOKS");
            String SAKNR = datum.getString("SAKNR");
            String XLOEV = datum.getString("XLOEV");
            String TXT50 = datum.getString("TXT50");


            List<JSONObject> kz_object = (List<JSONObject>) datum.get("IT_BUKRS");
            for (int i = 0; i < kz_object.size(); i++) { // 明细数据
                JSONObject kz = kz_object.get(i);
                String SAKNR1 = kz.getString("SAKNR");
                String BUKRS = kz.getString("BUKRS");
                String XSPEB = kz.getString("XSPEB");
                String XLOEB = kz.getString("XLOEB");
                boolean mxsj = selectCbkz(SAKNR1, BUKRS);
                if (mxsj) {
                    update += "更新:"+updateCbkz(SAKNR1, BUKRS, XSPEB, XLOEB) + ",";

                } else {
                    update += "新增:"+insertCbkz(SAKNR1, BUKRS, XSPEB, XLOEB) + ",";
                }
            }

            boolean zsj = selectCb(SAKNR);
            if (zsj) {
                update += "更新明细:"+updateCb(KTOPL, KTOKS, SAKNR, XLOEV,TXT50) + ",";

            } else {
                update += "新增明细:"+insertCb(KTOPL, KTOKS, SAKNR, XLOEV,TXT50) + ",";
            }
        }
        rs.executeUpdate("insert into uf_sap (qqcs,xycs,gxjg,qqsj,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                params, execute, update, currDate, "17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_sap");
    }


    // 修改主数据
    public boolean updateCb(String KTOPL, String KTOKS, String SAKNR, String XLOEV,String TXT50) {
        boolean b = rs.executeUpdate("update uf_kjkmzsj set KTOPL =?,KTOKS=?,SAKNR=?,XLOEV=?,TXT50=? where SAKNR = ? ",
                KTOPL, KTOKS, SAKNR, XLOEV,TXT50, SAKNR);
        return b;
    }


    // 新增主数据
    public boolean insertCb(String KTOPL, String KTOKS, String SAKNR, String XLOEV,String TXT50) {
        boolean b = rs.executeUpdate("insert into uf_kjkmzsj ( KTOPL,KTOKS,SAKNR,XLOEV,TXT50,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?) ",
                KTOPL, KTOKS, SAKNR, XLOEV,TXT50, "13", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_kjkmzsj");


        return b;
    }

    // 查询主数据
    public boolean selectCb(String SAKNR) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  uf_kjkmzsj where SAKNR =?", SAKNR);


        return rs1.next();
    }

    // 修改扩展数据
    public boolean updateCbkz(String SAKNR1, String BUKRS, String XSPEB, String XLOEB) {
        boolean b = rs.executeUpdate("update uf_kjkmmx set SAKNR =?,BUKRS=?,XSPEB=?,XLOEB=? where SAKNR =? and BUKRS = ? ",
                SAKNR1, BUKRS, XSPEB, XLOEB,  SAKNR1, BUKRS);
        return b;
    }


    // 新增扩展数据
    public boolean insertCbkz(String SAKNR1, String BUKRS, String XSPEB, String XLOEB) {
        boolean b = rs.executeUpdate("insert into uf_kjkmmx ( SAKNR,BUKRS,XSPEB,XLOEB,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?) ",
                SAKNR1, BUKRS, XSPEB, XLOEB,  "20", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_kjkmmx");


        return b;
    }

    // 查询扩展数据
    public boolean selectCbkz(String SAKNR1, String BUKRS) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  uf_kjkmmx where SAKNR =? and BUKRS = ?", SAKNR1, BUKRS);


        return rs1.next();
    }


}

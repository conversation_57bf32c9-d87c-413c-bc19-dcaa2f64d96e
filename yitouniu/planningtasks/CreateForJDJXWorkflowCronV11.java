package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;
import yitouniu.util.CreateJxWorkflowUtil;
import yitouniu.util.TimeUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/11/14 15:06
 * @Description TODO
 * @Version 1.0
 */
public class CreateForJDJXWorkflowCronV11 extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    public static final String WORKFLOWID_ARCHIVE = "2109";
//    public static final String WORKFLOWID_ARCHIVE = "967";

    public static final String JDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","jdjxformmodeid"));

    @Override
    public void execute() {

        //季度选项框  0-3对应1-4

        String jxlxName = "季度";
        //0：季度  1：月度
        String jxlxValue = "0";

        List<Map<String, String>> ryList = new ArrayList<>();
        List<String> managerIdList = new ArrayList<>();
        HashSet<String> departmentIdSet = new HashSet<>();


        String rySql = "select a.rylx, a.sftsry,a.okrmb,a.jd,a.nf, b.id, b.lastname,b.workcode,b.departmentid,b.jobtitle,b.managerid,b.createdate " +
                " from uf_ryssxx a " +
                " left join HrmResource b on a.cfry = b.id " +
                " where cflx = " + jxlxValue;
        rs.executeQuery(rySql);
        while (rs.next()) {
            Map<String, String> map = new HashMap<>();
            String jd = rs.getString("jd");
            if (StringUtils.isEmpty(jd)) {
                baseBean.writeLog("CreateForJDJXWorkflowCron:" +rs.getString("lastname")+"没有季度信息，跳过");
                continue;
            }
            //已经制定过的不再制定
            if (hasCreated(Integer.parseInt(jd), rs.getString("id"))) {
                continue;
            }
            String tsry = rs.getString("sftsry");
            map.put("sftsry", tsry);

            map.put("id", rs.getString("id"));
            map.put("lastname", rs.getString("lastname"));
            map.put("workcode", rs.getString("workcode"));
            String departmentId = rs.getString("departmentid");
            departmentIdSet.add(departmentId);
            map.put("departmentid", departmentId);
            map.put("jobtitle", rs.getString("jobtitle"));
            String managerId = rs.getString("managerid");
            if (managerId.equals("276")) {
                managerId = rs.getString("id");
            }
            managerIdList.add(managerId);
            map.put("managerid", managerId);
            map.put("createdate", rs.getString("createdate"));
            map.put("okrmb", rs.getString("okrmb"));
            map.put("year", rs.getString("nf"));
            map.put("quarter", jd);
            map.put("rylx", rs.getString("rylx"));
            ryList.add(map);
        }
        baseBean.writeLog("CreateForJDJXWorkflowCron:获取ryList=" + ryList);

        //获取特殊人员的固定模版
        Map<String,List<Map<String,String>>> tsryListMap = CreateJxWorkflowUtil.getTemplateByName();

        String managerIdString = managerIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        RecordSet gjzgRs = new RecordSet();
        String gjzgSql = "select id,managerid from HrmResource where id in (" + managerIdString + ")";
        gjzgRs.executeQuery(gjzgSql);
        Map<String, String> gjzgMap = new HashMap<>();
        while (gjzgRs.next()) {
            String managerid = gjzgRs.getString("managerid");
            if("276".equals(managerid)){
                managerid = gjzgRs.getString("id");
            }
            gjzgMap.put(gjzgRs.getString("id"), managerid);
        }
        baseBean.writeLog("CreateForJDJXWorkflowCron:获取隔级主管id=" + gjzgMap);
        //获取部门的一级部门id
        Map<String, String> yjbmMap = CreateJxWorkflowUtil.getYjbmMap(departmentIdSet);
        baseBean.writeLog("CreateForJDJXWorkflowCron:获取部门的一级部门id=" + yjbmMap);
        //获取所有杭州一级部门和负责人
        Map<String, String> yjbmfzrMap = CreateJxWorkflowUtil.getYjbmfzrMap();
        baseBean.writeLog("CreateForJDJXWorkflowCron:所有杭州一级部门和负责人=" + yjbmfzrMap);
        //创建流程
        createWorkflow(ryList, gjzgMap, LocalDate.now(), yjbmMap, yjbmfzrMap, jxlxValue, jxlxName, tsryListMap);

    }


    public boolean hasCreated(int quarter, String xm) {
        boolean flag = false;
        RecordSet rs1 = new RecordSet();
        baseBean.writeLog("CreateForJDJXWorkflowCron:判断是否已经制定过=" + quarter + ":" + xm);
        String sql = "select xm from uf_jxzdhdb where jd = ? and xm = ? and sfycj = 0";
        rs1.executeQuery(sql, quarter, xm);
        while (rs1.next()){
            String xm1 = rs1.getString("xm");
            if (StringUtils.isNotEmpty(xm1)) {
                flag = true;
            }
        }
        return flag;
    }


    /**
     * 创建绩效制定流程
     * @param ryList
     * @param gjzgMap
     * @param nowTime
     * @param yjbmMap
     * @param yjbmfzrMap
     * @param jxlxValue
     * @param jxlxName
     * @param tsryListMap
     */
    private static void createWorkflow(List<Map<String,String>> ryList, Map<String,String> gjzgMap, LocalDate nowTime, Map<String,String> yjbmMap, Map<String,String> yjbmfzrMap, String jxlxValue, String jxlxName, Map<String,List<Map<String,String>>> tsryListMap){
        RecordSet rs = new RecordSet();
        for(Map<String,String> o :ryList){
            try {
                JSONObject result = new JSONObject();

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0]  = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqrq"); //申请日期
                wrti[0].setFieldValue(nowTime.toString());
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1]  = new WorkflowRequestTableField();
                wrti[1].setFieldName("gjzg"); //隔级主管
                wrti[1].setFieldValue(gjzgMap.get(o.get("managerid"))) ;
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("szyjbm"); //所属一级部门
                wrti[2].setFieldValue(yjbmMap.get(o.get("departmentid")));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("szyjbmfzr"); //所属一级部门负责人
                wrti[3].setFieldValue(yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("jxlx"); //绩效类型
                wrti[4].setFieldValue(jxlxValue);
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                String requestName = "";
                String year = o.get("year");
                String nowQuarter = o.get("quarter");


                wrti[5]  = new WorkflowRequestTableField();
                wrti[5].setFieldName("jd"); //季度
                wrti[5].setFieldValue(nowQuarter);
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                requestName = "认养一头牛"+year+"年Q"+ (Integer.parseInt(nowQuarter) + 1) +"季度员工绩效目标制定流程-" + o.get("lastname");



                wrti[6]  = new WorkflowRequestTableField();
                wrti[6].setFieldName("nf"); //年份
                wrti[6].setFieldValue(String.valueOf(year));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7]  = new WorkflowRequestTableField();
                wrti[7].setFieldName("xm"); //姓名
                wrti[7].setFieldValue(o.get("id"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8]  = new WorkflowRequestTableField();
                wrti[8].setFieldName("gh"); //工号
                wrti[8].setFieldValue(o.get("workcode"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9]  = new WorkflowRequestTableField();
                wrti[9].setFieldName("bm"); //部门
                wrti[9].setFieldValue(o.get("departmentid"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                wrti[10]  = new WorkflowRequestTableField();
                wrti[10].setFieldName("gw"); //岗位
                wrti[10].setFieldValue(o.get("jobtitle"));
                wrti[10].setView(true);
                wrti[10].setEdit(true);

                wrti[11]  = new WorkflowRequestTableField();
                wrti[11].setFieldName("zjsj"); //直接上级
                wrti[11].setFieldValue(o.get("managerid"));
                wrti[11].setView(true);
                wrti[11].setEdit(true);

                wrti[12]  = new WorkflowRequestTableField();
                wrti[12].setFieldName("dqgwrzsj"); //入职时间
                wrti[12].setFieldValue(o.get("createdate"));
                wrti[12].setView(true);
                wrti[12].setEdit(true);

                wrti[13]  = new WorkflowRequestTableField();
                wrti[13].setFieldName("rylx"); //枚举值0：普通员工、1：二级部门负责人、2：一级部门负责人
                wrti[13].setFieldValue(o.get("rylx"));
                wrti[13].setView(true);
                wrti[13].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //季度&&特殊人员，需要插入明细表
                if("0".equals(jxlxValue) && "0".equals(o.get("sftsry"))){
                    new BaseBean().writeLog("进入添加明细");
                    List<Map<String,String>> tsryList = tsryListMap.get(o.get("okrmb"));
                    int detailrows = tsryList.size() ;//添加指定条数明细
                    //添加明细数据
                    wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
                    for(int i = 0 ; i < detailrows ; i++){
                        wrti = new WorkflowRequestTableField[5]; //字段个数   **************

                        wrti[0]  = new WorkflowRequestTableField();
                        wrti[0].setFieldName("mbxh"); //目标序号
                        wrti[0].setFieldValue(tsryList.get(i).get("mqbs"));
                        wrti[0].setView(true);//字段是否可见
                        wrti[0].setEdit(true);//字段是否可编辑

                        wrti[1]  = new WorkflowRequestTableField();
                        wrti[1].setFieldName("mbobjective"); //目标(Objective)
                        wrti[1].setFieldValue(tsryList.get(i).get("mb"));
                        wrti[1].setView(true);
                        wrti[1].setEdit(true);

                        wrti[2]  = new WorkflowRequestTableField();
                        wrti[2].setFieldName("gjjgkeyresults"); //关键结果（Key?Results）
                        wrti[2].setFieldValue(tsryList.get(i).get("gjjg"));
                        wrti[2].setView(true);
                        wrti[2].setEdit(true);

                        wrti[3]  = new WorkflowRequestTableField();
                        wrti[3].setFieldName("qzbfb"); //权重（百分比）
                        wrti[3].setFieldValue(tsryList.get(i).get("qzbfb"));
                        wrti[3].setView(true);
                        wrti[3].setEdit(true);

                        wrti[4]  = new WorkflowRequestTableField();
                        wrti[4].setFieldName("smbdmb"); //使命必达目标（3.5分）
                        wrti[4].setFieldValue(tsryList.get(i).get("smbdmb"));
                        wrti[4].setView(true);
                        wrti[4].setEdit(true);


                        wrtri[i] = new WorkflowRequestTableRecord();
                        wrtri[i].setWorkflowRequestTableFields(wrti);
                    }

                }

                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID_ARCHIVE);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(o.get("id"));//创建人id
                wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName(requestName);//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("id"))));
                if(!requestid.isEmpty()){
                    if(Integer.parseInt(requestid) >0){
                        String sql = "insert into uf_jxzdhdb (xm, jd, sfycj) values(?,?,?)";
                        boolean flag = rs.executeUpdate(sql, o.get("id"), nowQuarter, 0);

                        result.put("status", "0");
                        result.put("requestid", requestid);
                        if(!flag){
                            new BaseBean().writeLog("createWorkflow:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果成功但是插入核对表失败，请手动插入，数据为绩效为类型=" + jxlxValue+", 月度or季度=" + nowQuarter + ", 姓名=" + o.get("id"));
                        }
                        if (Lists.newArrayList("1","2").contains(o.get("rylx"))) {
                            new BaseBean().writeLog("人员类型为1或者2 不插入uf_bmjdjx表");
                            continue;
                        }
                        //制定成功后直接加入信息表
                        String insertTableName = "uf_bmjdjx";

                        String insertMainSql = "insert into " + insertTableName + " (yjbmmc, bmfzr, jxsznf, jxszyf, formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values(?,?,?,?,?,?,?,?,?)  ";
                        Date date = new Date();
                        int mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), year, nowQuarter);

                        if(mainId < 0){
                            boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), year, nowQuarter, JDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                            new BaseBean().writeLog("CreateJxWorkflowUtil insertFlag = " + insertFlag);

                            if(insertFlag){
                                mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), year, nowQuarter);
                                //修改建模模块权限
                                ModeRightInfo modeRightInfo = new ModeRightInfo();
                                modeRightInfo.setNewRight(true);
                                modeRightInfo.editModeDataShare(1, Integer.parseInt(JDJXFORMMODEID),mainId);
                            } else {
                                new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");
                            }
                        }

                        //不管是否存在，先将原来的删除了
                        rs.executeUpdate("delete from " + insertTableName + "_dt1 where mainid = ? and xm = ?", mainId, o.get("id"));
                        //插入明细数据
                        String insertDetailSql = "insert into " + insertTableName + "_dt1  (mainid, xm, jxzdlc, bm, sfzdwc, gh) values(?,?,?,?,?,?)";
                        boolean insertDetailFlag = rs.executeUpdate(insertDetailSql, mainId, o.get("id"), requestid, o.get("departmentid"), 1, o.get("workcode"));
                        new BaseBean().writeLog("CreateJxWorkflowUtil insertDetailFlag = " + insertDetailFlag);

                    }else{
                        result.put("status", "1");
                        result.put("requestid", requestid);
                    }
                }
                new BaseBean().writeLog("CreateJxWorkflowUtil:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果"+result.toJSONString());
            } catch (Exception e) {
                new BaseBean().writeLog("createWorkflow : 异常错误" + e);
                new BaseBean().writeLog(e);
            }
        }

    }

}

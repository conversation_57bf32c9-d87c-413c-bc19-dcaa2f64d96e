package yitouniu.planningtasks;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 绩效改进-测试
 */
public class CreateForJXGJWorkflowCron extends BaseCronJob {

    public static final String WORKFLOWID = "1018";

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    RecordSet rsUpdate = new RecordSet();

    @Override
    public void execute() {

        List<Map<String, String>> mapList = new ArrayList<>();

//        String currentDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);

        // 查询绩效跟踪改进基础表
        String zcsql = "select a.id as aid, a.sfcf, a.gh, a.gjzq, a.qssj, a.gzjd, a.nf, " +
                "b.id, b.lastname,b.workcode,b.departmentid,b.jobtitle,b.managerid,b.createdate " +
                "from uf_ygjxgjcf a left join HrmResource b on a.ygxm = b.id ";
        rs.executeQuery(zcsql);

        while (rs.next()) {
            // 是否触发
            String sfcf = rs.getString("sfcf");
            if (!"0".equals(sfcf)) {
                Map<String, String> map = new HashMap<>();
                map.put("id", rs.getString("id")); // 员工id
                map.put("lastname", rs.getString("lastname")); // 员工id
                map.put("aid", rs.getString("aid")); // 绩效跟踪改进基础表id
                map.put("gh", rs.getString("gh"));  // 员工工号
                map.put("gjzq", rs.getString("gjzq"));  // 改进周期
                map.put("qssj", rs.getString("qssj")); // 起始时间
                map.put("gzjd", rs.getString("gzjd"));  // 归属季度
                map.put("nf", rs.getString("nf")); // 年份
                map.put("zjsj", rs.getString("managerid")); // 上级
                map.put("bm", rs.getString("departmentid")); // 部门
                map.put("gw", rs.getString("jobtitle")); // 岗位

                RecordSet gjzgRs = new RecordSet();
                String gjzgSql = "select id,managerid from HrmResource where id = " + rs.getString("managerid");
                gjzgRs.executeQuery(gjzgSql);
                if (gjzgRs.next()) {
                    map.put("gjzg", gjzgRs.getString("managerid")); // 隔级主管
                }
                mapList.add(map);
            }
        }

        createWorkflow(mapList);
    }


    public void createWorkflow(List<Map<String, String>> mapList) {
        baseBean.writeLog("绩效改进CreateForJXGJWorkflowCron的创建流程数据=" + JSON.toJSONString(mapList));
        try {
            for (Map<String, String> mainMap : mapList) {

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0] = new WorkflowRequestTableField();
                wrti[0].setFieldName("xm"); //姓名
                wrti[0].setFieldValue(mainMap.get("id"));
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1] = new WorkflowRequestTableField();
                wrti[1].setFieldName("gh"); //工号
                wrti[1].setFieldValue(mainMap.get("gh"));
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2] = new WorkflowRequestTableField();
                wrti[2].setFieldName("gjzq"); //改进周期
                wrti[2].setFieldValue(mainMap.get("gjzq"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3] = new WorkflowRequestTableField();
                wrti[3].setFieldName("qssj"); //起始时间
                wrti[3].setFieldValue(mainMap.get("qssj"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("gzjd"); //归属季度
                wrti[4].setFieldValue(mainMap.get("gzjd"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                wrti[5] = new WorkflowRequestTableField();
                wrti[5].setFieldName("nf"); //年份
                wrti[5].setFieldValue(mainMap.get("nf"));
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                wrti[6] = new WorkflowRequestTableField();
                wrti[6].setFieldName("bm"); //部门
                wrti[6].setFieldValue(mainMap.get("bm"));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7] = new WorkflowRequestTableField();
                wrti[7].setFieldName("gw"); //岗位
                wrti[7].setFieldValue(mainMap.get("gw"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8] = new WorkflowRequestTableField();
                wrti[8].setFieldName("zjsj"); //直接上级
                wrti[8].setFieldValue(mainMap.get("zjsj"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9] = new WorkflowRequestTableField();
                wrti[9].setFieldName("gjzg"); //隔级主管
                wrti[9].setFieldValue(mainMap.get("gjzg"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(mainMap.get("fkhgllr"));//创建人id
                wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                String quarter = "";
                if ("0".equals(mainMap.get("gzjd"))) {
                    quarter = "Q1";
                }
                else if ("1".equals(mainMap.get("gzjd"))) {
                    quarter = "Q2";
                }
                else if ("2".equals(mainMap.get("gzjd"))) {
                    quarter = "Q3";
                }
                else if ("3".equals(mainMap.get("gzjd"))) {
                    quarter = "Q4";
                }

                String requestName = "认养一头牛" + mainMap.get("nf") + "年" + quarter + "季度员工绩效改进流程-" + mainMap.get("lastname");
                wri.setRequestName(requestName);//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri, Integer.parseInt(mainMap.get("id"))));

                if (!"".equals(requestid)) {
                    String updateSql = "update uf_ygjxgjcf set sfcf = 0 where id = ?";
                    rsUpdate.executeUpdate(updateSql, mainMap.get("aid"));
                }
                baseBean.writeLog("绩效改进流程创建requestid=" + requestid);
            }
        }
        catch (Exception e) {
            baseBean.writeLog("绩效改进流程创建异常=" + Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }
    }
}

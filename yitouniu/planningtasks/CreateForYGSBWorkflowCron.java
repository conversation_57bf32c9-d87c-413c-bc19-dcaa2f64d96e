package yitouniu.planningtasks;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.util.*;

/**
 * 阳光申报
 */
public class CreateForYGSBWorkflowCron extends BaseCronJob {

//    public static final String WORKFLOWID = "1015"; // 测试
    public static final String WORKFLOWID = "1727"; // 正式

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();
    RecordSet rUpdate = new RecordSet();
    RecordSet rsItem = new RecordSet();


    @Override
    public void execute() {

        List<Map<String, String>> mapList = new ArrayList<>();
        Map<String, List<Map<String, String>>> itemListMap = new HashMap<>();

        String currentDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);

        // 查询自查时间基础表
        String zcsql = "select * from uf_zczjsb";
        rs.executeQuery(zcsql);

        while (rs.next()) {
            // 自查开启时间
            String zcqdsj = rs.getString("zcqdsj");
            if (null != zcqdsj && zcqdsj.equals(currentDate)) {
                Map<String, String> map = new HashMap<>();
                map.put("id", rs.getString("id"));
                map.put("fkhgllr", rs.getString("fkhgllr"));  // 自查人
                map.put("zcsyb", rs.getString("zcsyb"));  // 自查事业部
                map.put("zcbk", rs.getString("zcbk")); // 自查板块
                map.put("zcgszt", rs.getString("zcgszt"));  // 自查公司主体
                map.put("zcbm", rs.getString("zcbm")); // 自查部门
                map.put("zcwcsj", rs.getString("zcwcsj")); // 自查完成时间
                mapList.add(map);

                // 查询自查时间基础明细表
                String zcitemsql = "select * from uf_zczjsb_dt1 where mainid = ?";
                rsItem.executeQuery(zcitemsql, map.get("id"));

                List<Map<String, String>> itemList = new ArrayList<>();
                while (rsItem.next()) {
                    Map<String, String> itemMap = new HashMap<>();
                    itemMap.put("id", rsItem.getString("id"));
                    itemMap.put("mainId", rsItem.getString("mainid"));
                    itemMap.put("kzbh", rsItem.getString("kzbh")); // 控制编号
                    itemMap.put("lcz", rsItem.getString("lcz")); // 流程组
                    itemMap.put("kzmb", rsItem.getString("kzmb")); // 控制目标
                    itemMap.put("kzyq", rsItem.getString("kzyq")); // 控制要求
                    itemList.add(itemMap);
                }
                itemListMap.put(rsItem.getString("mainid"), itemList);
            }
        }

        createWorkflow(currentDate, mapList, itemListMap);
    }


    public void createWorkflow(String currentDate, List<Map<String, String>> mapList, Map<String, List<Map<String, String>>> itemListMap) {
        baseBean.writeLog("阳光申报CreateForYGSBWorkflowCron的创建流程数据=" + JSON.toJSONString(mapList) + "-明细数据=" + JSON.toJSONString(itemListMap));
        try {
            for (Map<String, String> mainMap : mapList) {

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0] = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqr"); //申请人
                wrti[0].setFieldValue(mainMap.get("fkhgllr"));
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1] = new WorkflowRequestTableField();
                wrti[1].setFieldName("sqrq1"); //申请日期
                wrti[1].setFieldValue(currentDate);
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2] = new WorkflowRequestTableField();
                wrti[2].setFieldName("szgs"); //所属公司
                wrti[2].setFieldValue(mainMap.get("zcgszt"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3] = new WorkflowRequestTableField();
                wrti[3].setFieldName("szbm"); //所在部门
                wrti[3].setFieldValue(mainMap.get("zcbm"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("zcsyb"); //自查事业部
                wrti[4].setFieldValue(mainMap.get("zcsyb"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                wrti[5] = new WorkflowRequestTableField();
                wrti[5].setFieldName("zcbk"); //自查板块
                wrti[5].setFieldValue(mainMap.get("zcbk"));
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                wrti[6] = new WorkflowRequestTableField();
                wrti[6].setFieldName("zcgszt"); //自查公司主体
                wrti[6].setFieldValue(mainMap.get("zcgszt"));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7] = new WorkflowRequestTableField();
                wrti[7].setFieldName("zcbm"); //自查部门
                wrti[7].setFieldValue(mainMap.get("zcbm"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8] = new WorkflowRequestTableField();
                wrti[8].setFieldName("fkhgllr"); //自查人
                wrti[8].setFieldValue(mainMap.get("fkhgllr"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9] = new WorkflowRequestTableField();
                wrti[9].setFieldName("zcwcsj"); //自查完成时间
                wrti[9].setFieldValue(mainMap.get("zcwcsj"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                wrti[10] = new WorkflowRequestTableField();
                wrti[10].setFieldName("dyjmsjid"); //id
                wrti[10].setFieldValue(mainMap.get("id"));
                wrti[10].setView(true);
                wrti[10].setEdit(true);


                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                List<Map<String, String>> items = itemListMap.get(mainMap.get("id"));
                //添加明细数据
                wrtri = new WorkflowRequestTableRecord[items.size()];//添加指定条数行明细数据
                for (int i = 0; i < items.size(); i++) {
                    wrti = new WorkflowRequestTableField[5]; //字段个数   **************

                    wrti[0] = new WorkflowRequestTableField();
                    wrti[0].setFieldName("kzbh"); //控制编号
                    wrti[0].setFieldValue(items.get(i).get("kzbh"));
                    wrti[0].setView(true);//字段是否可见
                    wrti[0].setEdit(true);//字段是否可编辑

                    wrti[1] = new WorkflowRequestTableField();
                    wrti[1].setFieldName("lcz"); //流程组
                    wrti[1].setFieldValue(items.get(i).get("lcz"));
                    wrti[1].setView(true);
                    wrti[1].setEdit(true);

                    wrti[2] = new WorkflowRequestTableField();
                    wrti[2].setFieldName("kzmb"); //控制目标
                    wrti[2].setFieldValue(items.get(i).get("kzmb"));
                    wrti[2].setView(true);
                    wrti[2].setEdit(true);

                    wrti[3] = new WorkflowRequestTableField();
                    wrti[3].setFieldName("kzyq"); //控制要求
                    wrti[3].setFieldValue(items.get(i).get("kzyq"));
                    wrti[3].setView(true);
                    wrti[3].setEdit(true);

                    wrtri[i] = new WorkflowRequestTableRecord();
                    wrtri[i].setWorkflowRequestTableFields(wrti);
                }

                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(mainMap.get("fkhgllr"));//创建人id
                wri.setIsnextflow("0"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName("自查自纠申报流程");//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri, Integer.parseInt(mainMap.get("fkhgllr"))));

                baseBean.writeLog("自查自纠申报流程创建requestid=" + requestid);

                rUpdate.executeUpdate("update uf_zczjsb set zczjlc = ? where id = ?", requestid, mainMap.get("id"));
            }
        }
        catch (Exception e) {
            baseBean.writeLog("自查自纠申报流程创建异常=" + Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }
    }
}

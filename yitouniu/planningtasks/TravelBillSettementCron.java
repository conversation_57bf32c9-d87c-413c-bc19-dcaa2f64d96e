package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.esb.TravelBillSettementBtripTrain;
import yitouniu.esb.TravelBillSettementFlight;
import yitouniu.esb.TravelBillSettementHotel;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/31 10:43
 * @Description TODO
 * @Version 1.0
 */
public class TravelBillSettementCron  extends BaseCronJob {
    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        TravelBillSettementHotel travelBillSettementHotel = new TravelBillSettementHotel();
        TravelBillSettementBtripTrain travelBillSettementBtripTrain = new TravelBillSettementBtripTrain();
        TravelBillSettementFlight travelBillSettementFlight = new TravelBillSettementFlight();
        Map params = new HashMap();
        JSONObject jsonObject = new JSONObject();


        LocalDate nowDate = LocalDate.now();
        LocalDate yesDate = nowDate.plus(-1, ChronoUnit.DAYS);
        jsonObject.put("startTime",yesDate.toString());
        jsonObject.put("endTime",nowDate.toString());
        params.put("data",jsonObject);



        baseBean.writeLog("TravelBillSettementCron:travelBillSettementHotel:>>>start");
        Map<String,String> mapHotel = travelBillSettementHotel.executeParam(params);
        if("S".equals(mapHotel.get("MSGTY"))){
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementHotel:拉取成功，日期范围为"+yesDate+"到"+nowDate);
        } else {
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementHotel:拉取失败，日期范围为"+yesDate+"到"+nowDate);
        }
        baseBean.writeLog("TravelBillSettementCron:travelBillSettementHotel:>>>end");



        baseBean.writeLog("TravelBillSettementCron:travelBillSettementBtripTrain:>>>start");
        Map<String,String> mapBtripTrain = travelBillSettementBtripTrain.executeParam(params);
        if("S".equals(mapBtripTrain.get("MSGTY"))){
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementBtripTrain:拉取成功，日期范围为"+yesDate+"到"+nowDate);
        } else {
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementBtripTrain:拉取失败，日期范围为"+yesDate+"到"+nowDate);
        }
        baseBean.writeLog("TravelBillSettementCron:travelBillSettementBtripTrain:>>>end");



        baseBean.writeLog("TravelBillSettementCron:travelBillSettementFlight:>>>start");
        Map<String,String> mapFlight = travelBillSettementFlight.executeParam(params);
        if("S".equals(mapFlight.get("MSGTY"))){
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementFlight:拉取成功，日期范围为"+yesDate+"到"+nowDate);
        } else {
            baseBean.writeLog("TravelBillSettementCron:travelBillSettementFlight:拉取失败，日期范围为"+yesDate+"到"+nowDate);
        }
        baseBean.writeLog("TravelBillSettementCron:travelBillSettementFlight:>>>end");

    }

}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.esb.BankAccountESB;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 16:55 2021/12/20
 */
public class BankAccountCron extends BaseCronJob {
    @Override
    public void execute() {
        BankAccountESB bankAccountESB = new BankAccountESB();
        Map params = new HashMap();
        JSONObject jsonObject = new JSONObject();

        RecordSet rs = new RecordSet();
        //生产
//        String sql = "select MAX(lastmodifiedon) as lastmodifiedon from uf_lhh";
        //测试
        String sql = "select MAX(lastmodifiedon) as lastmodifiedon from uf_YHZSJ_KHYH";
        rs.executeQuery(sql);
        new BaseBean().writeLog("BankAccountCron sql="+sql);
        if(rs.next()){
            jsonObject.put("startTime",rs.getString("lastmodifiedon"));
            new BaseBean().writeLog("BankAccountCron有以往日期数据"+rs.getString("lastmodifiedon"));
        } else{
            jsonObject.put("startTime","");
        }
        String endTime = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        jsonObject.put("endTime",endTime);

        params.put("DATA",jsonObject);

        Map result = bankAccountESB.executeParam(params);
        new BaseBean().writeLog("定时读取资金平台银行主数据开户行数据："+result.get("MSGTY"));
    }
}

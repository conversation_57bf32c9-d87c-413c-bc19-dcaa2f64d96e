package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.SAPUtil;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @program: oa
 * @description: 全量客户主数据
 * @author: haiyang
 * @create: 2023-08-18 09:39
 **/
public class KHData002FullCronV2 extends BaseCronJob {

    private String FUNID = "ZINF002";
    private RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        new BaseBean().writeLog("KHData002Cron——start");
        for (int a = 0; a < 2; a++) {
            String type = "";
            String tableName = ""; // 主表
            String detail = ""; // 明细表
            String jmidMain = ""; // 主表建模id
            String jmiddetail = ""; // 明细表
            if(a==0){
                type = "C"; // 客户
                tableName = "uf_khzsj";
                detail = "uf_khmx";
                jmidMain = "14";
                jmiddetail = "18";

            }else {
                type = "V"; // 供应商
                tableName = "uf_gyszsj";
                detail = "uf_gysmx";
                jmidMain = "15";
                jmiddetail = "19";
            }

            long time = System.currentTimeMillis();
            String currDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String currTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
            String readTime = "1991-01-01";
//            rs.executeQuery("select MAX(read_time) as read_time from " + tableName);
//            if(rs.next()){
//                String o = rs.getString("read_time");
//                if(StringUtils.isBlank(o)){
//                    readTime = "1991-01-01";
//                } else {
//                    readTime = o;
//                }
//            }
//            new BaseBean().writeLog("readTime="+readTime);
            String params = "{\n" +
                    "    \"CTRL\":{\n" +
                    "        \"SYSID\":\"OA\",\n" +
                    "        \"REVID\":\"SAP\",\n" +
                    "        \"FUNID\":\""+FUNID+"\",\n" +
                    "        \"INFID\":\"" + time + "\",\n" +
                    "        \"UNAME\":\"\",\n" +
                    "        \"DATUM\":\"" + currDate + "\",\n" +
                    "        \"UZEIT\":\"" + currTime + "\",\n" +
                    "        \"KEYID\":\"\",\n" +
                    "        \"MSGTY\":\"\",\n" +
                    "        \"MSAGE\":\"\"\n" +
                    "    },\n" +
                    "    \"DATA\":{\n" +
                    "        \"ZBUTYPE\":\""+type+"\",\n" +
                    "        \"BUGROUP\":\"\",\n" +
                    "        \"PARTNER\":\"\",\n" +
                    "        \"BEGDAT\":\"" + readTime + "\",\n" +
                    "        \"ENDDAT\":\"" + currDate + "\"\n" +
                    "    }\n" +
                    "}";
            String execute = SAPUtil.execute(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
            String update = "";

            for (JSONObject datum : data) { // 主数据
                String ZBUTYPE = datum.getString("ZBUTYPE");
                String NAME1 = datum.getString("NAME1");
//                String NAME2 = datum.getString("NAME2");
                String BUGROUP = datum.getString("BUGROUP");
                String PARTNER = datum.getString("PARTNER");
                String busort2 = datum.getString("BUSORT2");
                String liquidDat = datum.getString("LIQUID_DAT");
                String channelUnitCode = datum.getString("KATR6");
                String channelUnitDesc = datum.getString("KA6TX");
//                String VBUND = datum.getString("VBUND");
//                String TAXNUMXL = datum.getString("TAXNUMXL");
//                String XBLCK = datum.getString("XBLCK");
//                String SPERRMain = datum.getString("SPERR");
//                String NOT_RELEASED = datum.getString("NOT_RELEASED");
//                String NAME_CO = datum.getString("NAME_CO");
//                String STREET = datum.getString("STREET");
//                String REGIO = datum.getString("REGIO");
//                String ORT01 = datum.getString("ORT01");
//                String ORT02 = datum.getString("ORT02");
//                String TELF1 = datum.getString("TELF1");
//                String AUFSD = datum.getString("AUFSD");
                String STR_SUPPL1 = datum.getString("STR_SUPPL1");

                List<JSONObject> kz_object = (List<JSONObject>) datum.get("IT_BUKRS");
                for (int i = 0; i < kz_object.size(); i++) { // 明细数据
                    JSONObject kz = kz_object.get(i);
                    String PARTNER1 = kz.getString("PARTNER");
                    String BUKRS = kz.getString("BUKRS");
                    String AKONT = kz.getString("AKONT");
                    String NODEL = kz.getString("NODEL");
                    String SPERR = kz.getString("SPERR");
                    boolean mxsj = selectCbkz(PARTNER1, AKONT,detail);
                    if (mxsj) {
                        update +="更新明细:"+ updateCbkz(PARTNER1, BUKRS, AKONT, NODEL, SPERR,detail) + ",";

                    } else {
                        update += "新增明细:"+insertCbkz(PARTNER1, BUKRS, AKONT, NODEL, SPERR,detail,jmiddetail) + ",";
                    }
                }

                boolean zsj = selectCb(PARTNER,tableName);
                if (zsj) {
                    update += "更新:"+updateCb(ZBUTYPE, NAME1, BUGROUP, PARTNER,STR_SUPPL1,currDate,tableName, busort2, liquidDat, channelUnitCode, channelUnitDesc) + ",";

                } else {
                    update += "新增:"+insertCb(ZBUTYPE, NAME1, BUGROUP, PARTNER,STR_SUPPL1,currDate,tableName,jmidMain, busort2, liquidDat, channelUnitCode, channelUnitDesc) + ",";
                }
            }
            rs.executeUpdate("insert into uf_sap (qqcs,xycs,gxjg,qqsj,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                    params, execute, update, currDate, "17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
            WorkflowUtil.ModeDataShare("uf_sap");
        }
    }



    // 修改主数据
    public boolean updateCb(String ZBUTYPE, String NAME1, String BUGROUP, String PARTNER, String STR_SUPPL1, String currDate, String tableName, String busort2, String liquidDat, String channelUnitCode, String channelUnitDesc) {
        boolean b = rs.executeUpdate("update "+tableName+" set ZBUTYPE =?,NAME1=?,BUGROUP=?,PARTNER=?,STR_SUPPL1=?,read_time=?,ssx2=?,LIQUID_DAT=?,KATR6=?,KA6TX=? where PARTNER = ? ",
                ZBUTYPE, NAME1, BUGROUP, PARTNER, STR_SUPPL1,currDate,busort2, liquidDat,channelUnitCode,channelUnitDesc,PARTNER);
        return b;
    }


    // 新增主数据
    public boolean insertCb(String ZBUTYPE, String NAME1, String BUGROUP, String PARTNER, String STR_SUPPL1, String currDate, String tableName, String jmid, String busort2, String liquidDat, String channelUnitCode, String channelUnitDesc) {
        boolean b = rs.executeUpdate("insert into "+tableName+" ( ZBUTYPE,NAME1,BUGROUP,PARTNER,STR_SUPPL1,read_time,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME,ssx2, LIQUID_DAT,KATR6,KA6TX) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",
                ZBUTYPE, NAME1, BUGROUP, PARTNER,STR_SUPPL1,currDate, jmid, "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()), busort2, liquidDat, channelUnitCode, channelUnitDesc);
        WorkflowUtil.ModeDataShare(tableName);


        return b;
    }

    // 查询主数据
    public boolean selectCb( String PARTNER,String tableName) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  "+tableName+" where PARTNER =?",PARTNER);


        return rs1.next();
    }

    // 修改扩展数据
    public boolean updateCbkz(String PARTNER, String BUKRS, String AKONT, String NODEL, String SPERR,String tableName) {
        boolean b = rs.executeUpdate("update "+tableName+" set PARTNER =?,BUKRS=?,AKONT=?,NODEL=?,SPERR=? where PARTNER =? and AKONT = ? ",
                PARTNER, BUKRS, AKONT, NODEL, SPERR,PARTNER,AKONT);
        return b;
    }


    // 新增扩展数据
    public boolean insertCbkz(String PARTNER, String BUKRS, String AKONT, String NODEL, String SPERR,String tableName,String jmid) {
        boolean b = rs.executeUpdate("insert into "+tableName+" ( PARTNER,BUKRS,AKONT,NODEL,SPERR,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?) ",
                PARTNER, BUKRS, AKONT, NODEL, SPERR,  jmid, "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare(tableName);


        return b;
    }

    // 查询扩展数据
    public boolean selectCbkz( String PARTNER,String AKONT,String tableName) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  "+tableName+" where PARTNER =? and AKONT = ?",PARTNER,AKONT);


        return rs1.next();
    }
}

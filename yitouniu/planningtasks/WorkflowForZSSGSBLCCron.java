package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/2/24 11:29
 * @Description 资损事故上报流程 定时创建
 * @Version 1.0
 */
public class WorkflowForZSSGSBLCCron extends BaseCronJob {


    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    //资损事故上报流程workflowid
    //测试
//    public static final String WORKFLOWID = "965";
//    //正式
    public static final String WORKFLOWID = "1251";



    @Override
    public void execute() {
        //获取需要发起的人员
        List<Map<String,String>> ryList = getAllTriggerPerson();

        LocalDate nowTime = LocalDate.now();

        for(Map<String, String> o : ryList){
            try {
                JSONObject result = new JSONObject();

                String lastName = "";
                String subcompanydesc = "";
                rs.executeQuery("select a.lastname, b.subcompanydesc from HrmResource a, HrmSubCompany b where a.subcompanyid1 = b.id and a.id = ? ", o.get("ry"));
                if(rs.next()){
                    lastName = rs.getString("lastName");
                    subcompanydesc = rs.getString("subcompanydesc");
                }

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0]  = new WorkflowRequestTableField();
                //申请日期
                wrti[0].setFieldName("sqrq");
                wrti[0].setFieldValue(nowTime.toString());
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1]  = new WorkflowRequestTableField();
                //申请人
                wrti[1].setFieldName("sqr");
                wrti[1].setFieldValue(o.get("ry")) ;
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                //申请部门
                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("sqbm");
                wrti[2].setFieldValue(o.get("bm"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("sqgs"); //申请公司
                wrti[3].setFieldValue(o.get("fqfb"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                //流程请求workflowid
                wbi.setWorkflowId(WORKFLOWID);
                //流程基本信息
                WorkflowRequestInfo wri = new WorkflowRequestInfo();
                //创建人id
                wri.setCreatorId(o.get("ry"));
                //0：停留在创建节点，1：流转到下一个节点
                wri.setIsnextflow("1");
                //0 正常，1重要，2紧急
                wri.setRequestLevel("0");

                String requestName = subcompanydesc + "-资损事故上报流程-" + lastName + "-" + nowTime;
                //流程标题
                wri.setRequestName(requestName);
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("ry"))));
                if(!"".equals(requestid)){
                    if(Integer.parseInt(requestid) >0){
                        result.put("status", "0");
                        result.put("requestid", requestid);
                    }else{
                        result.put("status", "1");
                        result.put("requestid", requestid);
                    }
                }
                new BaseBean().writeLog("execute "+o.get("ygh")+"的资损事故上报流程创建结果 "+result.toJSONString());
            } catch (Exception e) {
                new BaseBean().writeLog("execute 异常错误" + e);
                new BaseBean().writeLog(e);
            }

        }

    }


    public List<Map<String, String>> getAllTriggerPerson(){
        List<Map<String,String>> result = new ArrayList<>();
        rs.executeQuery("select * from uf_zssgsbrymd where sfqy = 0 ");
        while (rs.next()){
            Map<String, String> map = new HashMap<>();
            map.put("ry", rs.getString("ry"));
            map.put("bm", rs.getString("bm"));
            map.put("fqfb", rs.getString("fqfb"));
            map.put("ygh", rs.getString("ygh"));
            result.add(map);
        }
        return result;
    }

}

package yitouniu.planningtasks;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;
import weaver.workflow.webservices.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题整改
 */
public class CreateForWTZGWorkflowCron extends BaseCronJob {

//    public static final String WORKFLOWID = "1016"; // 测试
    public static final String WORKFLOWID = "1730"; // 正式

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    RecordSet rs2 = new RecordSet();

    RecordSet rsUpdate = new RecordSet();

    @Override
    public void execute() {

        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, List<Map<String, String>>> itemListMap = new HashMap<>();

        String currentDate = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);

        String sql = "SELECT  a.REQUESTID,b.wtbh,c.currentnodetype  from  \n" +
                "formtable_main_740  a  \n" +
                "LEFT JOIN   formtable_main_740_dt1 b  on a.id= b.mainid  \n" +
                "LEFT JOIN   workflow_requestbase c  on a.REQUESTID=c.REQUESTID";

        rs2.executeQuery(sql);
        Map<String, List<String>> filterMap = new HashMap<>();
        while (rs2.next()) {
            List<String> list = filterMap.computeIfAbsent(rs2.getString("wtbh"), k -> new ArrayList<>());
            list.add(rs2.getString("currentnodetype"));
        }

        // 查询问题整改基础表
        String zcsql = "select * from uf_wtzgtz";
        rs.executeQuery(zcsql);

        while (rs.next()) {
            String zgztnk = rs.getString("zgztnk");
            if (StringUtils.isBlank(zgztnk) || "1".equals(zgztnk) || "2".equals(zgztnk)) {

                String xzgwcsj = rs.getString("xzgwcsj"); // 新整改时间
                String zgwcsj = rs.getString("zgwcsj"); // 首次整改时间

                if (StringUtils.isNotBlank(xzgwcsj)) {
                    DateTime parse = DateUtil.parse(xzgwcsj, DatePattern.NORM_DATE_PATTERN);
                    if (parse.isAfter(new Date())) {
                        continue;
                    }
                }
                else if (StringUtils.isNotBlank(zgwcsj)) {
                    DateTime parse = DateUtil.parse(zgwcsj, DatePattern.NORM_DATE_PATTERN);
                    if (parse.isAfter(new Date())) {
                        continue;
                    }
                }

                List<String> wtbhList = filterMap.get(rs.getString("id"));
                if (CollectionUtil.isNotEmpty(wtbhList)) {
                    List<String> list = wtbhList.stream()
                            .filter(o -> !"3".equals(o))
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(list)) {
                        // 说明不在完结状态
                        continue;
                    }
                }

                Map<String, String> itemMap = new HashMap<>();
                itemMap.put("id", rs.getString("id")); // id
                itemMap.put("zgfzr", rs.getString("zgfzr")); // 整改负责人
                itemMap.put("zggszt", rs.getString("zggszt")); // 整改公司主体
                itemMap.put("zgbm", rs.getString("zgbm"));  // 整改部门
                itemMap.put("zgsyb", rs.getString("zgsyb"));  // 整改事业部
                itemMap.put("zgbk", rs.getString("zgbk")); // 整改板块
                itemMap.put("wtly", rs.getString("wtly"));  // 问题来源
                itemMap.put("wtms", rs.getString("wtms")); // 问题描述
                itemMap.put("yxjz", rs.getString("yxjz")); // 影响、价值
                itemMap.put("je", rs.getString("je")); // 金额
                itemMap.put("zgxdcs", rs.getString("zgxdcs")); // 整改行动措施
                if (StringUtils.isNotBlank(xzgwcsj)){
                    itemMap.put("zgwcsj", xzgwcsj); // 新整改完成时间
                } else {
                    itemMap.put("zgwcsj", zgwcsj); // 整改完成时间
                }

                itemMap.put("wtzglc", rs.getString("wtzglc")); // requestid

                String key = rs.getString("zgfzr") + rs.getString("zggszt") + rs.getString("zgbm") + rs.getString("zgsyb") + rs.getString("zgbk");
                map.put(key, itemMap);
                List<Map<String, String>> maps = itemListMap.computeIfAbsent(key, k -> new ArrayList<>());
                maps.add(itemMap);
            }
        }

        createWorkflow(currentDate, map, itemListMap);
    }


    public void createWorkflow(String currentDate, Map<String, Map<String, String>> map, Map<String, List<Map<String, String>>> itemListMap) {
        baseBean.writeLog("问题整改CreateForWTZGWorkflowCron的创建流程数据=" + JSON.toJSONString(map) + "-明细数据=" + JSON.toJSONString(itemListMap));
        try {
            for (String k : map.keySet()) {
                Map<String, String> mainMap = map.get(k);
                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0] = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqr"); //申请人
                wrti[0].setFieldValue(mainMap.get("zgfzr"));
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1] = new WorkflowRequestTableField();
                wrti[1].setFieldName("sqrq1"); //申请日期
                wrti[1].setFieldValue(currentDate);
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2] = new WorkflowRequestTableField();
                wrti[2].setFieldName("szgs"); //所属公司
                wrti[2].setFieldValue(mainMap.get("zggszt"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3] = new WorkflowRequestTableField();
                wrti[3].setFieldName("szbm"); //所在部门
                wrti[3].setFieldValue(mainMap.get("zgbm"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4] = new WorkflowRequestTableField();
                wrti[4].setFieldName("zgsyb"); //整改事业部
                wrti[4].setFieldValue(mainMap.get("zgsyb"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                wrti[5] = new WorkflowRequestTableField();
                wrti[5].setFieldName("zgbk"); //整改板块
                wrti[5].setFieldValue(mainMap.get("zgbk"));
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                wrti[6] = new WorkflowRequestTableField();
                wrti[6].setFieldName("zggszt"); //整改公司主体
                wrti[6].setFieldValue(mainMap.get("zggszt"));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7] = new WorkflowRequestTableField();
                wrti[7].setFieldName("zgbm"); //整改部门
                wrti[7].setFieldValue(mainMap.get("zgbm"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8] = new WorkflowRequestTableField();
                wrti[8].setFieldName("zgfzr"); //整改负责人
                wrti[8].setFieldValue(mainMap.get("zgfzr"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                List<Map<String, String>> items = itemListMap.get(k);
                List<String> ids = new ArrayList<>(items.size());
                //添加明细数据
                wrtri = new WorkflowRequestTableRecord[items.size()];//添加指定条数行明细数据
                for (int i = 0; i < items.size(); i++) {
                    wrti = new WorkflowRequestTableField[7]; //字段个数   **************

                    wrti[0] = new WorkflowRequestTableField();
                    wrti[0].setFieldName("wtly"); //问题来源
                    wrti[0].setFieldValue(items.get(i).get("wtly"));
                    wrti[0].setView(true);//字段是否可见
                    wrti[0].setEdit(true);//字段是否可编辑

                    wrti[1] = new WorkflowRequestTableField();
                    wrti[1].setFieldName("wtbh"); //问题编号
                    wrti[1].setFieldValue(items.get(i).get("id"));
                    wrti[1].setView(true);
                    wrti[1].setEdit(true);

                    wrti[2] = new WorkflowRequestTableField();
                    wrti[2].setFieldName("wtms"); //问题描述
                    wrti[2].setFieldValue(items.get(i).get("wtms"));
                    wrti[2].setView(true);
                    wrti[2].setEdit(true);

                    wrti[3] = new WorkflowRequestTableField();
                    wrti[3].setFieldName("yxjz"); //影响、价值
                    wrti[3].setFieldValue(items.get(i).get("yxjz"));
                    wrti[3].setView(true);
                    wrti[3].setEdit(true);

                    wrti[4] = new WorkflowRequestTableField();
                    wrti[4].setFieldName("je"); //金额
                    wrti[4].setFieldValue(items.get(i).get("je"));
                    wrti[4].setView(true);
                    wrti[4].setEdit(true);

                    wrti[5] = new WorkflowRequestTableField();
                    wrti[5].setFieldName("zgxdcs"); //整改行动措施
                    wrti[5].setFieldValue(items.get(i).get("zgxdcs"));
                    wrti[5].setView(true);
                    wrti[5].setEdit(true);

                    wrti[6] = new WorkflowRequestTableField();
                    wrti[6].setFieldName("zgwcsj"); //整改完成时间
                    wrti[6].setFieldValue(items.get(i).get("zgwcsj"));
                    wrti[6].setView(true);
                    wrti[6].setEdit(true);

                    wrtri[i] = new WorkflowRequestTableRecord();
                    wrtri[i].setWorkflowRequestTableFields(wrti);

                    ids.add(items.get(i).get("id"));
                }

                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(mainMap.get("zgfzr"));//创建人id
                wri.setIsnextflow("0"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName("问题整改流程");//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri, Integer.parseInt(mainMap.get("zgfzr"))));

                baseBean.writeLog("问题整改流程创建requestid=" + requestid);

                if (CollectionUtil.isNotEmpty(ids)) {
                    boolean b = rsUpdate.executeUpdate("update uf_wtzgtz set wtzglc = " + requestid + " where id in(" + StrUtil.join(",", ids) +")");
                    baseBean.writeLog("问题整改=" + StrUtil.join(",", ids) + ",更新requestid=" + requestid + ",结果=" + b);
                }
            }
        }
        catch (Exception e) {
            baseBean.writeLog("问题整改创建异常=" + Throwables.getStackTraceAsString(e));
            e.printStackTrace();
        }
    }
}

package yitouniu.planningtasks;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.esb.DZHDDataESB;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 电子回单定时任务
 */
public class DZHDCron  extends BaseCronJob {
    private static RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        Map map = new HashMap();
        new BaseBean().writeLog("电子回单开始------");
        List<String> requestList = new ArrayList<>();
        List<String> notecodeList = new ArrayList<>();
        // 获取需要生成的电子回单数据
        rs.executeQuery("select  *  from  view_dzhd");

        while (rs.next()){
            requestList.add(rs.getString("requestid"));
            notecodeList.add(rs.getString("NOTECODE"));
        }
        for (int i = 0; i < requestList.size(); i++) {
            map.put("requestid",requestList.get(i));
            map.put("NOTECODE",notecodeList.get(i));
            // 执行电子回单
            DZHDDataESB.execute(map);
        }

       /* map.put("requestid","139197");
        map.put("NOTECODE","ZJFK_6000_202010_00001403");
        DZHDDataESB.execute(map);*/

    }
}

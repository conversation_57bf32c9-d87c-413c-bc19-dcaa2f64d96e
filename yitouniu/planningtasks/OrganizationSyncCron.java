package yitouniu.planningtasks;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrganizationSyncCron extends BaseCronJob {

    @Override
    public void execute() {
        RecordSet userSearch = new RecordSet();
        RecordSet depSearch = new RecordSet();
        RecordSet comSearch = new RecordSet();

        Map<String, String> userInsert = new HashMap();
        Map<String, String> depInsert = new HashMap();
        Map<String, String> comInsert = new HashMap();

        Map<String, String> userUpdate = new HashMap();
        Map<String, String> depUpdate = new HashMap();
        Map<String, String> comUpdate = new HashMap();

        List<String> insert = new ArrayList<>();
        List<String> update = new ArrayList<>();

        RecordSet insertSet = new RecordSet();
        RecordSet updateSet = new RecordSet();

        String userSearchSql = "select c.departmentname,a.id id,a.uuid uuid,a.loginid loginid,a.lastname lastname,b.subcompanycode subcompanycode,b.subcompanydesc subcompanydesc from HrmResource a left join HrmSubCompany b on a.subcompanyid1 = b.id left join  HrmDepartment c on   c.id=a.departmentid";

        userSearch.executeQuery(userSearchSql);
        while (userSearch.next()) {
            userInsert.put(userSearch.getString("loginid"), "insert into uf_SAPrenyuan (MODEUUID,formmodeid,modedatacreater,modedatacreatertype,rymc,BUKRS,BUTXT,KTEXT,khmc,oadlm,oary) "
                    + "values('"
                    + userSearch.getString("uuid") + "',23,1,0,'"
                    + userSearch.getString("lastname") + "','"
                    + userSearch.getString("subcompanycode") + "','"
                    + userSearch.getString("subcompanydesc") + "','"
                    + userSearch.getString("departmentname") + "','"
                    + userSearch.getString("lastname") + "','"
                    + userSearch.getString("loginid")  + "','"
                    + userSearch.getString("id") + "')"
            );
            userUpdate.put(userSearch.getString("loginid"), "UPDATE uf_SAPrenyuan set "
                    + "rymc ='" + userSearch.getString("lastname")
                    + "', BUKRS ='" + userSearch.getString("subcompanycode")
                    + "', BUTXT ='" + userSearch.getString("subcompanydesc")
                    + "', KTEXT ='" + userSearch.getString("departmentname")
                    + "', khmc ='" + userSearch.getString("lastname")
                    + "', oadlm ='" + userSearch.getString("loginid")
                    + "', oary ='" + userSearch.getString("id")
                    + "' where oadlm = '" + userSearch.getString("loginid") + "'"
            );
        }


        String depSearchSql = "select id,uuid from HrmDepartment";

        depSearch.execute(depSearchSql);
        while (depSearch.next()) {
            depInsert.put(depSearch.getString("id"), "insert into formtable_main_306(formmodeid,modedatacreater,modedatacreatertype,MODEUUID,oabm)"
                    + "values("
                    + "46,1,0,'"
                    + depSearch.getString("uuid") + "','"
                    + depSearch.getString("id") + "')"
            );
            depUpdate.put(depSearch.getString("id"), "UPDATE formtable_main_306 set "
                    + "oabm='" + depSearch.getString("id")
                    + "' where oabm = '" + depSearch.getString("id") + "'"
            );
        }


        String comSearchSql = "select id,subcompanycode,subcompanydesc,uuid from HrmSubCompany";
        comSearch.execute(comSearchSql);
        while (comSearch.next()) {
            comInsert.put(comSearch.getString("id"),  "insert into uf_zzdmys(formmodeid,modedatacreater,modedatacreatertype,MODEUUID,oagsmc,zzdm,gsmc)"
                    + "values("
                    + "24,1,0,'"
                    + comSearch.getString("uuid") + "','"
                    + comSearch.getString("id") + "','"
                    + comSearch.getString("subcompanycode") + "','"
                    + comSearch.getString("subcompanydesc") + "')"
            );

            comUpdate.put(comSearch.getString("id"), "UPDATE uf_zzdmys set "
                    + "oagsmc='" + comSearch.getString("id")
                    + "', zzdm ='" + comSearch.getString("subcompanycode")
                    + "', gsmc ='" + comSearch.getString("subcompanydesc")
                    + "' where oagsmc = '" + comSearch.getString("id") + "'"
            );

        }


        List<String> ufSAPrenyuan = new ArrayList<>();
        RecordSet ufSAPrenyuanSearch = new RecordSet();
        String ufSAPrenyuanSearchSql = "select MODEUUID,formmodeid,modedatacreater,modedatacreatertype,rymc,BUKRS,BUTXT,KTEXT,khmc,oadlm from uf_SAPrenyuan";
        ufSAPrenyuanSearch.execute(ufSAPrenyuanSearchSql);
        while (ufSAPrenyuanSearch.next()) {
            ufSAPrenyuan.add(ufSAPrenyuanSearch.getString("oadlm"));
        }
        List<String> formTableMain306 = new ArrayList<>();
        RecordSet formTableMain306Search = new RecordSet();
        String formTableMain306Sql = "select formmodeid,modedatacreater,modedatacreatertype,MODEUUID,oabm from formtable_main_306";
        formTableMain306Search.execute(formTableMain306Sql);
        while (formTableMain306Search.next()) {
            formTableMain306.add(formTableMain306Search.getString("oabm"));
        }
        List<String> ufZzdmys = new ArrayList<>();
        RecordSet ufZzdmysSearch = new RecordSet();
        String ufZzdmysSearchSql = "select formmodeid,modedatacreater,modedatacreatertype,MODEUUID,oagsmc,zzdm,gsmc from uf_zzdmys";
        ufZzdmysSearch.execute(ufZzdmysSearchSql);
        while (ufZzdmysSearch.next()) {
            ufZzdmys.add(ufZzdmysSearch.getString("oagsmc"));
        }


        for (String k : userInsert.keySet()) {
            if (ufSAPrenyuan.contains(k)) {
                update.add(userUpdate.get(k));
            } else {
                insert.add(userInsert.get(k));
            }
        }
        for (String k : depInsert.keySet()) {
            if (formTableMain306.contains(k)) {
                update.add(depUpdate.get(k));
            } else {
                insert.add(depInsert.get(k));
            }
        }
        for (String k : comInsert.keySet()) {
            if (ufZzdmys.contains(k)) {
                update.add(comUpdate.get(k));
            } else {
                insert.add(comInsert.get(k));
            }
        }

        for (String k : insert) {
            insertSet.executeUpdate(k);
        }
        for (String k : update) {
            updateSet.executeUpdate(k);
        }
    }
}

package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.SAPUtil;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 成本中心主数据获取
 */
public class CBZXData003Cron extends BaseCronJob {


    private String FUNID = "ZINF003";
    private RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        long time = new Date().getTime();
        String currDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String currTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        String params = "{\n" +
                "    \"CTRL\":{\n" +
                "        \"SYSID\":\"OA\",\n" +
                "        \"REVID\":\"SAP\",\n" +
                "        \"FUNID\":\"" + FUNID + "\",\n" +
                "        \"INFID\":\"" + time + "\",\n" +
                "        \"UNAME\":\"sysadmin\",\n" +
                "        \"DATUM\":\"" + currDate + "\",\n" +
                "        \"UZEIT\":\"" + currTime + "\",\n" +
                "        \"KEYID\":\"\",\n" +
                "        \"TABIX\":0,\n" +
                "        \"MSGTY\":\"\",\n" +
                "        \"MSAGE\":\"\"\n" +
                "    },\n" +
                "    \"DATA\":{\n" +
                "        \"BUKRS\":\"6000\",\n" +
                "        \"FKBER\":\"\"\n" +
                "    }\n" +
                "}";
        String execute = SAPUtil.execute(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
        String update = "";

        for (JSONObject datum : data) {
            String KOKRS = datum.getString("KOKRS");
            String KOSTL = datum.getString("KOSTL");
            String LTEXT = datum.getString("LTEXT");
            String FKBER = datum.getString("FKBER");
            String KOSAR = datum.getString("KOSAR");
            String type = getType(KOSAR);
            String DATBI = datum.getString("DATBI");
            String DATAB = datum.getString("DATAB");
            String BUKRS = datum.getString("BUKRS");
            String BKZKP = datum.getString("BKZKP");
            boolean b = selectCb(KOSTL);
            if(b){
                update += "更新:"+updateCb(KOKRS, KOSTL, LTEXT, FKBER, type, DATBI, DATAB, BUKRS, BKZKP)+",";

            }else {
                update += "新增:"+insertCb(KOKRS, KOSTL, LTEXT, FKBER, type, DATBI, DATAB, BUKRS, BKZKP)+",";
            }
        }
        rs.executeUpdate("insert into uf_sap (qqcs,xycs,gxjg,qqsj,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                params,execute,update,currDate,"17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_sap");
    }


    public String getType(String param) {
        String type = "";
        switch (param) {
            case "E":

                type = "研发";
                break;
            case "F":
                type = "生产";
                break;
            case "G":
                type = "后勤";
                break;
            case "H":
                type = "服务成本中心";
                break;
            case "L":
                type = "管理";
                break;
            case "M":
                type = "制造";
                break;
            case "S":
                type = "社会";
                break;
            case "V":
                type = "销售";
                break;
            case "W":
                type = "管理机构";
                break;

        }
        return type;
    }

    // 修改
    public boolean updateCb(String KOKRS, String KOSTL, String LTEXT, String KOSAR, String FKBER, String DATBI, String DATAB, String BUKRS, String BKZKP) {
        boolean b = rs.executeUpdate("update uf_cbzxzsj set KOKRS =?,KOSTL=?,LTEXT=?,KOSAR=?,FKBER=?,DATBI=?,DATAB=?,BUKRS=?,BKZKP=? where KOSTL = ? ",
                KOKRS, KOSTL, LTEXT, KOSAR, FKBER, DATBI, DATAB, BUKRS, BKZKP, KOSTL);
        return b;
    }


    // 新增
    public boolean insertCb(String KOKRS, String KOSTL, String LTEXT, String KOSAR, String FKBER, String DATBI, String DATAB, String BUKRS, String BKZKP) {
        boolean b = rs.executeUpdate("insert into uf_cbzxzsj ( KOKRS,KOSTL,LTEXT,KOSAR,FKBER,DATBI,DATAB,BUKRS,BKZKP,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",
                KOKRS, KOSTL, LTEXT, KOSAR, FKBER, DATBI, DATAB, BUKRS, BKZKP, "12", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_cbzxzsj");


        return b;
    }

    // 查询
    public boolean selectCb( String KOSTL) {
         RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  uf_cbzxzsj where KOSTL =?",KOSTL);


        return rs1.next();
    }


}



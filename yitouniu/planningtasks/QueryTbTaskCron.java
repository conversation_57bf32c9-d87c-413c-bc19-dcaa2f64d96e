package yitouniu.planningtasks;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.TeambitionBaseConfig;
import yitouniu.util.TeambitionHttpToken;
import yitouniu.util.TeambitionUtils;

import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/12/14 17:17
 * @Description TODO
 * @Version 1.0
 */
public class QueryTbTaskCron extends BaseCronJob {

    public static final String IT_TABLE_NAME = "formtable_main_91";

    public static final String UF_TB_TASK = "uf_tb_task";

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        Map<String, String> xqflSelectItem = new HashMap<>();
        rs.executeQuery("select * from workflow_SelectItem where fieldid = 26438");
        while(rs.next()){
            xqflSelectItem.put(rs.getString("selectvalue"), rs.getString("selectname"));
        }
        String taskSql = "SELECT\n" +
                "\ta.taskId,\n" +
                "\ta.xqmc,\n" +
                "\ta.ygrt,\n" +
                "\ta.xqfl ,\n" +
                "CASE\n" +
                "\t\ta.yxjpd \n" +
                "\t\tWHEN '0' THEN\n" +
                "\t\t'P0' \n" +
                "\t\tWHEN '1' THEN\n" +
                "\t\t'P1' \n" +
                "\t\tWHEN '2' THEN\n" +
                "\t\t'P2' \n" +
                "\t\tWHEN '3' THEN\n" +
                "\t\t'P3' \n" +
                "\tEND AS yxjpd,\n" +
                "\ta.xqdjr,\n" +
                "\ta.szxt,\n" +
                "\ta.xtmc,\n" +
                "\tb.tbr,\n" +
                "\tb.qwjjsj,\n" +
                "\tc.lastname AS tbrmc,\n" +
                "\td.departmentname,\n" +
                "\te.lastname AS xqdjrmc \n" +
                "FROM\n" +
                "\tformtable_main_91_dt1 a\n" +
                "\tLEFT JOIN formtable_main_91 b ON a.mainid= b.id\n" +
                "\tLEFT JOIN HrmResource c ON b.tbr= c.id\n" +
                "\tLEFT JOIN HrmDepartment d ON b.szbm= d.id\n" +
                "\tLEFT JOIN HrmResource e ON a.xqdjr= e.id \n" +
                "WHERE\n" +
                "\ta.xqfl IS NOT NULL \n" +
                "\tAND ( a.taskId IS NOT NULL OR a.taskId <> '' )";
        rs.executeQuery(taskSql);
        Map<String, Map<String, String>> taskMaps = new HashMap<>();
        List<String> taskIds = new ArrayList<>();
        while (rs.next()) {
            Map<String, String> o = new HashMap<>();
            String taskId = rs.getString("taskId");
            if (StringUtils.isBlank(taskId)){
                continue;
            }
            o.put("taskId", taskId);
            o.put("xqmc", rs.getString("xqmc"));
            o.put("xqfl", rs.getString("xqfl"));
            o.put("xqdjr", rs.getString("xqdjr"));
            o.put("szxt", rs.getString("szxt"));
            o.put("yxjpd", rs.getString("yxjpd"));
            o.put("xtmc", rs.getString("xtmc"));
            o.put("tbrmc", rs.getString("tbrmc"));
            o.put("departmentname", rs.getString("departmentname"));
            o.put("xqdjrmc", rs.getString("xqdjrmc"));
            o.put("qwjjsj", rs.getString("qwjjsj"));
            o.put("ygrt", rs.getString("ygrt"));
            taskIds.add(taskId);
            taskMaps.put(taskId, o);
        }
        new BaseBean().writeLog("execute：需要查询的taskIds = " + taskIds);
//        new BaseBean().writeLog("execute：taskMaps = " + taskMaps);

        String queryTaskIds = String.join(",", taskIds);
        //获取token
        String token = null;
        try {
            token = TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID, TeambitionBaseConfig.APP_SECRET);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        //非创建更新使用的headers
        Map<String, String> headers = TeambitionHttpToken.returnHeaders(token);
        //获取所有项目下，对应的自定义id对应name的Map
        List<String> projectIdList = returnProjectIdList();
        String projectIds = String.join(",", projectIdList);

        Map<String, Map<String,String>> taskTypes = new HashMap<>();
        for (String o : projectIdList){
            Map<String,String> map = new HashMap<>();
            if("63762c4b35b4694cc8685db4".equals(o) || "636481476c5c9a9857e59bf4".equals(o)){
                map = TeambitionUtils.returnTaskCfTypes(o, headers, "需求");
            } else {
                map = TeambitionUtils.returnTaskCfTypes(o, headers, "任务");
            }
            taskTypes.put(o, map);
        }

        //获取任务详情
        JSONArray resultArray = TeambitionUtils.returnTaskInfos(queryTaskIds, headers);
        try {

            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();

            new BaseBean().writeLog("execute：本次拉取的数量为：" + resultArray.size());
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject o = resultArray.getJSONObject(i);
                List insertData = new ArrayList<>();
                List updateData = new ArrayList<>();
                String taskId = o.getString("taskId");
                List dataList = new ArrayList<>();
                new BaseBean().writeLog("execute：当前为第"+(i+1)+"个，taskId = " + taskId);
                dataList.add(taskMaps.get(taskId).get("xqmc"));
                dataList.add(o.getString("content"));
                dataList.add(o.getString("note"));
                String projectId = o.getString("projectId");
                if(!projectIds.contains(projectId)){
                    continue;
                }
                dataList.add(projectId);
                String tfsId = o.getString("tfsId");
                dataList.add(tfsId);
                String gzlzt = TeambitionUtils.returnTaskflowStatusNameIdV3(tfsId, projectId, headers);
                dataList.add(gzlzt);
                dataList.add(o.getString("tasklistId"));
                String stageId = o.getString("stageId");
                dataList.add(stageId);
                dataList.add(o.getString("tagIds"));
                dataList.add(o.getString("creatorId"));
                dataList.add(o.getString("executorId"));
                dataList.add(o.getString("involveMembers"));
                dataList.add(o.getString("storyPoint"));
                dataList.add(o.getString("isDone"));
                dataList.add(o.getString("isArchived"));
                dataList.add(o.getString("visible"));
                dataList.add(o.getString("uniqueId"));
                dataList.add(o.getString("startDate"));
                dataList.add(o.getString("dueDate"));
                dataList.add(o.getString("accomplishTime"));
                dataList.add(o.getString("created"));
                dataList.add(o.getString("updated"));
                dataList.add(o.getString("sfcId"));
                String xqlx = "";
                String xqf = "";
                String xqbm = "";
                String xqdjr = "";
                String xqsqsj = "";
                String qwjjsj = "";
                String ygrt = "";
                String rwyxj = "";
//                JSONArray customfieldsArray = o.getJSONArray("customfields");
                if(taskTypes.get(projectId) == null ){
                    continue;
                }

                xqlx = xqflSelectItem.get(taskMaps.get(taskId).get("xqfl"));
                xqf = taskMaps.get(taskId).get("tbrmc");
                xqbm = taskMaps.get(taskId).get("departmentname");
                xqdjr = taskMaps.get(taskId).get("xqdjrmc");
                xqsqsj = o.getString("created");
                qwjjsj = taskMaps.get(taskId).get("qwjjsj");
                ygrt = taskMaps.get(taskId).get("ygrt");
                rwyxj = taskMaps.get(taskId).get("yxjpd");

                dataList.add(xqlx);
                dataList.add(xqf);
                dataList.add(xqbm);
                dataList.add(xqdjr);
                dataList.add(xqsqsj);
                dataList.add(qwjjsj);
                dataList.add(ygrt);
                dataList.add(rwyxj);
                dataList.add(taskMaps.get(taskId).get("xtmc"));
                String stageName = TeambitionUtils.returnStageNameV3(projectId, stageId, headers);
                dataList.add(stageName);

                int id = selectTbTask(taskId);
                if (id > 0) {
                    CollectionUtils.addAll(updateData, new Object[dataList.size()]);
                    Collections.copy(updateData, dataList);
                    updateData.add(id);
                    updateAllData.add(updateData);
                } else {
                    CollectionUtils.addAll(insertData, new Object[dataList.size()]);
                    Collections.copy(insertData, dataList);
                    insertData.add(0, taskId);
                    insertAllData.add(insertData);
                }

            }
            if (insertAllData.size() > 0) {
                baseBean.writeLog("QueryTbTaskCron: insertAllData新增：" + insertAllData.size());
                flag += insertTbTask(insertAllData) + ",";
            }

            if (updateAllData.size() > 0) {
                baseBean.writeLog("QueryTbTaskCron: updateAllData更新：" + updateAllData.size());
                flag += updateTbTask(updateAllData) + ",";
            }
            if (flag.contains("false")) {
                baseBean.writeLog("处理数据失败");
            } else {
                baseBean.writeLog(" 处理数据成功");
            }
        } catch (Exception e) {
            baseBean.writeLog("异常错误",e);
        }

    }


    public int selectTbTask(String taskId) {
        RecordSet rs1 = new RecordSet();
        int id = -1;
        rs1.executeQuery("select id from " + UF_TB_TASK + " where rwid =?", taskId);
        if (rs1.next()) {
            id = rs1.getInt("id");
        }
        return id;
    }

    public boolean updateTbTask(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("update " + UF_TB_TASK + " set oasrwmc =?,tbsrwmc =?,rwbz =?,xmid =?,rwztid =?,gzlzt =?,rwfzid =?,rwlid =?,bqidjh =?," +
                "cjrid =?,zxrid =?,cyzidjh =?,storypoint =?,sfrwywc =?,sfrwfrhsz =?,rwysx =?,rwszid =?,rwkssjutc =?,rwjzsjutc =?," +
                "rwwcsjutc =?,cjsjutc =?,gxsjutc =?,rwlxid =?,xqlx =?,xqf =?,xqbm =?,xqdjr =?,xqsqsj =?,qwjjsj =?,ygrt=?," +
                "rwyxj =?,szxm =?,rwlmc=? where id = ?", list);
    }

    public boolean insertTbTask(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("insert into " + UF_TB_TASK + " (rwid, oasrwmc, tbsrwmc, rwbz, xmid, rwztid, gzlzt, rwfzid, rwlid, bqidjh, " +
                "cjrid, zxrid, cyzidjh , storypoint, sfrwywc, sfrwfrhsz, rwysx, rwszid, rwkssjutc, rwjzsjutc, " +
                "rwwcsjutc, cjsjutc, gxsjutc, rwlxid, xqlx, xqf, xqbm, xqdjr, xqsqsj, qwjjsj, ygrt, " +
                "rwyxj, szxm, rwlmc) " +
                "values (?,?,?,?,?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?, " +
                "?,?,?,?,?,?,?,?,?,?,?, " +
                "?,?,?) ", list);
    }

    public List<String> returnProjectIdList(){
        List<String> list = new ArrayList<>();
        list.add("63743a25fa424151c03cab1d");//基础运维项目
        list.add("6375fd5dc321f22adfc59cb6");//旺店通项目
        list.add("6375ffcf35b4694cc8681280");//WMS项目
        list.add("636481476c5c9a9857e59bf4");//业务中台迭代运维开发项目
        list.add("6376265fb9bc882d91c9fead");//溯源项目
        list.add("6375fffec151e9f0498aba6e");//SRM项目
        list.add("6376005f963a091420655fe7");//中间件项目
        list.add("63774452a456f16b1832b0e7");//金蝶项目
        list.add("63773cf1003cea325a8a5585");//SAP项目
        list.add("63760183b722ed7092ff6516");//数智化财务项目
        list.add("63762f2e3997deea0518fdd8");//POS项目
        list.add("63760499f1925c588575f66a");//大数据项目
        list.add("63762c4b35b4694cc8685db4");//会员商城项目
        list.add("62ce26b955f89d363cea065f");//数据中台周任务

        return list;
    }



}

package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import yitouniu.util.SAPUtil;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/12/16 10:43
 * @Description TODO
 * @Version 1.0
 */
public class WLZSJCalibrateAction implements Action{

    private final static String FUNID = "ZINF063";


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tablename = requestInfo.getRequestManager().getBillTableName();
        Date date = new Date();
        String currDate = new SimpleDateFormat("yyyy-MM-dd").format(date);
        String currTime = new SimpleDateFormat("HH:mm:ss").format(date);
        //类型
        String lx = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("lx".equals(name)) {
                lx = Util.null2String(properties[i].getValue());
            }
        }
        //若不为新增
        if(!"0".equals(lx)){
            return SUCCESS;
        }

        //明细
        Map<String ,String> map = null ;
        List<Map<String,String>> mapList = new ArrayList<>();
        DetailTable dt = requestInfo.getDetailTableInfo().getDetailTable()[0];
        Row[] s = dt.getRow();
        for (int j = 0; j < s.length; j++) {
            Row r = s[j];
            Cell c[] = r.getCell();
            map = new HashMap<>();
            for (int k = 0; k < c.length; k++) {
                Cell c1 = c[k];
                String name = c1.getName();
                String value = c1.getValue();
                if(StringUtils.isBlank(value)){
                    continue;
                }
                if("MAKTX".equals(name)){
                    map.put("MAKTX",value);
                }else if("BCODE".equals(name)){
                    boolean result=value.matches("[0-9]+");
                    if(!result){
                        requestInfo.getRequestManager().setMessagecontent("第"+(j+1)+"行的条码不为纯数字，请检查");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                    map.put("BCODE",value);
                }else if("MATNR".equals(name)) {
                    boolean result=value.matches("[0-9]+");
                    if(!result){
                        requestInfo.getRequestManager().setMessagecontent("第"+(j+1)+"行的物料编码不为纯数字，请检查");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }map.put("MATNR", value);
                }
            }
            mapList.add(map);
        }
        JSONObject request = new JSONObject();
        JSONObject ctrl = getCtrlJson(requestid, currDate, currTime);
        JSONArray data = new JSONArray();
        //类型  T条码、W物料号、M描述
        for(int i = 0 ; i<mapList.size();i++){
            Map<String,String> o = mapList.get(i);
            if(StringUtils.isNotBlank(o.get("MATNR"))){
                JSONObject p = new JSONObject();
                p.put("ZNO", i+1);
                p.put("ZTYPE", "W");
                p.put("ZCODE", o.get("MATNR"));
                data.add(p);
            }
            if(StringUtils.isNotBlank(o.get("MAKTX"))){
                JSONObject p = new JSONObject();
                p.put("ZNO", i+1);
                p.put("ZTYPE", "M");
                p.put("ZCODE", o.get("MAKTX"));
                data.add(p);
            }
            if(StringUtils.isNotBlank(o.get("BCODE"))){
                JSONObject p = new JSONObject();
                p.put("ZNO", i+1);
                p.put("ZTYPE", "T");
                p.put("ZCODE", o.get("BCODE"));
                data.add(p);
            }
        }
        request.put("CTRL", ctrl);
        request.put("DATA", data);

        String execute = SAPUtil.execute(request.toJSONString());
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject resultCtrl = jsonObject.getJSONObject("CTRL");
        String errorMessage = "";
        List<JSONObject> resultData = (List<JSONObject>) jsonObject.get("DATA"); // 获取响应结果
        if(!"S".equals(resultCtrl.getString("MSGTY"))){
            for (JSONObject result : resultData) {
                String zmsgty = result.getString("ZMSGTY");
                String ztype = result.getString("ZTYPE");
                String zno = result.getString("ZNO");
                if (!"S".equals(zmsgty)) {
                    if("T".equals(ztype)){
                        errorMessage = errorMessage + "第【"+zno+"】行的条码重复，";
                    } else if("W".equals(ztype)){
                        errorMessage = errorMessage + "第【"+zno+"】行的物料号重复，";
                    } else if("M".equals(ztype)){
                        errorMessage = errorMessage + "第【"+zno+"】行的物料描述重复，";
                    }
                }
            }
        }
        if(errorMessage.length() > 0){
            requestInfo.getRequestManager().setMessagecontent(errorMessage);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }


    public JSONObject getCtrlJson(String requestId, String currDate, String currTime){
        JSONObject result = new JSONObject();
        result.put("SYSID","OA");
        result.put("REVID","SAP");
        result.put("FUNID",FUNID);
        result.put("INFID",requestId);
        result.put("UNAME","sysadmin");
        result.put("DATUM",currDate);
        result.put("UZEIT",currTime);
        result.put("KEYID","");

        return result;
    }


}

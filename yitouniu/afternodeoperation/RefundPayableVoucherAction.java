package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 其他应付退款推送凭证
 */
public class RefundPayableVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();
    private String hrmName = "";

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");

        String mainid = "";
        String NAME1 = ""; // 收款单位名称
        String VBUND = ""; // 贸易伙伴
        String PARTNER = ""; // 收款单位编码
        String bz = ""; // 备注
        String lcbh = ""; // 流程编号
        String tklx = ""; // 退款类型
        String bankNum = ""; // 退款类型
        String UNAME = ""; // 申请人


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");
            PARTNER = rs1.getString("PARTNER");
            VBUND = rs1.getString("VBUND");
            NAME1 = rs1.getString("NAME1");
            bz = rs1.getString("BKTXT");
            lcbh = rs1.getString("ZXBLN");
            tklx = rs1.getString("tklx");
            bankNum = rs1.getString("BANKNUM");
            UNAME = rs1.getString("UNAME");

        }
        hrmName = ActionUtil.getHrmName(UNAME);

        if (!"1".equals(tklx)) { // 不是员工押金

            String SGTXT = hrmName+"申请--退"+NAME1+"保证金";
            // 删除明细表1
            sql = "DELETE FROM " + tableName + "_dt2  WHERE mainid = ?";
            new BaseBean().writeLog("--->明细表三删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");


            // 查询明细表1的数据

            List<String> je = new ArrayList<>();//金额


            sql = "select * from " + tableName + "_dt1 where mainid = ?";
            rs1.executeQuery(sql, mainid);
            while (rs1.next()) {

                je.add(rs1.getString("WRBTR"));


            }
            String totalMoney = "0.0";
            for (int i = 0; i < je.size(); i++) {
                String money = je.get(i);
                //totalMoney += Double.valueOf(money);
                totalMoney =  ActionUtil.bigDecimalAdd(totalMoney,money);
                // 借方数据
                ActionUtil.insertData(tableName + "_dt2", "", "", "", "", SGTXT, money, "", "29", "2", "", "", PARTNER, VBUND, mainid, "KZ");

            }


            //贷方数据
            ActionUtil.insertData(tableName + "_dt2", "", bankNum, "103", "", SGTXT, totalMoney , "", "50", "", "", "", "", "", mainid, "KZ");

        }

        return SUCCESS;
    }


}

package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 核销流程冻结金额
 */
public class WriteOffDJMoneyAction extends WriteOffMoney implements Action {
    private RecordSet recordSet = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid(); // 获取requestid

        new BaseBean().writeLog(requestid + "WriteOffDJMoneyAction开始");
        try {

       /* new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");


        String sql = "select  *  from  " + tableName + "_dt1  where mainid = (select  id  from  " + tableName + "   where requestid = ?)";
        List<String> xzyfkList = new ArrayList<>(); // 选择预付款
        List<String> yfkhxhje1List = new ArrayList<>(); // 核销后金额
        List<String> yfkbchxje1List = new ArrayList<>(); // 本次核销金额

        recordSet.executeQuery(sql, requestid);
        while (recordSet.next()) {

            xzyfkList.add(recordSet.getString("xzyfk"));
            yfkhxhje1List.add(recordSet.getString("yfkhxhje1"));
            yfkbchxje1List.add(recordSet.getString("yfkbchxje1"));


        }*/
            super.getData(requestid);
            boolean ishx = super.isIshx();
             List<String> xzyfkList = super.getXzyfkList(); // 选择预付款
            List<String> yfkhxhje1List = super.getYfkhxhje1List(); // 预付款核销后金额
            List<String> yfkbchxje1List = super.getYfkbchxje1List(); // 预付款本次核销金额
            new  BaseBean().writeLog("shuju :"+ishx+","+xzyfkList+","+yfkhxhje1List+","+yfkbchxje1List );
            //shuju :true,[1042, 1343, 1424],[0, 0, 286560.18],[198798.03, 400000, 13439.82]
            if (ishx) {
                if (xzyfkList.size() > 0) {
                    for (int i = 0; i < xzyfkList.size(); i++) {
                        Map<String, String> findyfje = ActionUtil.findyfje(xzyfkList.get(i));
                        String djje = findyfje.get("djje"); // 冻结金额
                        String yfksyje = findyfje.get("yfksyje"); // 本次预付金额

                        String hxje = yfkhxhje1List.get(i);
                        if ("".equals(hxje) || hxje == null || Double.valueOf(hxje) < 0) {
                            requestInfo.getRequestManager().setMessageid(requestid);
                            requestInfo.getRequestManager().setMessage("核销后金额不能为空或者为负数");
                            return FAILURE_AND_CONTINUE;
                        } else {
                            double hxjeDouble = Double.valueOf(hxje);
                            String bcheje = yfkbchxje1List.get(i); // 本次核销金额

                                if (!"".equals(djje) && djje != null) {
                                    bcheje = (Double.valueOf(djje) + Double.valueOf(bcheje)) + "";
                                }
                                // 冻结为本次核销金额   本次预付为本次核销金额
                                recordSet.executeUpdate("update uf_yufukuanku set yfksyje = ?, djje = ? where id= ?", hxjeDouble, bcheje, xzyfkList.get(i));


                        }
                    }
                }
            }
        } catch (Exception e) {
            new BaseBean().writeLog("WriteOffDJMoneyAction异常信息" + e);
        }


        return SUCCESS;
    }
}

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiAlitripBtripApprovalNewRequest;
import com.dingtalk.api.request.OapiAlitripBtripCostCenterQueryRequest;
import com.dingtalk.api.request.OapiAlitripBtripInvoiceSearchRequest;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiUserGetByMobileRequest;
import com.dingtalk.api.request.OapiAlitripBtripApprovalNewRequest.OpenApiNewApplyRq;
import com.dingtalk.api.request.OapiAlitripBtripApprovalNewRequest.OpenItineraryInfo;
import com.dingtalk.api.request.OapiAlitripBtripApprovalNewRequest.OpenUserInfo;
import com.dingtalk.api.request.OapiAlitripBtripCostCenterQueryRequest.OpenCostCenterQueryRq;
import com.dingtalk.api.request.OapiAlitripBtripInvoiceSearchRequest.OpenInvoiceRq;
import com.dingtalk.api.response.OapiAlitripBtripApprovalNewResponse;
import com.dingtalk.api.response.OapiAlitripBtripCostCenterQueryResponse;
import com.dingtalk.api.response.OapiAlitripBtripInvoiceSearchResponse;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiUserGetByMobileResponse;
import com.taobao.api.ApiException;
import com.taobao.api.internal.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

public class AliTravelAction implements Action {
    public AliTravelAction() {
    }

    @Override
    public String execute(RequestInfo request) {
        String requestid = request.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, new Object[]{requestid});
        if (main.next()) {
            String token = getToken();
            String mainId = main.getString("id");
            String itemSql = "select * from " + tableName + "_dt1 where mainid = ?";
            item.executeQuery(itemSql, new Object[]{mainId});
            String lcbh = main.getString("lcbh");
            String szgs = main.getString("szgs");
            String szbm = main.getString("szbm");
            String ccsy1 = main.getString("ccsy");
            String htxt1 = ccsy1.replaceAll("<br>", "");
            String ccsy = htxt1.replaceAll("&nbsp;", "");
            String userId = main.getString("sqr");
            String txrUserIds = main.getString("cctxr");
            (new BaseBean()).writeLog("txrUserIds" + txrUserIds);
            String[] txrUserId = txrUserIds.split(",");
            int i = 0;

            while(item.next()) {
                try {
                    String startTime = item.getString("ksrq") + " " + item.getString("kssj") + ":00";
                    String endTime = item.getString("jsrq") + " " + item.getString("jssj") + ":00";
                    String dcwf = item.getString("dcwf");
                    String jtgj = item.getString("jtgj");
                    String cfcs = item.getString("cfcs");
                    String mdcs = item.getString("mdcs");
                    String ccsc = main.getString("ccsc");
                    RecordSet userInfo = this.getUserInfo(userId);
                    String mobile = "";
                    String lastname = "";
                    if (userInfo.next()) {
                        mobile = userInfo.getString("mobile");
                        lastname = userInfo.getString("lastname");
                    }

                    String alUserId = getUserId(mobile, token);
                    (new BaseBean()).writeLog("0" + alUserId);
                    DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/approval/new");
                    (new BaseBean()).writeLog("0111");
                    OapiAlitripBtripApprovalNewRequest req = new OapiAlitripBtripApprovalNewRequest();
                    (new BaseBean()).writeLog("02222");
                    OpenApiNewApplyRq obj1 = new OpenApiNewApplyRq();
                    (new BaseBean()).writeLog("03333" + ccsc);
                    obj1.setTripDay(Double.valueOf(ccsc).longValue());
                    (new BaseBean()).writeLog("1" + ccsc);
                    obj1.setThirdpartApplyId(requestid + i);
                    (new BaseBean()).writeLog("2" + requestid + i);
                    obj1.setTripTitle("商旅V2" + lastname + item.getString("ksrq"));
                    (new BaseBean()).writeLog("3" + ccsy);
                    List<OpenItineraryInfo> list3 = new ArrayList();
                    OpenItineraryInfo obj4 = new OpenItineraryInfo();
                    list3.add(obj4);
                    obj4.setTripWay(Long.parseLong(dcwf));
                    (new BaseBean()).writeLog("7" + dcwf);
                    obj4.setItineraryId(requestid + i);
                    obj4.setTrafficType(Long.parseLong(jtgj));
                    (new BaseBean()).writeLog("9" + jtgj);
                    obj4.setDepCity(cfcs);
                    obj4.setArrCity(mdcs);
                    obj4.setCostCenterId(Long.parseLong(getCostCenter(alUserId, token)));
                    (new BaseBean()).writeLog("setCostCenterIdsetCostCenterIdsetCostCenterIdsetCostCenterIdsetCostCenterId" + obj4.getCostCenterId());
                    obj4.setInvoiceId(Long.parseLong(getInvoiceId(alUserId, "dinga9e6db0a7491905135c2f4657eb6378f", token)));
                    (new BaseBean()).writeLog("setInvoiceIdsetInvoiceIdsetInvoiceIdsetInvoiceIdsetInvoiceId" + obj4.getInvoiceId());
                    obj4.setDepDate(StringUtils.parseDateTime(startTime));
                    obj4.setArrDate(StringUtils.parseDateTime(endTime));
                    obj1.setItineraryList(list3);
                    obj1.setDeptName(szbm);
                    obj1.setTripCause(ccsy);
                    obj1.setCorpName(szgs);
                    obj1.setUserid(alUserId);
                    obj1.setUserName(lastname);
                    List<OpenUserInfo> list6 = new ArrayList();
                    OpenUserInfo obj7 = new OpenUserInfo();
                    obj7.setUserid(alUserId);
                    obj7.setUserName(lastname);
                    list6.add(obj7);
                    String[] var39 = txrUserId;
                    int var40 = txrUserId.length;

                    for(int var41 = 0; var41 < var40; ++var41) {
                        String txr = var39[var41];
                        (new BaseBean()).writeLog("txrtxrtxrtxrtxrtxrtxrtxrtxr" + txr);
                        OpenUserInfo txrInfo = new OpenUserInfo();
                        RecordSet userInfoCxr = this.getUserInfo(txr);
                        if (userInfoCxr.next()) {
                            String alUserIdTxr = getUserId(userInfoCxr.getString("mobile"), token);
                            txrInfo.setUserid(alUserIdTxr);
                            txrInfo.setUserName(userInfoCxr.getString("lastname"));
                            (new BaseBean()).writeLog("alUserIdTxr" + txrInfo.getUserid());
                            (new BaseBean()).writeLog("lastnameTxr" + txrInfo.getUserName());
                            list6.add(txrInfo);
                        }
                    }

                    obj1.setTravelerList(list6);
                    obj1.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
                    obj1.setStatus(1L);
//                    obj1.setThirdpartBusinessId(requestid + i);
                    req.setRq(obj1);
                    (new BaseBean()).writeLog("req" + req.getRq());
                    OapiAlitripBtripApprovalNewResponse rsp = (OapiAlitripBtripApprovalNewResponse)client.execute(req, token);
                    (new BaseBean()).writeLog("rspbody" + rsp.getBody());
                    (new BaseBean()).writeLog("rspErrmsg" + rsp.getErrmsg());
                    ++i;
                } catch (ApiException var46) {
                    var46.printStackTrace();
                    return "0";
                }
            }
        }

        return "1";
    }

    public static String getToken() {
        String res = "";
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey("ding370bf6nydf1ztuo7");
        request.setAppsecret("ar71FOl64xb7UTxvylPdw0VkBRXD5qdgD7gEg6CWiAoe0OW1MeMnYsRu5E-SsRmd");
        request.setHttpMethod("GET");

        try {
            OapiGettokenResponse response = (OapiGettokenResponse)client.execute(request);
            res = response.getAccessToken();
            return res;
        } catch (ApiException var7) {
            var7.printStackTrace();
            return res;
        } finally {
            ;
        }
    }

    public static String getUserId(String mobile, String token) {
        (new BaseBean()).writeLog("mobilemobilemobilemobiletokentokentokentoken" + mobile + "   " + token);
        String res = "";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get_by_mobile");
        OapiUserGetByMobileRequest request = new OapiUserGetByMobileRequest();
        request.setMobile(mobile);

        try {
            OapiUserGetByMobileResponse execute = (OapiUserGetByMobileResponse)client.execute(request, token);
            res = execute.getUserid();
            (new BaseBean()).writeLog("getUseridgetUseridgetUseridgetUseridgetUserid" + res);
            return res;
        } catch (ApiException var9) {
            var9.printStackTrace();
            return res;
        } finally {
            ;
        }
    }

    public RecordSet getUserInfo(String userid) {
        RecordSet recordSet = new RecordSet();
        String sql = "select *  from  hrmresource where id = ?";
        recordSet.executeQuery(sql, new Object[]{userid});
        return recordSet;
    }

    public static String getInvoiceId(String userId, String corpId, String access_token) {
        String id = "";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/invoice/search");
        OapiAlitripBtripInvoiceSearchRequest req = new OapiAlitripBtripInvoiceSearchRequest();
        OpenInvoiceRq obj1 = new OpenInvoiceRq();
        obj1.setUserid(userId);
        obj1.setCorpid(corpId);
        req.setRq(obj1);
        OapiAlitripBtripInvoiceSearchResponse rsp = null;

        try {
            rsp = (OapiAlitripBtripInvoiceSearchResponse)client.execute(req, access_token);
            JSONObject json = JSONObject.parseObject(rsp.getBody());
            JSONArray arr = json.getJSONArray("invoice");
            JSONObject rs = JSONObject.parseObject(String.valueOf(arr.get(0)));
            id = String.valueOf(rs.get("id"));
            return id;
        } catch (ApiException var14) {
            var14.printStackTrace();
            return id;
        } finally {
            ;
        }
    }

    private static String getCostCenter(String userId, String token) {
        String id = "";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/cost/center/query");
        OapiAlitripBtripCostCenterQueryRequest req = new OapiAlitripBtripCostCenterQueryRequest();
        OpenCostCenterQueryRq obj1 = new OpenCostCenterQueryRq();
        obj1.setUserid(userId);
        obj1.setNeedOrgEntity(false);
        obj1.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
        req.setRq(obj1);
        (new BaseBean()).writeLog("bbbbbbbbreqreqreqreqreqreqreqaaaa" + req);
        OapiAlitripBtripCostCenterQueryResponse rsp = null;

        try {
            rsp = (OapiAlitripBtripCostCenterQueryResponse)client.execute(req, token);
        } catch (ApiException var10) {
            var10.printStackTrace();
        }

        (new BaseBean()).writeLog("rsprsprsprsprspaaaaaaaaa" + rsp);
        JSONObject json = JSONObject.parseObject(rsp.getBody());
        JSONArray arr = json.getJSONArray("cost_center_list");
        JSONObject rs = JSONObject.parseObject(String.valueOf(arr.get(0)));
        id = String.valueOf(rs.get("id"));
        return id;
    }
}

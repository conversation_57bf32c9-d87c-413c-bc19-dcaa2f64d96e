package yitouniu.afternodeoperation;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.CreateJxWorkflowUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/24 16:21
 * @Description 个人绩效归档Action
 * @Version 1.0
 */
public class JXPersonalEvaluateArchivePreAction implements Action {

    public static String uf_grjxxx = "uf_grjxxx";

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        String jxlx = "";
        String nf = "";
        //月度或季度
        String time = "";
        String yjbmmc = "";
        String yjbmfzr = "";
        String gjzg = "";

        String updateTableName = "";
        Map<String ,String> bmJXMap = new HashMap<>();

        if(main.next()){
            //mainId
            String mainId = main.getString("id");
            //制定流程的id
            String zdlcId = main.getString("zdlc");
            bmJXMap.put("zdlcId", zdlcId);
            //绩效评价流程
            String jxpjlcId = requestid;
            //价值观得分
            String jzgdf = main.getString("jzgdf");
            //部门（即个人）业绩得分
            String bmjgryjdf = main.getString("bmjgryjdf");
            //系统结果
            String grjxjg = main.getString("grjxjg");
            if(StringUtils.isBlank(grjxjg)){
                grjxjg = null;
            }
//            //一轮复核结果
//            String ylfhjg = main.getString("ylfhjg");
            //一级部门负责人
            yjbmfzr = main.getString("yjbmfzr");
            bmJXMap.put("yjbmfzr", yjbmfzr);
            //一级部门名称id
            yjbmmc = main.getString("yjbmmc");
            bmJXMap.put("yjbmmc", yjbmmc);
            //绩效类型
            jxlx = main.getString("jxlx");
            bmJXMap.put("jxlx", jxlx);
            //年份
            nf = main.getString("nf");
            bmJXMap.put("nf", nf);

            //隔级主管
            bmJXMap.put("gjzg",main.getString("gjzg"));

            //0 : 季度
            if("0".equals(jxlx)){
                updateTableName = "uf_bmjdjx";
                time = main.getString("jd");
            } else {
                updateTableName = "uf_bmydjxxx";
                time = main.getString("yd");
            }
            bmJXMap.put("time", time);
            String updateJXVerifyTableSql = "update " + updateTableName + "_dt1 set jxpjlc = ?, jzgdf = ?, okrdf = ?, xtjg = ? where jxzdlc = ?";
            boolean updateJXVerifyTableSqlFlag = rs.executeUpdate(updateJXVerifyTableSql, jxpjlcId, jzgdf, bmjgryjdf, grjxjg, zdlcId);
            if(!updateJXVerifyTableSqlFlag){
                requestInfo.getRequestManager().setMessagecontent("更新部门核对表信息失败");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

        }

//        //如果这个一级部门下没有人需要个人评价了的话，触发创建部门绩效评价流程
//        boolean queryFlag = query(updateTableName, yjbmmc, nf, time);
//        baseBean.writeLog("该一级部门下该年份该时间内，是否还有人没触发 queryFlag = " +queryFlag);
//        if(!queryFlag){
//            int jxxxId = CreateJxWorkflowUtil.select(updateTableName, yjbmmc, nf, time);
//            bmJXMap.put("bmjxxxhdbid", String.valueOf(jxxxId));
//            //获取一级部门负责人的所属部门
//            int yjbmfzrDepId = queryDepId(yjbmfzr);
//            bmJXMap.put("yjbmfzrDepId", String.valueOf(yjbmfzrDepId));
//            //获取明细
//            List<Map<String, String>> itemMapList = queryByMainId(updateTableName, jxxxId);
//            baseBean.writeLog("一级部门bmJXMap = " + bmJXMap);
//            baseBean.writeLog("一级部门下的明细itemMapList = " + itemMapList);
//            if(itemMapList.size() >0){
//                //创建部门绩效评价流程
//                boolean flag = CreateJxWorkflowUtil.createWorkflowForFirstDepartmentEvaluate(bmJXMap, itemMapList, jxlx);
//                if(!flag){
//                    requestInfo.getRequestManager().setMessagecontent("创建部门绩效评价流程失败");
//                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
//                    return FAILURE_AND_CONTINUE;
//                }
//            } else {
//                requestInfo.getRequestManager().setMessagecontent("该一级部门下没有个人评价绩效流程明细");
//                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
//                return FAILURE_AND_CONTINUE;
//            }
//        } else {
//            baseBean.writeLog("该一级部门下还有个人评价绩效流程未走完。");
//        }

        return SUCCESS;
    }

    /**
     * 查询这个一级部门下是否还有人没有评价完毕
     * @param updateTableName
     * @param yjbmmc
     * @param jxsznf
     * @param jxszyf
     * @return
     */
    public boolean query(String updateTableName, String yjbmmc, String jxsznf, String jxszyf){
        String querySql = "select a.id " +
                " from " + updateTableName + "_dt1 a " +
                " left join " + updateTableName + " b on a.mainid = b.id " +
                " where a.xtjg is null and b.yjbmmc = ? and b.jxsznf = ? and b.jxszyf = ? ";
        rs.executeQuery(querySql, yjbmmc, jxsznf, jxszyf);
        return rs.next();
    }

    /**
     * 通过id获取所有明细
     * @param updateTableName
     * @param mainId
     * @return
     */
    public static List<Map<String, String>> queryByMainId(String updateTableName, int mainId){
        List<Map<String, String>> resultList = new ArrayList<>();
        String sql = "select a.id,a.xm,a.jxpjlc,a.jzgdf,a.okrdf,a.xtjg,a.bm,b.jobtitle,b.lastname from " + updateTableName + "_dt1 a  left join HrmResource b on a.xm = b.id where a.mainid = ?";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, mainId);
        while(rs.next()){
            Map<String, String> map = new HashMap<>();
            //明细id
            map.put("mxid", rs.getString("id"));
            //姓名
            map.put("xm", rs.getString("xm"));
            //绩效评价流程
            map.put("jxpjlc", rs.getString("jxpjlc"));
            //价值观得分
            map.put("jzgdf", rs.getString("jzgdf"));
            //okr得分
            map.put("okrdf", rs.getString("okrdf"));
            //系统结果
            map.put("xtjg", rs.getString("xtjg"));
            //部门
            map.put("bm", rs.getString("bm"));
            //岗位
            map.put("gw", rs.getString("jobtitle"));
            //姓名名称
            map.put("lastname", rs.getString("lastname"));
            resultList.add(map);
        }
        return resultList;
    }

    public int queryDepId(String id){
        int depId = -1;
        rs.executeQuery("select departmentid from HrmResource where id = ?", id);
        if(rs.next()){
            depId = rs.getInt("departmentid");
        }
        return depId;
    }





}

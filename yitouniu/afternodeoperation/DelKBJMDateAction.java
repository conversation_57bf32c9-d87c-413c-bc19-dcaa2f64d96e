package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

/**
 * 牧场游退回删除建模数据
 */
public class DelKBJMDateAction implements Action {
    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid();

        recordSet.executeUpdate("delete from uf_CDYYJL where lcid = ?",requestid);

        return SUCCESS;
    }
}

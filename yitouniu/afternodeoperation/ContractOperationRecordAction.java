package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/6/22 18:41
 * @Description 合同操作记录入表
 * @Version 1.0
 */
public class ContractOperationRecordAction implements Action {

    private final static String DATA_BASE_NAME = "uf_htczjl";
    private final static String STAMP_TIME = "stamp_time";
    private final static String CONTRACT_UPLOAD_TIME = "contract_upload_time";
    private final static String CONTRACT_ARCHIVE_TIME = "contract_archive_time";


    @Override
    public String execute(RequestInfo requestInfo) {
        // 流程id
        String requestid = requestInfo.getRequestid();
        //主表
        RecordSet main = new RecordSet();
        //获取表名
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String) tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        //检验这条流程是否已经插入过记录
        boolean isSelectFlag = selectByRequestId(requestid);
        new BaseBean().writeLog("ContractOperationRecordAction：isSelectFlag = " + isSelectFlag);

        boolean resultFlag = false;

        new BaseBean().writeLog("ContractOperationRecordAction：tableName = " + tableName);
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");

        if (main.next()) {
            String operateType = main.getString("operateType");
            new BaseBean().writeLog("ContractOperationRecordAction：operateType = " + operateType);
            switch (operateType){
                case "0" :
                    resultFlag = insertTimeByOperateType(STAMP_TIME, requestid, isSelectFlag);
                    break;
                case "1" :
                    resultFlag = insertTimeByOperateType(CONTRACT_UPLOAD_TIME, requestid, isSelectFlag);
                    break;
                case "2" :
                    resultFlag = insertTimeByOperateType(CONTRACT_ARCHIVE_TIME, requestid, isSelectFlag);
                    break;
                default:
                    new BaseBean().writeLog("ContractOperationRecordAction：合同流程操作类型operateType = " + operateType);
                    break;
            }
        }
        new BaseBean().writeLog("ContractOperationRecordAction：resultFlag = " + resultFlag);

        if(!resultFlag){
            new BaseBean().writeLog("ContractOperationRecordAction：合同流程操作记录时间更新失败operateType = " + resultFlag);
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }

    /**
     * 按照字段名，类型，更新三个时间
     * @param fieldName
     * @param requestId
     * @param createFlag
     * @return
     */
    public boolean insertTimeByOperateType(String fieldName, String requestId, boolean createFlag){
        new BaseBean().writeLog("ContractOperationRecordAction：fieldName = " + fieldName);
        new BaseBean().writeLog("ContractOperationRecordAction：requestId = " + requestId);
        new BaseBean().writeLog("ContractOperationRecordAction：createFlag = " + createFlag);
        if(!createFlag){
            boolean flag = insertRecord(requestId);
            String id = selectIdByRequestId(requestId);
            ModeRightInfo modeRightInfo = new ModeRightInfo();
            modeRightInfo.setNewRight(true);
            modeRightInfo.editModeDataShare(1,112, Integer.parseInt(id));
            new BaseBean().writeLog("ContractOperationRecordAction：插入一条新流程记录，插入标记flag = " + flag);
        }
        RecordSet rs = new RecordSet();
        String sql = "update " + DATA_BASE_NAME + " set " + fieldName + " = ? where process_id = ? ";
        return rs.executeUpdate(sql,String.valueOf(new SimpleDateFormat("yyyy-MM-dd").format(new Date())),requestId);
    }

    /**
     * 查找该流程记录是否存在
     * @param requestId
     * @return
     */
    public boolean selectByRequestId(String requestId) {
        RecordSet rs = new RecordSet();
        String sql = "select * from " + DATA_BASE_NAME + " where process_id = ?";
        rs.executeQuery(sql, requestId);
        return rs.next();
    }

    /**
     * 查找该流程记录的id
     * @param requestId
     * @return
     */
    public String  selectIdByRequestId(String requestId) {
        RecordSet rs = new RecordSet();
        String id = null;
        String sql = "select * from " + DATA_BASE_NAME + " where process_id = ?";
        rs.executeQuery(sql, requestId);
        if(rs.next()){
            id = rs.getString("id");
        }
        return id;
    }

    /**
     * 插入一条流程操作纪录
     * @param requestId
     * @return
     */
    public boolean insertRecord(String requestId){
        String createId = "";
        String departmentId = "";
        String name = "";
        String employeeId = "";
        String processEname = "";
        String processEcode = "";
        String processCreateTime = "";
        String allDepartmentEname = "";
        String depId = "";
        String companyId = "";
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from workflow_requestbase where requestid = ? ", requestId);
        if(rs1.next()){
            createId = rs1.getString("creater");
            processEname = rs1.getString("requestname");
            processEcode = rs1.getString("requestmark");
            processCreateTime = rs1.getString("createdate");
        }
        new BaseBean().writeLog("ContractOperationRecordAction：createId = " + createId);
        new BaseBean().writeLog("ContractOperationRecordAction：processEname = " + processEname);
        new BaseBean().writeLog("ContractOperationRecordAction：processEcode = " + processEcode);
        new BaseBean().writeLog("ContractOperationRecordAction：processCreateTime = " + processCreateTime);
        RecordSet rs2 = new RecordSet();
        rs2.executeQuery("select * from HrmResource where id = ? ", createId);
        if(rs2.next()){
            departmentId = rs2.getString("departmentid");
            name = rs2.getString("lastname");
            employeeId = rs2.getString("loginid");
            companyId = rs2.getString("subcompanyid1");
        }
        depId = departmentId;
        new BaseBean().writeLog("ContractOperationRecordAction：departmentId = " + departmentId);
        new BaseBean().writeLog("ContractOperationRecordAction：name = " + name);
        new BaseBean().writeLog("ContractOperationRecordAction：employeeId = " + employeeId);
        int n = 0;
        while(true){   //1>2>3
            RecordSet rs = new RecordSet();
            rs.executeQuery("select departmentname, supdepid from hrmdepartment where id = ? ", departmentId);
            if(rs.next()){
                departmentId = rs.getString("supdepid");
                new BaseBean().writeLog("ContractOperationRecordAction：循环中departmentId = " + departmentId);
                if(n==0){
                    allDepartmentEname = rs.getString("departmentname");
                } else {
                    allDepartmentEname = rs.getString("departmentname") + ">" + allDepartmentEname;
                }
                n = n + 1;
                if("0".equals(departmentId) || "".equals(departmentId)){
                    new BaseBean().writeLog("ContractOperationRecordAction：判断是否为0中departmentId = " + departmentId);
                    break;
                }
            }
        }
        new BaseBean().writeLog("ContractOperationRecordAction：allDepartmentEname = " + allDepartmentEname);
        new BaseBean().writeLog("ContractOperationRecordAction：departmentId = " + departmentId);

        RecordSet rsCreate = new RecordSet();
        String insertSql = "insert into " + DATA_BASE_NAME + "(all_department_ename, department_id, name, employee_id, process_ename, process_id, process_ecode, process_create_time, company_id, employee_data_id, formmodeid, modedatacreater, modedatacreatertype, modedatacreatedate, modedatacreatetime)  values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
        Date date = new Date();
        return rsCreate.executeUpdate(insertSql, allDepartmentEname, depId, name, employeeId, processEname, requestId, processEcode, processCreateTime, companyId, createId, 112, 1, 0, getNowTimeStr(date, "yyyy-MM-dd"), getNowTimeStr(date, "HH:mm:ss"));
    }

    private static String getNowTimeStr(Date time, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }






}

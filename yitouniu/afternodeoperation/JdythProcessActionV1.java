package yitouniu.afternodeoperation;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.SAPUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/4/18 15:11
 * @Description TODO
 * @Version 1.0
 */
public class JdythProcessActionV1 implements Action {
    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestId = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String) tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";

        main.executeQuery(sql, requestId);
        if (main.next()) {
            //mainId
            int mainId = main.getInt("id");
            String allErrMessage = "";


            Map<String, List<Map<String, String>>> addressMap = getAddress(mainId, tableName);

            baseBean.writeLog("execute addressMap = " + addressMap);

            if(addressMap.isEmpty()){
                return SUCCESS;
            }

            Map<String,String> provinceMap = SAPUtil.queryProvinceCode();



            for(Map.Entry<String, List<Map<String, String>>> object : addressMap.entrySet()){
                String errMessage = "";
                List<String> errRow = new ArrayList<>();
                List<String> errRowReq = new ArrayList<>();
                List<String> errRowUpdate = new ArrayList<>();
                Set<String> errName = new HashSet<>();
                Set<String> errNameBUSORT2 = new HashSet<>();
                if(object.getValue().size() == 0){
                    continue;
                }

                int line = 1;
                for (Map<String, String> o : object.getValue()){
                    //先判断新增的里面名称是否重复,如果有问题，则不解析地址
                    String name = o.get("name");
                    String address = o.get("address");
                    boolean nextLineFlag = false;
                    if(StringUtils.isNotBlank(name)){
                        rs.executeQuery("select id from uf_khzsj where NAME1 = ? ", name);
                        if(rs.next()){
                            if("5".equals(object.getKey()) && StringUtils.isBlank(o.get("BU_SORT2"))){
                                errNameBUSORT2.add(name);
                                nextLineFlag = true;
                            }
                            if(!"5".equals(object.getKey())){
                                errName.add(name);
                                nextLineFlag = true;
                            }
                        }
                    }
                    if(nextLineFlag){
                        line++;
                        continue;
                    }
                    if("全国".equals(address) || "0".equals(o.get("sfyykd100xzsj"))){
                        continue;
                    }
                    JSONObject result = addressAnalyse(address);
                    if(result != null && result.getInteger("code") == 200){
                        JSONObject dataObject = (JSONObject) result.get("data");
                        JSONArray resultData = dataObject.getJSONArray("result");
                        JSONObject xzq = ((JSONObject)resultData.get(0)).getJSONObject("xzq");
                        if(xzq == null){
                            errRow.add(String.valueOf(line));
                            continue;
                        }

                        String province = xzq.getString("province");
                        String city = xzq.getString("city");
                        String district = xzq.getString("district");
                        String subArea = xzq.getString("subArea");

                        //当省市区地址有一个为空时，解析错误
                        if(StringUtils.isBlank(province) || StringUtils.isBlank(city) || StringUtils.isBlank(district) || StringUtils.isBlank(subArea)){
                            errRow.add(String.valueOf(line));
                            continue;
                        }
                        //当省市不为空且区为空时，设置区为其他区
                        if(StringUtils.isNotBlank(province) && StringUtils.isNotBlank(city) && StringUtils.isBlank(district)){
                            district = "其他区";
                        }
                        String provinceCode = provinceMap.get(province);
                        boolean flag = update(tableName, provinceCode, province, city, district, o.get("id"), object.getKey());
                        if(!flag){
                            errRowUpdate.add(String.valueOf(line));
                        }
                    } else {
                        errRowReq.add(String.valueOf(line));
                    }

                    line++;
                }

                baseBean.writeLog("errName = " +errName);
                baseBean.writeLog("errNameBUSORT2 = " +errNameBUSORT2);
                baseBean.writeLog("errRow = " +errRow);
                baseBean.writeLog("errRowUpdate = " +errRowUpdate);
                baseBean.writeLog("errMessage = " +errMessage);

                if(errName.size() != 0){
                    errMessage = errMessage + "名称重复，请检查名称为 "+errName+" 的数据，";
                }
                if(errNameBUSORT2.size() != 0){
                    errMessage = errMessage + "名称重复，搜索项为空，请填写搜索项，请检查名称为 "+errNameBUSORT2+" 的数据，";
                }
                if(errRow.size() != 0){
                    errMessage = errMessage + "地址解析失败，请检查第" + errRow + "行的送货地址，";
                }
                if(errRowUpdate.size() != 0){
                    errMessage = errMessage + "省市区地址信息更新失败，请检查第" + errRowUpdate + "行的送货地址。";
                }
                if(errRowReq.size() != 0){
                    errMessage = errMessage + "第" + errRowReq + "的单子请求快递100失败。请重试";
                }
                String detailName = "";
                if("5".equals(object.getKey())){
                    detailName = "Z002主数据信息（客户）";
                } else if ("6".equals(object.getKey())){
                    detailName = "Z003主数据信息（送达方）";
                } else {
                    detailName = "Z009主数据信息（店铺）";
                }
                if(errMessage.length() != 0) {
                    allErrMessage = allErrMessage + " " + detailName + "errMessage = " + errMessage + " , ";
                }
            }

            if(allErrMessage.length() != 0){
                requestInfo.getRequestManager().setMessagecontent(allErrMessage);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

        }



        return SUCCESS;
    }



    public Map<String, List<Map<String, String>>> getAddress(int mainId, String tableName){
        Map<String, List<Map<String, String>>> addressMap = new HashMap<>();
        List<Map<String, String>> item5 = getAddress(mainId, tableName, "5");
        List<Map<String, String>> item6 = getAddress(mainId, tableName, "6");
        List<Map<String, String>> item7 = getAddress(mainId, tableName, "7");
        addressMap.put("5", item5);
        addressMap.put("6", item6);
        addressMap.put("7", item7);

        return addressMap;
    }

    public boolean update(String tableName,String provinceCode, String province, String city, String district, String id, String tableNo){
        String sql = "update " + tableName + "_dt" + tableNo + " set REGIO=?, s=?, CITY1=?, CITY2=?, sfyykd100xzsj = '0' where id in (" + id + ") ";

        return rs.executeUpdate(sql, provinceCode, province, city, district);
    }

    public List<Map<String, String>> getAddress(int mainId, String tableName, String type){
        List<Map<String, String>> addressList = new ArrayList<>();
        String sql = "select * from " + tableName + "_dt" + type + " where mainid = ?";

        baseBean.writeLog("getAddress sql = " + sql);

        rs.executeQuery(sql, mainId);
        while (rs.next()){
            Map<String,String> map = new HashMap<>();
            map.put("id", rs.getString("id"));
            map.put("address", rs.getString("STREET"));
            map.put("name", rs.getString("NAME_ORG1"));
            if("5".equals(type)){
                map.put("BU_SORT2", rs.getString("BU_SORT2"));
                map.put("sfyykd100xzsj", rs.getString("sfyykd100xzsj"));
            }
            if("6".equals(type)){
                map.put("sfyykd100xzsj", rs.getString("sfyykd100xzsj"));
            }
            addressList.add(map);
        }
        return addressList;
    }

    public JSONObject addressAnalyse(String address) {
        Map<String, Object> queryParam = new HashMap<>();
        String t = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new HashMap<>();
        param.put("content", address);
        queryParam.put("param", JSON.toJSONString(param));
        queryParam.put("key", "kOVNIwrT5246");
        queryParam.put("t",t);
        String s = JSON.toJSONString(param) + t + "kOVNIwrT5246" + "501fc3d563f64de7aa8ed61e9cc481ac";
        queryParam.put("sign", DigestUtils.md5Hex(s).toUpperCase());
        String response = HttpRequest.post("https://api.kuaidi100.com/address/resolution").header("Content-Type", "application/x-www-form-urlencoded").form(queryParam).execute().body();
        baseBean.writeLog("sdfAddressAnalyse：response="+response);
        return JSON.parseObject(response);
    }




}

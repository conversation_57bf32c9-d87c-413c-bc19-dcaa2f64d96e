package yitouniu.afternodeoperation;

import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * <AUTHOR>
 * @Date 2022/12/16 14:57
 * @Description TODO
 * @Version 1.0
 */
public class MobilePreAction  implements Action {
    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tablename = requestInfo.getRequestManager().getBillTableName();
        //礼品赠送方送房手机号
        String lpzsflxfs = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("lpzsflxfs".equals(name)) {
                lpzsflxfs = Util.null2String(properties[i].getValue());
            }
        }

        return null;
    }
}

package yitouniu.afternodeoperation;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/30 17:28
 * @Description TODO
 * @Version 1.0
 */
public class MFDDProcessPreActionV2 implements Action{

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        if (main.next()) {
            //mainId
            int mainId = main.getInt("id");
            String errMessage = "";
            List<String> errMessageRow = new ArrayList<>();
            List<String> errRow = new ArrayList<>();
            List<String> errRowUpdate = new ArrayList<>();
            List<String> errRowReq = new ArrayList<>();
            List<Map<String, String>> addressList = getAddress(mainId, tableName);
            Set<String> bhSet = new HashSet<>();
            Map<String,String> idBhMap = new HashMap<>();
            Map<String,String> bhAddressMap = new HashMap<>();
            List<Map<String, String>> newAddressList = new ArrayList<>();
            baseBean.writeLog("execute：addressList="+addressList);
            for(Map<String,String> o : addressList){
                if(StringUtils.isBlank(o.get("zhdzxx"))){
                    if(StringUtils.isNotBlank(o.get("REGION")) && StringUtils.isNotBlank(o.get("s")) && StringUtils.isNotBlank(o.get("CITY")) && StringUtils.isNotBlank(o.get("DISTRICT")) && StringUtils.isNotBlank(o.get("STREET"))){
                        continue;
                    } else {
                        errMessageRow.add(o.get("bh"));
                        continue;
                    }
                } else {
                    if(StringUtils.isNotBlank(o.get("REGION")) && StringUtils.isNotBlank(o.get("s")) && StringUtils.isNotBlank(o.get("CITY")) && StringUtils.isNotBlank(o.get("DISTRICT")) && StringUtils.isNotBlank(o.get("STREET"))){
                        continue;
                    } else if(StringUtils.isBlank(o.get("REGION")) && StringUtils.isBlank(o.get("s")) && StringUtils.isBlank(o.get("CITY")) && StringUtils.isBlank(o.get("DISTRICT")) && StringUtils.isBlank(o.get("STREET"))){

                    } else {
                        errMessageRow.add(o.get("bh"));
                        continue;
                    }
                }

                boolean addFlag = bhSet.add(o.get("bh"));

                if(addFlag){
                    idBhMap.put(o.get("bh"), o.get("id"));
                    //需要验证的信息，用,隔开
                    String verifyMessage = o.get("zhdzxx")+","+o.get("TEL_NUMBER")+","+o.get("NAME_CO")+","+o.get("cbzxbm");
                    bhAddressMap.put(o.get("bh"), verifyMessage);
                    newAddressList.add(o);
                }else {
                    //如果相同订单编号对应不同地址、收件人、手机号、成本中心的话，返回报错
                    String verifyMessage = o.get("zhdzxx")+","+o.get("TEL_NUMBER")+","+o.get("NAME_CO")+","+o.get("cbzxbm");
                    baseBean.writeLog("verifyMessage="+verifyMessage);
                    baseBean.writeLog("bhAddressMap.get(o.get(\"bh\"))="+bhAddressMap.get(o.get("bh")));
                    if(!bhAddressMap.get(o.get("bh")).equals(verifyMessage)){
                        requestInfo.getRequestManager().setMessagecontent("相同订单编号地址、收件人、手机号、成本中心必须一样，请检查订单编号为" + o.get("bh") + "行的综合详细地址、收件人、手机号、成本中心等信息");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                    idBhMap.put(o.get("bh"), idBhMap.get(o.get("bh")) + "," + o.get("id"));
                }
            }

            Map<String,String> provinceMap =  queryProvinceCode();

            for (Map<String, String> o : newAddressList){
                JSONObject result = addressAnalyse(o.get("zhdzxx"));
                if(result != null && result.getInteger("code") == 200){
                    JSONObject dataObject = (JSONObject) result.get("data");
                    JSONArray resultData = dataObject.getJSONArray("result");
                    JSONObject xzq = ((JSONObject)resultData.get(0)).getJSONObject("xzq");
                    if(xzq == null){
                        errRow.add(o.get("bh"));
                        continue;
                    }
                    String province = xzq.getString("province");
                    String city = xzq.getString("city");
                    String district = xzq.getString("district");
                    String subArea = xzq.getString("subArea");

                    //当省市区地址有一个为空时，解析错误
                    if(StringUtils.isBlank(province) || StringUtils.isBlank(city) || StringUtils.isBlank(district) || StringUtils.isBlank(subArea)){
                        errRow.add(o.get("bh"));
                        continue;
                    }
                    //当省市不为空且区为空时，设置区为其他区
                    if(StringUtils.isNotBlank(province) && StringUtils.isNotBlank(city) && StringUtils.isBlank(district)){
                        district = "其他区";
                    }
                    String provinceCode = provinceMap.get(province);
                    boolean flag = update(tableName, provinceCode, province, city, district, subArea, idBhMap.get(o.get("bh")));
                    if(!flag){
                        errRowUpdate.add(o.get("bh"));
                    }
                } else {
                    errRowReq.add(o.get("bh"));
                }
            }
            if(errMessageRow.size() != 0){
                errMessage = errMessage + "订单行信息数据有问题，请检查订单编号为" + errMessageRow + "行上五个填写的内容，";
            }
            if(errRow.size() != 0){
                errMessage = errMessage + "地址解析失败，请检查订单编号为" + errRow + "行的综合详细地址，";
            }
            if(errRowUpdate.size() != 0){
                errMessage = errMessage + "省市区地址信息更新失败，请检查订单编号为" + errRowUpdate + "行的综合详细地址。";
            }
            if(errRowReq.size() != 0){
                errMessage = errMessage + "订单编号为" + errRowReq + "的单子请求快递100失败。请重试";
            }
            if(errMessage.length() != 0){
                requestInfo.getRequestManager().setMessagecontent(errMessage);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }



        }
        return SUCCESS;

    }


    public List<Map<String, String>> getAddress(int mainId, String tableName){
        List<Map<String, String>> addressList = new ArrayList<>();
        rs.executeQuery("select * from " + tableName + "_dt1 where mainid = ? and (sfyykd100xzsj <> '0' or sfyykd100xzsj is null)", mainId);
        while (rs.next()){
            Map<String,String> map = new HashMap<>();
            map.put("id", rs.getString("id"));
            //三个需要校验的东西
            map.put("zhdzxx", rs.getString("zhdzxx"));
            map.put("TEL_NUMBER", rs.getString("TEL_NUMBER"));
            map.put("NAME_CO", rs.getString("NAME_CO"));
            map.put("cbzxbm", rs.getString("cbzxbm"));

            map.put("bh", rs.getString("bh"));
            map.put("REGION", rs.getString("REGION"));
            map.put("s", rs.getString("s"));
            map.put("CITY", rs.getString("CITY"));
            map.put("DISTRICT", rs.getString("DISTRICT"));
            map.put("STREET", rs.getString("STREET"));
            addressList.add(map);
        }
        return addressList;
    }

    public JSONObject addressAnalyse(String address) {
        Map<String, Object> queryParam = new HashMap<>();
        String t = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new HashMap<>();
        param.put("content", address);
        queryParam.put("param", JSON.toJSONString(param));
        queryParam.put("key", "kOVNIwrT5246");
        queryParam.put("t",t);
        String s = JSON.toJSONString(param) + t + "kOVNIwrT5246" + "501fc3d563f64de7aa8ed61e9cc481ac";
        queryParam.put("sign", DigestUtils.md5Hex(s).toUpperCase());
        String response = HttpRequest.post("https://api.kuaidi100.com/address/resolution").header("Content-Type", "application/x-www-form-urlencoded").form(queryParam).execute().body();
        baseBean.writeLog("addressAnalyse：responseV1="+response);
        return JSON.parseObject(response);
    }

    public static String md5Encryptor(Map<String, String> param, String t,String key, String secret) {
        try {
            // 获取当前时间戳
            // 拼接字符串
            String input = JSON.toJSONString(param) + t + key + secret;
            System.out.println("input= "+input);

            // 获取MD5实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算MD5摘要
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            System.out.println("hexString="+hexString);
            return hexString.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }

    public boolean update(String tableName,String provinceCode, String province, String city, String district, String subArea, String id){
        return  rs.executeUpdate("update " + tableName + "_dt1 set REGION=?, s=?, CITY=?, DISTRICT=?, STREET=?, sfyykd100xzsj = 0 where id in (" + id + ") ", provinceCode, province, city, district, subArea);
    }

    public Map<String,String> queryProvinceCode(){
        Map<String,String> provinceCodeMap = new HashMap<>();
        rs.executeQuery("select * from formtable_main_267");
        while(rs.next()){
            provinceCodeMap.put(rs.getString("sqmc"),rs.getString("sqdm"));
        }
        return provinceCodeMap;
    }


    public static void main(String[] args) {

        Map<String, Object> queryParam = new HashMap<>();
        String t = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new HashMap<>();
        param.put("content", "上海市杨浦区杨高支路");
        queryParam.put("param", JSON.toJSONString(param));
        queryParam.put("key", "kOVNIwrT5246");
        queryParam.put("t",t);
        String s = JSON.toJSONString(param) + t + "kOVNIwrT5246" + "501fc3d563f64de7aa8ed61e9cc481ac";
        queryParam.put("sign", DigestUtils.md5Hex(s).toUpperCase());
        System.out.println("queryParam: "+ JSON.toJSONString(queryParam));
        String response = HttpRequest.post("https://api.kuaidi100.com/address/resolution").header("Content-Type", "application/x-www-form-urlencoded").form(queryParam).execute().body();
        System.out.println(JSON.toJSONString(response));

    }


}

package yitouniu.afternodeoperation;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/2/28 18:08
 * @Description 采购申购单 申请人提交采购时间控制
 * @Version 1.0
 */
public class PurchaseTimeControlAction implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        //申请类型，0 NB常规物料申购 1 NBA资产类申购 2 NBK费用类申购 3 NBL临时申购 4 NBJ紧急类申购
        String applyType = "";
        //申请公司
        String applyCompanyId = "";
        //申请人第一次提交成功日期
        String sqrtjrq = "";
        //申请公司代码
        String companyCode = "";

        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (Property property : properties) {
            String name = property.getName();
            if ("BSART".equals(name)) {
                applyType = Util.null2String(property.getValue());
            } else if ("sqgs".equals(name)) {
                applyCompanyId = Util.null2String(property.getValue());
            } else if ("sqrtjrq".equals(name)) {
                sqrtjrq = Util.null2String(property.getValue());
            }
        }

        rs.executeQuery("select zzdm from uf_zzdmys where oagsmc = ?",applyCompanyId);
        while(rs.next()) {
            companyCode = rs.getString("zzdm");
        }
        int startDay = 0;
        int endDay = 0;
        int compareDay = 0;

        String checkStatus = null;

        //查找控制日期和是否检查Status
        RecordSet rs2 = new RecordSet();
        String sql2 = "select start_day, end_day, check_status from uf_cg_time_control where company_id= ?";
        rs2.executeQuery(sql2,companyCode);
        while(rs2.next()) {
            startDay = Integer.parseInt(rs2.getString("start_day"));
            endDay = Integer.parseInt(rs2.getString("end_day"));
            checkStatus = rs2.getString("check_status");
        }
        new BaseBean().writeLog("CGTimeControl startDay " + startDay + " endDay = " + endDay + " checkStatus = " + checkStatus);

        LocalDate nowDay = LocalDate.now();
        boolean flag = true;
        if(!"4".equals(applyType)){
            if(StringUtils.isBlank(sqrtjrq)){
                compareDay = nowDay.getDayOfMonth();
            } else {
                compareDay = Integer.parseInt(sqrtjrq.substring(8,10));
            }
            if(compareDay < startDay || compareDay > endDay || !"X".equals(checkStatus)){
                requestInfo.getRequestManager().setMessagecontent("今天不在采购日期内，请择日申请");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
        }

        if(StringUtils.isBlank(sqrtjrq)){
            rs.executeUpdate("update " + tableName + " set sqrtjrq = ? where requestid = ?", nowDay, requestid);
        }



        return SUCCESS;
    }


}

package yitouniu.afternodeoperation;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 * @Date 2022/11/16 11:38
 * @Description 拒腐流程，云片网接入短信通知
 * @Version 1.0
 */
public class YunPianSMSRefusalPreAction implements Action {

    RecordSet rs = new RecordSet();

    public static final String YUNPIAN_SMS_APIKEY = Util.null2String(new BaseBean().getPropValue("yunPianSMS","SMSApiKey"));
    public static final String YUNPIAN_SMS_SINGLE_SEND_URL = Util.null2String(new BaseBean().getPropValue("yunPianSMS","SMSSingleSendUrl"));

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet rs = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);
        String mobile = "";
        String mb = "";
        if (main.next()) {
            mobile = main.getString("lpzsflxfs");
        }

        if(!isValidPhoneNumber(mobile)){
            requestInfo.getRequestManager().setMessagecontent("手机号格式不正确，请确认");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        //查找模版
        String mbSql = "select * from uf_yp_sms where lcaction = ?";
        rs.executeQuery(mbSql, "YunPianSMSRefusalAction");
        if(rs.next()){
            mb = rs.getString("mb");
        }

        JSONObject result = JSONObject.parseObject(singleSend(YUNPIAN_SMS_APIKEY, mb, mobile));
        new BaseBean().writeLog("YunPianSMSRefusalAction:result = "+result.toJSONString());
        if(result.getInteger("code")!=0){
            requestInfo.getRequestManager().setMessagecontent(result.toJSONString());
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }


    public String singleSend(String apiKey, String text, String mobile) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("apikey", apiKey);
        params.put("text", text);
        params.put("mobile", mobile);
        return HttpUtil.post(YUNPIAN_SMS_SINGLE_SEND_URL, params);
    }

    public static boolean isValidPhoneNumber(String phone) {
        // 定义手机号正则表达式
        String regex = "^1[3-9]\\d{9}$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(phone);
        // 进行匹配
        return matcher.matches();
    }



}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.file.Prop;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.*;

/**
 * 费用报销和差旅报销凭证
 */
    public class ExpenseVoucherAction implements Action {
    private RecordSet recordSet = new RecordSet();
    private final String AUART_TYPE = "Z100,Z200,Z300";
    private Calendar cale = null;
    private String hrmName = "";
    private String lclx = ""; // 流程类型
    private List<Map<String,String>> flseList ; // 福利费税额;


    @Override
    public String execute(RequestInfo requestInfo) {
        cale = Calendar.getInstance();
        flseList = new ArrayList<>(); // 福利费数据
        int month = cale.get(Calendar.MONTH) + 1;
        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        String mainid = "";
        String sfhx = ""; // 是否有借款
        String jkje = ""; // 借款金额
        double bxzje = 0.00; // 实报金额
        String bankNum = ""; // 银行信息
        String sqr = ""; // 申请人


        double sbje = 0.00; // 实报金额


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");
            sfhx = rs1.getString("sfhx");
            jkje = rs1.getString("jkje");
            bxzje = Double.valueOf(rs1.getString("bxzje"));

            bankNum = rs1.getString("BANKNUM");
            sqr = rs1.getString("sqr");
            lclx = rs1.getString("lclx");
            sbje = Double.valueOf(rs1.getString("sbje"));

        }


        // 员工名称
        hrmName = ActionUtil.getHrmName(sqr);
        // 删除明细表四
        sql = "DELETE FROM " + tableName + "_dt3  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表三删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");
        // 删除明细表一
        sql = "DELETE FROM " + tableName + "_dt4 WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表四删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");


        // 查询明细表1的数据

        List<String> je = new ArrayList<>();//实报金额
        List<String> se = new ArrayList<>(); // 税额
        List<String> bxje = new ArrayList<>(); // 不含税金额
        List<String> fykm = new ArrayList<>(); // 费用科目
        List<String> sl = new ArrayList<>(); // 税率
        List<String> aufnr = new ArrayList<>(); // 内部订单号
        List<String> AUART = new ArrayList<>(); // 内部订单类型
        List<String> fplx = new ArrayList<>(); // 发票类型
        List<String> KOSTLList = new ArrayList<>(); // 费用归属部门
        List<String> bzList = new ArrayList<>(); // 备注
        List<String> fphmss = new ArrayList<>(); // 发票号码手输
        List<String> mxkh = new ArrayList<>(); // 发票号码手输


        sql = "select * from " + tableName + "_dt1 where mainid = ?";
        rs1.executeQuery(sql, mainid);
        while (rs1.next()) {

            je.add(rs1.getString("sbje"));
            se.add(rs1.getString("dkse"));
            fykm.add(rs1.getString("fykm"));
            sl.add(rs1.getString("taxRate1"));
            aufnr.add(rs1.getString("AUFNR"));
            AUART.add(rs1.getString("AUART"));
            fplx.add(rs1.getString("fplx"));
            bxje.add(rs1.getString("bxje"));
            KOSTLList.add(rs1.getString("KOSTL"));
            bzList.add(rs1.getString("bz"));
            fphmss.add(rs1.getString("fphmss"));
            mxkh.add(rs1.getString("mxkh"));

        }
        new BaseBean().writeLog("---> + 实报金额: " + je + ",税额: " + se + ", 费用科目: " + fykm + ",税率: " + sl);
        double jkje1 = findJKJE(jkje);
        new BaseBean().writeLog("--->借款金额：" + jkje1 + "<br/>");
        new BaseBean().writeLog("--->报销金额删除：" + bxzje + "<br/>");
        // 判断借款类型
        Map<String, String> data = ActionUtil.findData(sqr);
        if ("0".equals(sfhx)) { // 是借款
            if (sbje<=0) { // 借款大于报销

                // 生成凭证
                String zje = insertPZData(je, fykm, sqr, tableName, se, aufnr, mainid, sl, AUART, fplx, bxje, KOSTLList,bzList,fphmss,mxkh);
                if (!"0.00".equals(zje)) {
                    String SGTXT = hrmName + "报销: " + month + "月" + "员工费用报销";
                    String rybh = data.get("rybh"); // 人员编号
                    String KOSTL = data.get("KOSTL"); // 成本中心
                    String KUNNR = data.get("KUNNR"); // 客户编码
                    // 贷方数据
                    ActionUtil.insertData(tableName + "_dt3", "", bankNum, "", "", SGTXT, zje, rybh, "11", "", "", "", "", "", mainid, "");
                    if (flseList.size()>0&&flseList!=null){
                        for (int i = 0; i < flseList.size(); i++) {
                            Map<String, String> map = flseList.get(i);

                            ActionUtil.insertData(tableName + "_dt3", "", map.get("HKONT"), "", "", SGTXT, map.get("shuie"), "", "50", "", "", "", "", "", mainid, "");
                        }

                    }
                    updatePZTS(tableName,requestid,"0");
                }else {
                    updatePZTS(tableName,requestid,"1");
                }
            }

            if (sbje>0) { // 报销大于借款


                // 生成凭证
                double zje = Double.valueOf(insertPZData(je, fykm, sqr, tableName, se, aufnr, mainid, sl, AUART, fplx, bxje, KOSTLList,bzList,fphmss,mxkh));
                String SGTXT = hrmName + "报销: " + month + "月" + "员工费用报销";
                String rybh = data.get("rybh"); // 人员编号
                String KOSTL = "";

                String KUNNR = data.get("KUNNR"); // 客户编码

                //double sjje = bxzje - jkje1;
                if (zje > 0) {

                    // 贷方数据
                    ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT, zje + "", rybh, "11", "", "", "", "", "", mainid, "");
                    if (flseList.size()>0&&flseList!=null){
                        for (int i = 0; i < flseList.size(); i++) {
                            Map<String, String> map = flseList.get(i);

                            ActionUtil.insertData(tableName + "_dt3", "", map.get("HKONT"), "", "", SGTXT, map.get("shuie"), "", "50", "", "", "", "", "", mainid, "");
                        }

                    }
                    updatePZTS(tableName,requestid,"0");
                }else {
                    updatePZTS(tableName,requestid,"1");
                }
                    // 支付凭证生成
                    ActionUtil.insertData(tableName + "_dt4", KOSTL, "", "", "", SGTXT, sbje+"" , rybh, "01", "", "", "", "", "", mainid, "");
                    ActionUtil.insertData(tableName + "_dt4", "", bankNum, "108", "", SGTXT, sbje +"", "", "50", "", "", "", "", "", mainid, "");


            }

        } else { // 不是借款
            String text = "";
            if ("0".equals(lclx)) { // 差旅
                text = "差旅费";
            } else { // 费用
                text = "员工费用报销";

            }
            String SGTXT = hrmName + "报销: " + month + "月" + text;
            String rybh = data.get("rybh"); // 人员编号
            String KOSTL = data.get("KOSTL"); // 成本中心
            String KUNNR = data.get("KUNNR"); // 客户编码
            // 生成凭证
            String zje = insertPZData(je, fykm, sqr, tableName, se, aufnr, mainid, sl, AUART, fplx, bxje, KOSTLList,bzList,fphmss,mxkh);
            if (!"0.00".equals(zje)) {

                // 贷方数据
                ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT, zje + "", rybh, "11", "", "", "", "", "", mainid, "");
                if (flseList.size()>0&&flseList!=null){
                    for (int i = 0; i < flseList.size(); i++) {
                        Map<String, String> map = flseList.get(i);

                        ActionUtil.insertData(tableName + "_dt3", "", map.get("HKONT"), "", "", SGTXT, map.get("shuie"), "", "50", "", "", "", "", "", mainid, "");
                    }

                }
                updatePZTS(tableName,requestid,"0");
            }else {
                updatePZTS(tableName,requestid,"1");
            }
            // 支付凭证生成
            ActionUtil.insertData(tableName + "_dt4", "", "", "", "", SGTXT, bxzje+"", rybh, "01", "", "", "", "", "", mainid, "");
            ActionUtil.insertData(tableName + "_dt4", "", bankNum, "108", "", SGTXT, bxzje+"", "", "50", "", "", "", "", "", mainid, "");

        }


        return SUCCESS;

    }

    public String insertPZData(List<String> je, List<String> fykm, String sqr, String tableName, List<String> se, List<String> aufnr, String mainid, List<String> sl, List<String> AUART, List<String> fplx, List<String> bxje, List<String> KOSTLList,List<String> bzList,List<String>fphmss,List<String> mxkh) {

        cale = Calendar.getInstance();

        int month = cale.get(Calendar.MONTH) + 1;
        Map<String, String> data = ActionUtil.findData(sqr);
        String zje = "0.00"; // 总金额

        for (int i = 0; i < je.size(); i++) {
            String fykmlx = fykm.get(i);
            if (!"".equals(fykmlx) && fykmlx != null  && !"168".equals(fykmlx) && !"167".equals(fykmlx)) {
            //if (!"".equals(fykmlx) && fykmlx != null  && !"414".equals(fykmlx) && !"379".equals(fykmlx) ) {
                zje = ActionUtil.bigDecimalAdd(zje, je.get(i));
                Map<String, String> km = ActionUtil.findKm(fykmlx);
                String codeName = km.get("codeName"); // SAP科目编码
                String text = "";
                if ("1".equals(lclx)) { // 费用
                    text = "-"+bzList.get(i);
                }
                String SGTXT =  month + "月" + km.get("name")+text;

                //借方数据
                String shuie = se.get(i); // 税额
                String sbje = je.get(i); // 实报金额
                String bhsje = bxje.get(i);// 不含税金额
                String KOSTL = KOSTLList.get(i); // 成本中心
                String aufnrI = AUART.get(i); // 内部订单
                if (!"".equals(aufnrI) && aufnrI != null && AUART_TYPE.indexOf(aufnrI) != -1) {
                    KOSTL = "";
                }
                if (shuie != null && !"".equals(shuie) && Double.valueOf(shuie) != 0&&!"1".equals(fplx.get(i))) {

                    Map<String, String> slMap = getSL(sl.get(i));
                    String taxRate1 = slMap.get("taxRate1"); // 费用描述
                    String HKONT = slMap.get("HKONT"); // 科目
                    if (!"0".equals(fplx.get(i))&&!"6".equals(fplx.get(i))){
                        taxRate1 = "计算抵扣旅客运输服务";
                        HKONT = "2221010107";
                    }



                    if("6001000000".equals(codeName)){ // 客服红包
                        HKONT = "2221010502";
                        KOSTL = "";
                        taxRate1 = "应交税费-应交增值税-销项税额-9％";

                    }
                    String SGTXTSL = hrmName + "报销: " + month + "月" + taxRate1+"-"+fphmss.get(i);

                    //借方税额数据数据
                    ActionUtil.insertData(tableName + "_dt3", "", HKONT, "", "", SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, "");
                    String flf = Prop.getPropValue("niu-flf", "subjectid"); // 科目类型
                    //if ("6601010200".equals(codeName)||"6601190000".equals(codeName)){ // 福利费和业务招待费
                    if (flf.indexOf(codeName)!=-1){ // 福利费和业务招待费
                        ActionUtil.insertData(tableName + "_dt3", KOSTL,  codeName, "", aufnr.get(i), SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, "");
                        Map<String,String> map = new HashMap<>();
                        map.put("shuie",shuie);
                        map.put("KOSTL",KOSTL);
                        map.put("HKONT","2221010601");
                        flseList.add(map);
                        //flse = ActionUtil.bigDecimalAdd(flse, shuie);
                    }
                }
                if("1".equals(fplx.get(i))){ // 普票
                    bhsje = sbje;
                }
                String subjectid = Prop.getPropValue("niu-subject", "subjectid"); // 科目类型
                String aufnrValue = aufnr.get(i); // 内部订单
                if(subjectid.indexOf(codeName)!=-1){  // 属于这两个费用的科目成本中心和的内部订单为空
                    //if("1263".equals(supsubject)||"1270".equals(supsubject)){  // 属于这两个费用的科目成本中心和的内部订单为空
                    KOSTL = "";
                    aufnrValue = "";
                }



                ActionUtil.insertData(tableName + "_dt3", KOSTL, codeName, "", aufnrValue, SGTXT, bhsje, "", "40", "", "", mxkh.get(i), "", "", mainid, "");
            }
        }
        return zje;
    }


    /**
     * 获取借款金额
     */
    public double findJKJE(String id) {

        String sql = "select * from uf_jiekuanku where id= ?";
        double jkwhxje = 0.0;
        recordSet.executeQuery(sql, id);
        if (recordSet.next()) {
            jkwhxje = Double.valueOf(recordSet.getString("jkwhxje"));

        }
        return jkwhxje;
    }


    /**
     * 根据税率找会计科目
     *
     * @param
     * @return
     */
    public Map getSL(String sl) {
        Map map = new HashMap();
        String taxRate1 = "";
        String HKONT = "";
        switch (sl) {
            case "0":
                taxRate1 = "13％税率发票";
                HKONT = "2221010101";

                break;
            case "1":
                taxRate1 = "3%税率发票";
                HKONT = "2221010105";
                break;
            case "2":
                taxRate1 = "5%税率";
                HKONT = "2221010104";
                break;
            case "3":
                taxRate1 = "6%税率发票";
                HKONT = "2221010103";
                break;
            case "4":
                taxRate1 = "9%税率发票";
                HKONT = "2221010102";
                break;
            case "6":
                taxRate1 = "1%税率发票";
                HKONT = "2221010108";
                break;
        }
        map.put("taxRate1", taxRate1);
        map.put("HKONT", HKONT);
        return map;
    }

    public void updatePZTS(String tableName,String requestid,String sfpzsc ){

        recordSet.executeUpdate("update "+tableName+" set sftsdybpz = ?  where requestid = ?",sfpzsc,requestid); // 不生成凭证

    }


}

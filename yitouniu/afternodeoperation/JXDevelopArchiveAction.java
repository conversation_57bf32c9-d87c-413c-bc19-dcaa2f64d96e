package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.CreateJxWorkflowUtil;
import yitouniu.util.TimeUtils;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/17 16:13
 * @Description 绩效制定归档action
 * @Version 1.0
 */
public class JXDevelopArchiveAction implements Action {

    RecordSet rs = new RecordSet();

    public static final String JDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","jdjxformmodeid"));
    public static final String YDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","ydjxformmodeid"));

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        if (main.next()) {
            //绩效类型
            String jxlx = main.getString("jxlx");
            String xm = main.getString("xm");
            String bm = main.getString("bm");
            String szyjbm = main.getString("szyjbm");
            String yd = main.getString("yd");
            String jd = main.getString("jd");
            String archiveTableName = "";
            String value = null;
            if("0".equals(jxlx)){
                value = jd;
                archiveTableName = "uf_bmjdjx";
            } else {
                value = yd;
                archiveTableName = "uf_bmydjxxx";
            }
            int mainId = select(archiveTableName, szyjbm, value);
            if(mainId > 0){
                int id = selectByJxzdlc(requestid, archiveTableName);
                if(id > 0){
                    boolean flag =  rs.executeUpdate("update " + archiveTableName + "_dt1 set sfzdwc = '0' where jxzdlc = ?", requestid);
                    if(!flag){
                        requestInfo.getRequestManager().setMessagecontent("保存数据失败——更新是否完成制定标识失败");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                } else {
                    //插入明细数据
                    String insertDetailSql = "insert into " + archiveTableName + "_dt1  (mainid, xm, jxzdlc, bm, sfzdwc) values(?,?,?,?,?)";
                    boolean flag = rs.executeUpdate(insertDetailSql, mainId, xm, requestid, bm, "0");
                    if(!flag){
                        requestInfo.getRequestManager().setMessagecontent("插入明细数据错误");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                }
            } else {
                requestInfo.getRequestManager().setMessagecontent("该一级部门、该月度/季度 未生成原始数据，请联系管理员");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
        }
        return SUCCESS;
    }

    /**
     * 查询mainId
     * @return
     */
    public int select(String archiveTableName, String szyjbm, String value){
        String sql = "select id from " + archiveTableName + " where yjbmmc = ? and jxszyf = ?";
        rs.executeQuery(sql, szyjbm, value);
        if(rs.next()){
            return rs.getInt("id");
        }
        return -1;
    }


    public int selectByJxzdlc(String jxzdlc, String insertTableName){
        rs.executeQuery("select id from " + insertTableName + "_dt1 where jxzdlc = ?", jxzdlc);
        if(rs.next()){
            return rs.getInt("id");
        }
        return -1;
    }




}

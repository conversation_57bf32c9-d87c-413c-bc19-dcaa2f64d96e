package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 删除流程并释放金额
 */
public class DelAndLoanMoneyAction implements Action {
    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 获取requestid
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");


        String sql = "select  *  from  " + tableName + "  where requestid = ?";
        String sfhx = ""; // 是否有借款
        String jkje = ""; // 借款金额
        double bxzje = 0.00; // 实报金额
        recordSet.executeQuery(sql, requestid);
        if (recordSet.next()) {

            sfhx = recordSet.getString("sfhx");
            jkje = recordSet.getString("jkje");
            bxzje = Double.valueOf(recordSet.getString("bxzje"));
        }


        if ("0".equals(sfhx)) { // 是借款
            Map jkje1 = ActionUtil.findJKJE(jkje);
            double jkwhxje = Double.valueOf((String) jkje1.get("jkwhxje"));
            String jkjebgx = (String) jkje1.get("jkjebgx");
            String djje = (String) jkje1.get("djje"); // 冻结金额

            if (jkwhxje > bxzje) { // 借款大于报销

                double sjje = jkwhxje + bxzje;
                if (Double.valueOf(djje) > bxzje) {
                    djje = (Double.valueOf(djje) - bxzje) + "";
                } else {
                    djje = "0";
                }
                recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ? where id = ?", sjje, djje, jkje);
            } else {
                String je = getJe(requestid);
                if (je != "" && je != null) {
                    jkjebgx = je;
                    djje = (Double.valueOf(djje) - Double.valueOf(je)) + "";
                } else {
                    jkjebgx = djje;
                    djje = "0";
                }
                recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", jkjebgx, djje, jkje);

            }



    }
        return SUCCESS;
}

    public String getJe(String requestid) {
        String sql = "select * from uf_je where lcid= ?";
        Map map = new HashMap();
        recordSet.executeQuery(sql, requestid);
        String whxje = "";
        if (recordSet.next()) {
            whxje = recordSet.getString("je");


        }
        return whxje;
    }
}

package yitouniu.afternodeoperation;

import com.google.common.base.Joiner;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/1/12 14:53
 * @Description TODO
 * @Version 1.0
 */
public class PdWmsPricePreAction implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {
        try {
            String requestId = requestInfo.getRequestid();
            String tableName = requestInfo.getRequestManager().getBillTableName();

            Property[] properties = requestInfo.getMainTableInfo().getProperty();

            String sqr = "";

            for (int i = 0; i < properties.length; ++i) {
                String name = properties[i].getName();
                if ("sqr".equals(name)) {
                    sqr = Util.null2String(properties[i].getValue());
                }
            }

            //明细
            Map<String, Map<String, String>> tableItemMap = new HashMap<>();
            List<String> itemcodeList = new ArrayList<>();
            String sql = "select b.id,b.itemcode,b.quantity from " + tableName + " a left join " + tableName + "_dt1 b on a.id = b.mainid where a.requestid = ? ";
            rs.executeQuery(sql, requestId);
            while (rs.next()) {
                Map<String, String> tableItem = new HashMap<>();
                String itemcode = rs.getString("itemcode");
                tableItem.put("itemcode", itemcode);
                tableItem.put("quantity", rs.getString("quantity"));
                itemcodeList.add(itemcode);
                tableItemMap.put(rs.getString("id"), tableItem);
            }
            String BWKEY = "";
            String sqlGc = "select gcdm from HrmResource a left join uf_gongsigongchangd b on a.subcompanyid1 = b.gsdmms where a.id = ? ";
            rs.executeQuery(sqlGc, sqr);
            if (rs.next()) {
                BWKEY = rs.getString("gcdm");
            }
            baseBean.writeLog("itemcodeList = " + itemcodeList);
            baseBean.writeLog("tableItemMap = " + tableItemMap);
            Map<String, String> itemcodeCost = getItemcodeCost(itemcodeList, BWKEY);
            baseBean.writeLog("itemcodeCost = " + itemcodeCost);

            BigDecimal amount = new BigDecimal(BigInteger.ZERO);

            for (Map.Entry<String, Map<String, String>> entry : tableItemMap.entrySet()) {
                String id = entry.getKey();
                Map<String, String> item = entry.getValue();
                String itemcode = item.get("itemcode");
                String costStr = "0";
                if (StringUtils.isNotBlank(itemcodeCost.get(itemcode))) {
                    costStr = itemcodeCost.get(itemcode);
                }
                BigDecimal cost = new BigDecimal(costStr);
                boolean flag = rs.executeUpdate("update " + tableName + "_dt1 set cost = ? where id = ? ", cost, id);
                baseBean.writeLog("id行 = " + id + ",cost = " + cost + "更新明细成本价flag = " + flag);
                BigDecimal quantity = new BigDecimal(item.get("quantity"));
                BigDecimal varianceAmount = cost.multiply(quantity);
                amount = amount.add(varianceAmount);
            }
            boolean cyjefFlag = rs.executeUpdate("update " + tableName + " set cyje = ? where requestid = ?", amount, requestId);
            baseBean.writeLog("requestid = " + requestId + ", amount = " + amount + "更新主表差异金额flag = " + cyjefFlag);
        }
        catch (Exception e) {
            baseBean.writeLog("PdWmsPriceAction:异常：", e);
            requestInfo.getRequestManager().setMessagecontent("PdWmsPriceAction:异常：" + e);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }


    public Map<String, String> getItemcodeCost(List<String> itemcodeList, String BWKEY) {
        Map<String, String> result = new HashMap<>();
        String matnr = Joiner.on("','").join((Iterable<?>) itemcodeList);
        matnr = "'" + matnr + "'";

        String sql = "select MATNR,STPRS,PEINH from uf_wlzsj where MATNR in (" + matnr + ") and BWKEY = " + BWKEY;
        baseBean.writeLog("getItemcodeCost sql = " + sql);
        rs.executeQuery(sql);
        while (rs.next()) {
//            result.put("itemcode", rs.getString("MATNR"));
            String STPRSStr = rs.getString("STPRS");
            if (StringUtils.isBlank(STPRSStr)) {
                STPRSStr = "0";
            }
            BigDecimal STPRS = new BigDecimal(STPRSStr);
            BigDecimal PEINH = new BigDecimal(rs.getString("PEINH"));
//            BigDecimal o = new BigDecimal(BigInteger.ZERO);

            result.put(rs.getString("MATNR"), STPRS.divide(PEINH).toString());
        }

        return result;
    }


}

package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/4/17 9:29
 * @Description 建档一体化推送数据到sap  Z002主数据信息（客户）
 * @Version 1.0
 */
public class JdythZ002ToSapActionV15 implements Action {
    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestId = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String)tableNameByRequestId.get("tableName");
        Map<String, String> mainTableMap = new HashMap<>();
        int mainId = -1;
        String lcbh = "";
        int fhms = -1;
        String fktjms = "";
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestId);
        if(main.next()) {
            mainId = main.getInt("id");
            lcbh = main.getString("lcbh");
            fhms = main.getInt("fhms");
            fktjms = main.getString("sapfktj");
            mainTableMap.put("xyed", main.getString("xyed"));
            mainTableMap.put("STR_SUPPL1", main.getString("STR_SUPPL1"));
            mainTableMap.put("LIQUID_DAT", main.getString("LIQUID_DAT"));
            mainTableMap.put("BUKRS", main.getString("BUKRS"));
            mainTableMap.put("VKGRP", main.getString("VKGRP"));
            mainTableMap.put("VKBUR", main.getString("VKBUR"));
            mainTableMap.put("KVGR4", main.getString("KVGR4"));
            mainTableMap.put("PERNR", main.getString("PERNR"));
            mainTableMap.put("KATR3", main.getString("qdlxzyjxs"));
            /*效期要求*/
            mainTableMap.put("ZXQYQ", convertXqyq(main.getString("xqyq")));
            /*客户渠道*/
            mainTableMap.put("ZKHQD", main.getString("ZKHQD"));
            /*销售区域冻结*/
            mainTableMap.put("AUFSD", main.getString("AUFSD"));
            /*客户组5*/
            mainTableMap.put("KVGR5", main.getString("KVGR5"));
            /***********/
            mainTableMap.put("KATR5", main.getString("qddl"));
            mainTableMap.put("KATR6", main.getString("gzqdznbmjky"));
            mainTableMap.put("ZQDMC", main.getString("xssqqd"));


            mainTableMap.put("CUST_ADDRESS", main.getString("yfkpdz"));
            mainTableMap.put("CUST_TELEPHONE", main.getString("yfkpdh"));
            mainTableMap.put("CUST_BANKNAME", main.getString("yfkhx"));
            mainTableMap.put("ZCUST_BANKACCOUNT", main.getString("yfzh"));

            mainTableMap.put("CUST_EMAIL", main.getString("yx1"));
            mainTableMap.put("CUST_MOBILE", main.getString("lxdh1"));
            mainTableMap.put("ZNAME_SALES", main.getString("xsywbjky"));

            String zterm = main.getString("ZTERM");

            if ("1".equals(zterm)) {
                zterm = "A";
            } else if ("2".equals(zterm)) {
                zterm = "B";
            } else if ("3".equals(zterm)) {
                zterm = "C";
            } else if ("4".equals(zterm)) {
                zterm = "D";
            }
            mainTableMap.put("ZTERM_BASIC", zterm);

            String zqdlx = main.getString("khlx");
            if ("0".equals(zqdlx)) {
                zqdlx = "01";
            } else if ("1".equals(zqdlx)) {
                zqdlx = "02";
            } else if ("2".equals(zqdlx)) {
                zqdlx = "03";
            } else if ("3".equals(zqdlx)) {
                zqdlx = "04";
            } else if ("4".equals(zqdlx)) {
                zqdlx = "05";
            } else if ("5".equals(zqdlx)) {
                zqdlx = "06";
            }
            mainTableMap.put("ZQDLX", zqdlx);
        }
        mainTableMap.put("KUKLA", fhms == 0 ? "01" : "02");
        mainTableMap.put("ZTERM", fktjms.split(" ")[0]);

        JSONArray costCenterArray = getKOSTL(tableName, mainId);
        baseBean.writeLog("JdythZ002ToSapAction.costCenterArray" + costCenterArray);

        Map<String, JSONObject> objectMap = getZ002ReqJson(requestId, mainId, lcbh, tableName, costCenterArray, mainTableMap);

        baseBean.writeLog("JdythZ002ToSapAction.objectMap " + objectMap);

        if(objectMap.isEmpty()){
            return SUCCESS;
        }
        //请求后的
        Map<String,String> map = new HashMap<>();

        String errMsg = "";
        int sCount = 0;
        int aCount = 0;
        int eCount = 0;
        //空
        int errCount = 0;
        String finalPartner = "";
        String finalNAME_ORG1 = "";
        for (Map.Entry<String, JSONObject> o : objectMap.entrySet()) {
            JSONObject resp = JSONObject.parseObject(SAPUtil.execute(o.getValue().toJSONString()));
            String[] key = o.getKey().split(",");
            String line = key[0];
            String id = key[1];

            baseBean.writeLog("JdythZ002ToSapAction.objectMap,key:" + o.getKey() + ",value:" + o.getValue() + ",resp:" + resp);

            if(resp != null){
                JSONObject ctrl = resp.getJSONObject("CTRL");
                JSONArray data = resp.getJSONArray("DATA");
                String MSGTY = ctrl.getString("MSGTY");
                String partner = data.getJSONObject(0).getString("PARTNER");
                if(StringUtils.isBlank(finalPartner)){
                    finalPartner = partner;
                    finalNAME_ORG1 = o.getValue().getJSONObject("DATA").getJSONObject("BASIC").getString("NAME_ORG1");
                }
                String MSAGE = ctrl.getString("MSAGE");
                if("S".equals(MSGTY)){
                    sCount++;
                    updateItem5Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                } else if ("A".equals(MSGTY)){
                    aCount++;
                    updateItem5Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else if("E".equals(MSGTY)){
                    eCount++;
                    updateItem5Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else {
                    errCount++;
                    updateItem5Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                }
            }
        }
        if(objectMap.size() == 1){
            if(!rs.executeUpdate("update " + tableName + "_dt6 set khbm = ? where mainid = ?", finalPartner, mainId)){
                requestInfo.getRequestManager().setMessagecontent("更新明细表6的Z002客户编码失败");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
            if(!rs.executeUpdate("update " + tableName + "_dt7 set KNRZE = ? where mainid = ?", finalPartner, mainId)){
                requestInfo.getRequestManager().setMessagecontent("更新明细表7的总部失败");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
        }
        if (!rs.executeUpdate("update " + tableName + " set htqyfmc = ?,htqyfdm = ? where requestid = ?", finalNAME_ORG1, finalPartner, requestId)) {
            requestInfo.getRequestManager().setMessagecontent("更新主表的SAP客户名称、编码失败");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
//        if(!rs.executeUpdate("update " + tableName + " set z002zsjxxkhsl = ? where requestid = ?", objectMap.size(), requestId)){
//            requestInfo.getRequestManager().setMessagecontent("更新主表的Z002主数据信息（客户）数量失败");
//            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
//            return FAILURE_AND_CONTINUE;
//        }
        if (aCount > 0 || eCount > 0 || errCount > 0) {
            String msg = "总数为" + objectMap.size() + "个，成功的为" + sCount + "个,逻辑成功的为" + aCount + "个，失败的为" + (eCount + errCount) + "个";
            requestInfo.getRequestManager().setMessagecontent(msg + " " + errMsg);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }

    private String convertXqyq(String xqyq) {
        if ("0".equals(xqyq)) {
            return "2";
        }
        if ("2".equals(xqyq)) {
            return "3";
        }
        if ("3".equals(xqyq)) {
            return "4";
        }
        return xqyq;
    }


    /**
     * 获取 Z002 的请求json
     * @param requestId
     * @param mainId
     * @param lcbh
     * @param tableName
     * @param costCenterArray
     * @return
     */
    public Map<String, JSONObject> getZ002ReqJson(String requestId, Integer mainId, String lcbh
            , String tableName, JSONArray costCenterArray, Map<String, String> map){
        int line = 1;
        Map<String, JSONObject> objectMap = new HashMap<>();
        rs.executeQuery("select * from " + tableName + "_dt5 where mainid = ? and sfyts <> 0 order by id", mainId);
        while(rs.next()){
            JSONObject reqJson = new JSONObject();
            JSONObject ctrlReq = SAPUtil.getCtrlReq(requestId, line, lcbh, "ZINF066", "5");
            JSONObject dataReq = new JSONObject();
            JSONObject basicJson = new JSONObject();
            basicJson.put("BU_GROUP", "Z002");
            basicJson.put("NAME_ORG1", rs.getString("NAME_ORG1"));
            basicJson.put("BU_SORT2", rs.getString("BU_SORT2"));
            basicJson.put("NAME_CO", rs.getString("NAME_CO"));
            basicJson.put("STR_SUPPL1", map.get("STR_SUPPL1"));
            basicJson.put("STREET", rs.getString("STREET"));
            basicJson.put("CITY1", rs.getString("CITY1"));
            basicJson.put("CITY2", rs.getString("CITY2"));
            basicJson.put("REGIO", rs.getString("REGIO"));
            basicJson.put("TEL_NUMBER", rs.getString("TEL_NUMBER"));
            basicJson.put("LAND1", "CN");
            basicJson.put("LANGU", "ZH");
            basicJson.put("TAXNUM", rs.getString("TAXNUM"));
            basicJson.put("RISK_CLASS", "B");
            basicJson.put("CHECK_RULE", "Z1");
            basicJson.put("KUKLA", map.get("KUKLA"));
            basicJson.put("LIQUID_DAT", map.get("LIQUID_DAT").replace("-", ""));
            // 邮箱
            basicJson.put("SMTP_ADDR", rs.getString("SMTP_ADDR"));
            // 渠道属性 直营-0 经销商-1
            basicJson.put("KATR3", map.get("KATR3"));
            /*效期要求*/
            basicJson.put("ZXQYQ", map.get("ZXQYQ"));
            /*客户渠道*/
            basicJson.put("ZKHQD", map.get("ZKHQD"));

            /***********/
            basicJson.put("KATR5", map.get("KATR5"));
            basicJson.put("KATR6", map.get("KATR6"));
            basicJson.put("ZQDMC", map.get("ZQDMC"));
            basicJson.put("ZTERM", map.get("ZTERM_BASIC"));
            basicJson.put("ZQDLX", map.get("ZQDLX"));

            JSONArray companyJsonArray = new JSONArray();
            JSONObject companyJson = new JSONObject();

            if ("0".equals(map.get("BUKRS"))) {
                companyJson.put("BUKRS", "6000");
            }
            if ("1".equals(map.get("BUKRS"))) {
                companyJson.put("BUKRS", "6100");
            }
            if ("2".equals(map.get("BUKRS"))){
                companyJson.put("BUKRS", "3000");
            }
            if ("3".equals(map.get("BUKRS"))){
                companyJson.put("BUKRS", "6300");
            }
            companyJson.put("AKONT", "**********");
            companyJson.put("ZUAWA", "031");
            companyJson.put("ZTERM", map.get("ZTERM"));
            companyJson.put("TOGRU", "8000");
            companyJson.put("KNRZE", "");
            companyJsonArray.add(companyJson);

            JSONArray bankJsonArray = new JSONArray();
            JSONObject bankJson = new JSONObject();
            bankJson.put("BANK_LAND", "CN");
            String BANK_ID = rs.getString("BANK_ID");
            String BANK_NO = rs.getString("BANK_NO");
            String KOINH = rs.getString("KOINH");
            bankJson.put("BANK_ID", BANK_ID);
            if(BANK_NO.length() >18){
                bankJson.put("BANK_NO", BANK_NO.substring(0, 18));
                bankJson.put("BKREF", BANK_NO.substring(18));
            } else {
                bankJson.put("BANK_NO", BANK_NO);
                bankJson.put("BKREF", "");
            }
            bankJson.put("KOINH", KOINH);
            if(StringUtils.isNotEmpty(BANK_ID) && StringUtils.isNotBlank(BANK_NO) && StringUtils.isNotBlank(KOINH)){
                bankJsonArray.add(bankJson);
            }

            JSONArray salesJsonArray = new JSONArray();
            JSONObject saleJson = new JSONObject();

            if ("0".equals(map.get("BUKRS"))){
                saleJson.put("VKORG", "6000");
            }
            if ("1".equals(map.get("BUKRS"))){
                saleJson.put("VKORG", "6100");
            }
            if ("2".equals(map.get("BUKRS"))){
                saleJson.put("VKORG", "3000");
            }
            if ("3".equals(map.get("BUKRS"))){
                saleJson.put("BUKRS", "6300");
            }
            saleJson.put("VTWEG", "00");
            saleJson.put("SPART", "00");
            saleJson.put("WAERS", "CNY");
            saleJson.put("VKBUR", map.get("VKBUR"));
            saleJson.put("VKGRP", map.get("VKGRP"));
            saleJson.put("KVGR4", map.get("KVGR4"));
            saleJson.put("VSBED", "01");
            saleJson.put("PODKZ", "X");
            saleJson.put("KALKS", "1");
            saleJson.put("KONDA", "01");
            saleJson.put("KTGRD", "01");
            saleJson.put("ZTERM_V", map.get("ZTERM"));
            saleJson.put("TAXKD", "1");
            saleJson.put("KVGR3", rs.getInt("KVGR3") == 0 ? "X" : "");
            saleJson.put("PERNR", map.get("PERNR"));
            /*销售区域冻结*/
            saleJson.put("AUFSD", map.get("AUFSD"));
            /*客户组5*/
            saleJson.put("KVGR5", map.get("KVGR5"));
            salesJsonArray.add(saleJson);

            JSONArray creditJsonArray = new JSONArray();
            JSONObject creditJson = new JSONObject();
            creditJson.put("ZBUKRS", rs.getString("SEGMENT"));
            creditJson.put("CREDIT_LIMIT", map.get("xyed"));
            creditJson.put("LIMIT_VALID_DATE", map.get("LIQUID_DAT").replace("-", ""));
            creditJsonArray.add(creditJson);


            JSONArray invoiceJsonArray = new JSONArray();
            JSONObject invoiceJson = new JSONObject();
            invoiceJson.put("CUST_ADDRESS", map.get("CUST_ADDRESS"));
            invoiceJson.put("CUST_TELEPHONE", map.get("CUST_TELEPHONE"));
            invoiceJson.put("CUST_BANKNAME", map.get("CUST_BANKNAME"));
            invoiceJson.put("ZCUST_BANKACCOUNT", map.get("ZCUST_BANKACCOUNT"));

            invoiceJson.put("ZZKPLX", "K");
            invoiceJson.put("INVOICE_WAY", "1");

            invoiceJson.put("CUST_EMAIL", map.get("CUST_EMAIL"));
            invoiceJson.put("CUST_MOBILE", map.get("CUST_MOBILE"));
            invoiceJson.put("ZNAME_SALES", map.get("ZNAME_SALES"));
            invoiceJsonArray.add(invoiceJson);

            dataReq.put("BASIC", basicJson);
            dataReq.put("company", companyJsonArray);
            if(!bankJsonArray.isEmpty()){
                dataReq.put("BANK", bankJsonArray);
            }
            dataReq.put("sales", salesJsonArray);
            dataReq.put("credit", creditJsonArray);
            dataReq.put("Cost_Center", costCenterArray);
            dataReq.put("ZJS", invoiceJsonArray);

            reqJson.put("CTRL", ctrlReq);
            reqJson.put("DATA", dataReq);

            objectMap.put(line + "," + rs.getString("id"), reqJson);

            line++;
        }

        return objectMap;
    }


    public JSONArray getKOSTL(String tableName, int mainId){
        rs.executeQuery("select * from " + tableName + "_dt8 where mainid = ?", mainId);
        JSONArray jsonArray = new JSONArray();
        while(rs.next()){
            JSONObject req = new JSONObject();
            String VKORG = rs.getString("VKORG");
            if ("0".equals(VKORG)){
                req.put("VKORG", "6000");
            }
            if ("1".equals(VKORG)){
                req.put("VKORG", "6100");
            }
            if ("2".equals(VKORG)){
                req.put("VKORG", "3000");
            }
            if ("3".equals(VKORG)){
                req.put("VKORG", "6300");
            }
            req.put("ZATTRIBUTE", rs.getString("ZATTRIBUTE"));
            req.put("KOSTL", rs.getString("KOSTL"));
            jsonArray.add(req);
        }

        return jsonArray;
    }

    /**
     * 更新明细表5的数据
     * @param tableName
     */
    public void updateItem5Sql(String tableName,String id, String sfyts, String partner, String MSGTY, String MSAGE){
        String sql = "";
        sql = "update "+tableName+"_dt5  set sfyts = ?,NAME_NO = ?, xxlx = ?, xxnr = ? where id = ?";
        rs.executeUpdate(sql, sfyts, partner, MSGTY, MSAGE, id);
    }








}

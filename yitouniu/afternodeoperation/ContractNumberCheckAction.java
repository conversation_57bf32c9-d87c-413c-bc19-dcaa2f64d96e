package yitouniu.afternodeoperation;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

/**
 * <AUTHOR>
 * @Date 2022/12/14 14:56
 * @Description 合同流程,关联合同编号 （补充与原始校验）
 * @Version 1.0
 */
public class ContractNumberCheckAction implements Action {
    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tablename = requestInfo.getRequestManager().getBillTableName();
        //关联合同编号
        String sourceContractNumber = "";
        //关联合同编号（1）
        String zsglhtbh = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("sourceContractNumber".equals(name)) {
                sourceContractNumber = Util.null2String(properties[i].getValue());
            }else if ("zsglhtbh".equals(name)) {
                zsglhtbh = Util.null2String(properties[i].getValue());
            }
        }
        String htbh = zsglhtbh;
        baseBean.writeLog("ContractNumberCheckAction zsglhtbh = " + zsglhtbh);
        baseBean.writeLog("ContractNumberCheckAction sourceContractNumber = " + sourceContractNumber);
        baseBean.writeLog("ContractNumberCheckAction htbh = " + htbh);

        for(int i = 0;i<1000;i++){
            String htlx = "";
            String hth = "";

            String sql = "select * from uf_httzb where hth = ?";
            rs.executeQuery(sql, htbh);
            if(rs.next()){
                htlx = rs.getString("htlx");
                htbh = rs.getString("glhth");
                hth = rs.getString("hth");
            }
            if("0".equals(htlx) || StringUtils.isBlank(htlx)){
                boolean flag = rs.executeUpdate("update " + tablename +" set sourceContractNumber = ? where requestid = ?", hth, requestid);
                baseBean.writeLog("ContractNumberCheckAction flag = " + flag);
                return SUCCESS;
            }

        }

        return SUCCESS;
    }


}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 设置物资需求需求单号
 */
public class WorkflowNumber implements Action {
    private final String TABLE_NAME = "formtable_main_301,formtable_main_629";
    //private final String TABLE_NAME = "formtable_main_143";

    @Override
    public String execute(RequestInfo requestInfo) {
        new BaseBean().writeLog("设置物资需求需求单号开始");
        RecordSet recordSet = new RecordSet();
        Map<String,String> tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestInfo.getRequestid()); // 获取表明

        String tableName = tableNameByRequestId.get("tableName");
        String bh = "";
        if (TABLE_NAME.contains(tableName)){
            bh = "ITEM";
        }else {
            bh = "bh";
        }

        String sql = "select * from  "+tableName+"  where requestid = ?";
        recordSet.executeQuery(sql,requestInfo.getRequestid());
        String id = "";
        String lcbh = "";
        if (recordSet.next()){
            id = recordSet.getString("id");
            lcbh = recordSet.getString("lcbh");
        }
        sql = "select * from  "+tableName+"_dt1  where mainid = ? ";
        recordSet.executeQuery(sql,id);
        List<String> detailedId = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        while (recordSet.next()){
            detailedId.add(recordSet.getString(bh));
            idList.add(recordSet.getString("id"));
        }

        for (int i = 0; i < detailedId.size(); i++) {

            String xqdhh = lcbh + detailedId.get(i);
            String updatesql = "update  "+tableName+"_dt1  set lcbhjxh = ? where id = ?";

            new BaseBean().writeLog("更新编号结果: "+recordSet.executeUpdate(updatesql,xqdhh,idList.get(i)));
        }
        return SUCCESS;
    }
}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 对公税率计算
 */
public class TaxRateCalculationDGAction implements Action {
    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        new BaseBean().writeLog("税率计算开始: " + requestid);
        try {


            List<String> idList = new ArrayList<>();
            List<String> taxRate1List = new ArrayList<>(); // 税率
            List<String> fpjeList = new ArrayList<>();// 发票价税合计


            Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
            String tableName = (String) tableNameByRequestId.get("tableName");

            String sql = "select  b.id,b.taxRate1,b.se,b.wsje,b.fkje  from  " + tableName + " a LEFT JOIN  " + tableName + "_dt1 b on a.id = b.mainid  where requestid = ?  ";
            recordSet.executeQuery(sql, requestid);
            while (recordSet.next()) {
                idList.add(recordSet.getString("id"));
                taxRate1List.add(recordSet.getString("taxRate1"));

                fpjeList.add(recordSet.getString("fkje"));


            }


            for (int i = 0; i < idList.size(); i++) {
                String id = idList.get(i);
                if (!"".equals(taxRate1List.get(i)) && taxRate1List != null) {
                    switch (taxRate1List.get(i)) {
                        case "0":

                            calculation("0.13", fpjeList.get(i),  tableName, id);
                            break;
                        case "1":
                            calculation("0.09", fpjeList.get(i),  tableName, id);
                            break;
                        case "2":
                            calculation("0.06", fpjeList.get(i),  tableName, id);
                            break;
                        case "3":
                            calculation("0.05", fpjeList.get(i), tableName, id);
                            break;
                        case "4":
                            calculation("0.03", fpjeList.get(i), tableName, id);
                            break;
                        case "5":
                            calculation("0.00", fpjeList.get(i),  tableName, id);
                            break;
                        case "6":
                            calculation("0.01", fpjeList.get(i), tableName, id);
                            break;

                    }
                } else {
                    sql = "update " + tableName + "_dt1 set se = ? ,wsje = ? where id = ?";
                    recordSet.executeUpdate(sql, "0.00", fpjeList.get(i), id);
                }
            }
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }

    public void calculation(String sll, String jshj, String tableName, String id) {
        double sl = Double.valueOf(sll) + 1;

        String wsje = String.valueOf(NumberUtil.divide(jshj, sl + "", 2)); // 无税金额
        String se = String.valueOf(NumberUtil.subtract(jshj, wsje)); // 无税金额

        String sql = "update " + tableName + "_dt1 set se = ? ,wsje = ? where id = ?";
        recordSet.executeUpdate(sql, se, wsje, id);

    }
}

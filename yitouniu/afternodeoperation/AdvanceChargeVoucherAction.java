package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 预付款推送凭证
 */
public class AdvanceChargeVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();
    private String hrmName = "";

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        String mainid = "";
        String NAME1 = ""; // 收款单位名称
        String VBUND = ""; // 贸易伙伴
        String PARTNER = ""; // 收款单位编码
        String lcbh = ""; // 流程编号
        String sqr = ""; // 流程编号
        String KUNNR = ""; // 客户编号
        String bankNum = ""; // 科目编码
        String fklb = ""; // 付款类型
        String fkfs = ""; // 付款方式


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");
            PARTNER = rs1.getString("PARTNER");
            VBUND = rs1.getString("VBUND");
            NAME1 = rs1.getString("NAME1");
            lcbh = rs1.getString("lcbh");
            sqr = rs1.getString("sqr");
            KUNNR = rs1.getString("KUNNR");
            bankNum = rs1.getString("BANKNUM");
            fklb = rs1.getString("fklb");
            fkfs = rs1.getString("fkfs");


        }

        String UMSKZ = ""; // 特别总账标识
        String gys = ""; // gys
        String jzm = "50"; //记账码
        if ("1".equals(fkfs)) { // 如果为承兑  借应付  贷应付票据
            bankNum = "";
            gys = PARTNER;
            UMSKZ = "7";
            jzm = "39";
        }
        hrmName = ActionUtil.getHrmName(sqr); // 获取员工姓名

        // 删除明细表三
        sql = "DELETE FROM " + tableName + "_dt3  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表三删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");


        // 查询明细表1的数据

        List<String> je = new ArrayList<>();//金额

        List<String> HKONT = new ArrayList<>(); // 费用科目

        List<String> aufnr = new ArrayList<>(); // 内部订单号
        List<String> RSTGR = new ArrayList<>(); // 原因代码
        List<String> cgddh = new ArrayList<>(); // 采购订单号
        List<String> bz = new ArrayList<>(); // 采购订单号
        List<String> fylx = new ArrayList<>(); // 采购订单号


        sql = "select * from " + tableName + "_dt1 where mainid = ?";
        rs1.executeQuery(sql, mainid);

        while (rs1.next()) {

            je.add(rs1.getString("WRBTR"));

            HKONT.add(rs1.getString("HKONT1"));

            aufnr.add(rs1.getString("AUFNR"));
            RSTGR.add(rs1.getString("RSTGR"));
            cgddh.add(rs1.getString("cgddh"));
            bz.add(rs1.getString("bz"));
            fylx.add(rs1.getString("fylx"));


        }

        // 付款类型
        String fklxValue = ActionUtil.getSelectName(billid, "fklb", fklb);


        String YF_totalMoney = "0.0"; // 预付总计金额
        String YJ_totalMoney = "0.0";// 押金总计金额
        String DT_totalMoney = "0.0";// 待摊费总计金额
        String BZJ_totalMoney = "0.0";// 保证金总计金额
        String QTYF_totalMoney = "0.0";// 单位往来(供应商)
        String QTYS_totalMoney = "0.0";// 单位往来(客户)
        String SGTXT = "";
        for (int i = 0; i < je.size(); i++) {
            SGTXT = hrmName + "申请--预付" + NAME1 + fklxValue + "款" + "-" + bz.get(i) + "-" + fylx.get(i);
            String money = je.get(i); // 金额
            String ddh = aufnr.get(i); // 订单号

            String km = HKONT.get(i);
            switch (km) {
                // 预付账款 KZ
                case "3":
                    YF_totalMoney = ActionUtil.bigDecimalAdd(YF_totalMoney, money); // 总金额
                    ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT, money, "", "29", "A", cgddh.get(i), "", PARTNER, VBUND, mainid, "KZ");
                    break;
                // 押金 KZ
                case "1":
                    ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT, money, "", "29", "3", cgddh.get(i), "", PARTNER, VBUND, mainid, "KZ");
                    YJ_totalMoney = ActionUtil.bigDecimalAdd(YJ_totalMoney, money); // 总金额

                    break;
                // 其他应收款-待摊费用 SA
                case "0":
                    ActionUtil.insertData(tableName + "_dt3", "", "1221030000", "", "", SGTXT, je.get(i), "", "40", "", cgddh.get(i), "", PARTNER, VBUND, mainid, "ZP");
                    DT_totalMoney = ActionUtil.bigDecimalAdd(DT_totalMoney, money); // 总金额

                    break;
                // 保证金 DZ
                case "2":
                    ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT, money, KUNNR, "09", "3", cgddh.get(i), "", "", VBUND, mainid, "DZ");
                    BZJ_totalMoney = ActionUtil.bigDecimalAdd(BZJ_totalMoney, money); // 总金额

                    break;
                // 其他应付款-单位往来(供应商) KZ
                case "4":

                    ActionUtil.insertData(tableName + "_dt3", "", "1221070000", "", "", SGTXT, money, "", "29", "1", cgddh.get(i), "", PARTNER, VBUND, mainid, "KZ");
                    QTYF_totalMoney = ActionUtil.bigDecimalAdd(QTYF_totalMoney, money); // 总金额

                    break;
                // 其他应收款-单位往来(客户) DZ
                case "5":
                    ActionUtil.insertData(tableName + "_dt3", "", "**********", "", "", SGTXT, money, KUNNR, "09", "4", cgddh.get(i), "", "", VBUND, mainid, "DZ");
                    QTYS_totalMoney = ActionUtil.bigDecimalAdd(QTYS_totalMoney, money); // 总金额

                    break;

            }


        }
        // 贷方数据
        if (Double.valueOf(YF_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, YF_totalMoney + "", "", jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "KZ");

        }
        if (Double.valueOf(YJ_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, YJ_totalMoney + "", "", jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "KZ");

        }
        if (Double.valueOf(DT_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, DT_totalMoney + "", "", jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "SA");

        }
        if (Double.valueOf(BZJ_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, BZJ_totalMoney + "", KUNNR, jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "DZ");

        }
        if (Double.valueOf(QTYF_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, QTYF_totalMoney + "", "", jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "KZ");

        }
        if (Double.valueOf(QTYS_totalMoney) != 0) {
            ActionUtil.insertData(tableName + "_dt3", "", bankNum, RSTGR.get(0), "", SGTXT, QTYS_totalMoney + "", "", jzm, UMSKZ, cgddh.get(0), "", gys, VBUND, mainid, "DZ");

        }


        return SUCCESS;

    }


}

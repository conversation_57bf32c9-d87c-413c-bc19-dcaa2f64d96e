package yitouniu.afternodeoperation;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.OMSUtils;
import yitouniu.util.TimeUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/22 16:37
 * @Description 指定批次推送OMS
 * @Version 1.0
 */
public class DesignatedBatchActionV3 implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);
        JSONObject requestObj = new JSONObject();
        String result = "";
        String apiName = "";
        if (main.next()) {
            JSONObject expiryDate = new JSONObject();
            //mainId
            String mainId = main.getString("id");
            //店铺类型
            int expiryType = main.getInt("lx");
            //店铺编码
            String shopCode = main.getString("dpid");
            //店铺名称
            String shopName = main.getString("dpmc");
            //付款开始时间
            String startTime = main.getString("fkkssj");
            //付款结束时间
            String endTime = main.getString("fkjssj");
            //备注
            String remarks = main.getString("bz");
            //请求类型  1：校验  2：新增
            int type = main.getInt("type");
            // oa 创建人
            String createUser = main.getString("oaCreator");
            String oaNum = main.getString("oaNum");
            String api = "";
            if(type == 1){
                apiName = "校验";
                api = Util.null2String(new BaseBean().getPropValue("oms_configuration","expiryPreApi"));
            } else {
                apiName = "新增";
                api = Util.null2String(new BaseBean().getPropValue("oms_configuration","expirySaveApi"));
            }
//            expiryDate.put("expiryType",expiryType);
            //默认为指定店铺 2
            expiryDate.put("expiryType",2);
            expiryDate.put("shopCode",shopCode);
            expiryDate.put("shopName",shopName);
            expiryDate.put("startTime",startTime);
            expiryDate.put("endTime",endTime);
            expiryDate.put("remarks",remarks);
            expiryDate.put("oaCreator",createUser);
            expiryDate.put("oaNum",oaNum);

            //循环明细表获取明细
            String itemSql = "select * from " + tableName + "_dt1 where mainid = ? ";
            item.executeQuery(itemSql, mainId);

            JSONArray itemArray = new JSONArray();
            while(item.next()){
                JSONObject itemObj = new JSONObject();
                //指定维度
                int appointDimension = replaceZdwd(item.getInt("zdwd"));
                //指定内容
                String appointContent = item.getString("zdnr");
                //指定类型
                int zdlx = replaceZdlx(item.getInt("zdlx"));
                //开始时间/天数
                String startDateDay = item.getString("kssjts");
                //结束时间/天数
                String endDateDay = item.getString("jssjts");
                // 是否最便宜快递
                String cheapestExpress = item.getString("cheapestExpress");

                if(zdlx == 1){
                    try {
                        long start = TimeUtils.changeTimeToDate(startDateDay, "yyyyMMdd").getTime();
                        long end = TimeUtils.changeTimeToDate(endDateDay, "yyyyMMdd").getTime();
                        if(end<start){
                            requestInfo.getRequestManager().setMessagecontent("【结束生产日期/天数】小于【开始生产日期/天数】，不允许!");
                            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                            return FAILURE_AND_CONTINUE;
                        }
                    } catch (Exception e) {
                        baseBean.writeLog("时间格式转换异常",e);
                        requestInfo.getRequestManager().setMessagecontent("类型为生产日期范围，时间格式错误，不允许!");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                }else if(zdlx == 2){
                    try {
                        int start = Integer.parseInt(startDateDay);
                        int end = Integer.parseInt(endDateDay);
                        if(start < 0 || end < 0){
                            requestInfo.getRequestManager().setMessagecontent("录入的【开始生产日期/天数】或【结束生产日期/天数】不正确，不允许");
                            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                            return FAILURE_AND_CONTINUE;
                        }
                    } catch (NumberFormatException e) {
                        baseBean.writeLog("数字格式转换异常",e);
                        requestInfo.getRequestManager().setMessagecontent("类型为生产天数范围，格式错误，不允许!");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }

                }


                //订单标签
                String orderLabel = item.getString("ddbq");
                //序号
                int serialNo = item.getInt("serialNo");
                itemObj.put("appointDimension", appointDimension);
                itemObj.put("appointContent", appointContent);
                itemObj.put("startDateDay", startDateDay);
                itemObj.put("endDateDay", endDateDay);
                itemObj.put("serialNo", serialNo);
                itemObj.put("appointType", zdlx);
                itemObj.put("orderLabel", orderLabel);
                itemObj.put("cheapestExpress", cheapestExpress);
                itemArray.add(itemObj);
            }

            requestObj.put("expiryDate", expiryDate);
            requestObj.put("expiryDateItems", itemArray);

            //获取token
            OMSUtils omsUtils = new OMSUtils();
            String token = omsUtils.getToken();

            baseBean.writeLog(apiName + "请求request = "+requestObj);
            result = HttpRequest
                    .post(OMSUtils.OMS_OMS_URL + api)
                    .header("r3-api-token",token)
                    .body(requestObj.toJSONString()).execute().body();
            new BaseBean().writeLog(apiName + "请求返回="+result);

            JSONObject resultJson = JSONObject.parseObject(result);

            if(resultJson.getInteger("code") != 0){
                JSONObject messageJson = JSONObject.parseObject(resultJson.getString("message"));
                if(messageJson == null){
                    requestInfo.getRequestManager().setMessagecontent("未校验出错误但错误，请联系负责人确认");
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }
                if(StringUtils.isNotBlank(messageJson.getString("error"))){
                    requestInfo.getRequestManager().setMessagecontent(messageJson.getString("error"));
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                } else {
                    JSONArray errorItems = messageJson.getJSONArray("errorItem");
                    if(errorItems.size() == 0){
                        return SUCCESS;
                    }
                    JSONObject errorItem = errorItems.getJSONObject(0);
                    Map<String, Object> userMap = new HashMap<>();
                    //循环转换
                    Iterator it =errorItem.entrySet().iterator();
                    while (it.hasNext()) {
                        Map.Entry<String, Object> entry = (Map.Entry<String, Object>) it.next();
                        if(StringUtils.isNotBlank(String.valueOf(entry.getValue()))){
                            rs.executeUpdate("update "+ tableName + "_dt1 set jkxyjg = ? where serialNo = ? and mainid = ?", entry.getValue(), entry.getKey(), mainId);
                        }
                        userMap.put(entry.getKey(), entry.getValue());
                    }
                    baseBean.writeLog("execute：errorItems changeTo userMap= " + userMap);
                }
                requestInfo.getRequestManager().setMessagecontent(result);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            } else {

                try {
                    String id = resultJson.getString("data");
                    rs.executeUpdate("update "+ tableName + " set clid = ? where requestid = ? ", id, requestid);
                } catch (Exception e) {
                    baseBean.writeLog("指定批次推送OMS保存返回id异常：" + Throwables.getStackTraceAsString(e));
                }

                //清除返回的校验结果
                boolean flag = rs.executeUpdate("update "+ tableName + "_dt1 set jkxyjg = '' where mainid = ? ", mainId);
                if(!flag){
                    requestInfo.getRequestManager().setMessagecontent("清除返回的校验结果失败，请重试");
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }
            }
        }
        return SUCCESS;
    }

    public int replaceZdwd(int zdwd){
        switch (zdwd){
            case 0:
                return 2;
            case 1:
                return 3;
            case 2:
                return 4;
            case 3:
                return 5;
            case 4:
                return 7;
            case 5:
                /*主播ID+商品编码*/
                return 8;
            default:
                return -1;
        }
    }

    public int replaceZdlx(int zdlx){
        switch (zdlx){
            case 0 :
                return 1;
            case 1:
                return 2;
            default:
                return -1;
        }
    }





}

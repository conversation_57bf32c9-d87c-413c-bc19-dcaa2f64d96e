package yitouniu.afternodeoperation;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.TimeUtils;
import yitouniu.util.ZJPTUtil;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/3/28 16:30
 * @Description 资金平台付款——投资款支付
 * @Version 1.0
 */
public class ZjptFkAction implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    public final static String SRCOUTSYSTEMCODE = "OA";
    public final static String TRANSCODE = "PMSQ01";

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestId = requestInfo.getRequestid();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String)tableNameByRequestId.get("tableName");
        baseBean.writeLog("ZjptFkAction requestId = " + requestId);
        String request = getFkRequest(requestId, tableName, new Date());
        if(StringUtils.isBlank(request)){
            requestInfo.getRequestManager().setMessagecontent("未获取到正确的请求报文，请联系管理员");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        try {
            baseBean.writeLog("投资款支付 request = " + request);
            String response = ZJPTUtil.WebServiceZJPT(request);
            baseBean.writeLog("投资款支付 response = " + response);
            if(!response.contains("支付中")){
                requestInfo.getRequestManager().setMessagecontent(response);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
        } catch (Exception e) {
            baseBean.writeLog("ZjptFkAction 异常 " + e);
        }
        return SUCCESS;
    }


    public String getFkRequest(String requestId, String tableName, Date date){
        String sql = "select * from " + tableName + " where requestid = ?";
        rs.executeQuery(sql, requestId);
        StringBuilder sb = new StringBuilder();
        if(rs.next()){

            sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"+
                    "<MBS>\n" +
                    "    <pub>\n" +
                    "        <SRCOUTSYSTEMCODE>" + SRCOUTSYSTEMCODE + "</SRCOUTSYSTEMCODE>\n" +
                    "        <SRCBATCHNO>" + rs.getString("lcbh")+ "</SRCBATCHNO>\n" +
                    "        <TRANSCODE>" + TRANSCODE + "</TRANSCODE>\n" +
                    "        <TRANSDATETIME>" + TimeUtils.getTimeStr(date, "yyyy-MM-dd HH:mm:ss") + "</TRANSDATETIME>\n" +
                    "        <MD5>123456</MD5>\n" +
                    "    </pub>\n" +
                    "    <req>\n" +
                    "        <head>\n" +
                    "            <ALLAMOUNT>"+rs.getString("fkje")+"</ALLAMOUNT>\n" +
                    "            <ALLCOUNT>1</ALLCOUNT>\n" +
                    "        </head>\n" +
                    "        <list>\n" +
                    "            <detail>\n" +
                    "                <CONTACTS/>" +
                    "                <SRCSERIALNO>"+rs.getString("lcbh")+"</SRCSERIALNO>\n" +
                    "                <SRCNOTECODE>"+rs.getString("lcbh")+"</SRCNOTECODE>\n" +
                    "                <ORGCODE>"+rs.getString("BUKRS")+"</ORGCODE>\n" +
                    "                <APPLYORGCODE>"+rs.getString("BUKRS")+"</APPLYORGCODE>\n" +
                    "                <PAYDATE>"+TimeUtils.getTimeStr(date, "yyyy-MM-dd")+"</PAYDATE>\n" +
                    "                <PAYTYPECODE>103</PAYTYPECODE>\n" +
                    "                <SETTLEMENTMODECODE>101</SETTLEMENTMODECODE>\n" +
                    "                <ABSTRACTS/>\n" +
                    "                <ISURGENT>0</ISURGENT>\n" +
//                    "                <PURPOSE>"+PURPOSE+"</PURPOSE>\n" +
                    "                <CHECKCODE/>\n" +
                    "                <OURORGCODE>"+rs.getString("BUKRS")+"</OURORGCODE>\n" +
                    "                <OURCURCODE>CNY</OURCURCODE>\n" +
                    "                <CREATEDBY>OA</CREATEDBY>\n" +
                    "                <OURAMOUNT>"+rs.getString("fkje")+"</OURAMOUNT>\n" +
//                    "                <OURBANKACCOUNTNUMBER>"+OURBANKACCOUNTNUMBER+"</OURBANKACCOUNTNUMBER>\n" +
                    "                <OPPOBJECTNAME>"+rs.getString("LIFNR")+"</OPPOBJECTNAME>\n" +
                    "                <OPPOBJECTCODE>"+rs.getString("VBUND")+"</OPPOBJECTCODE>\n" +
//                    "                <OPPBANKLOCATIONCODE>"+OPPBANKLOCATIONCODE+"</OPPBANKLOCATIONCODE>\n" +
                    "                <OPPBANKLOCATIONS>"+rs.getString("skzh")+"</OPPBANKLOCATIONS>\n" +
                    "                <OPPBANKACCOUNTNUMBER>"+rs.getString("yxmc")+"</OPPBANKACCOUNTNUMBER>\n" +
                    "                <OPPBANKACCOUNTNAME>"+rs.getString("LIFNR")+"</OPPBANKACCOUNTNAME>\n" +
                    "                <OPPDIRECTCURCODE>CNY</OPPDIRECTCURCODE>\n" +
                    "                <OPPPRIVATEFLAG>2</OPPPRIVATEFLAG>\n" +
                    "            </detail>" +
                    "        </list>\n" +
                    "    </req>\n" +
                    "</MBS>");




        }

        return sb.toString();
    }












}

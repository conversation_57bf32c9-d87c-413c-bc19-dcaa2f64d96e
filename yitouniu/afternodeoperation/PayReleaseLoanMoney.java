package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.Map;
// 支付成功扣款
public class PayReleaseLoanMoney implements Action {
    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 获取requestid
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");


        String sql = "select  *  from  " + tableName + "  where requestid = ?";
        String sfhx = ""; // 是否有借款
        String PAYSTATE = ""; // 支付状态
        String jkje = ""; // 借款金额
        double bxzje = 0.00; // 实报金额
        String sfsfje = ""; // 实报金额
        String sbje = ""; // 打款金额
        recordSet.executeQuery(sql, requestid);
        if (recordSet.next()) {

            sfhx = recordSet.getString("sfhx");
            jkje = recordSet.getString("jkje");
            PAYSTATE = recordSet.getString("PAYSTATE");
            bxzje = Double.valueOf(recordSet.getString("bxzje"));
            sbje = recordSet.getString("sbje");
        }


        if ("0".equals(sfhx)) { // 是借款
            Map<String,String> jkje1 = ActionUtil.findJKJE(jkje);
            double jkwhxje = Double.valueOf(jkje1.get("jkwhxje"));
            String jkjebgx =  jkje1.get("jkjebgx");
            String djje =  jkje1.get("djje"); // 冻结金额

            if (Double.valueOf(sbje) > 0) { // 报销大于借款

                jkwhxje = bxzje - Double.valueOf(sbje);
                if (!"2".equals(PAYSTATE)) { // 支付失败

                    if (Double.valueOf(djje) > jkwhxje) {
                        djje = (Double.valueOf(djje) - jkwhxje) + "";
                    } else {
                        djje = "0";
                    }
                    recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", jkwhxje, djje, jkje);

                } else { // 支付成功


                    if (Double.valueOf(djje) > jkwhxje) {

                        djje = (Double.valueOf(djje) - Double.valueOf(jkwhxje)) + "";
                    } else {

                        djje = "0";
                    }
                    recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", "0", djje, jkje);

                }



            }else{
                jkwhxje = bxzje + Double.valueOf(jkwhxje);
                if (Double.valueOf(djje) > bxzje) {
                    djje = (Double.valueOf(djje) - bxzje) + "";
                } else {
                    djje = "0";
                }
                recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", jkwhxje, djje, jkje);

            }


        }
        return SUCCESS;
    }
}
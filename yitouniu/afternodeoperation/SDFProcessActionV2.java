package yitouniu.afternodeoperation;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/2/8 9:26
 * @Description TODO
 * @Version 1.0
 */
public class SDFProcessActionV2 implements Action {
    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        //事项，操作类型 0：新增/1：修改
        String sx = "";

        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("sx".equals(name)) {
                sx = Util.null2String(properties[i].getValue());
            }
        }
        if (main.next()) {
            //mainId
            int mainId = main.getInt("id");
            String errMessage = "";
            List<String> errRow = new ArrayList<>();
            Set<Integer> errSdfmc = new HashSet<>();
            List<Integer> errPhone = new ArrayList<>();
            List<Integer> errAddress = new ArrayList<>();
            List<String> errRowReq = new ArrayList<>();
            List<String> errRowUpdate = new ArrayList<>();
            Set<String> sdfmcSet = new HashSet<>();

            List<Map<String, String>> addressList = getAddress(mainId, tableName, sx);

            baseBean.writeLog("execute addressList = " + addressList);

            if(addressList == null){
                return SUCCESS;
            }

            Map<String,String> provinceMap =  queryProvinceCode();

            int line = 1;
            for (Map<String, String> o : addressList){
                //先判断新增的里面送达方名称是否重复、电话是否有空格,如果有问题，则不解析地址
                String sdfmc = o.get("sdfmc");
                String phone = o.get("phone");
                String address = o.get("address");
                boolean nextLineFlag = false;
                if(StringUtils.isNotBlank(sdfmc)){
                    rs.executeQuery("select id from uf_khzsj where NAME1 = ? ", sdfmc);
                    if(rs.next()){
                        errSdfmc.add(line);
                        nextLineFlag = true;
                    }
                    if(!sdfmcSet.add(sdfmc)){
                        errSdfmc.add(line);
                        nextLineFlag = true;
                    }
                }
                if(StringUtils.isNotBlank(phone)){
                    if(StrUtil.containsBlank(phone)){
                        errPhone.add(line);
                        nextLineFlag = true;
                    }
                }
                if(StringUtils.isNotBlank(address)){
                    if(StrUtil.containsBlank(address)){
                        errAddress.add(line);
                        nextLineFlag = true;
                    }
                }

                if(StringUtils.isBlank(address)){
                    nextLineFlag = true;
                }

                if(nextLineFlag){
                    line++;
                    continue;
                }

                JSONObject result = addressAnalyse(address);
                if(result != null && result.getInteger("code") == 200){
                    JSONObject dataObject = (JSONObject) result.get("data");
                    JSONArray resultData = dataObject.getJSONArray("result");
                    JSONObject xzq = ((JSONObject)resultData.get(0)).getJSONObject("xzq");

                    if(xzq == null){
                        errRow.add(String.valueOf(line));
                        continue;
                    }
                    String province = xzq.getString("province");
                    String city = xzq.getString("city");
                    String district = xzq.getString("district");
                    String subArea = xzq.getString("subArea");

                    //当省市区地址有一个为空时，解析错误
                    if(StringUtils.isBlank(province) || StringUtils.isBlank(city) || StringUtils.isBlank(district) || StringUtils.isBlank(subArea)){
                        errRow.add(String.valueOf(line));
                        continue;
                    }
                    //当省市不为空且区为空时，设置区为其他区
                    if(StringUtils.isNotBlank(province) && StringUtils.isNotBlank(city) && StringUtils.isBlank(district)){
                        district = "其他区";
                    }
                    String provinceCode = provinceMap.get(province);
                    boolean flag = update(tableName, provinceCode, province, city, district, o.get("id"), sx);
                    if(!flag){
                        errRowUpdate.add(String.valueOf(line));
                    }
                } else {
                    errRowReq.add(String.valueOf(line));
                }

                line++;
            }
            if(errSdfmc.size() != 0){
                errMessage = errMessage + "送达方名称重复，请检查第" + errSdfmc + "行的送达方名称，";
            }
            if(errPhone.size() != 0){
                errMessage = errMessage + "电话存在空白符，请检查第" + errPhone + "行的电话字段，";
            }
            if(errAddress.size() != 0){
                errMessage = errMessage + "送货地址存在空白符，请检查第" + errAddress + "行的送货地址，";
            }
            if(errRow.size() != 0){
                errMessage = errMessage + "地址解析失败，请检查第" + errRow + "行的送货地址，";
            }
            if(errRowUpdate.size() != 0){
                errMessage = errMessage + "省市区地址信息更新失败，请检查第" + errRowUpdate + "行的送货地址。";
            }
            if(errRowReq.size() != 0){
                errMessage = errMessage + "第" + errRowReq + "的单子请求快递100失败。请重试";
            }
            if(errMessage.length() != 0){
                requestInfo.getRequestManager().setMessagecontent(errMessage);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

        }

        return SUCCESS;
    }


    public List<Map<String, String>> getAddress(int mainId, String tableName, String sx){
        List<Map<String, String>> addressList = new ArrayList<>();
        String sql = "";
        if("0".equals(sx)){
            sql = "select * from " + tableName + "_dt1 where mainid = ? and (sfyykd100xzsj <> '0' or sfyykd100xzsj is null)";
        } else {
            sql = "select * from " + tableName + "_dt2 where mainid = ? and (sfyykd100xzsj <> '0' or sfyykd100xzsj is null)";
        }
        baseBean.writeLog("getAddress sql = " + sql);
        rs.executeQuery(sql, mainId);
        while (rs.next()){
            Map<String,String> map = new HashMap<>();
            map.put("id", rs.getString("id"));
            if("0".equals(sx)){
                map.put("address", rs.getString("jddzshdz"));
                map.put("sdfmc", rs.getString("sdfmc"));
                map.put("phone", rs.getString("dhshrdh"));
            } else {
                map.put("address", rs.getString("shdzx"));
                map.put("sdfmc", rs.getString("sdfmc"));
                map.put("phone", rs.getString("dhx"));
            }
            addressList.add(map);
        }
        return addressList;
    }

    public boolean update(String tableName,String provinceCode, String province, String city, String district, String id, String sx){
        String sql = "";
        if("0".equals(sx)){
            sql = "update " + tableName + "_dt1 set dqbm=?, s=?, cs=?, qyqx=?, sfyykd100xzsj = '0' where id in (" + id + ") ";
        } else {
            sql = "update " + tableName + "_dt2 set dqbmx=?, sx=?, csx=?, qyqxx=?, sfyykd100xzsj = '0' where id in (" + id + ") ";
        }
        return rs.executeUpdate(sql, provinceCode, province, city, district);
    }

    public Map<String,String> queryProvinceCode(){
        Map<String,String> provinceCodeMap = new HashMap<>();
        rs.executeQuery("select * from formtable_main_267");
        while(rs.next()){
            provinceCodeMap.put(rs.getString("sqmc"),rs.getString("sqdm"));
        }
        return provinceCodeMap;
    }


//    public static JSONObject addressAnalyse(String address) {
//        Map<String, Object> queryParam = new HashMap<>();
//        queryParam.put("content", address);
//        queryParam.put("secret_key", "bwPr7Y33RHZbBsDjs7");
//        queryParam.put("secret_code", "0db4f037e2094f15a811261722e8a6e9");
//        queryParam.put("secret_sign", DigestUtils.md5Hex("bwPr7Y33RHZbBsDjs7876a0bec81cd455e98aa1eab1e1dc079").toUpperCase());
//        String response = HttpRequest.post("http://cloud.kuaidi100.com/api").form(queryParam).execute().body();
//        new BaseBean().writeLog("addressAnalyse：response="+response);
//        return JSON.parseObject(response);
//    }

    public JSONObject addressAnalyse(String address) {
        Map<String, Object> queryParam = new HashMap<>();
        String t = String.valueOf(System.currentTimeMillis());
        Map<String, String> param = new HashMap<>();
        param.put("content", address);
        queryParam.put("param", JSON.toJSONString(param));
        queryParam.put("key", "kOVNIwrT5246");
        queryParam.put("t",t);
        String s = JSON.toJSONString(param) + t + "kOVNIwrT5246" + "501fc3d563f64de7aa8ed61e9cc481ac";
        queryParam.put("sign", DigestUtils.md5Hex(s).toUpperCase());
        String response = HttpRequest.post("https://api.kuaidi100.com/address/resolution").header("Content-Type", "application/x-www-form-urlencoded").form(queryParam).execute().body();
        baseBean.writeLog("sdfAddressAnalyse：response="+response);
        return JSON.parseObject(response);
    }

}

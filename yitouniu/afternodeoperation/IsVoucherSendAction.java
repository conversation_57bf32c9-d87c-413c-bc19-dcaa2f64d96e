package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 凭证是否推送
 */
public class IsVoucherSendAction implements Action {
    private RecordSet recordSet = new RecordSet();
    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 获取requestid
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");




        String sql = "select  *  from  " + tableName + "  where requestid = ?";
        String sfhx = ""; // 是否有借款
        String jkje = ""; // 借款金额
        double bxzje = 0.00; // 实报金额
        String sfdjje = ""; // 是否冻结金额
        String sbje = ""; // 是否冻结金额
        recordSet.executeQuery(sql, requestid);
        if (recordSet.next()) {

            sfhx = recordSet.getString("sfhx");
            jkje = recordSet.getString("jkje");
            bxzje = Double.valueOf(recordSet.getString("bxzje"));
            //sfdjje = recordSet.getString("sfdjje");
            sbje = recordSet.getString("sbje");
        }


        //if (!"1".equals(sfdjje)) { // 已冻结过金额,不需要重复冻结
            //recordSet.executeUpdate("update " + tableName + " set sfdjje = ?  where requestid = ?", 1, requestid); // 不生成凭证

            if ("0".equals(sfhx)) { // 是借款
                Map jkje1 = ActionUtil.findJKJE(jkje);
                double jkwhxje = Double.valueOf((String) jkje1.get("jkwhxje"));
                String djje = (String) jkje1.get("djje");
                if (Double.valueOf(sbje)<0) { // 借款大于报销

                    recordSet.executeUpdate("update " + tableName + " set sftspz = ?  where requestid = ?", 1, requestid); // 不生成凭证

                    double sjje = jkwhxje - bxzje;
                    if (!"".equals(djje) && djje != null) {
                        bxzje = Double.valueOf(bxzje) + Double.valueOf(djje);
                    }

                    recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ? where id = ?", sjje, bxzje, jkje);
                } else {
                    if (!"".equals(djje) && djje != null) {
                       // recordSet.executeUpdate("insert into uf_je (lcid,je) values (?,?)", requestid, jkwhxje);
                        jkwhxje = Double.valueOf(jkwhxje) + Double.valueOf(djje);

                    }
                    recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", "0", jkwhxje, jkje);

                }

            }
        //}
        return SUCCESS;
    }




}

package yitouniu.afternodeoperation;


import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: haiyang
 * @Date: 2025-06-11 09:58
 * @Desc:
 */
public class FreeOrderCostCenterAndPxPreActionV1 implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    private List<String> costCenterList = new ArrayList<>();
    private List<String> pxList = new ArrayList<>();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        if (main.next()) {
            int mainId = main.getInt("id");
            String errMessage = "";
            List<Map<String, String>> costCenterAndPxList = getCostCenterAndPx(mainId, tableName);
            Map<String, String> notForzenCostCenterMap = queryNotFrozenCostCenter(costCenterList);
            Map<String, String> notForzenPxMap = queryNotFrozenPx(pxList);
            for(Map<String,String> o : costCenterAndPxList){
                if (!notForzenCostCenterMap.containsKey(o.get("cbzx"))){
                    errMessage = errMessage + "订单编号为" + o.get("bh") + "的成本中心【"+ o.get("cbzx")+"】已冻结；";
                }
                if (!notForzenPxMap.containsKey(o.get("px"))){
                    errMessage = errMessage + "订单编号为" + o.get("bh") + "的品项【"+ o.get("px") +"】已禁用；";
                }
            }
            if(!errMessage.isEmpty()){
                requestInfo.getRequestManager().setMessagecontent(errMessage);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

        }

        return SUCCESS;

    }

    private Map<String, String> queryNotFrozenPx(List<String> pxList) {
        if (CollectionUtils.isEmpty(pxList)) return new HashMap<>();
        Map<String, String> notForzenPxMap = new HashMap<>();
        String pxStr = Joiner.on("','").join((Iterable<?>) pxList);
        pxStr = "'" + pxStr + "'";
        String pxSql = "select * from uf_sap_px where zt = 0 and px in (" + pxStr + ")";
        baseBean.writeLog("pxSql = " + pxSql);
        rs.executeQuery(pxSql);
        while (rs.next()){
            notForzenPxMap.put(rs.getString("px"), "1");
        }
        return notForzenPxMap;
    }

    private Map<String, String> queryNotFrozenCostCenter(List<String> costCenterList) {
        if (CollectionUtils.isEmpty(costCenterList)) return new HashMap<>();
        Map<String, String> notForzenCostCenterMap = new HashMap<>();
        String costCenterStr = Joiner.on("','").join((Iterable<?>) costCenterList);
        costCenterStr = "'" + costCenterStr + "'";
        // 修改测试环境表名
        String costCenterSql = "select * from formtable_main_306 where sfdj = 1 and sapbmbm in (" + costCenterStr + ")";
        baseBean.writeLog("costCenterSql = " + costCenterSql);
        rs.executeQuery(costCenterSql);
        while (rs.next()){
            notForzenCostCenterMap.put(rs.getString("sapbmbm"), "1");
        }
        return notForzenCostCenterMap;
    }

    private List<Map<String, String>> getCostCenterAndPx(int mainId, String tableName) {
        List<Map<String, String>> costCenterAndPxList = new ArrayList<>();
        rs.executeQuery("select * from " + tableName + "_dt1 where mainid = ?", mainId);
        while (rs.next()){
            Map<String,String> map = new HashMap<>();
            map.put("id", rs.getString("id"));
            map.put("bh", rs.getString("bh"));
            map.put("cbzx", rs.getString("cbzx"));
            map.put("px", rs.getString("px"));
            costCenterList.add(rs.getString("cbzx"));
            pxList.add(rs.getString("px"));
            costCenterAndPxList.add(map);
        }
        return costCenterAndPxList;
    }
}

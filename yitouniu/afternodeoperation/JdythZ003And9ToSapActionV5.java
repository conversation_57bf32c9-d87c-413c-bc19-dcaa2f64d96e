package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: oa
 * @description: 1
 * @author: haiyang
 * @create: 2023-08-15 09:48
 **/
public class JdythZ003And9ToSapActionV5 implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestId = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String)tableNameByRequestId.get("tableName");
        Map<String, String> mainTableMap = new HashMap<>();
        int mainId = -1;
        String lcbh = "";
        String fktjms = "";
        Integer z002zsjxxkhsl = null;
        String xyed = "";
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestId);
        if(main.next()) {
            mainId = main.getInt("id");
            lcbh = main.getString("lcbh");
            fktjms = main.getString("fktjms");
            z002zsjxxkhsl = main.getInt("z002zsjxxkhsl");
            mainTableMap.put("xyed", main.getString("xyed"));
            mainTableMap.put("STR_SUPPL1", main.getString("STR_SUPPL1"));
            mainTableMap.put("LIQUID_DAT", main.getString("LIQUID_DAT"));
            mainTableMap.put("BUKRS", main.getString("BUKRS"));
            mainTableMap.put("VKGRP", main.getString("VKGRP"));
            mainTableMap.put("VKBUR", main.getString("VKBUR"));
            mainTableMap.put("KVGR4", main.getString("KVGR4"));
            mainTableMap.put("PERNR", main.getString("PERNR"));
            mainTableMap.put("RISK_CLASS", main.getString("fxlbriskclass"));
            mainTableMap.put("KATR3", main.getString("qdlxzyjxs"));
        }
        mainTableMap.put("ZTERM", fktjms.split(" ")[0]);

        JSONArray costCenterArray = getKOSTL(tableName, mainId);

        Map<String, JSONObject> z003Map = getZ003ReqJson(requestId, mainId, lcbh, tableName, mainTableMap);
        Map<String, JSONObject> z009Map = getZ009ReqJson(requestId, mainId, lcbh, tableName, costCenterArray, mainTableMap);

        if(z003Map.isEmpty() && z009Map.isEmpty()){
            return SUCCESS;
        }

        Map z003Result = invockData(z003Map, tableName, "Z003", requestId, z002zsjxxkhsl);
        if(z003Result == null){
            requestInfo.getRequestManager().setMessagecontent("action异常");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        baseBean.writeLog("z003Result = " + z003Result);

        Map z009Result = invockData(z009Map, tableName, "Z009", requestId, z002zsjxxkhsl);
        if(z009Result == null){
            requestInfo.getRequestManager().setMessagecontent("action异常");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        baseBean.writeLog("z009Result = " + z009Result);


        String z003type = (String) z003Result.get("type");
        String z009type = (String) z009Result.get("type");
        if("S".equals(z003type) && "S".equals(z009type)){
            String z003Msg ="z003Msg = " + z003Result.get("message") + " " + z003Result.get("result");
            String z009Msg ="z009Msg = " + z009Result.get("message") + " " + z009Result.get("result");
            requestInfo.getRequestManager().setMessagecontent(z003Msg + "    " + z009Msg);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return SUCCESS;
        } else {
            String z003Msg ="z003Msg = " + z003Result.get("message") + " " + z003Result.get("result") + " errMsg = " + z003Result.get("errMsg");
            String z009Msg ="z009Msg = " + z009Result.get("message") + " " + z009Result.get("result") + " errMsg = " + z009Result.get("errMsg");
            requestInfo.getRequestManager().setMessagecontent(z003Msg + "   " + z009Msg);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

    }


    /**
     * 请求sap
     * @param z003Map
     * @param tableName
     * @return
     */
    public Map invockData(Map<String, JSONObject> z003Map, String tableName, String type, String requestId, Integer z002zsjxxkhsl){
        JSONObject resp = null;
        Map map = new HashMap();
        int count = z003Map.size();
        int sCount = 0;
        int aCount = 0;
        int eCount = 0;
        String errMsg = "";
        //空
        int errCount = 0;
        String finalPartner = "";
        String finalNAME_ORG1 = "";
        for (Map.Entry<String, JSONObject> o : z003Map.entrySet()) {
            // 调用sap返回结果
            resp = JSONObject.parseObject(SAPUtil.execute(o.getValue().toJSONString()));

            String[] key = o.getKey().split(",");
            String line = key[0];
            String id = key[1];

            if(resp != null){
                JSONObject ctrl = resp.getJSONObject("CTRL");
                JSONArray data = resp.getJSONArray("DATA");
                String MSGTY = ctrl.getString("MSGTY");
                String partner = data.getJSONObject(0).getString("PARTNER");
                String MSAGE = ctrl.getString("MSAGE");

                if(StringUtils.isBlank(finalPartner) && "Z009".equals(type)){
                    finalPartner = partner;
                    finalNAME_ORG1 = o.getValue().getJSONObject("DATA").getJSONObject("BASIC").getString("NAME_ORG1");
                }
                if("S".equals(MSGTY)){
                    sCount++;
                    if("Z003".equals(type)){
                        updateItem6Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                    } else if("Z009".equals(type)) {
                        updateItem7Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                    }
                } else if ("A".equals(MSGTY)){
                    aCount++;
                    if("Z003".equals(type)){
                        updateItem6Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                    } else if("Z009".equals(type)) {
                        updateItem7Sql(tableName, id, "0", partner, MSGTY, MSAGE);
                    }
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else if("E".equals(MSGTY)){
                    eCount++;
                    if("Z003".equals(type)){
                        updateItem6Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    } else if("Z009".equals(type)) {
                        updateItem7Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    }
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else {
                    errCount++;
                    if("Z003".equals(type)){
                        updateItem6Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    } else if("Z009".equals(type)) {
                        updateItem7Sql(tableName, id, "1", null, MSGTY, MSAGE);
                    }
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                }
            } else {
                map.put("type", "F");
                map.put("message", "异常失败");
                map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
                map.put("errMsg", errMsg);
                return map;
            }

        }

        //更新主表数据
        if(StringUtils.isNotBlank(finalPartner) && "Z009".equals(type) && z002zsjxxkhsl == 0){
            rs.executeUpdate("update " + tableName + " set htqyfmc = ?,htqyfdm = ? where requestid = ?", finalNAME_ORG1, finalPartner, requestId);
        }

        if(aCount > 0 || eCount > 0 || errCount > 0){
            map.put("type", "F");
            map.put("message", "部分失败");
            map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
            map.put("errMsg", errMsg);
            return map;
        }
        map.put("type", "S");
        map.put("message", "全部成功");
        map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
        map.put("errMsg", errMsg);
        return map;
    }




    /**
     * 获取 Z003 的请求json
     * @param requestId
     * @param mainId
     * @param lcbh
     * @param tableName
     * @return
     */
    public Map<String, JSONObject> getZ003ReqJson(String requestId, Integer mainId, String lcbh
            , String tableName, Map<String, String> map){
        int line = 1;
        Map<String, JSONObject> objectMap = new HashMap<>();
        rs.executeQuery("select * from " + tableName + "_dt6 where mainid = ? and sfyts <> 0", mainId);
        while(rs.next()){
            JSONObject reqJson = new JSONObject();
            JSONObject ctrlReq = SAPUtil.getCtrlReq(requestId, line, lcbh, "ZINF065", "6");
            JSONObject dataReq = new JSONObject();
            dataReq.put("BU_GROUP", "Z003");
            dataReq.put("NAME_ORG1", rs.getString("NAME_ORG1"));
            dataReq.put("NAME_CO", rs.getString("NAME_CO"));
            dataReq.put("STR_SUPPL1", map.get("STR_SUPPL1"));
            dataReq.put("STREET", rs.getString("STREET"));
            dataReq.put("CITY1", rs.getString("CITY1"));
            dataReq.put("CITY2", rs.getString("CITY2"));
            dataReq.put("REGIO", rs.getString("REGIO"));
            dataReq.put("TEL_NUMBER", rs.getString("TEL_NUMBER"));
            dataReq.put("LAND1", "CN");
            dataReq.put("LANGU", "ZH");
            dataReq.put("RISK_CLASS", "B");
            dataReq.put("CHECK_RULE", "Z1");
            dataReq.put("KUKLA", "02");
            dataReq.put("VKORG", "0".equals(rs.getString("VKORG")) ? "6000" : "6100");
            dataReq.put("VTWEG", "00");
            dataReq.put("SPART", "00");
            dataReq.put("WAERS", "CNY");
            dataReq.put("VWERK", "6010");
            dataReq.put("VSBED", "01");
            dataReq.put("PODKZ", "X");
            dataReq.put("KALKS", "1");
            dataReq.put("KONDA", "01");
            dataReq.put("KTGRD", "01");
            dataReq.put("TAXKD", "1");
            dataReq.put("ZKUNNR", rs.getString("khbm"));
            dataReq.put("PERNR", rs.getString("PERNR"));

            reqJson.put("CTRL", ctrlReq);
            reqJson.put("DATA", dataReq);

            objectMap.put(line + "," + rs.getString("id"), reqJson);

            line++;
        }

        return objectMap;
    }


    /**
     * 获取 Z009 的请求json
     * @param requestId
     * @param mainId
     * @param lcbh
     * @param tableName
     * @param costCenterArray
     * @return
     */
    public Map<String, JSONObject> getZ009ReqJson(String requestId, Integer mainId, String lcbh
            , String tableName, JSONArray costCenterArray
            , Map<String, String> map){
        int line = 1;
        Map<String, JSONObject> objectMap = new HashMap<>();
        rs.executeQuery("select * from " + tableName + "_dt7 where mainid = ? and sfyts <> 0 order by id", mainId);
        while(rs.next()){
            JSONObject reqJson = new JSONObject();
            JSONObject ctrlReq = SAPUtil.getCtrlReq(requestId, line, lcbh, "ZINF066", "7");
            JSONObject dataReq = new JSONObject();
            JSONObject basicJson = new JSONObject();
            basicJson.put("BU_GROUP", "Z009");
            basicJson.put("NAME_ORG1", rs.getString("NAME_ORG1"));
            basicJson.put("STR_SUPPL1", map.get("STR_SUPPL1"));
            basicJson.put("STREET", "全国");
            basicJson.put("CITY1", "全国");
            basicJson.put("CITY2", "全国");
            basicJson.put("REGIO", "QG");
            basicJson.put("TEL_NUMBER", rs.getString("TEL_NUMBER"));
            basicJson.put("LAND1", "CN");
            basicJson.put("LANGU", "ZH");
            basicJson.put("RISK_CLASS", map.get("RISK_CLASS"));
            basicJson.put("CHECK_RULE", "Z1");
            basicJson.put("KUKLA", "01");
            basicJson.put("LIQUID_DAT", map.get("LIQUID_DAT").replace("-", ""));
            basicJson.put("KATR3", map.get("KATR3"));

            JSONArray companyJsonArray = new JSONArray();
            JSONObject companyJson = new JSONObject();
            companyJson.put("BUKRS", Integer.parseInt(map.get("BUKRS")) == 0 ? "6000" : "6100" );
            companyJson.put("AKONT", "**********");
            companyJson.put("ZUAWA", "031");
            companyJson.put("ZTERM", map.get("ZTERM"));
            companyJson.put("TOGRU", "8000");
            companyJson.put("KNRZE", rs.getString("KNRZE"));
            companyJsonArray.add(companyJson);

//            JSONArray bankJsonArray = new JSONArray();
//            JSONObject bankJson = new JSONObject();
//            bankJsonArray.add(bankJson);

            JSONArray salesJsonArray = new JSONArray();
            JSONObject saleJson = new JSONObject();
            saleJson.put("VKORG", Integer.parseInt(map.get("BUKRS")) == 0 ? "6000" : "6100" );
            saleJson.put("VTWEG", "00");
            saleJson.put("SPART", "00");
            saleJson.put("WAERS", "CNY");
            saleJson.put("VKBUR", map.get("VKBUR"));
            saleJson.put("VKGRP", map.get("VKGRP"));
            saleJson.put("KVGR4", map.get("KVGR4"));
            saleJson.put("VSBED", "01");
            saleJson.put("PODKZ", "X");
            saleJson.put("KALKS", "1");
            saleJson.put("KONDA", "01");
            saleJson.put("KTGRD", "01");
            saleJson.put("ZTERM_V", map.get("ZTERM"));
            saleJson.put("TAXKD", "1");
            //默认为空
            saleJson.put("KVGR3", "");
            saleJson.put("PERNR", map.get("PERNR"));
            salesJsonArray.add(saleJson);

            JSONArray creditJsonArray = new JSONArray();
            JSONObject creditJson = new JSONObject();
            creditJson.put("ZBUKRS", rs.getString("SEGMENT"));
            creditJson.put("CREDIT_LIMIT", 0.00);
            creditJson.put("LIMIT_VALID_DATE", map.get("LIQUID_DAT").replace("-", ""));
//            creditJson.put("CREDIT_LIMIT", xyed);
//            creditJson.put("LIMIT_VALID_DATE", rs.getString("LIQUID_DAT").replace("-", ""));
            creditJsonArray.add(creditJson);

            dataReq.put("BASIC", basicJson);
            dataReq.put("company", companyJsonArray);
//            dataReq.put("BANK", bankJsonArray);
            dataReq.put("sales", salesJsonArray);
            dataReq.put("credit", creditJsonArray);
            dataReq.put("Cost_Center", costCenterArray);

            reqJson.put("CTRL", ctrlReq);
            reqJson.put("DATA", dataReq);

            objectMap.put(line + "," + rs.getString("id"), reqJson);

            line++;
        }

        return objectMap;
    }


    public JSONArray getKOSTL(String tableName, int mainId){
        rs.executeQuery("select * from " + tableName + "_dt8 where mainid = ?", mainId);
        JSONArray jsonArray = new JSONArray();
        while(rs.next()){
            JSONObject req = new JSONObject();
            String VKORG = rs.getString("VKORG");
            req.put("VKORG", "0".equals(VKORG) ?"6000" :"6100");
            req.put("ZATTRIBUTE", rs.getString("ZATTRIBUTE"));
            req.put("KOSTL", rs.getString("KOSTL"));
            jsonArray.add(req);
        }

        return jsonArray;
    }

    /**
     * 更新明细表6的数据
     * @param tableName
     */
    public void updateItem6Sql(String tableName,String id, String sfyts, String partner, String MSGTY, String MSAGE){
        String sql = "";
        sql = "update "+tableName+"_dt6  set sfyts = ?,NAME_NO = ?, xxlx = ?, xxnr = ? where id = ?";
        rs.executeUpdate(sql, sfyts, partner, MSGTY, MSAGE, id);
    }

    /**
     * 更新明细表7的数据
     * @param tableName
     */
    public void updateItem7Sql(String tableName,String id, String sfyts, String partner, String MSGTY, String MSAGE){
        String sql = "";
        sql = "update "+tableName+"_dt7  set sfyts = ?,NAME_NO = ?, xxlx = ?, xxnr = ? where id = ?";
        rs.executeUpdate(sql, sfyts, partner, MSGTY, MSAGE, id);
    }

}

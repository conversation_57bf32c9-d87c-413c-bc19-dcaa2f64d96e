package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import yitouniu.esb.ReturnMsgToSAP;
import yitouniu.util.SAPUtil;
import yitouniu.util.TimeUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/2/6 15:00
 * @Description 送达方主数据创建
 * @Version 1.0
 */
public class CreateDelivererMasterDataAction extends ReturnMsgToSAP implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestId = requestInfo.getRequestid();
        String tableName = requestInfo.getRequestManager().getBillTableName();

        String sql = "select * from " + tableName + " where requestid = ?";
        rs.executeQuery(sql, requestId);

        //事项，操作类型 0：新增/1：修改
        String sx = "";
        //流程编号
        String lcbh = "";
        //mainId
        int mainId = 0;
        if (rs.next()) {
            sx = rs.getString("sx");
            lcbh = rs.getString("lcbh");
            mainId = rs.getInt("id");
        }
        List<Map<String,String>> itemMapList = getItemData(sx, tableName, mainId);
//        if(itemMapList.size() == 0 ){
//            requestInfo.getRequestManager().setMessagecontent("明细表均为空，请检查数据");
//            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
//            return FAILURE_AND_CONTINUE;
//        }
        baseBean.writeLog("itemMapList = " + itemMapList);


        //明细
        Map resultMap = null;
        if("0".equals(sx)){
            resultMap = invockAddData(itemMapList,requestId, lcbh, tableName);
        } else if("1".equals(sx)){
            resultMap = invockUpdateData(itemMapList, requestId, lcbh, tableName);
        } else {
            requestInfo.getRequestManager().setMessagecontent("事项的值为空，请检查");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        baseBean.writeLog("resultMap = " + resultMap);


        if(resultMap != null){
            String type = (String) resultMap.get("type");
            if("S".equals(type)){
                requestInfo.getRequestManager().setMessagecontent(resultMap.get("message") + " " + resultMap.get("result"));
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return SUCCESS;
            } else {
                requestInfo.getRequestManager().setMessagecontent(resultMap.get("message") + " " + resultMap.get("result") + " errMsg = " + resultMap.get("errMsg"));
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
        }else {
            requestInfo.getRequestManager().setMessagecontent("action异常");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
    }


    /**
     * 新增
     * @param itemMapList
     * @param requestId
     * @param lcbh
     * @param tableName
     * @return
     */
    public Map invockAddData(List<Map<String,String>> itemMapList, String requestId, String lcbh, String tableName){

        int line = 1;
        JSONObject resp = null;
        Map map = new HashMap();
        int count = itemMapList.size();
        int sCount = 0;
        int aCount = 0;
        int eCount = 0;
        String errMsg = "";
        //空
        int errCount = 0;
        for(Map<String,String> o : itemMapList){
            JSONObject req = getAddReq(o, line, requestId, lcbh);
            // 调用sap返回结果
            resp = JSONObject.parseObject(SAPUtil.execute(req.toJSONString()));

            if(resp != null){
                JSONObject ctrl = resp.getJSONObject("CTRL");
                JSONArray data = resp.getJSONArray("DATA");
                String MSGTY = ctrl.getString("MSGTY");
                String partner = data.getJSONObject(0).getString("PARTNER");
                String MSAGE = ctrl.getString("MSAGE");
                if("S".equals(MSGTY)){
                    sCount++;
                    updateItem1Sql(tableName, o.get("id"), "0", partner, MSGTY, MSAGE);
                } else if ("A".equals(MSGTY)){
                    aCount++;
                    updateItem1Sql(tableName, o.get("id"), "0", partner, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else if("E".equals(MSGTY)){
                    eCount++;
                    updateItem1Sql(tableName, o.get("id"), "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else {
                    errCount++;
                    updateItem1Sql(tableName, o.get("id"), "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                }
            } else {
                map.put("type", "F");
                map.put("message", "异常失败");
                map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
                map.put("errMsg", errMsg);
                return map;
            }
            line++;
        }
        if(aCount > 0 || eCount > 0 || errCount > 0){
            map.put("type", "F");
            map.put("message", "部分失败");
            map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
            map.put("errMsg", errMsg);
            return map;
        }
        map.put("type", "S");
        map.put("message", "全部成功");
        map.put("result", "总数为"+count+"个，成功的为"+sCount+"个,逻辑成功的为"+aCount+"个，失败的为"+(eCount+errCount)+"个");
        map.put("errMsg", errMsg);
        return map;
    }

    /**
     * 更新
     * @param itemMapList
     * @param requestId
     * @param lcbh
     * @param tableName
     * @return
     */
    public Map invockUpdateData(List<Map<String,String>> itemMapList, String requestId, String lcbh, String tableName){
        int line = 1;
        JSONObject resp = null;
        Map map = new HashMap();
        int count = itemMapList.size();
        int sCount = 0;
        int eCount = 0;
        String errMsg = "";
        //空
        int errCount = 0;
        for(Map<String,String> o : itemMapList){
            JSONObject req = getUpdateReq(o, line, requestId, lcbh);
            // 调用sap返回结果
            resp = JSONObject.parseObject(SAPUtil.execute(req.toJSONString()));

            if(resp != null){
                JSONObject ctrl = resp.getJSONObject("CTRL");
                JSONArray data = resp.getJSONArray("DATA");
                String MSGTY = ctrl.getString("MSGTY");
                String partner = data.getJSONObject(0).getString("PARTNER");
                String MSAGE = ctrl.getString("MSAGE");
                if("S".equals(MSGTY)){
                    sCount++;
                    updateItem2Sql(tableName, o.get("id"), "0", MSGTY);
                } else if("E".equals(MSGTY)){
                    eCount++;
                    updateItem2Sql(tableName, o.get("id"), "1",  MSGTY);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else {
                    errCount++;
                    updateItem2Sql(tableName, o.get("id"), "1",  MSGTY);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                }
            } else {
                map.put("type", "F");
                map.put("message", "异常失败");
                map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
                map.put("errMsg", errMsg);
                return map;
            }
            line++;
        }
        if(eCount > 0 || errCount > 0){
            map.put("type", "F");
            map.put("message", "部分失败");
            map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
            map.put("errMsg", errMsg);
            return map;
        }
        map.put("type", "S");
        map.put("message", "全部成功");
        map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
        map.put("errMsg", errMsg);
        return map;
    }

    /**
     * 根据操作事项获取不同明细
     * @param sx
     * @param tableName
     * @param mainId
     * @return
     */
    public List<Map<String,String>> getItemData(String sx, String tableName, int mainId){
        Map<String ,String> map = null ;
        List<Map<String,String>> mapList = new ArrayList<>();
        if("0".equals(sx)){
            rs.executeQuery("select * from " + tableName + "_dt1 where mainid = ? and (sfyts <> 0 or sfyts is null)", mainId);
            while (rs.next()){
                map = new HashMap<>();
                map.put("id", rs.getString("id"));
                map.put("khbm", rs.getString("khbm"));
                map.put("sdfmc", rs.getString("sdfmc"));
                map.put("dsrshr", rs.getString("dsrshr"));
                map.put("jd2gsdz", rs.getString("jd2gsdz"));
                map.put("jddzshdz", rs.getString("jddzshdz"));
                map.put("cs", rs.getString("cs"));
                map.put("qyqx", rs.getString("qyqx"));
                map.put("dqbm", rs.getString("dqbm"));
                map.put("dhshrdh", rs.getString("dhshrdh"));
                map.put("xszz", rs.getString("xszz"));
                mapList.add(map);
            }
        } else if("1".equals(sx)){
            rs.executeQuery("select * from " + tableName + "_dt2 where mainid = ? and (sfyts <> 0 or sfyts is null)", mainId);
            while (rs.next()){
                map = new HashMap<>();
                map.put("id", rs.getString("id"));
                map.put("sdfbm", rs.getString("sdfbm"));
                map.put("sdfmcx", rs.getString("sdfmcx"));
                map.put("dsrx", rs.getString("dsrx"));
                map.put("dhx", rs.getString("dhx"));
                map.put("shdzx", rs.getString("shdzx"));
                map.put("csx", rs.getString("csx"));
                map.put("qyqxx", rs.getString("qyqxx"));
                map.put("dqbmx", rs.getString("dqbmx"));
                map.put("sx", rs.getString("sx"));
//                map.put("xszz", rs.getString("xszz"));
                mapList.add(map);
            }
        }
        return mapList;
    }


    /**
     * 获取新增时候的req
     * @param o
     * @param line
     * @param requestId
     * @param lcbh
     * @return
     */
    public JSONObject getAddReq(Map<String,String> o, int line, String requestId, String lcbh){
        JSONObject req = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(requestId, line, lcbh);
        JSONObject dataReq = new JSONObject();
        dataReq.put("BU_GROUP", "Z003");
        dataReq.put("NAME_ORG1", o.get("sdfmc"));
        dataReq.put("NAME_CO", o.get("dsrshr"));
        dataReq.put("STR_SUPPL1", o.get("jd2gsdz"));
        dataReq.put("STREET", o.get("jddzshdz"));
        dataReq.put("CITY1", o.get("cs"));
        dataReq.put("CITY2", o.get("qyqx"));
        dataReq.put("REGIO", o.get("dqbm"));
        dataReq.put("TEL_NUMBER", o.get("dhshrdh"));
        dataReq.put("LAND1", "CN");
        dataReq.put("LANGU", "ZH");
        dataReq.put("RISK_CLASS", "B");
        dataReq.put("CHECK_RULE", "Z1");
        dataReq.put("KUKLA", "02");
        dataReq.put("VKORG", "0".equals(o.get("xszz")) ? "6000" : "6100");
        dataReq.put("VTWEG", "00");
        dataReq.put("SPART", "00");
        dataReq.put("WAERS", "CNY");
        dataReq.put("VWERK", "6010");
        dataReq.put("VSBED", "01");
        dataReq.put("PODKZ", "X");
        dataReq.put("KALKS", "1");
        dataReq.put("KONDA", "01");
        dataReq.put("KTGRD", "01");
        dataReq.put("TAXKD", "1");
        dataReq.put("ZKUNNR", o.get("khbm"));

        req.put("CTRL", ctrlReq);
        req.put("DATA", dataReq);

        return req;
    }

    /**
     * 获取更新时候的req
     * @param o
     * @param line
     * @param requestId
     * @param lcbh
     * @return
     */
    public JSONObject getUpdateReq(Map<String,String> o, int line, String requestId, String lcbh){
        JSONObject req = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(requestId, line, lcbh);
        JSONObject dataReq = new JSONObject();
        dataReq.put("PARTNER", o.get("sdfbm"));
        if(StringUtils.isNotBlank(o.get("sdfmcx"))){
            dataReq.put("NAME_ORG1", o.get("sdfmcx"));
        }
        if(StringUtils.isNotBlank(o.get("dsrx"))){
            dataReq.put("NAME_CO", o.get("dsrx"));
        }
        if(StringUtils.isNotBlank(o.get("dhx"))){
            dataReq.put("TEL_NUMBER", o.get("dhx"));
        }
        if(StringUtils.isNotBlank(o.get("shdzx"))){
            dataReq.put("STREET", o.get("shdzx"));
            dataReq.put("CITY1", o.get("csx"));
            dataReq.put("CITY2", o.get("qyqxx"));
            dataReq.put("REGIO", o.get("dqbmx"));
        }

        req.put("CTRL", ctrlReq);
        req.put("DATA", dataReq);

        return req;
    }

    /**
     * 获取默认CTRL
     * @param requestId
     * @param line
     * @param lcbh
     * @return
     */
    public JSONObject getCtrlReq(String requestId, int line, String lcbh){
        JSONObject ctrlReq = new JSONObject();
        Date date = new Date();
        ctrlReq.put("SYSID", "OA");
        ctrlReq.put("REVID", "SAP");
        ctrlReq.put("FUNID", "ZINF065");
        ctrlReq.put("INFID", requestId + System.currentTimeMillis());
        ctrlReq.put("UNAME", "sysadmin");
        ctrlReq.put("DATUM", TimeUtils.getTimeStr(date, "yyyy-MM-dd"));
        ctrlReq.put("UZEIT", TimeUtils.getTimeStr(date, "HH:mm:ss"));
        ctrlReq.put("KEYID", lcbh + "-" + line);

        return ctrlReq;
    }

    /**
     * 更新明细表1的数据
     * @param tableName
     */
    public void updateItem1Sql(String tableName,String id, String sfyts, String partner, String MSGTY, String MSAGE){
        String sql = "";
        sql = "update "+tableName+"_dt1  set sfyts = ?,sdfbm = ?, xxlx = ?, xxnr = ? where id = ?";
        rs.executeUpdate(sql, sfyts, partner, MSGTY, MSAGE, id);
    }


    /**
     * 更新明细表2的数据
     * @param tableName
     * @param id
     */
    public void updateItem2Sql(String tableName, String id, String sfyts, String MSGTY){
        String sql = "";
        sql = "update "+tableName+"_dt2  set sfyts = ?, xxlx = ? where id = ?";
        rs.executeUpdate(sql, sfyts, MSGTY, id);
    }

}

package yitouniu.afternodeoperation;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.OMSUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;


/**
 * @program: oa
 * @description: 客户随单满赠流程
 * @author: haiyang
 * @create: 2024-01-16 16:47
 **/
public class CustomerOrderWithGiftAction implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        String BWKEY = "";
        String currentMainId = "";
        List<String> platformOrderNos = Lists.newArrayList();
        List<String> validatePlatformOrderNos = Lists.newArrayList();
        // 流程中添加的赠品的价值<原始订单号, 总价值金额>
        Map<String, Double> dt1AmountMap = new HashMap<>();
        if (main.next()) {
            //mainId
            currentMainId = main.getString("id");
            // 物料所属公司代码
            BWKEY = main.getString("wlszgsdm");

            String itemSql = "select * from " + tableName + "_dt1 where mainid = ? ";
            item.executeQuery(itemSql, currentMainId);

            while(item.next()){
                String platformOrderNo = item.getString("ysddh");
                platformOrderNos.add(platformOrderNo);
                // 重复订单使用原因
                String repeatUseOrderReason = item.getString("zfddsyyy");
                // 如果重复订单使用原因不为空，说明业务需要跳过校验继续提交
                if (StringUtils.isEmpty(repeatUseOrderReason)) {
                    validatePlatformOrderNos.add(platformOrderNo);
                }

                double spze = item.getDouble("spze");
                Double platformOrderLineGiftAmount = dt1AmountMap.get(platformOrderNo);
                if (null == platformOrderLineGiftAmount){
                    dt1AmountMap.put(platformOrderNo, spze);
                } else {
                    dt1AmountMap.put(platformOrderNo, new BigDecimal(spze)
                            .add(new BigDecimal(platformOrderLineGiftAmount))
                            .doubleValue());
                }
            }
        }
        baseBean.writeLog("CustomerOrderWithGiftAction.BWKEY=" + BWKEY);
        baseBean.writeLog("platformOrderNos" + platformOrderNos);
        baseBean.writeLog("validatePlatformOrderNos" + validatePlatformOrderNos);
        baseBean.writeLog("dt1AmountMap" + dt1AmountMap);
        // 更新订单号被使用次数
        baseBean.writeLog("更新订单号被使用次数");
        updateOrderRepeatCount(platformOrderNos, currentMainId, tableName);
        // 查询单号在oa表中是否存在  查询记录后 通过mainId过滤不属于当前流程的
        baseBean.writeLog("校验订单是否存在并提示报错");
        String warningMsg = checkOrderNoExist(validatePlatformOrderNos, currentMainId, tableName, requestInfo);
        if (StringUtils.isNotEmpty(warningMsg)) {
            return warningMsg;
        }

        JSONObject omsPlatformOrderRequest = new JSONObject();
        omsPlatformOrderRequest.put("platformOrderNos", platformOrderNos);
        //获取token
        OMSUtils omsUtils = new OMSUtils();
        String token = omsUtils.getToken();
        String api = Util.null2String(new BaseBean().getPropValue("oms_configuration","giftOrderApi"));

        baseBean.writeLog( "请求request = " + omsPlatformOrderRequest);
        String result = HttpRequest
                .post(OMSUtils.OMS_OMS_URL + api)
                .header("r3-api-token",token)
                .body(omsPlatformOrderRequest.toJSONString()).execute().body();
        new BaseBean().writeLog( "请求返回="+result);

        JSONObject resultJson = JSONObject.parseObject(result);

        if(resultJson.getInteger("code") != 0){
            String errMsg = resultJson.getString("message");
            if(StringUtils.isEmpty(errMsg)){
                requestInfo.getRequestManager().setMessagecontent("未校验出错误但错误，请联系负责人确认");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
            requestInfo.getRequestManager().setMessagecontent(errMsg);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        JSONArray jsonArray = resultJson.getJSONArray("data");
        if (null == jsonArray) {
            requestInfo.getRequestManager().setMessagecontent("原始订单获取oms订单信息为空");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        List<String> skuNos = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject orderJsonObj = (JSONObject) jsonArray.get(i);
            JSONArray orderItemArr = orderJsonObj.getJSONArray("giftOrderItems");
            for (int j = 0; j < orderItemArr.size(); j++) {
                JSONObject orderItemObj = orderItemArr.getJSONObject(j);
                skuNos.add(orderItemObj.getString("skuCode"));
            }
        }
        baseBean.writeLog( "skuNos = " + skuNos);
        Map<String, String> skuCostPriceMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuNos)) {
            String matnr = Joiner.on("','").join((Iterable<?>) skuNos);
            matnr = "'" + matnr + "'";
            String sql1 = "select MATNR,STPRS,PEINH from uf_wlzsj where MATNR in (" + matnr + ") and BWKEY = " + BWKEY;
            rs.executeQuery(sql1);
            while (rs.next()) {
                String STPRSStr = rs.getString("STPRS");
                if (StringUtils.isBlank(STPRSStr)) {
                    STPRSStr = "0";
                }
                BigDecimal STPRS = new BigDecimal(STPRSStr);
                BigDecimal PEINH = new BigDecimal(rs.getString("PEINH"));

                skuCostPriceMap.put(rs.getString("MATNR"), STPRS.divide(PEINH).toString());
            }
        }
        baseBean.writeLog( "skuCostPriceMap = " + skuCostPriceMap);

        // 计算赠品总价 存到uf_omsysdd 和uf_omsysdd_dt1 表中
        //异步插入
        baseBean.writeLog( "主线程threadId = " + Thread.currentThread().getName());
        CompletableFuture.runAsync(() -> {
            baseBean.writeLog( "异步threadId = " + Thread.currentThread().getName());
            insertRequestRecord(jsonArray, skuCostPriceMap);
        });
        baseBean.writeLog( "执行费率计算开始");
        // 计算费率插入到requestId 获取的表中
        List<List> lists = calculateRate(jsonArray, skuCostPriceMap, dt1AmountMap, requestid, currentMainId);
        baseBean.writeLog( "执行费率计算结束");

        updateDt1(lists, tableName);
        baseBean.writeLog("全部结束");
        return SUCCESS;



    }

    private void updateOrderRepeatCount(List<String> platformOrderNos, String currentMainId, String tableName) {
        String validatePlatformOrdersStr = Joiner.on("','").join((Iterable<?>) platformOrderNos);
        validatePlatformOrdersStr = "'" + validatePlatformOrdersStr + "'";
        String existOrderSql = "select * from " + tableName + "_dt1 where ysddh in (" + validatePlatformOrdersStr + ")";
        baseBean.writeLog("updateOrderRepeatCount.sql:" + existOrderSql);
        rs.execute(existOrderSql);
        // mainid_ysddh, count
        Map<String, Integer> archivedOrderCountMap = new HashMap<>();
        while (rs.next()) {
            String ysddh = rs.getString("ysddh");
            String mainid = rs.getString("mainid");
            String requestid = rs.getString("lcid");
            baseBean.writeLog("updateOrderRepeatCount.requestid: " + requestid);
            baseBean.writeLog("updateOrderRepeatCount.ysddh: " + ysddh);
            baseBean.writeLog("updateOrderRepeatCount.mainid: " + mainid);
            baseBean.writeLog("updateOrderRepeatCount.currentmainid: " + currentMainId);
            // 是否已归档 默认false
            boolean hasArchived = false;
            if (StringUtils.isNotEmpty(requestid) && !requestid.equals("-1")) {
                RecordSet processRs = new RecordSet();
                // 等于1 是未归档
                String processStatusSql = "select * from workflow_requestbase where currentnodetype = 3 and requestid = " + requestid;
                baseBean.writeLog("updateOrderRepeatCount.processStatusSql: " + processStatusSql);
                processRs.execute(processStatusSql);
                while (processRs.next()) {
                    String str = processRs.getString("requestid");
                    if (StringUtils.isNotEmpty(str)) {
                        hasArchived = true;
                    }
                }
            }
            if (!mainid.equals(currentMainId)) {
                if (hasArchived) {
                    String key = currentMainId + "_" + ysddh;
                    Integer count = archivedOrderCountMap.get(key);
                    if (Objects.isNull(count)) {
                        archivedOrderCountMap.put(key, 1);
                    } else {
                        count++;
                        archivedOrderCountMap.put(key, count);
                    }
                }
            }
        }
        baseBean.writeLog("archivedOrderCountMap: " + archivedOrderCountMap);
        for (Map.Entry<String, Integer> entry : archivedOrderCountMap.entrySet()) {
            String[] keyArr = entry.getKey().split("_");
            String mainid = keyArr[0];
            String ysddh = keyArr[1];
            String sql = "update " + tableName + "_dt1 set zfddsycs = ? where mainid = ? and ysddh =?";
            baseBean.writeLog("更新次数sql：" + sql);
            rs.executeUpdate(sql,entry.getValue(), mainid, ysddh);
        }


    }

    /**
     * 校验订单是否在oa流程中已存在
     * @param validatePlatformOrderNos
     * @param currentMainId
     * @param tableName
     * @param requestInfo
     * @return
     */
    private String checkOrderNoExist(List<String> validatePlatformOrderNos, String currentMainId, String tableName, RequestInfo requestInfo) {
        if (CollectionUtils.isEmpty(validatePlatformOrderNos)) return "";
        String validatePlatformOrdersStr = Joiner.on("','").join((Iterable<?>) validatePlatformOrderNos);
        validatePlatformOrdersStr = "'" + validatePlatformOrdersStr + "'";
        String existOrderSql = "select * from " + tableName + "_dt1 where ysddh in (" + validatePlatformOrdersStr + ")";
        rs.execute(existOrderSql);
        while (rs.next()) {
            String ysddh = rs.getString("ysddh");
            String mainid = rs.getString("mainid");
            String requestid = rs.getString("lcid");
            baseBean.writeLog("原始订单号："+ ysddh +",mainid:" + mainid +",requestid:" + requestid);
            // 是否已归档 默认false
            boolean hasArchived = false;
            if (StringUtils.isNotEmpty(requestid) && !requestid.equals("-1")) {
                RecordSet processRs = new RecordSet();
                String processStatusSql = "select * from workflow_requestbase where currentnodetype =3 and requestid = " + requestid;
                processRs.execute(processStatusSql);
                String archivedRequestname = "";
                while (processRs.next()) {
                    String str = processRs.getString("requestid");
                    if (StringUtils.isNotEmpty(str)) {
                        hasArchived = true;
                        archivedRequestname =  processRs.getString("requestname");
                    }
                }
                if (!mainid.equals(currentMainId)) {
                    if (hasArchived) {
                        requestInfo.getRequestManager().setMessagecontent(ysddh + "原始订单已被其他已归档流程:【"+archivedRequestname+"】使用，请确认是否重复使用,若确认重复使用，请填写重复使用原因");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    } else {
                        RecordSet processRs1 = new RecordSet();
                        String processStatusSql1 = "select * from workflow_requestbase where currentnodetype =1 and requestid = " + requestid;
                        processRs1.execute(processStatusSql1);
                        while (processRs1.next()) {
                            String requestname = processRs1.getString("requestname");
                            if (StringUtils.isNotEmpty(requestname)) {
                                requestInfo.getRequestManager().setMessagecontent(ysddh + "原始订单已被其他未归档流程：【"+requestname+"】使用，禁止提交");
                                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                                return FAILURE_AND_CONTINUE;
                            }
                        }
                        requestInfo.getRequestManager().setMessagecontent(ysddh + "原始订单已被其他未归档流程使用，禁止提交");
                        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                        return FAILURE_AND_CONTINUE;
                    }
                }
            }
        }


        return "";
    }

    private List<List> calculateRate(JSONArray jsonArray,
                                        Map<String, String> skuCostPriceMap,
                                        Map<String, Double> dt1AmountMap,
                                        String requestid,
                                        String currentMainid
                                     ) {
        List<List> updateList = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            List list = Lists.newArrayList();
            JSONObject orderJsonObj = (JSONObject) jsonArray.get(i);
            String platformOrderNo = orderJsonObj.getString("platformOrderNo");
            Double newGiftAmount = dt1AmountMap.getOrDefault(platformOrderNo, 0.00);
            JSONArray orderItemArr = orderJsonObj.getJSONArray("giftOrderItems");
            baseBean.writeLog("calculateRate.orderItemArr: " + orderItemArr);
            BigDecimal ddzpze = new BigDecimal(0);
            if (CollectionUtils.isNotEmpty(orderItemArr)) {
                for (int j = 0; j < orderItemArr.size(); j++) {
                    JSONObject orderItemObj = orderItemArr.getJSONObject(j);
                    baseBean.writeLog("calculateRate.orderItemObj: "+ orderItemObj);
                    String skuCode = orderItemObj.getString("skuCode");
                    Integer quantity = orderItemObj.getInteger("quantity");
                    String costPrice = skuCostPriceMap.getOrDefault(skuCode, "0");
                    ddzpze = ddzpze.add(new BigDecimal(costPrice).multiply(new BigDecimal(quantity)));
                }
            }
            Double productAmt = orderJsonObj.getDouble("productAmt");
            baseBean.writeLog("平台单=" + platformOrderNo +",订单总额="+productAmt +"订单赠品总额="+ddzpze+",添加赠品总额=" + newGiftAmount);
            BigDecimal rate;
            if (productAmt == 0) {
                rate = new BigDecimal(0);
            } else {
                rate = new BigDecimal(newGiftAmount).add(ddzpze).divide(new BigDecimal(productAmt),2, BigDecimal.ROUND_HALF_UP);
            }
            baseBean.writeLog("费率="+rate);
            list.add(rate.toString());
//            list.add(orderJsonObj.getString("cpCRegionProvinceEname"));
//            list.add(orderJsonObj.getString("cpCRegionCityEname"));
//            list.add(orderJsonObj.getString("cpCRegionAreaEname"));
            list.add(orderJsonObj.getString("cpCShopTitle"));
            list.add(requestid);
            list.add(platformOrderNo);
            list.add(currentMainid);
            updateList.add(list);
        }
        return updateList;
    }

    public static void main(String[] args) {
        JSONObject omsPlatformOrderRequest = new JSONObject();
        omsPlatformOrderRequest.put("platformOrderNos", "LP00634281784648");
        //获取token
        String token ="";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("userName", "erp");
//            paramMap.put("userKey", userKeyMd5);
//            paramMap.put("requestSign", requestSign);
        paramMap.put("userKey", "e10adc3949ba59abbe56e057f20f883e");
        paramMap.put("requestSign", "cd70c798a580c5dbdd765690ab90b05b");
        String httpResponse = HttpRequest.get("http://**************:21180/api/auth/login").form(paramMap).execute().body();
        if(!"".equals(httpResponse)){
            JSONObject responseJson = JSONObject.parseObject(httpResponse);
            boolean successFlag = responseJson.getBoolean("success");
            if(successFlag){
                token = responseJson.getString("loginToken");
            }
        }

//        baseBean.writeLog( "请求request = " + omsPlatformOrderRequest);
        String result = HttpRequest
                .post("http://**************:21180/api/ip/oa/gift/order/info")
                .header("r3-api-token",token)
                .body(omsPlatformOrderRequest.toJSONString()).execute().body();
        System.out.println(result);
    }

    private void insertRequestRecord(JSONArray jsonArray, Map<String, String> skuCostPriceMap) {
        List<List> list = Lists.newArrayList();
        List<List> subList = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            List dataList = new ArrayList<>();
            JSONObject orderJsonObj = (JSONObject) jsonArray.get(i);
            String platformOrderNo = orderJsonObj.getString("platformOrderNo");
            dataList.add(platformOrderNo);
            dataList.add(orderJsonObj.getDouble("productAmt"));
            JSONArray orderItemArr = orderJsonObj.getJSONArray("giftOrderItems");

            BigDecimal ddzpze = new BigDecimal(0);
            if (CollectionUtils.isNotEmpty(orderItemArr)) {
                for (int j = 0; j < orderItemArr.size(); j++) {
                    List itemList = new ArrayList<>();
                    JSONObject orderItemObj = orderItemArr.getJSONObject(j);
                    String skuCode = orderItemObj.getString("skuCode");
                    itemList.add(skuCode);
                    itemList.add(orderItemObj.getInteger("quantity"));
                    String costPrice = skuCostPriceMap.getOrDefault(skuCode, "0");
                    itemList.add(costPrice);
                    itemList.add(platformOrderNo);
                    ddzpze = ddzpze.add(new BigDecimal(costPrice));
                    subList.add(itemList);
                }
            }
            dataList.add(ddzpze);
            list.add(dataList);
        }
        insertOmsysdd(list);
        insertOmsysddDt1(subList);
    }

    public boolean insertOmsysdd(List<List> list) {
        RecordSet rs = new RecordSet();
        String sql = "insert into uf_omsysdd (ptddh, ddze, ddzpze,formmodeid,modedatacreater,modedatacreatertype) values (?,?,?,196,1,1)";
        return rs.executeBatchSql(sql, list);
    }

    public boolean insertOmsysddDt1(List<List> list) {
        if (CollectionUtils.isEmpty(list)) return true;
        RecordSet rs = new RecordSet();
        String sql = "insert into uf_mosysddzpsj (zpbm, zpsl, zpcbj, ptddh,formmodeid,modedatacreater,modedatacreatertype) values (?,?,?,?,197,1,1)";
        return rs.executeBatchSql(sql, list);
    }

    public boolean updateDt1(List<List> list, String tableName) {
        baseBean.writeLog("updateList: " + list);
        RecordSet rs = new RecordSet();
        String sql = "update "+tableName+"_dt1 set ddzpzfl1 = ?, dpmc = ?, lcid = ? where ysddh = ? and mainid = ?";
        baseBean.writeLog("update sql:" + sql);
        return rs.executeBatchSql(sql, list);
    }
}

package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 核销流程扣减金额
 */
public class WriteOffSFMoneyAction extends WriteOffMoney implements Action {
    private RecordSet recordSet = new RecordSet();



    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 获取requestid
        new BaseBean().writeLog(requestid + "WriteOffSFMoneyAction");
        super.getData(requestid);
        boolean ishx = super.isIshx(); // 是否为预付款
        List<String> xzyfkList = super.getXzyfkList(); // 选择预付款
        List<String> yfkhxhje1List = super.getYfkhxhje1List(); // 预付款核销后金额
        List<String> yfkbchxje1List = super.getYfkbchxje1List(); // 预付款本次核销金额
        List<String> yfkje1List = super.getYfkje1List(); // 预付款本次核销金额
        new  BaseBean().writeLog("shuju :"+ishx+","+xzyfkList+","+yfkhxhje1List+","+yfkbchxje1List );
        if (ishx) {
            if (xzyfkList.size() > 0) {
                for (int i = 0; i < xzyfkList.size(); i++) {
                    Map<String, String> findyfje = ActionUtil.findyfje(xzyfkList.get(i));
                    String djje = findyfje.get("djje"); // 冻结金额
                    String yfksyje = yfkje1List.get(i); // 本次预付金额
                    String hxje = yfkhxhje1List.get(i);// 预付款核销后金额



                    if (!"".equals(hxje) && hxje != null && Double.valueOf(hxje) >= 0) {
                        double hxjeDouble = Double.valueOf(hxje);

                        String je = yfkbchxje1List.get(i); // 本次核销金额
                        new BaseBean().writeLog("==="+hxje+","+djje+","+je+"====");
                        if (!"".equals(djje) && djje != null&&Double.valueOf(djje)>0) {
                            hxje = (Double.valueOf(hxje) + Double.valueOf(djje)) + "";
                            djje = (Double.valueOf(djje)- Double.valueOf(je))+"";
                        }
                        new BaseBean().writeLog(hxje+","+djje);
                        recordSet.executeUpdate("update uf_yufukuanku set yfksyje = ?, djje = ? where id= ?", hxje,djje, xzyfkList.get(i));

                    } else {
                        requestInfo.getRequestManager().setMessageid(requestid);
                        requestInfo.getRequestManager().setMessage("核销后金额不能为空或者为负数");
                        return FAILURE_AND_CONTINUE;

                    }
                }
            }
        }

        return SUCCESS;
    }
}

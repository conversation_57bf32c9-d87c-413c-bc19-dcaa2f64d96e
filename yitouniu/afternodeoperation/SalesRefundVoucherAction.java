package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 销售退款推送凭证
 */
public class SalesRefundVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();
    private String hrmName = "";

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");

        String bz = ""; // 备注
        String lcbh = ""; // 流程编号
        String sqr = ""; // 申请人
        String KUNNR = ""; // 客户编码
        String mainid = ""; // id
        String bankNum = ""; // id
        String NAME1 = ""; // id


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");
            bz = rs1.getString("BKTXT");
            lcbh = rs1.getString("lcbh");
            sqr = rs1.getString("UNAME");
            KUNNR = rs1.getString("KUNNR");
            bankNum = rs1.getString("BANKNUM");
            NAME1 = rs1.getString("NAME1");

        }
        hrmName = ActionUtil.getHrmName(sqr);

        // 删除明细表1
        sql = "DELETE FROM " + tableName + "_dt3  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表三删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");


        // 查询明细表1的数据

        List<String> je = new ArrayList<>();//金额
        List<String> glxsdd = new ArrayList<>();//金额


        String SGTXT = hrmName+"申请--退"+NAME1+"货款";


        sql = "select * from " + tableName + "_dt1 where mainid = ?";
        rs1.executeQuery(sql, mainid);
        while (rs1.next()) {

            je.add(rs1.getString("WRBTR"));
            glxsdd.add(rs1.getString("glxsdd"));




        }
        String totalMoney = "0.0";
        for (int i = 0; i < je.size(); i++) {
            String money = je.get(i);
            //totalMoney += Double.valueOf(money);
            totalMoney = ActionUtil.bigDecimalAdd(totalMoney,money);
            // 借方数据
            ActionUtil.insertData(tableName + "_dt3", "", "", "", "", SGTXT,money , KUNNR, "05", "", glxsdd.get(i), "", "", "", mainid, "DZ");

        }


        //贷方数据
        ActionUtil.insertData(tableName + "_dt3", "", bankNum, "101", "", SGTXT, totalMoney , "", "50", "", "", "", "", "", mainid, "DZ");


        return SUCCESS;
    }


}

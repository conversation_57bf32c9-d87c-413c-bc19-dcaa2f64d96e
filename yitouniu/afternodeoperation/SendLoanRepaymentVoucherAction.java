package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.esb.client.EsbClient;
import com.weaver.esb.spi.EsbService;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.List;
import java.util.Map;

public class SendLoanRepaymentVoucherAction implements Action {
    private RecordSet rs1 = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {


        String requestid = requestInfo.getRequestid(); // 流程id

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "凭证推送开始----------------------------<br/>");

        String sql = "select * from " + tableName + " where requestid = ?";

        String mainid = "";
        String sqrq = "";
        String bz = "";
        String lcbh = "";
        String lclx = "";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");

            sqrq = rs1.getString("sqrq");
            lclx = rs1.getString("lclx");
            bz = rs1.getString("bz");
            lcbh = rs1.getString("lcbh");

        }
        if ("拆借".equals(lclx)) {

            String detailData_dt2 = getDetailData(tableName + "_dt2", mainid, requestid, lcbh, bz, sqrq);
            if (!"100".equals(detailData_dt2)) {
                requestInfo.getRequestManager().setMessageid(requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent(detailData_dt2);
                return FAILURE_AND_CONTINUE;

            }else{
                String detailData_dt3 = getDetailData(tableName + "_dt3", mainid, requestid, lcbh, bz, sqrq);
                rs1.executeUpdate("update  " + tableName + "_dt2 set fspz = 0 where mainid = ?", mainid);

                if (!"100".equals(detailData_dt3)) {
                    requestInfo.getRequestManager().setMessageid(requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent(detailData_dt3);
                    return FAILURE_AND_CONTINUE;
                }else{
                    rs1.executeUpdate("update  " + tableName + "_dt3 set fspz = 0 where mainid = ?", mainid);

                    return SUCCESS;
                }
            }

        } else {
            String detailData_dt2 = getDetailData(tableName + "_dt2", mainid, requestid, lcbh, bz, sqrq);
            if (!"100".equals(detailData_dt2)) {
                requestInfo.getRequestManager().setMessageid(requestInfo.getRequestid());
                requestInfo.getRequestManager().setMessagecontent(detailData_dt2);
                return FAILURE_AND_CONTINUE;

            }else{
                String detailData_dt3 = getDetailData(tableName + "_dt3", mainid, requestid, lcbh, bz, sqrq);
                rs1.executeUpdate("update  " + tableName + "_dt2 set fspz = 0 where mainid = ?", mainid);

                if (!"100".equals(detailData_dt3)) {
                    requestInfo.getRequestManager().setMessageid(requestInfo.getRequestid());
                    requestInfo.getRequestManager().setMessagecontent(detailData_dt3);
                    return FAILURE_AND_CONTINUE;
                }else{
                    rs1.executeUpdate("update  " + tableName + "_dt3 set fspz = 0 where mainid = ?", mainid);
                    return SUCCESS;
                }
            }

        }


    }

    public String getDetailData(String tableName, String mainid, String requestid, String lcbh, String bz, String sqrq) {

        JSONArray jsonArray = new JSONArray();
        String sql = "select * from " + tableName + " where mainid = ? and fspz is null ";
        String[] field = {"BSCHL", "UMSKZ", "WRBTR", "SGTXT", "KOSTL", "EBELN", "RSTGR", "ZUONR", "AUFNR", "HKONT", "KUNNR", "LIFNR", "ZFKBS", "VBUND", "BLART", "BUKRS"};
        rs1.executeQuery(sql, mainid);
        while (rs1.next()) {
            JSONObject jsonObject = new JSONObject();
            for (int i = 0; i < field.length; i++) {
                jsonObject.put(field[i], rs1.getString(field[i]));
            }
            jsonObject.put("ZOAID", lcbh);
            jsonObject.put("WAERS", "CNY");
            jsonObject.put("ZFKQQID", lcbh);
            jsonObject.put("BKTXT", bz);
            jsonObject.put("BLDAT", sqrq);
            jsonObject.put("ZBUTYP", "BU04");
            jsonObject.put("ZXBLN", lcbh);
            jsonArray.add(jsonObject);
        }
        String response = makeJson(jsonArray, requestid, lcbh);
        return response;
    }

    public String makeJson(JSONArray jsonArray, String requestid, String lcbh) {
        if (jsonArray.size() > 0 && jsonArray != null) {
            String params = "{\n" +
                    "\t\"UNAME\": \"sysadmin\",\n" +
                    "\t\"DATUM\": \"\",\n" +
                    "\t\"SYSID\": \"OA\",\n" +
                    "\t\"INFID\": \"" + requestid + "\",\n" +
                    "\t\"MSGTY\": \"\",\n" +
                    "\t\"FUNID\": \"ZINF011\",\n" +
                    "\t\"REVID\": \"SAP\",\n" +
                    "\t\"TABIX\": \"\",\n" +
                    "\t\"MSAGE\": \"\",\n" +
                    "\t\"KEYID\": \"" + lcbh + "\",\n" +
                    "\t\"UZEIT\": \"\"\n" +
                    "}";
            JSONObject jsonObject = JSONObject.parseObject(params);
            jsonObject.put("DATA", jsonArray);
            EsbService service = EsbClient.getService(); // 调用esb方法
            String response = service.execute("ZINF011", jsonObject.toJSONString()); //触发 ESB 事件
            JSONObject responseJSon = JSONObject.parseObject(response);
            String code = responseJSon.getString("code");
            if (!"100".equals(code)) {
                String msg = responseJSon.getString("msg");
                JSONObject jsonObject1 = JSONObject.parseObject(msg);
                List<JSONObject> data = (List<JSONObject>) jsonObject1.get("DATA");
                String string = data.get(0).getString("MSAGE");
                return string;
            }
            return code;
        }else {
            return "100";
        }
    }

}

package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class WriteOffMoney {

    private String tableName;
    private List<String> xzyfkList;
    private List<String> yfkje1List;
    private List<String> yfkhxhje1List;
    private List<String> yfkbchxje1List;
    private boolean ishx = false;
    private RecordSet recordSet = new RecordSet();




    public List<String> getYfkje1List() {
        return yfkje1List;
    }

    public void setYfkje1List(List<String> yfkje1List) {
        this.yfkje1List = yfkje1List;
    }

    public String getTableName() {
        return tableName;
    }

    public boolean isIshx() {
        return ishx;
    }

    public void setIshx(boolean ishx) {
        this.ishx = ishx;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getXzyfkList() {
        return xzyfkList;
    }

    public void setXzyfkList(List<String> xzyfkList) {
        this.xzyfkList = xzyfkList;
    }

    public List<String> getYfkhxhje1List() {
        return yfkhxhje1List;
    }

    public void setYfkhxhje1List(List<String> yfkhxhje1List) {
        this.yfkhxhje1List = yfkhxhje1List;
    }

    public List<String> getYfkbchxje1List() {
        return yfkbchxje1List;
    }

    public void setYfkbchxje1List(List<String> yfkbchxje1List) {
        this.yfkbchxje1List = yfkbchxje1List;
    }

    public void getData(String requestid){
        xzyfkList = new ArrayList<>(); // 选择预付款
        yfkhxhje1List = new ArrayList<>(); // 核销后金额
        yfkbchxje1List = new ArrayList<>(); // 本次核销金额
        yfkje1List = new ArrayList<>(); // 预付款金额
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        setTableName(tableName);

        String mainSql = "select  *  from  " + tableName + "   where requestid = ?";
        recordSet.executeQuery(mainSql,requestid);
        String id = "";
        String hxlx = "";
        if (recordSet.next()){
            id = recordSet.getString("id");
            hxlx = recordSet.getString("hxlx");
        }


        if ("0".equals(hxlx)){

            this.setIshx(true);
            String sql = "select  *  from  " + tableName + "_dt1  where mainid = ?";


            recordSet.executeQuery(sql, id);
            while (recordSet.next()) {

                xzyfkList.add(recordSet.getString("xzyfk")); // 选择预付款
                yfkhxhje1List.add(recordSet.getString("yfkhxhje1")); // 预付款核销后金额
                yfkbchxje1List.add(recordSet.getString("yfkbchxje1")); //预付款本次核销金额
                yfkje1List.add(recordSet.getString("yfkje1")); // 预付款金额


            }
        }

    }
}

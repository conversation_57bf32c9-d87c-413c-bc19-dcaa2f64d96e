package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 打款金额小于0释放借款金额
 */
public class ReleaseLoanMoney implements Action {

    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 获取requestid
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");


        String sql = "select  *  from  " + tableName + "  where requestid = ?";
        String sfhx = ""; // 是否有借款
        String PAYSTATE = ""; // 支付状态
        String jkje = ""; // 借款金额
        double bxzje = 0.00; // 实报金额
        String sfsfje = ""; // 实报金额
        String sbje = ""; // 打款金额
        recordSet.executeQuery(sql, requestid);
        if (recordSet.next()) {

            sfhx = recordSet.getString("sfhx");
            jkje = recordSet.getString("jkje");
            PAYSTATE = recordSet.getString("PAYSTATE");
            bxzje = Double.valueOf(recordSet.getString("bxzje"));
            sfsfje = recordSet.getString("sfsfje");
            sbje = recordSet.getString("sbje");
        }


            if ("0".equals(sfhx)) { // 是借款
                Map jkje1 = ActionUtil.findJKJE(jkje);

                String djje = (String) jkje1.get("djje"); // 冻结金额

                if (Double.valueOf(sbje) < 0) { // 借款大于报销

                    if (!"".equals(djje) && Double.valueOf(djje) > bxzje) {
                        djje = (Double.valueOf(djje) - bxzje) + "";
                    } else {
                        djje = "0";
                    }
                    recordSet.executeUpdate("update uf_jiekuanku set djje = ? where id = ?", djje, jkje);




/*
                        String je = getJe(requestid);
                        if (je != "" && je != null) {
                            jkjebgx = je;
                            djje = (Double.valueOf(djje) - Double.valueOf(je)) + "";
                        } else {
                            jkjebgx = djje;
                            djje = "0";
                        }
                        recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", jkjebgx, djje, jkje);

                    }
                } else { // 支付成功
                    if (jkwhxje > bxzje) { // 借款大于报销

                        if (Double.valueOf(djje) > bxzje) {
                            djje = (Double.valueOf(djje) - bxzje) + "";
                        } else {
                            djje = "0";
                        }
                        jkwhxje = Double.valueOf(jkjebgx)-bxzje;
                        recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ? where id = ?", jkwhxje, djje, jkje);
                    } else {
                        String je = getJe(requestid);
                        if (je != "" && je != null) {

                            djje = (Double.valueOf(djje) - Double.valueOf(je)) + "";
                        } else {

                            djje = "0";
                        }
                        recordSet.executeUpdate("update uf_jiekuanku set jkwhxje = ? ,djje = ?  where id = ?", "0", djje, jkje);

                    }*/
                }
            }
        return SUCCESS;
        }

    }





package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description 免费订单物料校验
 * @Version 1.0
 */
public class FreeOrderMaterialCheckAction implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        RecordSet depart = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String) tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        baseBean.writeLog("免费订单校验requestid=" + requestid);

        if (main.next()) {
            //mainId
            String mainId = main.getString("id");

            String sqlDtl = "select * from " + tableName + "_dt1 where mainid = " + mainId;
            item.executeQuery(sqlDtl);

            List<Integer> rowError = new ArrayList<>();

            int row = 1;
            while (item.next()) {

                String bukrs = item.getString("bukrs");
                String MATNR = item.getString("MATNR");

                if (StringUtils.isBlank(bukrs) || StringUtils.isBlank(MATNR)) {
                    requestInfo.getRequestManager().setMessagecontent("免费订单明细的工厂参数或物料参数为空id=" + item.getString("id"));
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }

                String execSql = "select * from view_wlzsj where BWKEY = " + bukrs + " and MATNR = '" + MATNR + "'";

                depart.executeQuery(execSql);

                if (!depart.next()) {
                    rowError.add(row);
                }

                row++;
            }

            if (rowError.size() == 0) {
                return SUCCESS;
            }

            requestInfo.getRequestManager().setMessagecontent("该公司主体下无相关物料，请检查物料编码是否有误或切换公司主体");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));

            return FAILURE_AND_CONTINUE;
        }

        requestInfo.getRequestManager().setMessagecontent("对应请求流程不存在requestid=" + requestid);
        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));

        return FAILURE_AND_CONTINUE;
    }

}

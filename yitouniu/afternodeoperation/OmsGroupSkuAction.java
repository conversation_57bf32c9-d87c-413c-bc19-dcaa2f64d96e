package yitouniu.afternodeoperation;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.OMSUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/3/22 18:13
 * @Description 组合商品推送OMS
 * @Version 1.0
 */
public class OmsGroupSkuAction implements Action {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    /**
     * 组合商品地址
     */
    public static final String OMS_SKU_GROUP_SAVE_API = "/api/ip/oa/sku/group/save";


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select id,sfkcf from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        if(main.next()){
            JSONObject request = new JSONObject();
            int id = main.getInt("id");
            int sfkcf = main.getInt("sfkcf");
            //查找未创建过的
            String itemSql = "select * from " + tableName + "_dt1 where mainid = ? and sfycj = 1";
            rs.executeQuery(itemSql, id);
            request.put("canSplit",sfkcf);
            JSONArray skuItemList = new JSONArray();
            while(rs.next()){
                JSONObject itemJson = new JSONObject();
                String zhmmc = rs.getString("zhmmc");
                String lineNo = rs.getString("xh");
                itemJson.put("skuGroupName", zhmmc);
                itemJson.put("lineNo", lineNo);
                JSONArray proItemList = new JSONArray();
                for(int i=1;i<5;i++){
                    String wlbm = "wlbm" + i;
                    String sl = "sl" + i;
                    String sfzp = "sfzp" + i;
                    String wlbmValue = rs.getString(wlbm);
                    String slValue = rs.getString(sl);
                    String sfzpValue = rs.getString(sfzp);
                    baseBean.writeLog("wlbm="+wlbm);
                    baseBean.writeLog("sl="+sl);
                    baseBean.writeLog("zfzp="+sfzp);
                    baseBean.writeLog("wlbmValue="+wlbmValue);
                    baseBean.writeLog("slValue="+slValue);
                    baseBean.writeLog("zfzpValue="+sfzpValue);
                    if(StringUtils.isNotBlank(wlbmValue) && StringUtils.isNotBlank(slValue) && StringUtils.isNotBlank(sfzpValue)){
                        JSONObject proItemJson = new JSONObject();
                        proItemJson.put("proCode", wlbmValue);
                        proItemJson.put("num", slValue);
                        proItemJson.put("isGift", sfzpValue);
                        proItemList.add(proItemJson);
                    }
                }
                itemJson.put("proItemList", proItemList);

                skuItemList.add(itemJson);
            }
            if(skuItemList.isEmpty()){
                return SUCCESS;
            }


            request.put("skuItemList",skuItemList);

            //获取token
            OMSUtils omsUtils = new OMSUtils();
            String token = omsUtils.getToken();
            baseBean.writeLog( "组合商品推送OMS 请求request = "+request.toJSONString());
            String response = HttpRequest
                    .post(OMSUtils.OMS_OMS_URL + OMS_SKU_GROUP_SAVE_API)
                    .header("r3-api-token",token)
                    .body(request.toJSONString()).execute().body();
            baseBean.writeLog("组合商品推送OMS 请求返回="+response);

            JSONObject responseJson = JSONObject.parseObject(response);
            String message = responseJson.getString("message");
            JSONArray dataArray =  responseJson.getJSONArray("data");

            if(responseJson.getInteger("code") != 0){
                if(message == null && dataArray.size() == 0){
                    requestInfo.getRequestManager().setMessagecontent("未校验出错误但错误，请联系负责人确认");
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }
                if(message != null){
                    requestInfo.getRequestManager().setMessagecontent("message"+message);
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }
                dataArray.forEach(o ->{
                    String code = ((JSONObject) o).getString("code");
                    int lineNo = ((JSONObject) o).getInteger("lineNo");
                    String dataMessage = ((JSONObject) o).getString("message");
                    if(code == null){
                        rs.executeUpdate("update " + tableName + "_dt1 set xxnr = ? where mainid = ? and xh = ?", dataMessage, id, lineNo);
                    }
                });
                requestInfo.getRequestManager().setMessagecontent("response"+response);
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            } else {
                dataArray.forEach(o ->{
                    String code = ((JSONObject) o).getString("code");
                    int lineNo = ((JSONObject) o).getInteger("lineNo");
                    rs.executeUpdate("update " + tableName + "_dt1 set zhmbm = ?,sfycj = 0,xxnr = ? where mainid = ? and xh = ?", code, "创建成功", id, lineNo);
                });
            }
        }
        return SUCCESS;
    }


}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公司拆借还款凭证推送
 */
public class LoanRepaymentVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();
    private String hrmName = "";

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        //String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");


        String VBUND = ""; // 借入贸易伙伴
        String mainid = "";
        String jcdw = ""; // 借出单位编码

        String BUKRS = ""; // 借出贸易伙伴
        String bz = ""; // 备注
        String lcbh = ""; // 流程编号
        String lclx = ""; // 流程类型
        String UNAME = ""; // 申请人
        String bankNum = ""; // 银行科目
        String szdw = ""; // 借出单位
        String NAME1 = ""; // 借出单位名称


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");

            VBUND = rs1.getString("VBUND");

            bz = rs1.getString("bz");
            lcbh = rs1.getString("lcbh");
            jcdw = rs1.getString("jcdw");
            BUKRS = rs1.getString("BUKRS");

            lclx = rs1.getString("lclx");
            UNAME = rs1.getString("UNAME");
            bankNum = rs1.getString("BANKNUM");
            szdw = rs1.getString("szdw");
            NAME1 = rs1.getString("NAME1");

        }

        if(BUKRS!=null&&!"".equals(BUKRS)&&BUKRS.length()>4){
            BUKRS = BUKRS.substring(2,BUKRS.length());
        }


        hrmName = ActionUtil.getHrmName(UNAME);

        // 删除明细表三
        sql = "DELETE FROM " + tableName + "_dt3  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表3删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");
        // 删除明细表三
        sql = "DELETE FROM " + tableName + "_dt2  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表2删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");

        String  KUNNR = findBmData(szdw); // 客户编码

        // 查询明细表1的数据

        List<String> je = new ArrayList<>();//金额


        sql = "select * from " + tableName + "_dt1 where mainid = ?";
        rs1.executeQuery(sql, mainid);
        while (rs1.next()) {

            je.add(rs1.getString("WRBTR"));


        }

        String totalMoney = "0.0"; // 预付总计金额
        for (int i = 0; i < je.size(); i++) {

            String money = je.get(i); // 金额
           // totalMoney += Double.valueOf(money); // 总金额
            totalMoney =  ActionUtil.bigDecimalAdd(totalMoney,money);


        }


        if("拆借".equals(lclx)) {
            // 借方
            String subName = ActionUtil.getSubName(szdw);

            String SGTXT = hrmName+"申请--付"+subName+"款";
            //借入
            //insertData(tableName + "_dt2", "", bankNum, "304", "", bz, totalMoney, "", "40", "", lcbh, "", "", "", mainid, "KZ",VBUND);

            // 借出
            insertData(tableName + "_dt3", "", "", "", "", SGTXT, totalMoney, KUNNR, "09", "4", "", "", "", "", mainid, "DZ",BUKRS);

            // 贷方
            //借入
            //insertData(tableName + "_dt2", "", "", "", "", bz, totalMoney , "", "39", "1", lcbh, "",  jcdw, "", mainid, "KZ",VBUND);

            //借出
            insertData(tableName + "_dt3", "", bankNum, "210", "", SGTXT, totalMoney, "", "50", "", "", "", "", "", mainid, "DZ",BUKRS);
        }else{ // 还款
            // 借方
            String SGTXT = hrmName+"申请--还"+NAME1+"款";
            //借入
            insertData(tableName + "_dt3", "", "", "", "", SGTXT, totalMoney, "", "29", "1", "", "", jcdw, "", mainid, "KZ",VBUND);

            // 借出
           // insertData(tableName + "_dt2", "", bankNum, "205", "", bz, totalMoney, "", "40", "", lcbh, "",  "", "", mainid, "DZ",BUKRS);

            // 贷方
            //借入
            insertData(tableName + "_dt3", "", bankNum, "309", "", SGTXT, totalMoney, "", "50", "", "", "","", "", mainid, "KZ",VBUND);

            //借出
           // insertData(tableName + "_dt2", "", "", "", "", bz, totalMoney , KUNNR, "19", "4", lcbh, "", "" , "", mainid, "DZ",BUKRS);

        }

        return SUCCESS;
    }


    /**
     *  更新数据

     */
    public  String insertData(String tableName,String KOSTL,String HKONT,String RSTGR,String AUFNR,String SGTXT,String WRBTR,String KUNNR,String BSCHL,
                                    String UMSKZ,String EBELN,String ZUONR,String LIFNR,String VBUND,String mainid,String BLART,String BUKRS){
        String insertSql = "insert into "+tableName+" (KOSTL,HKONT,RSTGR,AUFNR,SGTXT,WRBTR,KUNNR,BSCHL,UMSKZ,EBELN,ZUONR,LIFNR,VBUND,mainid,BLART,BUKRS) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        recordSet.executeUpdate(insertSql,KOSTL,HKONT,RSTGR,AUFNR,SGTXT,WRBTR,KUNNR,BSCHL,UMSKZ,EBELN,ZUONR,LIFNR,VBUND,mainid,BLART,BUKRS);
        return "";
    }

    public  String findBmData(String gs){
        String sql = "select * from uf_zzdmys where oagsmc = ?";
        recordSet.executeQuery(sql, gs);
        String  khbh = "";
        if (recordSet.next()) {
            khbh = recordSet.getString("khbh");

        }
        return khbh;
    }


}

package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/16 10:43
 * @Description TODO
 * @Version 1.0
 */
public class StoreAccountAdminChangeActionV1 implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tablename = requestInfo.getRequestManager().getBillTableName();

        String dp = "";
        String gjpt = "";
        String zhlx = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("dp".equals(name)) {
                dp = Util.null2String(properties[i].getValue());
            }
            if ("zhlx".equals(name)) {
                zhlx = Util.null2String(properties[i].getValue());
            }
            if ("gjpt".equals(name)) {
                gjpt = Util.null2String(properties[i].getValue());
            }
        }
        String id = "";
        if ("0".equals(zhlx)) {
            id = dp;
        } else if ("1".equals(zhlx)) {
            id = gjpt;
        }
        baseBean.writeLog("StoreAccountAdminChangeAction.zhlx = " + zhlx);
        baseBean.writeLog("StoreAccountAdminChangeAction.id = " + id);
        String dpgly = "";
        List<String> dpglyList = new ArrayList<String>();;
        String sql = "select * from uf_ptdpglb where id in (" + id + ")";
        rs.executeQuery(sql);
        while (rs.next()){
            dpglyList.add(rs.getString("dpgly"));
        }
        dpgly = String.join(",", dpglyList);
        boolean updateDpgly = rs.executeUpdate("update "+tablename+" set dpgly = '"+dpgly+"' where requestid = "+requestid);
        if(updateDpgly){
            return SUCCESS;
        } else {
            requestInfo.getRequestManager().setMessagecontent("店铺管理员更新失败，请联系系统管理员");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

    }
}

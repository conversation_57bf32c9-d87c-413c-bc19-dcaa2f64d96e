//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package yitouniu.afternodeoperation;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

public class SalaryVoucherAction implements Action {
    public SalaryVoucherAction() {
    }

    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet rs1 = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String billid = (String)tableNameByRequestId.get("id");
        (new BaseBean()).writeLog("------------------" + requestid + "开始----------------------------<br/>");
        String sqr = "";
        String WRBTR = "";
        String mainid = "";
        String bankNum = "";
        String xzsx = "";
        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, new Object[]{requestid});
        if (rs1.next()) {
            mainid = rs1.getString("id");
            xzsx = rs1.getString("xzsx");
            sqr = rs1.getString("ffry");
            WRBTR = rs1.getString("xzbfje");
            bankNum = rs1.getString("BANKNUM");
        }

        String hrmName = ActionUtil.getHrmName(sqr);
        sql = "DELETE FROM " + tableName + "_dt1  WHERE mainid = ?";
        Map<String, String> data = ActionUtil.findData(sqr);
        String rybh = (String)data.get("rybh");
        (new BaseBean()).writeLog("--->明细表一删除：" + rs1.executeUpdate(sql, new Object[]{mainid}));
        String currentDate = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
        if ("1".equals(xzsx)) {    //薪资补发
            String SGTXT = currentDate + "-" + hrmName + "-支付" + WRBTR + "工资";
            rs1.executeUpdate("update " + tableName + " set ywlx = ? where requestid = ?", new Object[]{"BU16", requestid});
            //借方数据
            ActionUtil.insertData(tableName + "_dt1", "", "**********", "", "", SGTXT, WRBTR, rybh, "09", "5", "", "", "", "", mainid, "DZ");
            //贷方数据，其中bankNum银行科目是资金平台返回的
            ActionUtil.insertData(tableName + "_dt1", "", bankNum, "106", "", SGTXT, WRBTR, "", "50", "", "", "", "", "", mainid, "DZ");
        }

        if ("3".equals(xzsx)) {    //生育金发放
            String SGTXT = currentDate + "-" + hrmName + "-支付" + WRBTR + "生育金";
            rs1.executeUpdate("update " + tableName + " set ywlx = ? where requestid = ?", new Object[]{"BU17", requestid});
            ActionUtil.insertData(tableName + "_dt1", "", "**********", "", "", SGTXT, WRBTR, rybh, "09", "5", "", "", "", "", mainid, "DZ");
            ActionUtil.insertData(tableName + "_dt1", "", bankNum, "103", "", SGTXT, WRBTR, "", "50", "", "", "", "", "", mainid, "DZ");
        }

        return "1";
    }
}

package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description 免费订单校验
 * @Version 1.0
 */
public class FreeOrderCheckAction implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        RecordSet depart = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String) tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        baseBean.writeLog("免费订单校验requestid=" + requestid);

        if (main.next()) {
            //mainId
            String mainId = main.getString("id");

            String sqlDtl = "select * from " + tableName + "_dt1 where mainid = " + mainId;
            item.executeQuery(sqlDtl);

            List<Integer> rowError = new ArrayList<>();

            int row = 1;
            while (item.next()) {

                String cbzx = item.getString("cbzx");
                String cbzxdybm = item.getString("cbzxdybm");

                if (StringUtils.isBlank(cbzx) || StringUtils.isBlank(cbzxdybm)) {
                    requestInfo.getRequestManager().setMessagecontent("明细的成本中心或部门为空 ===> id=" + item.getString("id"));
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }

                String departSql = "select * from formtable_main_306 where sfdj = 1 and sapbmbm = " + cbzx;

                depart.executeQuery(departSql);

                boolean flag = false;
                while (depart.next()) {
                    String oabm = depart.getString("oabm");
                    if (cbzxdybm.equals(oabm)) {
                        flag = true;
                        break;
                    }
                }
                if (!flag) {
                    rowError.add(row);
                }
                row++;
            }

            if (rowError.size() == 0) {
                return SUCCESS;
            }

            requestInfo.getRequestManager().setMessagecontent("第" + JSON.toJSONString(rowError) + "行成本中心对应部门有误，请重新核实。");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));

            return FAILURE_AND_CONTINUE;
        }

        requestInfo.getRequestManager().setMessagecontent("对应请求流程不存在requestid=" + requestid);
        requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));

        return FAILURE_AND_CONTINUE;
    }

}

package yitouniu.afternodeoperation;

import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

public class TestDemoAction extends BaseBean implements Action {
    @Override
    public String execute(RequestInfo request) {
        /*
         *  流程自定义接口代码示例
         *
         *  1:日志
         *	writeLog("in my online edited action");
         *
         *  2：异常
         *	boolean error=true;
         *	if(error) {
         *		request.getRequestManager().setMessageid("90001");
         *		request.getRequestManager().setMessagecontent("系统异常终止流程提交！");
         *	}
         *
         *  3：获取requestid
         *  String requestId = request.getRequestid();
         *
         *  4：获取表单名称
         *  String tablename = request.getRequestManager().getBillTableName();
         *
         *  5：查找表单内容
         *	RecordSet rs = new RecordSet();
         *  rs.execute("select * from "+tablename+" where requestid =  "+requestId);
         *	rs.next();
         *	String mainid = rs.getString("id");    					        //id：表单主键
         *	String wenben = rs.getString("wenben");					//wenben：表单设计的字段名称
         *
         *
         */


        return Action.SUCCESS;
    }
}

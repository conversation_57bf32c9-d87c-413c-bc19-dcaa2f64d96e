package yitouniu.afternodeoperation;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponseBody;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.RequestService;
import yitouniu.util.ActionUtil;
import yitouniu.util.DingTalkRobotUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/17 16:13
 * @Description 部门绩效评价归档action
 * @Version 1.0
 */
public class JXDepEvalueteAction implements Action {

    private final static String UF_GRJXXX = "uf_grjxxx";

    //测试
//    private final static String GRJXPJLC_MAIN = "formtable_main_617";
    //正式
    private final static String GRJXPJLC_MAIN = "formtable_main_694";

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        if (main.next()) {
            //id
            int id = main.getInt("id");
            //绩效类型
            String jxlx = main.getString("jxlx");
            //所属一级部门
            String szyjbm = main.getString("szyjbm");
            //一级部门负责人
            String szyjbmfzr = main.getString("szyjbmfzr");
            //绩效所属年份
            String nf = main.getString("nf");
            //绩效所属季度
            String jd = main.getString("jd");
            //绩效所属月份
            String yd = main.getString("yd");
            //部门绩效结果
            String bmjxjg = main.getString("bmjxjg");
            //部门绩效信息核对表id
            String bmjxxxhdbid = main.getString("bmjxxxhdbid");



            String updateTableName = "";
            int value = -1;
            if("0".equals(jxlx)){
                updateTableName = "uf_bmjdjx";
                value = Integer.parseInt(jd) + 1;
            } else {
                updateTableName = "uf_bmydjxxx";
                value = Integer.parseInt(yd) + 1;
            }

            List<List> items = queryByMainId(tableName, id);

            //更新主表数据
            String updateDetailSql = "update " + updateTableName + "  set bmjxjg = ? where id = ?";
            boolean flag = rs.executeUpdate(updateDetailSql, bmjxjg, bmjxxxhdbid);
            if(!flag){
                requestInfo.getRequestManager().setMessagecontent("更新主表数据错误");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

            //更新明细数据
            boolean itemFlag = update(items, updateTableName);
            if(!itemFlag){
                requestInfo.getRequestManager().setMessagecontent("更新明细数据错误");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }
            //去掉bp复核结果
            items.forEach(o->o.remove(1));
            //更新个人绩效信息表的数据
            boolean updateGrjxxxFlag = updateGrjxxxTable(items, updateTableName, UF_GRJXXX);
            if(!updateGrjxxxFlag){
                requestInfo.getRequestManager().setMessagecontent("更新个人绩效信息表个人绩效结果数据错误");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

            //更新个人绩效评价流程的数据
            boolean updateGrjxpjFlag = updateGrjxpjTable(items, updateTableName, GRJXPJLC_MAIN);
            if(!updateGrjxpjFlag){
                requestInfo.getRequestManager().setMessagecontent("更新个人绩效评价流程最终绩效结果数据错误");
                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return FAILURE_AND_CONTINUE;
            }

            List<Map<String, String>> pjlcList = queryPjlcByMainId(tableName, id);
            String token = DingTalkRobotUtil.getToken(DingTalkRobotUtil.OA_ROBOT_APPKEY, DingTalkRobotUtil.OA_ROBOT_APPSECRET);
            List<String> failList = new ArrayList<>();
            for(Map<String, String> o : pjlcList){
                RequestService rqs = new RequestService();
                String pjlc = o.get("pjlc");
                String mobile = o.get("mobile");
                String lastname = o.get("lastname");
                boolean returnstr = rqs.nextNodeBySubmit(null, Integer.parseInt(pjlc), 1, "系统自动提交");
                if(!returnstr){
                    requestInfo.getRequestManager().setMessagecontent("系统自动提交失败");
                    requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                    return FAILURE_AND_CONTINUE;
                }
                String requestName = "";
                if("0".equals(jxlx)){
                    requestName = "认养一头牛"+nf+"年Q"+value+"季度员工绩效评价审批流程-"+lastname;
                } else {
                    requestName = "认养一头牛"+nf+"年M"+value+"月员工绩效评价审批流程-"+lastname;
                }
                //钉钉推送个人聊天框绩效结果消息
//                String userId = DingTalkRobotUtil.userGetByMobile(mobile, token);
                StringBuilder sb = new StringBuilder();
//                List<String> userIds = new ArrayList<>();
//                userIds.add(userId);
                sb.append("亲爱的牛人伙伴，您")
                        .append(nf).append("年度");
                if ("0".equals(jxlx)) {
                    sb.append("Q")
                            .append(value).append("季度");
                } else {
                    sb.append("M")
                            .append(value).append("月度");
                }
                sb.append("的结果已出，请登录OA选择“").append(requestName).append("”流程查看最终结果。若对绩效评价结果有异议，须在2个工作日内填写《认养一头牛绩效考核申诉表》向邮箱：<EMAIL> 提出绩效申诉。");
                BatchSendOTOResponseBody response = pushDingTalkHealth(nf, value, sb, mobile, token, jxlx);
                if (response == null){
                    failList.add(lastname);
                } else {
                    rs.executeUpdate("update " + tableName + "_dt1 set sfyfsddxx = 0, fsddxxsjid = ? where pjlc = ? ", response.getProcessQueryKey(), pjlc);
                }


            }

            if(!failList.isEmpty()){
                baseBean.writeLog("部分人的消息发送失败，请确认以下人员的手机号或钉钉账号" + StringUtils.join(failList, ","));
//                requestInfo.getRequestManager().setMessagecontent("部分人的消息发送失败，请确认以下人员的手机号或钉钉账号" + StringUtils.join(failList, ","));
//                requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
                return SUCCESS;
            }

        }
        return SUCCESS;
    }


    /**
     * 通过id获取所有明细
     * @param tableName
     * @param mainId
     * @return
     */
    public List<List> queryByMainId(String tableName, int mainId){
        List<List> resultList = new ArrayList<>();
        String sql = "select * from " + tableName + "_dt1 where mainid = ?";
        rs.executeQuery(sql, mainId);
        while(rs.next()){
            List list = new ArrayList();
            //最终绩效结果
            list.add(rs.getString("zzjxjg"));
            //bp复核结果
            list.add(rs.getString("ylfhjg"));
            //明细id
            list.add(rs.getString("mxid"));
            resultList.add(list);
        }
        return resultList;
    }

    /**
     * 通过id获取id和评价流程
     * @param tableName
     * @param mainId
     * @return
     */
    public List<Map<String, String>> queryPjlcByMainId(String tableName, int mainId){
        List<Map<String, String>> list = new ArrayList<>();
        String sql = "select a.pjlc,a.xm,b.mobile,b.lastname as lastname from " + tableName + "_dt1 a, hrmresource b, workflow_requestbase c  where mainid = ? and a.xm = b.id and a.pjlc = c.requestid and c.currentnodetype <> '3' ";
        rs.executeQuery(sql, mainId);
        while(rs.next()){
            Map<String,String> map = new HashMap<>();
            String pjlc = rs.getString("pjlc");
            if(StrUtil.isBlank(pjlc)){
                continue;
            }
            map.put("pjlc", pjlc);
            map.put("xm", rs.getString("xm"));
            map.put("mobile", rs.getString("mobile"));
            map.put("lastname", rs.getString("lastname"));
            list.add(map);
        }
        return list;
    }

    public boolean update(List<List> list, String tableName) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("update "+tableName+"_dt1 set zzfhjg =?,ylfhjg =? where id = ?",list);
        return b;
    }


    /**
     * 更新个人绩效信息表的数据
     * @param list
     * @param updateTableName
     * @param grjxxxTableName
     * @return
     */
    public boolean updateGrjxxxTable(List<List> list, String updateTableName, String grjxxxTableName) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("UPDATE a set a.grjxjg = ? from " + grjxxxTableName + " a," + updateTableName + "_dt1 b WHERE a.zdlc = b.jxzdlc and b.id = ?",list);
        return b;
    }

    /**
     * 更新个人绩效评价流程表的数据
     *
     */
    public boolean updateGrjxpjTable(List<List> list, String updateTableName, String grjxpjTableName) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("UPDATE a set a.zzjxjg = ? from " + grjxpjTableName + " a," + updateTableName + "_dt1 b WHERE a.zdlc = b.jxzdlc and b.id = ?",list);
        return b;
    }


    private BatchSendOTOResponseBody pushDingTalkHealth(String year, int value, StringBuilder sb, String mobile, String token, String jxlx) {
        BatchSendOTOResponseBody responseBody = null;
        List<String> userIds = new ArrayList<>();
        String id = DingTalkRobotUtil.userGetByMobile(mobile, token);
        if(StringUtils.isNotBlank(id)){
            userIds.add(id);
        } else {
            baseBean.writeLog("pushDingTalkHealth 手机号为"+mobile+"的在钉钉找不到该用户，请确认");
            return null;
        }
        JSONObject msgParam = new JSONObject();
        String title = null;
        if("0".equals(jxlx)){
            title = year + "年度Q" + value + "绩效评价结果反馈";
        }else {
            title = year + "年度M" + value + "绩效评价结果反馈";
        }
        msgParam.put("title", title);
        msgParam.put("text", sb);

        responseBody = DingTalkRobotUtil.batchSend(DingTalkRobotUtil.OA_ROBOT_APPKEY, userIds, "sampleMarkdown", JSON.toJSONString(msgParam), token);

        if(StrUtil.isBlank(responseBody.getProcessQueryKey())){
            return null;
        }

        return responseBody;
    }







}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.file.Prop;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.*;

/**
 * 发票核销凭证生成
 */
public class InvoiceVerificationVoucherAction implements Action {

    private String VBUND = "";
    private String hrmName = "";
    private String PARTNER = ""; //
    private String kunnr = ""; //
    private String jzm = "35"; // 记账码
    private String pzlx = "KR"; // 凭证类型

    private String fybm = ""; // 部门
    private String sqrq = ""; // 申请日期
    private String NAME1 = ""; //// 收款单位名称
    private Calendar cale = null;
    private final String AUART_TYPE = "Z100,Z200,Z300";


    @Override
    public String execute(RequestInfo requestInfo) {
        try {


            String requestid = requestInfo.getRequestid(); // 流程id
            RecordSet rs1 = new RecordSet();
            Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
            String tableName = (String) tableNameByRequestId.get("tableName");


            new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
            String mainid = "";


            String sqr = ""; // 申请人
            String hxlx = ""; // 付款类型
            String xzskflx = ""; // 核销方类型

            String mxTable ="";
            String jshj =""; //价税合计
            String sql = "select * from " + tableName + " where requestid = ?";
            rs1.executeQuery(sql, requestid);
            if (rs1.next()) {
                mainid = rs1.getString("id");
                PARTNER = rs1.getString("hxdxgys"); // 供应商
                String hxdx1 = rs1.getString("hxdx1"); // 客户

                NAME1 = rs1.getString("fkdx");

                sqr = rs1.getString("sqr");
                sqrq = rs1.getString("sqrq");

                hxlx = rs1.getString("hxlx");
                xzskflx = rs1.getString("xzskflx"); //核销方类型
                fybm = rs1.getString("sqbm");

                if ("0".equals(hxlx)) {
                    mxTable = tableName+"_dt4";
                    jshj = "fpjshj";
                } else {
                    mxTable = tableName+"_dt2";
                    jshj = "fpje";

                }

            }
            if ("0".equals(hxlx)||"5".equals(hxlx)) {
                if ( "0".equals(xzskflx)) {
                    requestInfo.getRequestManager().setMessagecontent("核销类型为预付款核销(包括后台直扣)或者,核销方类型请选择供应商");
                    return FAILURE_AND_CONTINUE;
                }
               /* if ("2".equals(hxlx) && "1".equals(xzskflx)) {
                    requestInfo.getRequestManager().setMessagecontent("核销类型为应收款账扣核销,核销方类型请选择客户");
                    return FAILURE_AND_CONTINUE;
                }*/

                hrmName = ActionUtil.getHrmName(sqr);

                List<String> ywlx = new ArrayList<>();//费用科目

                List<String> pz = new ArrayList<>(); // 发票类型
                List<String> aufnr = new ArrayList<>(); // 内部订单号
                List<String> AUART = new ArrayList<>(); // 内部订单类型
                List<String> se = new ArrayList<>(); // 税额
                List<String> wsje = new ArrayList<>(); // 无税金额
                List<String> sl = new ArrayList<>(); // 税率
                List<String> fpjshj = new ArrayList<>(); // 发票总金额
                List<String> bz = new ArrayList<>(); // 备注

                List<String> fphmss = new ArrayList<>(); // 发票号码
                List<String> mxkh = new ArrayList<>(); // 发票号码

                // 删除明细表五
                sql = "DELETE FROM " + tableName + "_dt5  WHERE mainid = ?";
                new BaseBean().writeLog("--->明细表五删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");

                // 查询明细表4的数据
                // 发票数据
                sql = "select * from " + mxTable + "  where mainid = ?";
                rs1.executeQuery(sql, mainid);
                while (rs1.next()) {

                    ywlx.add(rs1.getString("ywlx"));
                    aufnr.add(rs1.getString("AUFNR"));
                    AUART.add(rs1.getString("AUART"));
                    pz.add(rs1.getString("fplx"));

                    se.add(rs1.getString("se"));
                    wsje.add(rs1.getString("wsje"));
                    fpjshj.add(rs1.getString(jshj));
                    sl.add(rs1.getString("taxRate1"));
                    bz.add(rs1.getString("bz"));
                    fphmss.add(rs1.getString("fphmss"));
                    mxkh.add(rs1.getString("mxkh"));
                }


                insertPZData(fpjshj, ywlx, tableName, se, mainid, sl, pz, wsje, aufnr,AUART,fphmss,bz,mxkh);



            }
        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }


    public void insertPZData(List<String> je, List<String> fykm, String tableName, List<String> se, String mainid, List<String> sl, List<String> pz, List<String> wsje, List<String> aufnrList, List<String> AUART,List<String> fphmss,List<String> bz,List<String> mxkh) {

        List<Map<String,String>> flseList = new ArrayList<>();
        String SGTXT = "";

        cale = Calendar.getInstance();
        int month = cale.get(Calendar.MONTH) + 1;
        String zje = "0.00";
        for (int i = 0; i < fykm.size(); i++) {
            String kmlx = fykm.get(i); // 费用科目
            if (!"".equals(kmlx) && kmlx != null && !"168".equals(kmlx) && !"167".equals(kmlx)) {


                zje = ActionUtil.bigDecimalAdd(zje, je.get(i));
                Map<String, String> km = ActionUtil.findKm(fykm.get(i));
                String codeName = km.get("codeName"); // SAP科目编码
                SGTXT = hrmName + "-"+sqrq + "-核销-"+NAME1  +"-"+bz.get(i)+"-"+ km.get("name");
                String KOSTL = ActionUtil.findBMData(fybm);// 成本中心
                //String KOSTL = data.get("KOSTL"); // 成本中心


                //借方数据
                String shuie = se.get(i); // 税额
                String sbje = je.get(i); // 实报金额
                String bhsje = wsje.get(i);// 不含税金额
                if (AUART.size() > 0) {  // 内部订单号
                    String aufnrI = AUART.get(i);
                    if (!"".equals(aufnrI) && aufnrI != null && AUART_TYPE.indexOf(aufnrI) != -1) {
                        KOSTL = "";
                    }
                }
                if (shuie != null && !"".equals(shuie) && Double.valueOf(shuie) != 0 && !"1".equals(pz.get(i))) {

                    Map<String, String> slMap = getSSL(sl.get(i));
                    String taxRate1 = slMap.get("taxRate1"); // 费用描述
                    String HKONT = slMap.get("HKONT"); // 科目
                   /* if ("2".equals(pz.get(i))) {
                        taxRate1 = "计算抵扣旅客运输服务";
                        HKONT = "2221010107";
                    }*/
                    String SGTXTSL = hrmName + "-"+sqrq + "-核销-"+NAME1+ taxRate1+"-"+fphmss.get(i);

                    //借方税额数据数据
                    ActionUtil.insertData(tableName + "_dt5", "", HKONT, "", "", SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, pzlx);
                    String flf = Prop.getPropValue("niu-flf", "subjectid"); // 科目类型
                    //if ("6601010200".equals(codeName)||"6601190000".equals(codeName)){ // 福利费和业务招待费
                    if (flf.indexOf(codeName)!=-1){ // 福利费和业务招待费
                        ActionUtil.insertData(tableName + "_dt5", KOSTL,  codeName, "", aufnrList.get(i), SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, pzlx);
                        Map<String,String> map = new HashMap<>();
                        map.put("shuie",shuie);
                        map.put("KOSTL",KOSTL);
                        map.put("HKONT","2221010601");
                        flseList.add(map);

                    }
                }
                if ("1".equals(pz.get(i))) {
                    bhsje = sbje;
                }
                String supsubject = km.get("supsubject"); // 所选科目上级
                String aufnrValue = aufnrList.get(i); // 内部订单


                String subjectid = Prop.getPropValue("niu-subject", "subjectid"); // 科目类型

                if(subjectid.indexOf(codeName)!=-1){  // 属于这两个费用的科目成本中心和的内部订单为空
                    //if("1263".equals(supsubject)||"1270".equals(supsubject)){  // 属于这两个费用的科目成本中心和的内部订单为空
                    KOSTL = "";
                    aufnrValue = "";
                }


                ActionUtil.insertData(tableName + "_dt5", KOSTL, codeName, "", aufnrValue, SGTXT, bhsje, "", "40", "", "", mxkh.get(i), "", "", mainid, pzlx);

            }
        }
        if (Double.valueOf(zje) > 0) {
            if (flseList!=null&&flseList.size()>0){
                for (int i = 0; i < flseList.size(); i++) {
                    Map<String, String> map = flseList.get(i);

                    ActionUtil.insertData(tableName + "_dt5", "", map.get("HKONT"), "", "", SGTXT, map.get("shuie"), "", "50", "", "", "", "", "", mainid, pzlx);
                }

            }
            ActionUtil.insertData(tableName + "_dt5", "", "", "", "", hrmName + "-"+sqrq + "-核销-"+NAME1 +"费用", zje + "", kunnr, jzm, "", "", "", PARTNER, VBUND, mainid, pzlx);
        }




    }

    public Map getSSL(String sl) {
        Map map = new HashMap();
        String taxRate1 = "";
        String HKONT = "";
        switch (sl) {
            case "0":
                taxRate1 = "13％税率发票 ";
                HKONT = "2221010101";

                break;
            case "1":

                taxRate1 = "9%税率发票";
                HKONT = "2221010102";
                break;
            case "2":
                taxRate1 = "6%税率发票";
                HKONT = "2221010103";

                break;
            case "3":
                taxRate1 = "5%税率";
                HKONT = "2221010104";
                break;
            case "4":
                taxRate1 = "3%征收率发票";
                HKONT = "2221010105";
                break;
            case "6":
                taxRate1 = "1%税率发票";
                HKONT = "2221010108";
                break;
        }
        map.put("taxRate1", taxRate1);
        map.put("HKONT", HKONT);
        return map;
    }

}

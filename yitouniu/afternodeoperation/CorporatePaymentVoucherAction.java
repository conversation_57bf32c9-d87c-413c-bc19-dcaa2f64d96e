package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.file.Prop;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.NumberUtil;

import java.util.*;

/**
 * 对公付款推送凭证
 */
public class CorporatePaymentVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();
    private String VBUND = "";
    private String hrmName = "";
    private String PARTNER = "";
    private String fybm = ""; // 部门
    private String NAME1 = ""; //// 收款单位名称
    private Calendar cale = null;
    private final String AUART_TYPE = "Z100,Z200,Z300";


    @Override
    public String execute(RequestInfo requestInfo) {
        try {


            String requestid = requestInfo.getRequestid(); // 流程id
            RecordSet rs1 = new RecordSet();
            Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
            String tableName = (String) tableNameByRequestId.get("tableName");
            String billid = (String) tableNameByRequestId.get("id"); // 类型id


            new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
            String mainid = "";

            //String VBUND = ""; // 贸易伙伴
            //String PARTNER = ""; // 收款单位编码

            String lcbh = ""; // 流程编号
            String sqr = ""; // 申请人
            String bankNum = ""; // 银行科目
            String fklx = ""; // 付款类型
            String fkfs = ""; // 付款方式


            String sql = "select * from " + tableName + " where requestid = ?";
            rs1.executeQuery(sql, requestid);
            if (rs1.next()) {
                mainid = rs1.getString("id");
                PARTNER = rs1.getString("PARTNER");
                VBUND = rs1.getString("VBUND");
                NAME1 = rs1.getString("NAME1");
                lcbh = rs1.getString("lcbh");
                sqr = rs1.getString("sqr");
                bankNum = rs1.getString("BANKNUM");
                fklx = rs1.getString("fklx");
                fkfs = rs1.getString("fkfs");
                fybm = rs1.getString("fybm");


            }
            hrmName = ActionUtil.getHrmName(sqr);
            String UMSKZ = ""; // 特别总账标识
            String gys = ""; // gys
            String jzm = "50"; //记账码
            if ("1".equals(fkfs)){ // 如果为承兑  借应付  贷应付票据
                bankNum ="";
                UMSKZ = "7";
                gys=PARTNER;
                jzm = "39";
            }

            // 付款类型
            String fklxValue = ActionUtil.getSelectName(billid, "fklx", fklx);
            String SGTXT = hrmName + "申请--付" + NAME1 + fklxValue + "款";

            // 删除明细表三
            sql = "DELETE FROM " + tableName + "_dt4  WHERE mainid = ?";
            new BaseBean().writeLog("--->明细表四删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");
            // 删除明细表三
            sql = "DELETE FROM " + tableName + "_dt5  WHERE mainid = ?";
            new BaseBean().writeLog("--->明细表5删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");


            // 查询明细表3的数据

            List<String> je = new ArrayList<>();//金额

            //List<String> HKONT = new ArrayList<>(); // 费用科目


            List<String> RSTGR = new ArrayList<>(); // 原因代码
            List<String> cgddh = new ArrayList<>(); // 订单号


            // 对公付款数据
            sql = "select * from " + tableName + "_dt3 where mainid = ?";
            rs1.executeQuery(sql, mainid);
            while (rs1.next()) {

                je.add(rs1.getString("WRBTR"));

               // HKONT.add(rs1.getString("HKONT"));


                RSTGR.add(rs1.getString("RSTGR"));
                cgddh.add(rs1.getString("ddh"));
            }


            // 查询明细表1的数据

            List<String> hjkm = new ArrayList<>();//费用科目

            List<String> pz = new ArrayList<>(); // 发票类型
            List<String> bz = new ArrayList<>(); // 备注

            List<String> se = new ArrayList<>(); // 税额
            List<String> wsje = new ArrayList<>(); // 无税金额
            List<String> sl = new ArrayList<>(); // 税率
            List<String> fkje = new ArrayList<>(); // 发票总金额
            List<String> aufnr = new ArrayList<>(); // 内部订单号
            List<String> AUART = new ArrayList<>(); // 内部订单类型
            List<String> fphmss = new ArrayList<>(); // 发票号码
            List<String> mxkh = new ArrayList<>(); // 明细客户
            // 发票数据
            sql = "select * from " + tableName + "_dt1 where mainid = ?";
            rs1.executeQuery(sql, mainid);
            while (rs1.next()) {
                aufnr.add(rs1.getString("AUFNR"));
                AUART.add(rs1.getString("AUART"));
                hjkm.add(rs1.getString("hjkm"));

                pz.add(rs1.getString("pz"));

                se.add(rs1.getString("se"));
                wsje.add(rs1.getString("wsje"));
                fkje.add(rs1.getString("fkje"));
                sl.add(rs1.getString("taxRate1"));
                fphmss.add(rs1.getString("fphmss"));
                mxkh.add(rs1.getString("mxkh"));
                bz.add(rs1.getString("bz"));
            }


            String YF_totalMoney = "0.0"; // 预付总计金额

            // 借方
            for (int i = 0; i < je.size(); i++) {
                String money = je.get(i); // 金额



                YF_totalMoney = ActionUtil.bigDecimalAdd(YF_totalMoney, money); // 总金额;
                ActionUtil.insertData(tableName + "_dt4", "", "", "", "", SGTXT, money, "", "25", "", cgddh.get(i), "", PARTNER, VBUND, mainid, "KZ");
            }

            // 贷方
            ActionUtil.insertData(tableName + "_dt4", "", bankNum, RSTGR.get(0), "", SGTXT, YF_totalMoney, "", jzm, UMSKZ, "", "", gys, "", mainid, "KZ");


            // 费用贷方和借方数据
            List list = new ArrayList();
            if (hjkm.size() > 0 && hjkm != null) {
                list = insertPZData(fkje, hjkm, sqr, tableName, se, mainid, sl, pz, wsje, bz, aufnr, AUART,fphmss,mxkh);
            }
            if (list.size() == 0 || list == null) {

                recordSet.executeUpdate("update " + tableName + " set sftspz = ?  where requestid = ?", 1, requestid);
            }else{
                recordSet.executeUpdate("update " + tableName + " set sftspz = ?  where requestid = ?", 0, requestid);

            }

        } catch (Exception e) {
            requestInfo.getRequestManager().setMessagecontent(e.getMessage());
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }


    public List insertPZData(List<String> je, List<String> fykm, String sqr, String tableName, List<String> se, String mainid, List<String> sl, List<String> pz, List<String> wsje, List<String> bz, List<String> aufnr, List<String> AUART,List<String> fphmss,List<String> mxkh) {

        List<Map<String,String>> flseList = new ArrayList<>();
        String SGTXT = "";
        List list = new ArrayList();
        cale = Calendar.getInstance();
        int month = cale.get(Calendar.MONTH) + 1;
        String zje = "0.00";
        for (int i = 0; i < fykm.size(); i++) {
            String kmlx = fykm.get(i); // 费用科目
            if (!"".equals(kmlx) && kmlx != null && !"168".equals(kmlx) && !"167".equals(kmlx)) {


                zje = ActionUtil.bigDecimalAdd(zje, je.get(i));
                Map<String, String> km = ActionUtil.findKm(fykm.get(i));
                String codeName = km.get("codeName"); // SAP科目编码
                SGTXT = hrmName + "申请--" + month + "月付"+NAME1 +"-"+bz.get(i)+"-"+ km.get("name");
                String KOSTL = ActionUtil.findBMData(fybm);// 成本中心
                //String KOSTL = data.get("KOSTL"); // 成本中心


                //借方数据
                String shuie = se.get(i); // 税额
                String sbje = je.get(i); // 实报金额
                String bhsje = wsje.get(i);// 不含税金额
                if (AUART.size() > 0) {  // 内部订单号
                    String aufnrI = AUART.get(i);
                    if (!"".equals(aufnrI) && aufnrI != null && AUART_TYPE.indexOf(aufnrI) != -1) {
                        KOSTL = "";
                    }
                }
                if (shuie != null && !"".equals(shuie) && Double.valueOf(shuie) != 0 && !"1".equals(pz.get(i))) {

                    Map<String, String> slMap = getSL(sl.get(i));
                    String taxRate1 = slMap.get("taxRate1"); // 费用描述
                    String HKONT = slMap.get("HKONT"); // 科目
                    if ("2".equals(pz.get(i))) {
                        taxRate1 = "计算抵扣旅客运输服务";
                        HKONT = "2221010107";
                    }
                    String SGTXTSL = "对公付款  : " + month + "月" + taxRate1+"-"+fphmss.get(i);

                    //借方税额数据数据
                    ActionUtil.insertData(tableName + "_dt5", "", HKONT, "", "", SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, "");
                    String flf = Prop.getPropValue("niu-flf", "subjectid"); // 科目类型
                    //if ("6601010200".equals(codeName)||"6601190000".equals(codeName)){ // 福利费和业务招待费
                    if (flf.indexOf(codeName)!=-1){ // 福利费和业务招待费
                        ActionUtil.insertData(tableName + "_dt5", KOSTL,  codeName, "", aufnr.get(i), SGTXTSL, shuie, "", "40", "", "", "", "", "", mainid, "");
                        Map<String,String> map = new HashMap<>();
                        map.put("shuie",shuie);
                        map.put("KOSTL",KOSTL);
                        map.put("HKONT","2221010601");
                        flseList.add(map);

                    }
                }
                if ("1".equals(pz.get(i))) {
                    bhsje = sbje;
                }
                String supsubject = km.get("supsubject"); // 所选科目上级
                String aufnrValue = aufnr.get(i); // 内部订单


                String subjectid = Prop.getPropValue("niu-subject", "subjectid"); // 科目类型

                if(subjectid.indexOf(codeName)!=-1){  // 属于这两个费用的科目成本中心和的内部订单为空
                //if("1263".equals(supsubject)||"1270".equals(supsubject)){  // 属于这两个费用的科目成本中心和的内部订单为空
                    KOSTL = "";
                    aufnrValue = "";
                }


                ActionUtil.insertData(tableName + "_dt5", KOSTL, codeName, "", aufnrValue, SGTXT, bhsje, "", "40", "", "", mxkh.get(i), "", "", mainid, "");
                list.add(1);
            }
        }
        if (Double.valueOf(zje) > 0) {
            if (flseList!=null&&flseList.size()>0){
                for (int i = 0; i < flseList.size(); i++) {
                    Map<String, String> map = flseList.get(i);

                    ActionUtil.insertData(tableName + "_dt5", "", map.get("HKONT"), "", "", SGTXT, map.get("shuie"), "", "50", "", "", "", "", "", mainid, "");
                }

            }
            ActionUtil.insertData(tableName + "_dt5", "", "", "", "", hrmName + "申请--" + month + "月" + NAME1 + "费用", zje + "", "", "35", "", "", "", PARTNER, VBUND, mainid, "");
        }
        //贷方
        return list;
    }

    public Map getSL(String sl) {
        Map map = new HashMap();
        String taxRate1 = "";
        String HKONT = "";
        switch (sl) {
            case "0":
                taxRate1 = "13％税率发票";
                HKONT = "2221010101";

                break;
            case "1":

                taxRate1 = "9%税率发票";
                HKONT = "2221010102";
                break;
            case "2":
                taxRate1 = "6%税率发票";
                HKONT = "2221010103";

                break;
            case "3":
                taxRate1 = "5%税率";
                HKONT = "2221010104";
                break;
            case "4":
                taxRate1 = "3%征收率发票";
                HKONT = "2221010105";
                break;
            case "6":
                taxRate1 = "1%税率发票";
                HKONT = "2221010108";
                break;
        }
        map.put("taxRate1", taxRate1);
        map.put("HKONT", HKONT);
        return map;
    }

}

package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/21 9:45
 * @Description 季度绩效okr修改
 * @Version 1.0
 */
public class JDJXModifyAction  implements Action {

    public static String uf_grjxxx = "uf_grjxxx";

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        RecordSet dt2Item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);
        String mainId = "";
        String xzjdokrqqidId = "";
        if (main.next()) {
            //mainId
            mainId = main.getString("id");
            //选择的季度okr的流程id
            xzjdokrqqidId = main.getString("xzjdokrqqid");
        }

        rs.executeQuery("select * from " + uf_grjxxx + " where zdlc = ?", xzjdokrqqidId);
        String grjxxxMainId = "";
        if (rs.next()) {
            //grjxxxMainId
            grjxxxMainId = rs.getString("id");
        }
        baseBean.writeLog("JDJXModifyAction:删除原明细表的mainid = " + grjxxxMainId);

        dt2Item.executeQuery("select * from " + tableName + "_dt2 where mainid = ?", mainId);
        //修改后需要插入的明细
        List<Map<String,String>> xgItemList = new ArrayList<>();
        while(dt2Item.next()){
            Map<String, String> xgMap = new HashMap<>();
            //修改目标序列
            xgMap.put("xgmbxl", dt2Item.getString("xgmbxl1"));
            //修改权重
            xgMap.put("xgqz", dt2Item.getString("xgqz"));
            //修改目标(Objective)
            xgMap.put("xgmbobjective", dt2Item.getString("xgmbobjective"));
            //修改关键结果（KeyResults)
            xgMap.put("xggjjgkeyresults", dt2Item.getString("xggjjgkeyresults"));
            //修改使命必达目标（3.5分）
            xgMap.put("xgsmbdmb35f", dt2Item.getString("xgsmbdmb35f"));
            xgItemList.add(xgMap);
        }
        baseBean.writeLog("JDJXModifyAction:修改后需要插入的明细 = " + xgItemList);

        //直接删除原明细表数据，然后新增数据
        String deleteSql = "delete from " + uf_grjxxx + "_dt1 where mainid = ? ";
        boolean deleteFlag = rs.executeUpdate(deleteSql, grjxxxMainId);
        baseBean.writeLog("JDJXModifyAction:删除原明细表数据SQL = " + deleteSql);
        baseBean.writeLog("JDJXModifyAction:删除原明细表数据标识 = " + deleteFlag);
        if(!deleteFlag){
            requestInfo.getRequestManager().setMessagecontent("删除旧数据时异常失败");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        String flag = "";
        for(Map<String, String> o : xgItemList){
            String insertSql = "insert into " + uf_grjxxx + "_dt1 (mainid, mbxh, qzbfb, mbobjective, gjjgkeyresults, smbdmb) values(?,?,?,?,?,?)  " ;
            //插入新数据
            flag = flag + (rs.executeUpdate(insertSql, grjxxxMainId, o.get("xgmbxl"), o.get("xgqz"), o.get("xgmbobjective"), o.get("xggjjgkeyresults"), o.get("xgsmbdmb35f")));
        }
        baseBean.writeLog("JDJXModifyAction:新增数据标识 = " + flag);
        if(flag.contains("false")){
            requestInfo.getRequestManager().setMessagecontent("插入新数据时失败");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }
        return SUCCESS;
    }


}

package yitouniu.afternodeoperation;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/5/23 15:27
 * @Description 个人绩效评价每一步状态action
 * @Version 1.0
 */
public class JXPersonalEvaluateStatusPreAction implements Action{

    RecordSet rs = new RecordSet();
    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        int nodeId = requestInfo.getRequestManager().getNodeid();
        int nextNodeId = requestInfo.getRequestManager().getNextNodeid();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";

        //制定流程id
        String zdlc = "";
        //绩效类型
        String jxlx = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("zdlc".equals(name)) {
                zdlc = Util.null2String(properties[i].getValue());
            } else if("jxlx".equals(name)){
                jxlx = Util.null2String(properties[i].getValue());
            }
        }
        String nodeName = "";
        rs.executeQuery("select nodename from workflow_nodebase where id = ?", nextNodeId);
        if (rs.next()){
            nodeName = rs.getString("nodename");
        }

        String jxTableName = "";
        if("0".equals(jxlx)){
            jxTableName = "uf_bmjdjx";
        } else if("1".equals(jxlx)){
            jxTableName = "uf_bmydjxxx";
        }
        if(StrUtil.isBlank(nodeName) || StrUtil.isBlank(jxTableName)){
            requestInfo.getRequestManager().setMessagecontent("获取节点名称或获取绩效核对表名失败");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        if(!rs.executeUpdate("update " + jxTableName + "_dt1 set pjlchj = ? where jxzdlc = ?", nodeName, zdlc)){
            requestInfo.getRequestManager().setMessagecontent("绩效核对表更新评价流程环节失败");
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }







}

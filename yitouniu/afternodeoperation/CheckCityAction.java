package yitouniu.afternodeoperation;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.Map;

public class CheckCityAction implements Action {
    @Override
    public String execute(RequestInfo request) {

        String requestid = request.getRequestid(); // 流程id
        RecordSet main = new RecordSet();
        RecordSet item = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);
        new BaseBean().writeLog("tableNamessssssssssssssssssssss"+tableName);
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        if (main.next()) {
            String token = getToken();
            String mainId = main.getString("id");
            String itemSql = "select * from " + tableName + "_dt1 where mainid = ?";
            item.executeQuery(itemSql, mainId);
            String userId = main.getString("sqr");
            new BaseBean().writeLog("userIdssssssssssssssssssssss"+userId);

            while (item.next()) {
                try {//oa用户id
//                    int i = 0;


                    //开始时间
                    //单程/往返
                    //交通工具
                    String jtgj = item.getString("jtgj");
                    //出发城市
                    String cfcs = item.getString("cfcs");
                    //目的城市
                    String mdcs = item.getString("mdcs");
                    //出差时长
                    RecordSet userInfo = getUserInfo(userId);
                    //手机号
                    String mobile="";
                    String lastname="";
                    if (userInfo.next()){
                         mobile = userInfo.getString("mobile");
                         lastname =  userInfo.getString("lastname");
                    }

                    new BaseBean().writeLog("mobileaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"+mobile);
                    //姓名
                    String alUserId = getUserId(mobile,token);
                    Long.parseLong(jtgj);
                            if("0".equals(jtgj)){
                                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/flight/city/suggest");
                                OapiAlitripBtripFlightCitySuggestRequest req1 = new OapiAlitripBtripFlightCitySuggestRequest();
                                OapiAlitripBtripFlightCitySuggestRequest.SuggestRq obj1 = new OapiAlitripBtripFlightCitySuggestRequest.SuggestRq();
                                obj1.setKeyword(cfcs);
                                obj1.setUserid(alUserId);
                                obj1.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
                                req1.setRq(obj1);
                                OapiAlitripBtripFlightCitySuggestRequest req2 = new OapiAlitripBtripFlightCitySuggestRequest();
                                OapiAlitripBtripFlightCitySuggestRequest.SuggestRq obj2 = new OapiAlitripBtripFlightCitySuggestRequest.SuggestRq();
                                obj2.setKeyword(mdcs);
                                obj2.setUserid(alUserId);
                                obj2.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
                                req2.setRq(obj2);
                                OapiAlitripBtripFlightCitySuggestResponse rsp1 = client.execute(req1, token);
                                OapiAlitripBtripFlightCitySuggestResponse rsp2 = client.execute(req2, token);
                                new BaseBean().writeLog("CheckCityAction:rsp1="+rsp1);
                                new BaseBean().writeLog("CheckCityAction:rsp2="+rsp2);
                                if (checkCities(rsp1.getBody())){
                                    request.getRequestManager().setMessageid("90001");
                                    request.getRequestManager().setMessagecontent(cfcs+"无机场！");
                                    return FAILURE_AND_CONTINUE;
                                } if(checkCities(rsp2.getBody())){
                                    request.getRequestManager().setMessageid("90001");
                                    request.getRequestManager().setMessagecontent(mdcs+"无机场！");
                                    return FAILURE_AND_CONTINUE;
                                }

                            }else if("1".equals(jtgj)){
                                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/train/city/suggest");
                                OapiAlitripBtripTrainCitySuggestRequest req1 = new OapiAlitripBtripTrainCitySuggestRequest();
                                OapiAlitripBtripTrainCitySuggestRequest.SuggestRq  obj1 = new OapiAlitripBtripTrainCitySuggestRequest.SuggestRq();
                                obj1.setKeyword(cfcs);
                                obj1.setUserid(alUserId);
                                obj1.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
                                req1.setRq(obj1);
                                OapiAlitripBtripTrainCitySuggestRequest req2 = new OapiAlitripBtripTrainCitySuggestRequest();
                                OapiAlitripBtripTrainCitySuggestRequest.SuggestRq  obj2 = new OapiAlitripBtripTrainCitySuggestRequest.SuggestRq();
                                obj2.setKeyword(mdcs);
                                obj2.setUserid(alUserId);
                                obj2.setCorpid("dinga9e6db0a7491905135c2f4657eb6378f");
                                req2.setRq(obj2);
                                OapiAlitripBtripTrainCitySuggestResponse rsp1 = client.execute(req1, token);
                                OapiAlitripBtripTrainCitySuggestResponse rsp2 = client.execute(req2, token);
                                new BaseBean().writeLog("CheckCityAction:rsp1="+rsp1);
                                new BaseBean().writeLog("CheckCityAction:rsp2="+rsp2);
                                if (checkCities(rsp1.getBody())){
                                    request.getRequestManager().setMessageid("90001");
                                    request.getRequestManager().setMessagecontent(cfcs+"无火车站！");
                                    return FAILURE_AND_CONTINUE;
                                } if(checkCities(rsp2.getBody())){
                                    request.getRequestManager().setMessageid("90001");
                                    request.getRequestManager().setMessagecontent(mdcs+"无火车站！");
                                    return FAILURE_AND_CONTINUE;
                                }
                            }

                } catch (ApiException e) {
                    e.printStackTrace();
                    return FAILURE_AND_CONTINUE;
                }
            }


        }


        return SUCCESS;
    }
    public static String getToken() {
        String res = "";
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey("ding370bf6nydf1ztuo7");
        request.setAppsecret("ar71FOl64xb7UTxvylPdw0VkBRXD5qdgD7gEg6CWiAoe0OW1MeMnYsRu5E-SsRmd");
        request.setHttpMethod("GET");
        try {
            OapiGettokenResponse response = client.execute(request);
            res = response.getAccessToken();
        } catch (ApiException e) {
            e.printStackTrace();
        } finally {
            return res;
        }


    }

    private static boolean checkCities(String a){
        com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(a);
        com.alibaba.fastjson.JSONObject json1 = json.getJSONObject("result");
        com.alibaba.fastjson.JSONArray arr = json1.getJSONArray("cities");
        return arr.isEmpty();
    }
    public  static String getUserId(String mobile, String token) {
        String res = "";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/user/get_by_mobile");
        OapiUserGetByMobileRequest request = new OapiUserGetByMobileRequest();
        request.setMobile(mobile);
        try {
            OapiUserGetByMobileResponse execute = client.execute(request, token);
            res = execute.getUserid();
        } catch (ApiException e) {
            e.printStackTrace();
        } finally {
            return res;
        }
    }

    public static  RecordSet getUserInfo(String userid) {
        new BaseBean().writeLog("useriddsadsadsadaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"+userid);
        RecordSet recordSet = new RecordSet();
        String sql = "select *  from  hrmresource where id = ?";
        recordSet.executeQuery(sql, userid);
        new BaseBean().writeLog("recordSetdfgdhgfhgfhfdhgf"+recordSet);

        return recordSet;
    }

    public static  String getInvoiceId(String userId,String corpId,String access_token){
        String id = "";
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/alitrip/btrip/invoice/search");
        OapiAlitripBtripInvoiceSearchRequest req = new OapiAlitripBtripInvoiceSearchRequest();
        OapiAlitripBtripInvoiceSearchRequest.OpenInvoiceRq obj1 = new OapiAlitripBtripInvoiceSearchRequest.OpenInvoiceRq();
        obj1.setUserid(userId);
        obj1.setCorpid(corpId);
        req.setRq(obj1);
        OapiAlitripBtripInvoiceSearchResponse rsp = null;
        try {
            rsp = client.execute(req, access_token);
            JSONObject json = JSONObject.fromObject(rsp.getBody());
            JSONArray arr = json.getJSONArray ("invoice");
            JSONObject rs = JSONObject.fromObject(arr.get(0));
            id=String.valueOf(rs.get("id"));

        } catch (ApiException e) {
            e.printStackTrace();
        }finally {
            return id;
        }
    }
}

package yitouniu.afternodeoperation;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 员工借款推送凭证
 */
public class EmployeeLoanVoucherAction implements Action {

    private RecordSet recordSet = new RecordSet();

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid = requestInfo.getRequestid(); // 流程id
        RecordSet rs1 = new RecordSet();

        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");
        String billid = (String) tableNameByRequestId.get("id"); // 类型id


        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");

        String bz = ""; // 备注
        String lcbh = ""; // 流程编号
        String sqr = ""; // 申请人
        String WRBTR = ""; // 金额
        String mainid = ""; // id
        String bankNum = ""; //
        String jklx = ""; //借款类型


        String sql = "select * from " + tableName + " where requestid = ?";
        rs1.executeQuery(sql, requestid);
        if (rs1.next()) {
            mainid = rs1.getString("id");
            bz = rs1.getString("bz");
            lcbh = rs1.getString("lcbh");
            sqr = rs1.getString("UNAME");
            WRBTR = rs1.getString("WRBTR");
            bankNum = rs1.getString("BANKNUM");
            jklx = rs1.getString("jklx");

        }
        // 员工名称
        String hrmName = ActionUtil.getHrmName(sqr);

        // 付款类型
        String fklxValue = ActionUtil.getSelectName(billid, "jklx", jklx);
        String SGTXT = hrmName+fklxValue+"-"+bz;

        // 删除明细表1
        sql = "DELETE FROM " + tableName + "_dt1  WHERE mainid = ?";
        new BaseBean().writeLog("--->明细表一删除：" + rs1.executeUpdate(sql, mainid) + "<br/>");

        Map<String,String> data = ActionUtil.findData(sqr); // 根据申请人获取成本中心
        String  rybh = data.get("rybh"); // 人员编号
        String  KOSTL = data.get("KOSTL"); // 成本中心
        String  KUNNR = data.get("KUNNR"); // 客户编码
        // 借方数据
        ActionUtil.insertData(tableName + "_dt1", "", "", "", "", SGTXT, WRBTR, rybh, "01", "", "", "", "", "", mainid, "DZ");


        //贷方数据
        ActionUtil.insertData(tableName + "_dt1", "", bankNum, "108", "", SGTXT, WRBTR , "", "50", "", "", "", "", "", mainid, "DZ");


        return SUCCESS;
    }


}

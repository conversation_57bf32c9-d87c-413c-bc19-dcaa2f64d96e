package yitouniu.afternodeoperation;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.esb.ReturnMsgToSAP;
import yitouniu.util.ActionUtil;

import java.util.Map;

// 资产购置流程V2.0
public class CreatePurchaseAction extends ReturnMsgToSAP   {

}

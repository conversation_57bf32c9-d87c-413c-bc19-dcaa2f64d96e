package yitouniu.afternodeoperation;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 看板日期限制
 */
public class KBDateLimitAction implements Action {

    private RecordSet recordSet = new RecordSet();

    BaseBean baseBean = new BaseBean();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        try {

            new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
            String tableName = (String) tableNameByRequestId.get("tableName");
            String selectSql = "select * from  " + tableName + " where requestid = ?";
            recordSet.executeQuery(selectSql, requestid);
            String ksrq = "";
            String jsrq = "";
            String cdmc = "";
            String kssjd = "";
            String jssjd = "";

            if (recordSet.next()) {
                ksrq = recordSet.getString("ksrq"); // 开始日期
                jsrq = recordSet.getString("jsrq"); // 结束日期
                kssjd = recordSet.getString("kssjd"); // 开始时间
                jssjd = recordSet.getString("jssjd"); // 结束时间
                cdmc = recordSet.getString("cdmc"); // 场地
            }
            String kssj = ""; // 开始时间
            String jssj = ""; // 开始时间
            if ("0".equals(kssjd)){ // 开始时间上午
                kssj = "09:00";
            }
            if ("1".equals(kssjd)){ // 开始时间下午
                kssj = "14:00";
            }
            if ("0".equals(jssjd)){ // 结束时间上午
                jssj = "12:00";
            }
            if ("1".equals(jssjd)){ // 结束时间下午
                jssj = "18:00";
            }
            boolean isExist = false;
            boolean isEveryExist = false;

            List<String> lcidList = new ArrayList<>();
            long kssjParse = simpleDateFormat.parse(ksrq+" "+kssj).getTime(); // 前端选择的开始日期时间
            long jssjParse = simpleDateFormat.parse(jsrq+" "+jssj).getTime();// 前端选择的结束日期时间

            baseBean.writeLog("action:ksrq=" + ksrq);
            baseBean.writeLog("action:jsrq=" + jsrq);

            baseBean.writeLog("action:kssjParse=" + kssjParse);
            baseBean.writeLog("action:jssjParse=" + jssjParse);

//            if("2".equals(cdmc)){
//                return SUCCESS;
//            } else {
//                String sql = "select * from  uf_CDYYJL where (ksrq >= ? or jsrq >= ?) and cdmc = ? and lcid != ?";
//                recordSet.executeQuery(sql, ksrq, ksrq, cdmc,requestid);
//                while (recordSet.next()) {
//                    String ksrqValue = recordSet.getString("ksrq");
//                    String jsrqValue = recordSet.getString("jsrq");
//                    String kssjValue = recordSet.getString("kssj");
//                    String jssjValue = recordSet.getString("jssj");
//                    long kssjtime = simpleDateFormat.parse(ksrqValue+" "+kssjValue).getTime();
//                    long jssjtime = simpleDateFormat.parse(jsrqValue+" "+jssjValue).getTime();
//                    baseBean.writeLog("action:kssjtime=" + kssjtime);
//                    baseBean.writeLog("action:jssjtime=" + jssjtime);
//                    if ((kssjParse>=kssjtime&&kssjParse<=jssjtime) || (jssjParse>=kssjtime&&jssjParse<=jssjtime) || (kssjParse<=kssjtime && jssjParse>=jssjtime)){ // 开始时间在数据库中开始时间和结束时间之间
//                        isExist = true;
//                        isEveryExist = true;
//                    } else {
//                        isEveryExist = false;
//                    }
//                    if(isEveryExist){
//                        lcidList.add(recordSet.getString("lcid"));
//                    }
//                }
//                baseBean.writeLog("action:lcidList=" + lcidList);
//
//                if(isExist) {
//                    //查询这个流程设计部这个节点是否已经审批
//                    //查询这个流程是否已经归档
//                    for(String id : lcidList){
//                        //1是否，0是是
//                        String sql3 = "select sfty from uf_CDYYJL where lcid = ?";
//                        recordSet.executeQuery(sql3, id);
//                        String sfty = "";
//                        if (recordSet.next()) {
//                            sfty = recordSet.getString("sfty");
//                        }
//                        //如果已经归档，则不让审批或提交
//                        if("0".equals(sfty)){
//                            requestInfo.getRequestManager().setMessageid(requestid);
//                            requestInfo.getRequestManager().setMessagecontent("时间冲突的另一个流程已经被审批归档，请退回或重新选择时间");
//                            return FAILURE_AND_CONTINUE;
//                        }
//                    }
//                }
//            }
            recordSet.executeUpdate("update "+tableName+" set kssj = ?,jssj = ? where requestid = ?",kssj,jssj,requestid);

            return SUCCESS;
        } catch (Exception e) {
            new BaseBean().writeLog(requestid+"异常信息: " + e);
        }
        return SUCCESS;
    }

}

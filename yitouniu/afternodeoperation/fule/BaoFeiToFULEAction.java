package yitouniu.afternodeoperation.fule;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.OMSUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/6 15:47
 * @Description 富勒 报废
 * @Version 1.0
 */
public class BaoFeiToFULEAction implements Action {
    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    public static final String FULE_URL = Util.null2String(new BaseBean().getPropValue("fule_configuration","fuleUrl"));
    public static final String FULE_BAOFEI_API = Util.null2String(new BaseBean().getPropValue("fule_configuration","scrapApi"));
    public static final String FULE_PANDIAN_API = Util.null2String(new BaseBean().getPropValue("fule_configuration","pandianApi"));

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);

        JSONObject request = new JSONObject();

        if(main.next()){
            int mainId = main.getInt("id");
            String lcbh = main.getString("lcbh");
            String sqr = main.getString("sqr");
            String werehousecode = main.getString("ckdm");
            String createDate = "";
            String createTime = "";
            String lastname = "";
            String sql3 = "select * from workflow_requestbase  where requestmark = ?";
            rs.executeQuery(sql3,lcbh);
            if(rs.next()){
                createDate = rs.getString("createdate");
                createTime = rs.getString("createtime");
            }
            String sqrq = createDate + " " +createTime;
            RecordSet rs4 = new RecordSet();
            String sql4 = "select * from HrmResource where id = ?";
            rs4.executeQuery(sql4,sqr);
            if(rs4.next()){
                lastname = rs4.getString("lastname");
            }
            baseBean.writeLog("BaoFeiToFULEAction11111111");

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            request.put("lcbh",lcbh);
            request.put("billDate",sdf.format(new Date()));
            request.put("sqr",lastname);
            request.put("sqrq",sqrq);

            JSONArray itemArr = new JSONArray();
            baseBean.writeLog("BaoFeiToFULEAction2222222222");
            String itemSql = "select * from " + tableName + "_dt1 where mainid = ?";
            rs.executeQuery(itemSql, mainId);
            while(rs.next()){
                JSONObject itemJson = new JSONObject();
                itemJson.put("bfxz", "0");
                itemJson.put("werehousecode",rs.getString("werehousecode"));
                itemJson.put("wlmc1",rs.getString("wlmc1"));
                String scrq = rs.getString("scrq");
                if(StringUtils.isNotBlank(scrq)){
                    itemJson.put("scrq",scrq.replaceAll("-",""));
                } else {
                    itemJson.put("scrq","00000000");
                }

                int planqty = Double.valueOf(rs.getString("planqty")).intValue();
                if(planqty > 0){
                    planqty = -planqty;
                }
                itemJson.put("planqty",planqty);
                itemArr.add(itemJson);
            }
            request.put("scrapOrderItemModels",itemArr);
        }
//        Map<String, String> header = getHeader(token);
        baseBean.writeLog("BaoFeiToFULEAction请求request=" + request);
        String result = HttpRequest
                .post(FULE_URL + FULE_BAOFEI_API)
                .body(request.toJSONString()).execute().body();
        baseBean.writeLog("BaoFeiToFULEAction请求返回="+result);
        JSONObject resultJson = JSONObject.parseObject(result);

        if(resultJson.getInteger("code") != 0){
            requestInfo.getRequestManager().setMessagecontent("BaoFeiToFULEAction失败，失败原因：" + result);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }



        return SUCCESS;
    }




}

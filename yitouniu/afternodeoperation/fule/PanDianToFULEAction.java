package yitouniu.afternodeoperation.fule;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.util.ActionUtil;
import yitouniu.util.OMSUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/6/6 15:47
 * @Description 富勒 盘点回传
 * @Version 1.0
 */
public class PanDianToFULEAction implements Action {
    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();

    public static final String FULE_URL = Util.null2String(new BaseBean().getPropValue("fule_configuration","fuleUrl"));
    public static final String FULE_BAOFEI_API = Util.null2String(new BaseBean().getPropValue("fule_configuration","scrapApi"));
    public static final String FULE_PANDIAN_API = Util.null2String(new BaseBean().getPropValue("fule_configuration","pandianApi"));

    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String)tableNameByRequestId.get("tableName");
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestid);
        JSONObject request = new JSONObject();
        if(main.next()){
            String pdbh = main.getString("pdbh");
            String sfty = main.getString("sfty");
            String sqck = main.getString("sqck");
            String status = "0".equals(sfty) ? "3" : "2";
            request.put("orderCode", pdbh);
            request.put("sqck", sqck);
            request.put("status", status);
            request.put("remark", "");
        }
        baseBean.writeLog("PanDianToFULEAction.request=" + request);
        String response = HttpRequest
                .post(FULE_URL + FULE_PANDIAN_API)
                .body(request.toJSONString()).execute().body();
        baseBean.writeLog("PanDianToFULEAction.response="+response);
        JSONObject resultJson = JSONObject.parseObject(response);

        if(resultJson.getInteger("code") != 0){
            requestInfo.getRequestManager().setMessagecontent("PanDianToFULEAction失败，失败原因：" + response);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }

        return SUCCESS;
    }




}

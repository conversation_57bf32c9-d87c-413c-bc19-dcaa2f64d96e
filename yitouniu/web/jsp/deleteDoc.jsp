<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="java.io.File" %>
<%@ page import="com.weaver.esb.spi.EsbService" %>
<%@ page import="com.weaver.esb.client.EsbClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>


<%
    String docid = "36693";

    RecordSet recordSet = new RecordSet();
    recordSet.executeQuery("select  a.id,b.imagefileid ,c.filerealpath from docimagefile b  left join   docdetail a on a.id = b.docid \n" +
            "left join imagefile c on b.imagefileid = c.imagefileid where a.id = ?", docid);

    String filerealpath = "d:\\ecology\\filesystem\\202010\\R\\车雪婷20201015135210.txt";
    /*if (recordSet.next()) {

        filerealpath = Util.null2String(recordSet.getString("filerealpath"));
        out.println("文档路径: " + filerealpath + "<br>");


    }

    String loginParam = "{\n" +
            "\t\"in0\": \"" + docid + "\",\n" +
            "\t\"in1\": \"45B24093D43CFAE4463FEC516347F2E3\"\n" +
            "}";
    String loginResult = esb("deleteDoc", loginParam, "out");
    out.println(loginResult);*/

    File file = new File(filerealpath);

    if (file.exists()) {
        boolean delete = file.delete();
        out.println(delete);
    }
%>


<%!
    public String esb(String event, String params, String outString) {
        EsbService service = EsbClient.getService(); // 调用esb方法
        String response = service.execute(event, params); //触发 ESB 事件
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject dataJSONObject = (JSONObject) jsonObject.get("data");
        String out = dataJSONObject.getString(outString);
        return out;
    }
%>



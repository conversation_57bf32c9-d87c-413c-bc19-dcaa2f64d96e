<%@ page import="java.io.FileOutputStream" %>
<%@ page import="com.sap.conn.jco.JCoDestination" %>
<%@ page import="com.sap.conn.jco.JCoFunction" %>
<%@ page import="yitouniu.util.ConnectPooled" %>
<%@ page import="com.sap.conn.jco.ext.DestinationDataProvider" %>
<%@ page import="java.io.File" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="com.sap.conn.jco.JCoDestinationManager" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="yitouniu.util.ActionUtil" %>
<%@ page import="java.util.*" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%!
    private RecordSet recordSet = new RecordSet();
%>
<%
    String requestid = "109112";
    new BaseBean().writeLog(requestid+"WriteOffDJMoneyAction开始");
    try {
           /* List<String> xzyfkList = new ArrayList<>(); // 选择预付款
            List<String> yfkhxhje1List = new ArrayList<>(); // 核销后金额
            List<String> yfkbchxje1List = new ArrayList<>(); // 本次核销金额*/
        new BaseBean().writeLog("------------------" + requestid + "开始----------------------------<br/>");
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);// 获取表明
        String tableName = (String) tableNameByRequestId.get("tableName");


        String sql = "select  *  from  " + tableName + "_dt1  where mainid = (select  id  from  " + tableName + "   where requestid = ?)";
        List<String> xzyfkList = new ArrayList<>(); // 选择预付款
        List<String> yfkhxhje1List = new ArrayList<>(); // 核销后金额
        List<String> yfkbchxje1List = new ArrayList<>(); // 本次核销金额

        recordSet.executeQuery(sql, requestid);
        while (recordSet.next()) {

            xzyfkList.add(recordSet.getString("xzyfk"));
            yfkhxhje1List.add(recordSet.getString("yfkhxhje1"));
            yfkbchxje1List.add(recordSet.getString("yfkbchxje1"));


        }
           /* super.getData(requestid);
            xzyfkList = super.getXzyfkList();
            yfkhxhje1List = super.getYfkhxhje1List();
            yfkbchxje1List = super.getYfkbchxje1List();*/
        if (xzyfkList.size() > 0) {
            for (int i = 0; i < xzyfkList.size(); i++) {
                Map<String,String> findyfje = ActionUtil.findyfje(xzyfkList.get(i));
                String djje = findyfje.get("djje"); // 冻结金额
                String yfksyje = findyfje.get("yfksyje"); // 本次预付金额
                String hxje = yfkhxhje1List.get(i);
                if ("".equals(hxje) || hxje == null || Double.valueOf(hxje) < 0) {
                    out.println("核销后金额不能为空或者为负数");

                } else {
                    double hxjeDouble = Double.valueOf(hxje);
                    String bcheje = yfkbchxje1List.get(i); // 本次核销金额
                    if(hxjeDouble==0){
                        if (!"".equals(djje) && djje != null) {
                            bcheje = (Double.valueOf(yfksyje)+Double.valueOf(bcheje))+"";
                        }
                        recordSet.executeUpdate("update uf_yufukuanku set yfksyje = 0,bcyfkje =?, djje = ? where id= ?",bcheje,bcheje,xzyfkList.get(i));
                    }
                    if(hxjeDouble>0){

                        if (!"".equals(djje) && djje != null) {
                            bcheje = (Double.valueOf(yfksyje)+Double.valueOf(bcheje))+"";
                        }
                        recordSet.executeUpdate("update uf_yufukuanku set yfksyje = ?,bcyfkje =?, djje = ? where id= ?",hxjeDouble,bcheje,bcheje,xzyfkList.get(i));

                    }
                }
            }
        }
    }catch (Exception e){
        out.println("WriteOffDJMoneyAction异常信息"+e);
    }


%>


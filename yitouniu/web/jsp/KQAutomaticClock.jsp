<%@ page import="com.engine.workflow.service.impl.RequestFormServiceImpl" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="com.engine.common.util.ServiceUtil" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.OnLineMonitor" %>
<%@ page import="java.util.Map" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.util.Date" %>
<%@ page import="com.engine.workflow.biz.requestForm.RequestFormBiz" %>
<%@ page import="javax.ws.rs.core.Context" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="com.engine.common.util.ParamUtil" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page import="com.engine.kq.service.KQAttendanceButtonService" %>
<%@ page import="com.engine.kq.service.impl.KQAttendanceButtonServiceImpl" %>
<%@ page import="weaver.general.BaseBean" %>

<%@ page language="java" contentType="text/html; charset=UTF-8" %>


<%

    String userid = request.getParameter("userid");



    getUser(request, userid);


    String s = punchButton(request, response);



    out.print(s);

%>


<%!
    private RecordSet rs = new RecordSet();

    private KQAttendanceButtonService getService(User user) {
        return (KQAttendanceButtonServiceImpl)ServiceUtil.getService(KQAttendanceButtonServiceImpl.class, user);
    }


    public String punchButton( HttpServletRequest request, HttpServletResponse response) {
        Object map = new HashMap();

        try {
            User user = HrmUserVarify.getUser(request, response);
            map = this.getService(user).punchButton(request, ParamUtil.request2Map(request), user);
        } catch (Exception e) {
            ((Map)map).put("status", "-1");
            new BaseBean().writeLog(e);
        }

        return JSONObject.toJSONString(map);
    }


    public void getUser(HttpServletRequest request, String userid) {

        String sql = "select  * from  hrmresource where id = ?";
        rs.executeQuery(sql, userid);
        User user = new User();
        if (rs.next()) {

            user.setUid(rs.getInt("id"));
            user.setLoginid(rs.getString("loginid"));
            user.setFirstname(rs.getString("firstname"));
            user.setLastname(rs.getString("lastname"));
            user.setAliasname(rs.getString("aliasname"));
            user.setTitle(rs.getString("title"));
            user.setTitlelocation(rs.getString("titlelocation"));
            user.setSex(rs.getString("sex"));
            user.setPwd(rs.getString("password"));
            String languageidweaver = rs.getString("systemlanguage");
            user.setLanguage(Util.getIntValue(languageidweaver, 0));

            user.setTelephone(rs.getString("telephone"));
            user.setMobile(rs.getString("mobile"));
            user.setMobilecall(rs.getString("mobilecall"));
            user.setEmail(rs.getString("email"));
            user.setCountryid(rs.getString("countryid"));
            user.setLocationid(rs.getString("locationid"));
            user.setResourcetype(rs.getString("resourcetype"));
            user.setStartdate(rs.getString("startdate"));
            user.setEnddate(rs.getString("enddate"));
            user.setContractdate(rs.getString("contractdate"));
            user.setJobtitle(rs.getString("jobtitle"));
            user.setJobgroup(rs.getString("jobgroup"));
            user.setJobactivity(rs.getString("jobactivity"));
            user.setJoblevel(rs.getString("joblevel"));
            user.setSeclevel(rs.getString("seclevel"));
            user.setUserDepartment(Util.getIntValue(rs.getString("departmentid"), 0));
            user.setUserSubCompany1(Util.getIntValue(rs.getString("subcompanyid1"), 0));
            user.setUserSubCompany2(Util.getIntValue(rs.getString("subcompanyid2"), 0));
            user.setUserSubCompany3(Util.getIntValue(rs.getString("subcompanyid3"), 0));
            user.setUserSubCompany4(Util.getIntValue(rs.getString("subcompanyid4"), 0));
            user.setManagerid(rs.getString("managerid"));
            user.setAssistantid(rs.getString("assistantid"));
            user.setPurchaselimit(rs.getString("purchaselimit"));
            user.setCurrencyid(rs.getString("currencyid"));
            user.setLastlogindate(rs.getString("currentdate"));
            user.setLogintype("1");
            user.setAccount(rs.getString("account"));


            user.setLoginip(request.getRemoteAddr());
        }
        request.getSession(true).setMaxInactiveInterval(60 * 60 * 24);
        request.getSession(true).setAttribute("weaver_user@bean", user);
        request.getSession(true).setAttribute("moniter", new OnLineMonitor("" + user.getUID(), user.getLoginip()));

    }





%>
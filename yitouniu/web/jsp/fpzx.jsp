<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="java.util.List" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="yitouniu.afternodeoperation.WriteOffSFMoneyAction" %>
<%@ page import="weaver.soa.workflow.request.RequestInfo" %>
<%@ page import="weaver.soa.workflow.request.RequestService" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>


<%
    RequestService rs = new RequestService();
    WriteOffSFMoneyAction writeOffSFMoneyAction = new  WriteOffSFMoneyAction();
    RequestInfo requestInfo = rs.getRequest(121927);
    writeOffSFMoneyAction.execute(requestInfo);
    List<String> yfkhxhje1List = writeOffSFMoneyAction.getYfkhxhje1List();
    out.println(yfkhxhje1List);
   /* RecordSet recordSet = new RecordSet();
    String sql = "select  a.requestid,a.sqr,b.fphm,c.currentnodetype from formtable_main_302 a LEFT  JOIN formtable_main_302_dt1 b on a.id = b.mainid LEFT JOIN  WORKFLOW_REQUESTBASE c on a.requestid = c.requestid where (c.currentnodetype != 3 and c.currentnodetype != 0)  and fphm !='' \n" +
            "UNION ALL \n" +
            "select  a.requestid,a.sqr,b.fphm,c.currentnodetype from formtable_main_292 a LEFT  JOIN formtable_main_292_dt1 b on a.id = b.mainid LEFT JOIN  WORKFLOW_REQUESTBASE c on a.requestid = c.requestid where (c.currentnodetype != 3 and c.currentnodetype != 0)  and fphm !=''";
    recordSet.executeQuery(sql);
    List<String> idlist = new ArrayList<>();
    List<String> fphmlist = new ArrayList<>();
    List<String> sqrlist = new ArrayList<>();

    while (recordSet.next()){
        idlist.add(recordSet.getString("requestid"));
        fphmlist.add(recordSet.getString("fphm"));
        sqrlist.add(recordSet.getString("sqr"));
    }
    for (int i = 0; i < idlist.size(); i++) {
        sql = "update fnaInvoiceLedger set status = 1 ,requestid = ?,reimburseperson =? where id = ? ";
        boolean b = recordSet.executeUpdate(sql, idlist.get(i), sqrlist.get(i), fphmlist.get(i));
        out.println("执行结果: "+b+"</br>");
    }*/

%>
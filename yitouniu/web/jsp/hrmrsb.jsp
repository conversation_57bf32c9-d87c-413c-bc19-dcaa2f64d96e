
<%@ page import="weaver.soa.workflow.request.RequestService" %>
<%@ page import="weaver.soa.workflow.request.RequestInfo" %>
<%@ page import="weaver.workflow.action.ESBNewAction" %>
<%@ page import="com.weaver.esb.client.EsbClient" %>
<%@ page import="com.weaver.esb.spi.EsbService" %>

<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%
    try {
        String parameter = request.getParameter("id");
        String[] split = parameter.split(",");
        for (int i = 0; i < split.length; i++) {
            RequestService requestService = new RequestService();
            int requetid = Integer.parseInt(split[i]);
            RequestInfo requestInfo = requestService.getRequest(requetid);
            ESBNewAction esbNewAction = new ESBNewAction();

            esbNewAction.setActionname("ZINF011_BX"); // 配置在流程中的标识

            String requestParams = esbNewAction.getRequestParams(requestInfo);

            out.println("参数:" + requestParams + "<br>");

            EsbService esbService = EsbClient.getService();

            String execute = esbService.execute("ZINF011", requestParams); // 事件标识和参数

            out.println("结果:" + execute +"<br>");
            out.println("------------------------------------------------"+"<br>" );
        }
        out.println("------------------------------------------------长度"+split.length );
    } catch (Exception e) {
        out.println("异常信息: "+e);
    }
%>
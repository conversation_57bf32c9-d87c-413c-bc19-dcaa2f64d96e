<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="cn.hutool.http.HttpRequest" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="java.util.Arrays" %>
<%@ page import="yitouniu.util.RsaEncrypt" %>
<%@ page import="java.net.URLEncoder" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%
    BaseBean baseBean = new BaseBean();
    Cookie[] cookies = request.getCookies();
//    //获取登录用户
//    String username = "";
//    for(Cookie o : cookies){
//        if("loginidweaver".equals(o.getName())){
//            username = o.getValue();
//        }
//    }
    //采用固定用户名（白银）
    String username = "白银";
    String url = "http://118.178.184.93:37799/webroot/decision/link/1tSV?ssoToken=";
    String token = URLEncoder.encode(RsaEncrypt.encrypt(username, RsaEncrypt.PUBLIC_KEY),"UTF-8");
    baseBean.writeLog("finebiLogin:token="+token);
    response.sendRedirect(url+token);
%>

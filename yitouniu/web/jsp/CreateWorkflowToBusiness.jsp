<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.hrm.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="java.io.IOException"%>
<%@ page import="net.sf.json.JSONArray"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="com.weaver.formmodel.util.DateHelper"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowBaseInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestTableField"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestTableRecord"%>
<%@ page import="weaver.workflow.webservices.WorkflowMainTableInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowServiceImpl"%>
<%@ page import="weaver.workflow.webservices.WorkflowDetailTableInfo"%>
<%@ page import="com.weaver.esb.spi.EsbService" %>
<%@ page import="com.weaver.esb.client.EsbClient" %>
<%@ page import="yitouniu.util.ActionUtil" %>
<%@ page import="weaver.conn.RecordSet" %>
<%
JSONObject resultObj = new JSONObject();
try {
	int _userid = Util.getIntValue(request.getParameter("userid"));
	UserManager userManager = new UserManager();
	User _user = userManager.getUserByUserIdAndLoginType(_userid, "1");

	int _wflowid = Util.getIntValue(request.getParameter("wflowid"));

	String params =  Util.null2String(request.getParameter("data"));
	JSONArray jsonArray = JSONArray.fromObject(params);

	WorkflowRequestInfo info = new WorkflowRequestInfo();
	info.setRequestName("费用报销-"+_user.getLastname()+"-"+DateHelper.getCurrentDate());
	info.setCreatorId(_userid+"");//创建人
	info.setRequestLevel("0");//0 正常，1重要，2紧急
	WorkflowBaseInfo base=new WorkflowBaseInfo();
	base.setWorkflowId(_wflowid+"");//流程id
	info.setWorkflowBaseInfo(base);
	info.setIsnextflow("0");//不流转到下一个节点

	WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[12];
	wrti[0]=new WorkflowRequestTableField();
	wrti[0].setFieldName("sqrq");// 提单日期
	wrti[0].setFieldValue(DateHelper.getCurrentDate()+"");//字段值
	wrti[0].setEdit(true);//是否能编辑
	wrti[0].setView(true);//是否可以查看
	wrti[0].setMand(true);//是否必填

	wrti[1]=new WorkflowRequestTableField();
	wrti[1].setFieldName("sqr");// 报销人员
	wrti[1].setFieldValue(_userid+"");//字段值
	wrti[1].setEdit(true);//是否能编辑
	wrti[1].setView(true);//是否可以查看
	wrti[1].setMand(true);//是否必填

	wrti[2]=new WorkflowRequestTableField();
	wrti[2].setFieldName("sqbm");// 报销部门
	wrti[2].setFieldValue(_user.getUserDepartment()+"");//字段值
	wrti[2].setEdit(true);//是否能编辑
	wrti[2].setView(true);//是否可以查看
	wrti[2].setMand(true);//是否必填

	wrti[3]=new WorkflowRequestTableField();
	wrti[3].setFieldName("cdzt");// 承担主体
	wrti[3].setFieldValue(_userid+"");//字段值
	wrti[3].setEdit(true);//是否能编辑
	wrti[3].setView(true);//是否可以查看
	wrti[3].setMand(true);//是否必填

	wrti[4]=new WorkflowRequestTableField();
	wrti[4].setFieldName("szgs");// 承担公司（分部）
	wrti[4].setFieldValue(_user.getUserSubCompany1()+"");//字段值
	wrti[4].setEdit(true);//是否能编辑
	wrti[4].setView(true);//是否可以查看
	wrti[4].setMand(true);//是否必填

	wrti[5]=new WorkflowRequestTableField();
	wrti[5].setFieldName("xgxm");// 相关项目
	wrti[5].setFieldValue("");//字段值
	wrti[5].setEdit(true);//是否能编辑
	wrti[5].setView(true);//是否可以查看
	wrti[5].setMand(true);//是否必填



	Map data = ActionUtil.findData(_userid + ""); // 获取人员账号
	Map rybh = getResponseByESB((String)data.get("rybh"));

	wrti[6]=new WorkflowRequestTableField();
	wrti[6].setFieldName("saprydm");// 相关客户
	wrti[6].setFieldValue((String)data.get("rybh"));//字段值
	wrti[6].setEdit(true);//是否能编辑
	wrti[6].setView(true);//是否可以查看
	wrti[6].setMand(true);//是否必填
	// 员工银行账号
	wrti[7]=new WorkflowRequestTableField();
	wrti[7].setFieldName("yxzh");// 相关客户
	wrti[7].setFieldValue((String)rybh.get("BANKN"));//字段值
	wrti[7].setEdit(true);//是否能编辑
	wrti[7].setView(true);//是否可以查看
	wrti[7].setMand(true);//是否必填
	// 员工银开户行
	wrti[8]=new WorkflowRequestTableField();
	wrti[8].setFieldName("yxmc");// 相关客户
	wrti[8].setFieldValue((String)rybh.get("BANKA"));//字段值
	wrti[8].setEdit(true);//是否能编辑
	wrti[8].setView(true);//是否可以查看
	wrti[8].setMand(true);//是否必填

	// 公司代码
	wrti[9]=new WorkflowRequestTableField();
	wrti[9].setFieldName("bukrs");// 公司代码
	wrti[9].setFieldValue((String)data.get("BUKRS"));//字段值
	wrti[9].setEdit(true);//是否能编辑
	wrti[9].setView(true);//是否可以查看
	wrti[9].setMand(true);//是否必填


	// 人员名称
	wrti[10]=new WorkflowRequestTableField();
	wrti[10].setFieldName("sqrmc");// 申请人名称
	wrti[10].setFieldValue((String)data.get("rymc"));//字段值
	wrti[10].setEdit(true);//是否能编辑
	wrti[10].setView(true);//是否可以查看
	wrti[10].setMand(true);//是否必填
	// 流程类型
	wrti[11]=new WorkflowRequestTableField();
	wrti[11].setFieldName("lclx");// 流程类型
	wrti[11].setFieldValue("1");//字段值
	wrti[11].setEdit(true);//是否能编辑
	wrti[11].setView(true);//是否可以查看
	wrti[11].setMand(true);//是否必填
	WorkflowRequestTableRecord[] tablre = new WorkflowRequestTableRecord[1]; //主字段只有一行数据
	tablre[0]=new WorkflowRequestTableRecord();
	tablre[0].setWorkflowRequestTableFields(wrti);
	WorkflowMainTableInfo maininfo=new WorkflowMainTableInfo();
	maininfo.setRequestRecords(tablre);
	info.setWorkflowMainTableInfo(maininfo);//添加主字段数据



	//添加明细数据
	tablre = new WorkflowRequestTableRecord[jsonArray.size()];//添加n行明细数据
	for(int i=0;i<jsonArray.size();i++){
		JSONObject s = jsonArray.getJSONObject(i);
		String fyrq = Util.null2String(s.get("fyrq"));
		String fykm = Util.null2String(s.get("fykm"));
		String fysm = Util.null2String(s.get("fysm"));
		String fp = Util.null2String(s.get("fp"));

		Map<String,String> wbz = findWBZ(fp); // 微报账数据

		String priceWithoutTax = Util.null2String(wbz.get("priceWithoutTax"));
		String xglc = Util.null2String(s.get("xglc"));
		String je = Util.null2String(s.get("je"));
		String tax = Util.null2String(wbz.get("tax")); // 税额
		String jmsjid = Util.null2String(s.get("id"));

		String invoiceType = Util.null2String(wbz.get("invoiceType")); // 类型
		String taxIncludedPrice = Util.null2String(wbz.get("taxIncludedPrice")); // 价税合计
		String taxRate = Util.null2String(wbz.get("taxRate")); // 税率
		String sl = getSl(taxRate); // 税率


		wrti = new WorkflowRequestTableField[13]; //字段信息
		wrti[0] = new WorkflowRequestTableField();
		wrti[0].setFieldName("rq");//费用日期
		wrti[0].setFieldValue(fyrq);
		wrti[0].setView(true);//字段是否可见
		wrti[0].setEdit(true);//字段是否可编辑

		wrti[1] = new WorkflowRequestTableField();
		wrti[1].setFieldName("fykm");//费用科目
		wrti[1].setFieldValue(fykm);
		wrti[1].setView(true);//字段是否可见
		wrti[1].setEdit(true);//字段是否可编辑

		wrti[2] = new WorkflowRequestTableField();
		wrti[2].setFieldName("fysm");//费用说明
		wrti[2].setFieldValue(fysm);
		wrti[2].setView(true);//字段是否可见
		wrti[2].setEdit(true);//字段是否可编辑

		wrti[3] = new WorkflowRequestTableField();
		wrti[3].setFieldName("bxje");//无税基恩
		wrti[3].setFieldValue(priceWithoutTax);
		wrti[3].setView(true);//字段是否可见
		wrti[3].setEdit(true);//字段是否可编辑

		wrti[4] = new WorkflowRequestTableField();
		wrti[4].setFieldName("fpyc");//发票
		wrti[4].setFieldValue(fp);
		wrti[4].setView(true);//字段是否可见
		wrti[4].setEdit(true);//字段是否可编辑

		wrti[5] = new WorkflowRequestTableField();
		wrti[5].setFieldName("fpje");//报销金额
		wrti[5].setFieldValue(taxIncludedPrice);
		wrti[5].setView(true);//字段是否可见
		wrti[5].setEdit(true);//字段是否可编辑

		wrti[6] = new WorkflowRequestTableField();
		wrti[6].setFieldName("dkse");//税额
		wrti[6].setFieldValue(tax);
		wrti[6].setView(true);//字段是否可见
		wrti[6].setEdit(true);//字段是否可编辑

		wrti[7] = new WorkflowRequestTableField();
		wrti[7].setFieldName("jmsjid");//建模数据id
		wrti[7].setFieldValue(jmsjid);
		wrti[7].setView(true);//字段是否可见
		wrti[7].setEdit(true);//字段是否可编辑

		wrti[8] = new WorkflowRequestTableField();
		wrti[8].setFieldName("fphm");//发票信息数据id
		wrti[8].setFieldValue(fp);
		wrti[8].setView(true);//字段是否可见
		wrti[8].setEdit(true);//字段是否可编辑



		wrti[9] = new WorkflowRequestTableField();
		wrti[9].setFieldName("bxry");//报销人员
		wrti[9].setFieldValue(_userid+"");
		wrti[9].setView(true);//字段是否可见
		wrti[9].setEdit(true);//字段是否可编辑

		wrti[10] = new WorkflowRequestTableField();
		wrti[10].setFieldName("bxbm");//报销部门
		wrti[10].setFieldValue(_user.getUserDepartment()+"");
		wrti[10].setView(true);//字段是否可见
		wrti[10].setEdit(true);//字段是否可编辑


		wrti[11] = new WorkflowRequestTableField();
		wrti[11].setFieldName("wbzfplx");//
		wrti[11].setFieldValue(invoiceType);
		wrti[11].setView(true);//字段是否可见
		wrti[11].setEdit(true);//字段是否可编辑

		wrti[12] = new WorkflowRequestTableField();
		wrti[12].setFieldName("taxrate1");//
		wrti[12].setFieldValue(sl);
		wrti[12].setView(true);//字段是否可见
		wrti[12].setEdit(true);//字段是否可编辑
		
		
		tablre[i] = new WorkflowRequestTableRecord();
		tablre[i].setWorkflowRequestTableFields(wrti);
	}

	//添加到明细表中
	WorkflowDetailTableInfo WorkflowDetailTableInfo[] = new WorkflowDetailTableInfo[1];
	WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
	WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(tablre);
	info.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
	WorkflowServiceImpl wsi = new WorkflowServiceImpl();
	int requestid = Integer.parseInt(wsi.doCreateWorkflowRequest(info, _userid));
	new weaver.general.BaseBean().writeLog("============_userid:"+_userid+",requestid:"+requestid+"============");
	resultObj.put("requestid", requestid);
	resultObj.put("status", "1");
} catch (Exception ex) {
	ex.printStackTrace();
	resultObj.put("status", "0");
	resultObj.put("errMsg", ex.getMessage());
}finally{
	try{
		out.print(resultObj.toString());
		out.flush();
	}catch(IOException ex){
		ex.printStackTrace();
	}
}
%>
<%!


	private RecordSet recordSet = new RecordSet();


	/**
	 * 调用esb方法
	 *
	 * @param
	 * @param
	 * @return
	 */
	public Map getResponseByESB(String rybm) {

		String params = "{\n" +
				"\t\"PARTNER\": \""+"0000"+rybm+"\",\n" +
				"\t\"BANKN\": \"\"\n" +
				"}";
		Map map = new HashMap();
		EsbService service = EsbClient.getService(); // 调用esb方法
		String response = service.execute("ZINF002YH", params); //触发 ESB 事件
		com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(response);
		String data = jsonObject.getString("data");
		com.alibaba.fastjson.JSONObject jsonObjectData = com.alibaba.fastjson.JSONObject.parseObject(data);
		List<com.alibaba.fastjson.JSONObject> list = (List<com.alibaba.fastjson.JSONObject>) jsonObjectData.get("list");
		map.put("BANKN",list.get(0).getString("BANKN"));
		map.put("BANKA",list.get(0).getString("BANKA"));
		return map;
	}
	/**
	 * 微报账数据
	 */
	public  Map findWBZ(String id) {
		Map map = new HashMap();
		String sql = "select * from fnaInvoiceLedger  where id = ?";
		recordSet.executeQuery(sql, id);

		if (recordSet.next()) {
			map.put("invoiceType", recordSet.getString("invoiceType"));
			map.put("priceWithoutTax", recordSet.getString("priceWithoutTax"));
			map.put("taxRate", recordSet.getString("taxRate"));
			map.put("taxIncludedPrice", recordSet.getString("taxIncludedPrice"));
			map.put("tax", recordSet.getString("tax"));
		}
		return map;
	}


	public String getSl(String slwb){

		String shuilv  = "";

		switch (slwb) {
			case "13":
				shuilv= "0";
				break;
			case "3":
				shuilv =  "1";
				break;
			case "5":
				shuilv =  "2";
				break;
			case "6":
				shuilv =  "3";
				break;
			case "9":
				shuilv =  "4";
				break;
			case "0":
				shuilv =  "5";
				break;
			case "1":
				shuilv =  "6";
				break;
		}
		return shuilv;

	}

%>
<%@ page import="java.util.Map" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="yitouniu.util.ActionUtil" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%
    String id = request.getParameter("id");
    String allCodeName = "6601200001,6601210001,6601210002,6601210003,6601210004,6601210005,6601210006,6601210010,6601210011,6601230000,6601240000,6601210014";
    Map km = ActionUtil.findKm(id);
    String  codeName = (String) km.get("codeName");
    Map<String,String> map = new HashMap();

    if (!"".equals(codeName)&&allCodeName.contains(codeName)){
        map.put("msg","0");
    }else {
        map.put("msg","1");
    }
    out.println(JSON.toJSONString(map));

%>
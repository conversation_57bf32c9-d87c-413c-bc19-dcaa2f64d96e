<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ page import="weaver.soa.workflow.request.RequestService" %>
<%@ page import="weaver.soa.workflow.request.RequestInfo" %>
<%@ page import="org.apache.commons.logging.LogFactory" %>
<%@ page import="org.apache.commons.logging.Log" %>

<%@ page import="yitouniu.afternodeoperation.*" %>

<%!
    private static Log log = LogFactory.getLog("-----executeAction开始-----");


%>
<%
    String parameter = request.getParameter("id");
    String[] split = parameter.split(",");
    for (int i = 0; i < split.length; i++) {
        int requestid = Integer.parseInt(split[i]);
        RequestService rs = new RequestService();
        /*List<Integer> list = new ArrayList<>();
        list.add(357896);*/


        RequestInfo requestInfo = rs.getRequest(requestid);
        //int creatorid = requestInfo.getRequestManager().getCreater();
        //String execute1 = new WriteOffDJMoneyAction().execute(requestInfo);
       // String execute1 = new WriteOffSFMoneyAction().execute(requestInfo);
       //String execute1 = new WriteOffKJMoneyAction().execute(requestInfo);
        String execute1 = new ExpenseVoucherAction().execute(requestInfo);
        //String execute1 = new WorkflowNumber().execute(requestInfo);
        out.println("结果" + execute1);
    }







/*WorkflowToMode workflowToMode = new WorkflowToMode();
        workflowToMode.execute(requestInfo);*/
 /*   for (int i = 0; i < list.size(); i++) {
        RequestInfo requestInfo = rs.getRequest(list.get(i));

        String execute = new wacai_cjhid_clfbx().execute(requestInfo);
        out.println("结果" +execute);

        String selectsql = "select * from FORMTABLE_MAIN_179_DT5 where mainid = (select id from FORMTABLE_MAIN_179 where requestid = ?) ";
        recordSet.executeQuery(selectsql,list.get(i));
        String hid = "";
        while (recordSet.next()){
            hid = recordSet.getString("hid");

        }

        String cklcsql = "update UF_CW_PZSCJL set hid = ? where cklc = ?";
        out.println("更新结果1" +recordSet.executeUpdate(cklcsql,hid,list.get(i)));


        List djbh = new ArrayList();
        List mxidlist = new ArrayList();
        String selectsql3 = "select * from UF_CW_PZSCJL where cklc = ? and mxid is not null ";
        recordSet.executeQuery(selectsql3,list.get(i));
        while (recordSet.next()){
            djbh.add(recordSet.getString("djbh"));
            mxidlist.add(recordSet.getString("mxid"));
        }
        out.println("流程编号" +djbh);
        out.println("明细id" +mxidlist);
        String updatesql = "update UF_CW_PZSCJL set hid = ? where mxid = ?";

        for (int j = 0; j < djbh.size();j++) {
            String ckhid = (String)djbh.get(j)+mxidlist.get(j);
            out.println("更新结果2" +recordSet.executeUpdate(updatesql,ckhid,mxidlist.get(j)));
        }

    }*/


%>
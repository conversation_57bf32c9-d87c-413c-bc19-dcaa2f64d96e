<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.util.*" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%!

    private RecordSet recordSet = new RecordSet();
%>

<%
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

    String ksrq = request.getParameter("ksrq"); // 开始时间
    String jsrq = request.getParameter("jsrq"); // 结束时间
    String kssjd = request.getParameter("kssjd"); // 开始时间段
    String jssjd = request.getParameter("jssjd"); // 结束时间段
    String requestid = request.getParameter("requestid"); // 请求id

    String cdmc = request.getParameter("cdmc"); // 场地
    String kssj = ""; // 开始时间
    String jssj = ""; // 结束时间
    if ("0".equals(kssjd)) { // 开始时间上午
        kssj = " 09:00";
    }
    if ("1".equals(kssjd)) { // 开始时间下午
        kssj = " 14:00";
    }
    if ("0".equals(jssjd)) { // 结束时间上午
        jssj = " 12:00";
    }
    if ("1".equals(jssjd)) { // 结束时间下午
        jssj = " 18:00";
    }
    rs.writeLog("ksrq = " + ksrq);
    rs.writeLog("jsrq = " + jsrq);
    rs.writeLog("kssjd = " + kssjd);
    rs.writeLog("jssjd = " + jssjd);
    long kssjParse = simpleDateFormat.parse(ksrq + kssj).getTime(); // 前端选择的开始日期时间
    long jssjParse = simpleDateFormat.parse(jsrq + jssj).getTime();// 前端选择的结束日期时间
    rs.writeLog("kssjParse = " + kssjParse);
    rs.writeLog("jssjParse = " + jssjParse);

    // 查询出所有大于等于开始时间的数据
    String sql = "select * from  uf_CDYYJL where (ksrq >= ? or jsrq >= ?) and cdmc = ? and lcid <> ?";
    recordSet.executeQuery(sql, ksrq, ksrq, cdmc, requestid);
    boolean isExist = false;
    boolean isEveryExist = false;
    boolean isAgree = false;
    List<String> lcidList = new ArrayList<>();

    if (!"2".equals(cdmc)) {
        while (recordSet.next()) {
            String ksrqValue = recordSet.getString("ksrq");
            String jsrqValue = recordSet.getString("jsrq");
            String kssjValue = recordSet.getString("kssj");
            String jssjValue = recordSet.getString("jssj");
            long kssjtime = simpleDateFormat.parse(ksrqValue + " " + kssjValue).getTime();
            long jssjtime = simpleDateFormat.parse(jsrqValue + " " + jssjValue).getTime();
            rs.writeLog("kssjtime = " + kssjtime);
            rs.writeLog("jssjtime = " + jssjtime);
            if ((kssjParse >= kssjtime && kssjParse <= jssjtime) || (jssjParse >= kssjtime && jssjParse <= jssjtime) || (kssjParse <= kssjtime && jssjParse >= jssjtime)) { // 开始时间在数据库中开始时间和结束时间之间
                isExist = true;
                isEveryExist = true;
            } else {
                isEveryExist = false;
            }
            if (isEveryExist) {
                lcidList.add(recordSet.getString("lcid"));
            }
        }
        if (isExist) {
            //查询这个流程负责人是否同意
            for (String id : lcidList) {
                //1是否，0是是
                String sql3 = "select sfty from uf_CDYYJL where lcid = ?";
                recordSet.executeQuery(sql3, id);
                String sfty = "";
                if (recordSet.next()) {
                    sfty = recordSet.getString("sfty");
                }
                //如果负责人已经同意，则不让审批或提交
                if ("0".equals(sfty)) {
                    isAgree = true;
                }
            }
        }
    }
    recordSet.executeUpdate("update formtable_main_49  set kssj = ?,jssj = ? where requestid = ?", kssj, jssj, requestid);

    Map map = new HashMap();
    map.put("msg", isExist);
    map.put("isAgree", isAgree);
    rs.writeLog("返回结果map = " + map);
    out.println(JSON.toJSONString(map));

%>
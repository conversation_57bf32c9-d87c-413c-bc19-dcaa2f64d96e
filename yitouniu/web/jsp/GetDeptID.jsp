<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="java.util.Map" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.util.HashMap" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%
    RecordSet recordSet = new RecordSet();
    String parameter = request.getParameter("id");
    String requestid = request.getParameter("requestid");
    String sql = "select  * from  hrmresource where id = ?";
    recordSet.executeQuery(sql,parameter);
    String departmentid = "";
    if (recordSet.next()){
        departmentid = recordSet.getString("departmentid");
    }


    recordSet.executeUpdate("update formtable_main_300 set sqbm = ? where requestid = ?",departmentid,requestid);

%>
<%@ page import="java.net.SocketAddress" %>
<%@ page import="java.net.InetSocketAddress" %>

<%@ page import="java.util.List" %>
<%@ page import="java.io.*" %>
<%@ page import="java.util.ArrayList" %>
<%@ page import="weaver.general.Base64" %>
<%@ page import="com.weaver.esb.spi.EsbService" %>
<%@ page import="com.weaver.esb.client.EsbClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>

<%@ page import="org.json.XML" %>
<%@ page import="java.util.Map" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="sun.net.ftp.FtpClient" %>
<%@ page import="yitouniu.util.ActionUtil" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%


    String nodtecode = "ZJFK_6000_202011_00000053";
    String requestid = "138549";
    // 通过接口获取电子路径
    String dzhdParams = "{\n" +
            "\t\"notecode\": \"" + nodtecode + "\"\n" +
            "}";
    String dzhd = esb("dzhd", dzhdParams, "out"); // 获取esb中电子回单数据
    String dzhdXmlJsonString = xmlToString(dzhd); // 结果xml转为json
    JSONObject jsonObject = JSONObject.parseObject(dzhdXmlJsonString);
    JSONObject mbs = (JSONObject) jsonObject.get("MBS");
    JSONObject resp = (JSONObject) mbs.get("resp");
    JSONObject list = (JSONObject) resp.get("list");
    String creatDoc = "";
    FtpClient ftp = null;
    if (list.containsKey("detail")) {
        JSONObject detail = (JSONObject) list.get("detail");
        //String filepath = detail.getString("filepath");
        String filename = detail.getString("filename");

        ftp = connectFTP("115.238.39.2", 1021, "YTN", "YTN1");

        String encode = download("/"+filename, ftp);
        out.println(encode+"<br>");
        out.println(filename+"<br>");

        String[] split = filename.split("/");
        out.println(split[split.length-1]+"<br>");
        String loginParam = "{\n" +
                "\t\"in0\": \"sysadmin\",\n" +
                "\t\"in2\": \"0\",\n" +
                "\t\"in1\": \"ryytnOA456\",\n" +
                "\t\"in3\": \"127.0.0.1\"\n" +
                "}";
        String loginResult = esb("login", loginParam, "out");
        String createDocParam = "{\n" +
                "\t\"in0\": {\n" +
                "\t\t\"attachments\": {\n" +
                "\t\t\t\"DocAttachment\": [\n" +
                "\t\t\t\t{\n" +
                "\t\t\t\t\t\"filecontent\": \"" + new String(encode) + "\",\n" +
                "\t\t\t\t\t\"filename\": \""+split[split.length-1]+"\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t]\n" +
                "\t\t},\n" +
                "\t\t\"docStatus\": \"1\",\n" +
                "\t\t\"docSubject\": \""+split[split.length-1]+"\",\n" +
                "\t\t\"docType\": \"2\",\n" +
                "\t\t\"doccontent\": \"\",\n" +
                "\t\t\"doccreaterid\": \"1\",\n" +
                "\t\t\"doccreatertype\": \"0\",\n" +
                "\t\t\"ownerid\": \"1\",\n" +
                "\t\t\"seccategory\": \"290\"\n" +
                "\t},\n" +
                "\t\"in1\": \"" + loginResult + "\"\n" +
                "}";
        creatDoc = esb("CreatDoc", createDocParam, "out");
        if (creatDoc != null && !"".equals(creatDoc)) {

            if (Integer.parseInt(creatDoc) > 0) {
                insertDoc(requestid, creatDoc);


            }
            out.println(creatDoc);

        }
    }
    try {
        ftp.close();
    } catch (IOException e) {
        e.printStackTrace();
    }


/*
    String filePath="D:\\ecology\\niu\\doc\\流程API文档.pdf";
    String str= new String(filePath.getBytes("ISO8859-1"), "UTF-8");
    String path=request.getSession().getServletContext().getRealPath(str);
    response.setContentType("application/pdf;charset=UTF-8");
    response.setHeader("Content-Disposition",
            "inline; filename=print.pdf");*/
    /*OutputStream outs = null;


    try {
        outs =response.getOutputStream();
        //InputStream in = new FileInputStream(path);
        int len=0;
        byte [] buffer = new byte[1024];
        while((len=in.read(buffer))>0)
            outs.write(buffer, 0, len);
        outs.flush();
        in.close();
        outs.close();

    } catch (Exception e) {
        e.printStackTrace();
    }

        //ftp://username:<EMAIL>/path
        // ********************************
       // request.getRequestDispatcher("ftp://115.238.39.2:1021").forward(request, response);


*/

%>

<%!

    //  执行esb事件
    public  String esb(String event, String params, String outString) {
        EsbService service = EsbClient.getService(); // 调用esb方法
        String response = service.execute(event, params); //触发 ESB 事件
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject dataJSONObject = (JSONObject) jsonObject.get("data");
        String out = dataJSONObject.getString(outString);

        return out;
    }

    public  FtpClient connectFTP(String url, int port, String username, String password) {
        //创建ftp
        FtpClient ftp = null;
        try {
            //创建地址
            SocketAddress addr = new InetSocketAddress(url, port);
            //连接
            ftp = FtpClient.create();
            ftp.connect(addr);
            //登陆
            ftp.login(username, password.toCharArray());
            ftp.setBinaryType();

        } catch (Exception e) {
            e.printStackTrace();

        }
        return ftp;
    }

    public  String download(String ftpFile, FtpClient ftp) {
        List<String> list = new ArrayList<String>();
        String str = "";
        InputStream is = null;
        //BufferedReader br = null;
        try {
            int byteread;
            byte data[] = new byte[1024];
            // 获取ftp上的文件
            is = ftp.getFileStream(ftpFile);
            /*//转为字节流
            br = new BufferedReader(new InputStreamReader(is));
            while((str=br.readLine())!=null){
                list.add(str);
            }*/
            ByteArrayOutputStream out1 = new ByteArrayOutputStream();
            while ((byteread = is.read(data)) != -1) {
                out1.write(data, 0, byteread);

                out1.flush();
            }
            byte[] content = out1.toByteArray();

            byte[] encode = Base64.encode(content);
            is.close();
            out1.close();
            // br.close();
            return new String(encode);
        }catch (Exception e) {
            e.printStackTrace();

        }
        return "";
    }

    /**
     * xml格式转化为json
     *
     * @param xml
     * @return
     */
    public  String xmlToString(String xml) {
        //将xml转为json
        org.json.JSONObject xmlJSONObj = null;
        try {
            xmlJSONObj = XML.toJSONObject(xml);

        } catch (org.json.JSONException e) {
            e.printStackTrace();
        }
        //设置缩进
        String jsonPrettyPrintString = xmlJSONObj.toString();
        //输出格式化后的json
        return jsonPrettyPrintString;
    }

    public  void insertDoc(String requestid, String docId) {
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        RecordSet rs = new RecordSet();
        String tableName = (String) tableNameByRequestId.get("tableName");
        rs.executeUpdate("update " + tableName + " set dzhd = ? where  requestid = ?", docId, requestid);

    }

%>

<%@ page import="com.weaver.esb.spi.EsbService" %>
<%@ page import="com.weaver.esb.client.EsbClient" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>

<%@ page import="org.json.XML" %>
<%@ page import="org.json.JSONException" %>
<%@ page import="java.io.InputStream" %>
<%@ page import="java.io.FileInputStream" %>
<%@ page import="java.io.ByteArrayOutputStream" %>
<%@ page import="weaver.general.Base64" %>
<%@ page import="java.io.File" %>
<%@ page import="com.dingtalk.api.response.CorpReportListResponse" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%

   /* try {

        SmbFile smbFile = new SmbFile("smb://192.168.126.97/shareDir/BOC/回单1.pdf");
        //通过 smbFile.isDirectory();isFile()可以判断smbFile是文件还是文件夹

        int length = smbFile.getContentLength();//得到文件的大小

        byte buffer[] = new byte[length];

        SmbFileInputStream in = new SmbFileInputStream(smbFile); //建立smb文件输入流

        while ((in.read(buffer)) != -1) {


            out.println(buffer.length);

        }

        in.close();

    } catch (Exception e) {

        e.printStackTrace();

    }*/
    String dzhdParams = "{\n" +
            "\t\"notecode\": \"ZJFK_6000_202009_00001028\"\n" +
            "}";
    String dzhd = esb("dzhd", dzhdParams,"out"); // 获取esb中电子回单数据
    String  dzhdXmlJsonString= xmlToString(dzhd); // 结果xml转为json
    JSONObject jsonObject = JSONObject.parseObject(dzhdXmlJsonString);
    JSONObject mbs = (JSONObject) jsonObject.get("MBS");
    JSONObject resp = (JSONObject) mbs.get("resp");
    JSONObject list = (JSONObject) resp.get("list");
    JSONObject detail = (JSONObject) list.get("detail");
    String filepath = detail.getString("filepath");
    String filename = detail.getString("filename");
    try {
         int byteread;
         byte data[] = new byte[1024];
         String fileName = "流程API文档.pdf";
         String filePath = "D:\\ecology\\niu\\doc\\" + fileName;
         InputStream input = new FileInputStream(new File(filePath));

         ByteArrayOutputStream out1 = new ByteArrayOutputStream();
         while ((byteread = input.read(data)) != -1) {
             out1.write(data, 0, byteread);

             out1.flush();
         }
         byte[] content = out1.toByteArray();


         input.close();
         out1.close();

    byte[] encode = Base64.encode(content);
         out.println("shujudaxiao111 :"+encode);
     String loginParam = "{\n" +
             "\t\"in0\": \"sysadmin\",\n" +
             "\t\"in2\": \"0\",\n" +
             "\t\"in1\": \"ryytnOA123\",\n" +
             "\t\"in3\": \"127.0.0.1\"\n" +
             "}";
     String loginResult = esb("login", loginParam,"out");
     String createDocParam ="{\n" +
             "\t\"in0\": {\n" +
             "\t\t\"attachments\": {\n" +
             "\t\t\t\"DocAttachment\": [\n" +
             "\t\t\t\t{\n" +
             "\t\t\t\t\t\"filecontent\": \""+new String(encode)+"\",\n" +
             "\t\t\t\t\t\"filename\": \"测试.pdf\"\n" +
             "\t\t\t\t}\n" +
             "\t\t\t]\n" +
             "\t\t},\n" +
             "\t\t\"docStatus\": \"1\",\n" +
             "\t\t\"docSubject\": \"测试.pdf\",\n" +
             "\t\t\"docType\": \"2\",\n" +
             "\t\t\"doccontent\": \"\",\n" +
             "\t\t\"doccreaterid\": \"1\",\n" +
             "\t\t\"doccreatertype\": \"0\",\n" +
             "\t\t\"ownerid\": \"1\",\n" +
             "\t\t\"seccategory\": \"275\"\n" +
             "\t},\n" +
             "\t\"in1\": \""+loginResult+"\"\n" +
             "}";
     String creatDoc = esb("CreateDoc", createDocParam,"docid");
     out.println(creatDoc);

     } catch(Exception e){
         out.println("异常信息: "+e);
     }
/*

    String filePath="D:\\ecology\\niu\\doc\\流程API文档.pdf";
    String str= new String(filePath.getBytes("ISO8859-1"), "UTF-8");
    String path=request.getSession().getServletContext().getRealPath(str);
    response.setContentType("application/pdf;charset=UTF-8");
    response.setHeader("Content-Disposition",
            "inline; filename=print.pdf");
    OutputStream outs = null;


    try {
        outs =response.getOutputStream();
        InputStream in = new FileInputStream(path);
        int len=0;
        byte [] buffer = new byte[1024];
        while((len=in.read(buffer))>0)
            outs.write(buffer, 0, len);
        outs.flush();
        in.close();
        outs.close();

    } catch (Exception e) {
        e.printStackTrace();
    }
*/


%>

<%!
    //  执行esb事件
    public String esb(String event, String params, String outString) {
        EsbService service = EsbClient.getService(); // 调用esb方法
        String response = service.execute(event, params); //触发 ESB 事件
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject dataJSONObject = (JSONObject) jsonObject.get("data");
        String out = dataJSONObject.getString(outString);
        return out;
    }


    /**
     * xml格式转化为json
     * @param xml
     * @return
     */
    public String xmlToString(String  xml){
        //将xml转为json
        org.json.JSONObject xmlJSONObj = null;
        try {
            xmlJSONObj = XML.toJSONObject(xml);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        //设置缩进
        String jsonPrettyPrintString = xmlJSONObj.toString();
        //输出格式化后的json
        return jsonPrettyPrintString;
    }


%>




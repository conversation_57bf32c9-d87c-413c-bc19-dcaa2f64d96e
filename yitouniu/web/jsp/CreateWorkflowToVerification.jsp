<%@ page language="java" contentType="text/html; charset=UTF-8"%>
<%@ page import="java.util.*" %>
<%@ page import="weaver.hrm.*" %>
<%@ page import="weaver.general.*" %>
<%@ page import="java.io.IOException"%>
<%@ page import="net.sf.json.JSONArray"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="com.weaver.formmodel.util.DateHelper"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowBaseInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestTableField"%>
<%@ page import="weaver.workflow.webservices.WorkflowRequestTableRecord"%>
<%@ page import="weaver.workflow.webservices.WorkflowMainTableInfo"%>
<%@ page import="weaver.workflow.webservices.WorkflowServiceImpl"%>
<%@ page import="weaver.workflow.webservices.WorkflowDetailTableInfo"%>
<%
JSONObject resultObj = new JSONObject();
try {
	int _userid = Util.getIntValue(request.getParameter("userid"));
	UserManager userManager = new UserManager();
	User _user = userManager.getUserByUserIdAndLoginType(_userid, "1");

	int _wflowid = Util.getIntValue(request.getParameter("wflowid"));

	String params =  Util.null2String(request.getParameter("data"));
	JSONArray jsonArray = JSONArray.fromObject(params);

	WorkflowRequestInfo info = new WorkflowRequestInfo();
	info.setRequestName("发票核销流程-"+_user.getLastname()+"-"+DateHelper.getCurrentDate());
	info.setCreatorId(_userid+"");//创建人
	info.setRequestLevel("0");//0 正常，1重要，2紧急
	WorkflowBaseInfo base=new WorkflowBaseInfo();
	base.setWorkflowId(_wflowid+"");//流程id
	info.setWorkflowBaseInfo(base);
	info.setIsnextflow("0");//不流转到下一个节点

	WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[7];
	wrti[0]=new WorkflowRequestTableField();
	wrti[0].setFieldName("sqrq");// 申请日期
	wrti[0].setFieldValue(DateHelper.getCurrentDate()+"");//字段值 
	wrti[0].setEdit(true);//是否能编辑
	wrti[0].setView(true);//是否可以查看
	wrti[0].setMand(true);//是否必填

	wrti[1]=new WorkflowRequestTableField();
	wrti[1].setFieldName("sqr");// 申请人
	wrti[1].setFieldValue(_userid+"");//字段值 
	wrti[1].setEdit(true);//是否能编辑
	wrti[1].setView(true);//是否可以查看
	wrti[1].setMand(true);//是否必填

	wrti[2]=new WorkflowRequestTableField();
	wrti[2].setFieldName("sqbm");// 申请部门	
	wrti[2].setFieldValue(_user.getUserDepartment()+"");//字段值 
	wrti[2].setEdit(true);//是否能编辑
	wrti[2].setView(true);//是否可以查看
	wrti[2].setMand(true);//是否必填


	wrti[3]=new WorkflowRequestTableField();
	wrti[3].setFieldName("sqgs");// 所属公司
	wrti[3].setFieldValue(_user.getUserSubCompany1()+"");//字段值 
	wrti[3].setEdit(true);//是否能编辑
	wrti[3].setView(true);//是否可以查看
	wrti[3].setMand(true);//是否必填

	wrti[4]=new WorkflowRequestTableField();
	wrti[4].setFieldName("hxlx");// 核销类型	
	wrti[4].setFieldValue("2");//字段值 
	wrti[4].setEdit(true);//是否能编辑
	wrti[4].setView(true);//是否可以查看
	wrti[4].setMand(true);//是否必填

	WorkflowRequestTableRecord[] tablre = new WorkflowRequestTableRecord[1]; //主字段只有一行数据
	tablre[0]=new WorkflowRequestTableRecord();
	tablre[0].setWorkflowRequestTableFields(wrti);
	WorkflowMainTableInfo maininfo=new WorkflowMainTableInfo();
	maininfo.setRequestRecords(tablre);
	info.setWorkflowMainTableInfo(maininfo);//添加主字段数据

	//添加明细数据
	tablre = new WorkflowRequestTableRecord[jsonArray.size()];//添加n行明细数据
	for(int i=0;i<jsonArray.size();i++){
		JSONObject s = jsonArray.getJSONObject(i);
		String fyrq = Util.null2String(s.get("fyrq"));
		String fykm = Util.null2String(s.get("fykm"));
		String fysm = Util.null2String(s.get("fysm"));
		String pjzs = Util.null2String(s.get("pjzs"));
		String xglc = Util.null2String(s.get("xglc"));
		String je = Util.null2String(s.get("je"));
		String fj = Util.null2String(s.get("fj"));
		String jmsjid = Util.null2String(s.get("id"));
		String fp = Util.null2String(s.get("fp"));
		String se = Util.null2String(s.get("se"));
		String jebhsj = Util.null2String(s.get("jebhsj"));
		
		wrti = new WorkflowRequestTableField[11]; //字段信息

		
		wrti[0] = new WorkflowRequestTableField(); 
		wrti[0].setFieldName("fphm");//选择发票
		wrti[0].setFieldValue(fp);
		wrti[0].setView(true);//字段是否可见
		wrti[0].setEdit(true);//字段是否可编辑
			
		
		wrti[1] = new WorkflowRequestTableField(); 
		wrti[1].setFieldName("fpje");//发票金额
		wrti[1].setFieldValue(je);
		wrti[1].setView(true);//字段是否可见
		wrti[1].setEdit(true);//字段是否可编辑
		
			
		wrti[2] = new WorkflowRequestTableField(); 
		wrti[2].setFieldName("fygzq");//发票日期
		wrti[2].setFieldValue(fyrq);
		wrti[2].setView(true);//字段是否可见
		wrti[2].setEdit(true);//字段是否可编辑	

		
		tablre[i] = new WorkflowRequestTableRecord();
		tablre[i].setWorkflowRequestTableFields(wrti);
	}

	//添加到明细表中
	WorkflowDetailTableInfo WorkflowDetailTableInfo[] = new WorkflowDetailTableInfo[2];
	WorkflowDetailTableInfo[1] = new WorkflowDetailTableInfo();
	WorkflowDetailTableInfo[1].setWorkflowRequestTableRecords(tablre);
	info.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
	WorkflowServiceImpl wsi = new WorkflowServiceImpl();
	int requestid = Integer.parseInt(wsi.doCreateWorkflowRequest(info, _userid));
	new weaver.general.BaseBean().writeLog("============_userid:"+_userid+",requestid:"+requestid+"============");
	resultObj.put("requestid", requestid);
	resultObj.put("status", "1");
} catch (Exception ex) {
	ex.printStackTrace();
	resultObj.put("status", "0");
	resultObj.put("errMsg", ex.getMessage());
}finally{
	try{
		out.print(resultObj.toString());
		out.flush();
	}catch(IOException ex){
		ex.printStackTrace();
	}
}
%>
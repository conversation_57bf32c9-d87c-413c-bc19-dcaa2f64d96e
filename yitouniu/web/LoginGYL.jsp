<%@page import="com.alibaba.fastjson.JSON" %>
<%@page import="com.ryytn.GYLService" %>
<%@page import="weaver.hrm.HrmUserVarify" %>
<%@    page import="weaver.hrm.User" %>

<%@page contentType="text/html;charset=utf-8" %>
<!DOCTYPE html >
<html>
<head>
    <meta http-equiv="Access-Control-Allow-Origin" content="*">
    <script src="/ryytn/jquery.min.js" type="text/javascript"></script>
    <script type="text/javascript">
        jQuery(document).ready(function () {

        })
    </script>
</head>
<body>
<%
    User user = HrmUserVarify.getUser(request, response);

    out.println(JSON.toJSONString(user));
    GYLService ehrService = new GYLService();
    try {
        String url = ehrService.login(user.getLoginid());
        response.sendRedirect(url);
    }
    catch (Exception e) {
        e.printStackTrace();
    }
%>
</body>
</html>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="com.alibaba.fastjson.JSONObject" %>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%
    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    String ids = Util.null2String(request.getParameter("ids"));
    baseBean.writeLog("ContractBatchDownload:ids=");
    baseBean.writeLog("ContractBatchDownload:ids = "+ids);
    String url = "http://oa.ryytngroup.com:8088";
    String docid = "";
    rs.executeQuery("select htfj from uf_httzb where id in ("+ids+")");
    while (rs.next()){
        docid = docid + rs.getString("htfj") + ",";
    }
    docid = docid.substring(0,docid.length()-1);
    baseBean.writeLog("ContractBatchDownload:docid = "+docid);
    JSONObject result = new JSONObject();
    result.put("docids", docid);

    out.print(result.toString());
//    response.sendRedirect(url+download);
//    response.sendRedirect(url);

%>


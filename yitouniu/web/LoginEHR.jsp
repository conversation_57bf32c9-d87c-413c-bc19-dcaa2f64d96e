<%@page import="java.net.URLEncoder"%>
<%@page import="weaver.hrm.HrmUserVarify"%>
<%@page import="com.ryytn.EHRService"%>
<%@	page import="java.util.*"  %>
<%@	page import="weaver.general.Util" %>
<%@	page import="weaver.hrm.User" %>

<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%@page contentType="text/html;charset=utf-8"%>
<!DOCTYPE html >
<html>
<head>
<meta http-equiv="Access-Control-Allow-Origin" content="*">
<script src="/ryytn/jquery.min.js" type="text/javascript"></script>
<script type="text/javascript">
jQuery(document).ready(function(){
	//window.location.href="/rrytn/LoginEHR.jsp?workflowid="+WfForm.BaseInfo().workflowid+"&mobile=1";
})

</script>
</head>
<body>
<%
User user = HrmUserVarify.getUser (request , response) ;
String userid = user.getUID()+"";
String workflowid = Util.null2String(request.getParameter("workflowid"));
String mobile = Util.null2String(request.getParameter("mobile"));

EHRService  ehrService = new EHRService();

String loginId = user.getLoginid();
String tokenId = ehrService.getToken(loginId);
out.println(loginId);
//String ret = ehrService.loginEHR(loginId, "");
//out.println(ret);
String ip = "http://adoptacow.51hrc.cn";
String url = "/RedseaPlatform/vwork/third/api/sso.mob?method=oauthLogin&token="+tokenId+"&client=pc&action=login";
if("".equals(workflowid)){
	response.sendRedirect(ip+url);
}else{
	rs.executeSql("select * from uf_wf2ehr where 	oalc = '"+workflowid+"' ");//
	String ehrddurl = "";
	if(rs.next()){
		ehrddurl = rs.getString("ehrddurl");
		if("1".equals(mobile)){
			ehrddurl = rs.getString("EMehrddurl");
		}
	}
	ehrddurl = URLEncoder.encode(ehrddurl,"UTF-8");
	rs.writeLog(ip+url+"&gotourl="+ehrddurl);
	response.sendRedirect(ip+url+"&gotourl="+ehrddurl);
}

%>
</body>
</html>
<%@ page language="java" contentType="application/json; charset=UTF-8"%>
<%@ page import="net.sf.json.JSONArray"%>
<%@ page import="net.sf.json.JSONObject"%>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.workflow.webservices.*" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="java.io.IOException" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.util.HashMap" %>
<%@ page import="java.util.Map" %><%@ page import="java.nio.charset.StandardCharsets"%>
<%!
	public static String fetchPostByTextPlain(HttpServletRequest request) {
        StringBuilder buffer = new StringBuilder();
        try{
            request.setCharacterEncoding("UTF-8");
            BufferedReader reader=null;
            reader = new BufferedReader(new InputStreamReader(request.getInputStream(),StandardCharsets.UTF_8));
            String line=null;
            while((line = reader.readLine())!=null){
                buffer.append(line);
            }
        }catch (IOException e) {
            System.out.println(e);
        }
        return buffer.toString();
    }
	
	public static Map getUserInfo(String uid) {
		Map map = new HashMap();
        String sql = "select * from hrmresource where workcode='"+uid+"'";
        RecordSet rs = new RecordSet();
        rs.execute(sql);
        while (rs.next()) {
			map.put("id",rs.getString("id"));
			map.put("subcompanyid1",rs.getString("subcompanyid1"));
			map.put("departmentid",rs.getString("departmentid"));
			map.put("lastname",rs.getString("lastname"));
            return map;
        }
        return null;
    }
%>
<%		//集昕销售订单SAP推送至OA 
		String jsonstr = Util.null2String(fetchPostByTextPlain(request));
		new BaseBean().writeLog("参数:"+jsonstr);
		JSONObject result = new JSONObject();
		if(!"".equals(jsonstr)){
			String errMessage = "200";
			try {
				JSONObject json = JSONObject.fromObject(jsonstr);
				//获取申请人字段（员工工号）
				String usercode = json.getString("EMPID");
				Map user = getUserInfo(usercode);
			
				WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
				//赋值方式
				errMessage = "【申请人】字段获取失败（EMPID）";
				wrti[0]  = new WorkflowRequestTableField();
				wrti[0].setFieldName("EMPID"); //申请人
				wrti[0].setFieldValue(Util.null2String(user.get("id")));
				wrti[0].setView(true);
				wrti[0].setEdit(true);


				errMessage = "【销售组织】字段获取失败（VKORG）";
				wrti[1]  = new WorkflowRequestTableField();
				wrti[1].setFieldName("VKORG"); //销售组织
				wrti[1].setFieldValue(json.getString("VKORG"));
				wrti[1].setView(true);
				wrti[1].setEdit(true);


				WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
				wrtri[0] = new WorkflowRequestTableRecord();
				wrtri[0].setWorkflowRequestTableFields(wrti);
				WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
				wmi.setRequestRecords(wrtri);
				
				//添加明细
				JSONArray jsonarr = json.getJSONArray("detailtableinfo"); //获取明细数据
				int detailrows = jsonarr.size() ;//添加指定条数明细
				//添加明细数据
				wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
				for(int i = 0 ; i < detailrows ; i++){
					JSONObject jsondetail = jsonarr.getJSONObject(i); //明细数据
					wrti = new WorkflowRequestTableField[21]; //字段个数   **************
					
					errMessage = "【销售凭证项目 】字段获取失败（POSNR）";
					wrti[0]  = new WorkflowRequestTableField();
					wrti[0].setFieldName("POSNR"); //销售凭证项目 
					wrti[0].setFieldValue(jsondetail.getString("POSNR"));
					wrti[0].setView(true);//字段是否可见
					wrti[0].setEdit(true);//字段是否可编辑
					
					errMessage = "【物料编码】字段获取失败（MATNR）";
					wrti[1]  = new WorkflowRequestTableField();
					wrti[1].setFieldName("MATNR"); //物料编码
					wrti[1].setFieldValue(jsondetail.getString("MATNR"));
					wrti[1].setView(true);
					wrti[1].setEdit(true);
					
					errMessage = "【销售订单项目短文本】字段获取失败（ARKTX）";
					wrti[2]  = new WorkflowRequestTableField();
					wrti[2].setFieldName("ARKTX"); //销售订单项目短文本
					wrti[2].setFieldValue(jsondetail.getString("ARKTX"));
					wrti[2].setView(true);
					wrti[2].setEdit(true);

				
					wrtri[i] = new WorkflowRequestTableRecord();
					wrtri[i].setWorkflowRequestTableFields(wrti);
				}
			
			//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
			WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
			WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
			WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri); 
			//流程基本信息设置
			WorkflowBaseInfo wbi = new WorkflowBaseInfo();
			wbi.setWorkflowId("35521");//流程请求id
			WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
			wri.setCreatorId((String)user.get("id"));//创建人id
			wri.setIsnextflow("0"); //停留在创建节点
			wri.setRequestLevel("0");//0 正常，1重要，2紧急
			wri.setRequestName("销售订单审批流程-"+Util.null2String(user.get("lastname")));//流程标题
			//添加主字段数据
			wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
			wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
			wri.setWorkflowBaseInfo(wbi);
			WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
			String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt((String)user.get("id"))));
			if(!"".equals(requestid)){
				if(Integer.parseInt(requestid) >0){
					result.put("status", "0");
					result.put("requestid", requestid);
				}else{
					result.put("status", "1");
					result.put("requestid", requestid);
				}
			}
			out.println(result.toString());
			out.flush();
			} catch (Exception e) {
             	// 使用众多异常的父类Exception,去捕获其所有子类异常
			 	result.put("status", "1");
			 	result.put("requestid", errMessage);
             	new BaseBean().writeLog("集昕销售订单审批流程：" + e);
			 	out.println(result.toString());
			 	out.flush();
			}
	}else{
		result.put("status", "1");
		result.put("requestid", "-9");
		out.println(result.toString());
		out.flush();
	}
		
%>

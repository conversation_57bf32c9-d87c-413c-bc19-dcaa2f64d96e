<%@page import="java.net.URLEncoder"%>
<%@page import="weaver.hrm.HrmUserVarify"%>
<%@page import="com.ryytn.EHRService"%>
<%@	page import="java.util.*"  %>
<%@	page import="weaver.general.Util" %>
<%@	page import="weaver.hrm.User" %>
<%@ page import="weaver.general.BaseBean" %>

<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%@page contentType="text/html;charset=utf-8"%>
<!DOCTYPE html >
<html>
<head>
<meta http-equiv="Access-Control-Allow-Origin" content="*">
<script src="/ryytn/jquery.min.js" type="text/javascript"></script>
<script type="text/javascript">
</script>
</head>
<body>
<%
User user = HrmUserVarify.getUser(request, response) ;
String userid = user.getUID()+"";
String processId = Util.null2String(request.getParameter("processId"));
BaseBean baseBean = new BaseBean();
baseBean.writeLog("LoginRedirectEHR:processId=" + processId);

EHRService ehrService = new EHRService();

String loginId = user.getLoginid();
String tokenId = ehrService.getToken(loginId);

String ip = "http://adoptacow.51hrc.cn";
String url = "/RedseaPlatform/vwork/third/api/sso.mob?method=oauthLogin&token="+tokenId+"&client=pc&action=login";
if("".equals(processId)){
	response.sendRedirect(ip+url);
}else{
	rs.executeSql("select process_link from uf_travel_info where process_id = '"+processId+"' ");//
	String ehrddurl = "";
	if(rs.next()){
		ehrddurl = rs.getString("process_link");
	}
	ehrddurl = URLEncoder.encode(ehrddurl,"UTF-8");
	baseBean.writeLog("LoginRedirectEHR:"+ip+url+"&gotourl="+ehrddurl);
	response.sendRedirect(ip+url+"&gotourl="+ehrddurl);
}
// http://adoptacow.51hrc.cn/RedseaPlatform/vwork/third/api/sso.mob?method=oauthLogin&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJVU0VSX0lEIjoiOTQ2MjExNjVmMjk3NDc3Yzk0YTkxNjMyZmUzNDU3NjYiLCJleHAiOjE2NTg5MjA2MzAsImlhdCI6MTY1ODkyMDIxMH0.cf6B7ZEFdb53TBzRmPMC5jZ_rnWKP26FdXXiZCuUvZA&client=pc&action=login&gotourl=%2FRedseaPlatform%2Fjsp%2FworkFlow%2Fapp%2Findex.jsp%3FmsgType%3Dcreate_wf%26formId%3Db5dbe8dd-a958-4128-b869-6f8179cded22%26%26packageId%3Dadoptacow%26defProcessId%3D68121-2-20220702171442%26version%3D6.0
%>
</body>
</html>
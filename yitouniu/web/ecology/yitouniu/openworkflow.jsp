<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%@ page import="weaver.general.Util,weaver.conn.RecordSet" %>
<%@ page import="weaver.integration.logging.Logger"%>
<%@ page import="weaver.integration.logging.LoggerFactory"%>
<%@ page import="java.util.*" %>
<%@ page import="java.io.IOException" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="java.io.OutputStreamWriter" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.net.URLConnection" %>
<%@ page import="yitouniu.hec.util.EncryptionApiUtil" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.User" %>
<%
	String requestid = request.getParameter("requestid");
    User user = HrmUserVarify.getUser(request,response) ;
	String userid = user.getUID()+"";
	RecordSet recordSet = new RecordSet();
	recordSet.executeQuery("select id from uf_zlhtcs where lcid = ?",requestid);
	recordSet.next();
	String formmodebillId = recordSet.getString("id");	
	
	BaseBean baseBean = new BaseBean();

	String urlNew = "http://************:28088/spa/workflow/index_form.jsp#/main/workflow/req?moduleid=formmode&authorizemodeId=3077&requestid="+requestid+"&authorizefieldid=20004&formmode_authorize=formmode_authorize&authorizeformmodebillId="+formmodebillId;
	
%>
<script type="text/javascript">

    location.replace('<%=urlNew%>');

</script>

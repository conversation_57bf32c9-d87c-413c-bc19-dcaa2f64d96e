<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%@ page import="weaver.general.Util,weaver.conn.RecordSet" %>
<%@ page import="weaver.integration.logging.Logger"%>
<%@ page import="weaver.integration.logging.LoggerFactory"%>
<%@ page import="java.util.*" %>
<%@ page import="java.io.IOException" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="java.io.OutputStreamWriter" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.net.URLConnection" %>
<%@ page import="yitouniu.hec.util.EncryptionApiUtil" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.User" %>
<%
    User user = HrmUserVarify.getUser(request,response) ;
    String userid = user.getUID()+"";
    RecordSet recordSet = new RecordSet();
    recordSet.executeQuery("select workcode from hrmresource where id = ?",userid);
    recordSet.next();
    String workcode = recordSet.getString("workcode");

    BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    String urlNew = "";

    String sql = "select * from uf_xtqzsz";
    rs.executeQuery(sql);
    rs.next();
    String tourl=rs.getString("qzdz")+"/workplace";
    Long time = Long.valueOf(100000);
    EncryptionApiUtil encryptionApiUtil = new EncryptionApiUtil();
    baseBean.writeLog("tourl："+tourl);
    baseBean.writeLog("workcode："+workcode);
    try {
        urlNew = encryptionApiUtil.getSsoUrl(tourl,workcode,time);
    }catch (Exception e){
        baseBean.writeLog("点单hec首页出错啦："+e);
    }
    baseBean.writeLog("点单hec首页地址："+urlNew);
%>
<script type="text/javascript">

    location.replace('<%=urlNew%>');

</script>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%@ page import="weaver.general.Util,weaver.conn.RecordSet" %>
<%@ page import="weaver.integration.logging.Logger"%>
<%@ page import="weaver.integration.logging.LoggerFactory"%>
<%@ page import="java.util.*" %>
<%@ page import="java.io.IOException" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="java.io.OutputStreamWriter" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.net.URLConnection" %>
<%@ page import="yitouniu.hec.util.EncryptionApiUtil" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.general.BaseBean" %>
<%
    User user = HrmUserVarify.getUser(request,response) ;
	String userid = user.getUID()+"";
	RecordSet recordSet = new RecordSet();
	recordSet.executeQuery("select workcode from hrmresource where id = ?",userid);
	recordSet.next();
	String workcode = recordSet.getString("workcode");	
	BaseBean baseBean = new BaseBean();
String workflowid = request.getParameter("workflowid");
RecordSet rs = new RecordSet();
String sql = "select tzdz from uf_hecrkjc where lcid = '"+workflowid+"'";
rs.executeSql(sql);
String tzdz = "";
if(rs.next()){
    tzdz += rs.getString("tzdz");
}
sql = "select * from uf_xtqzsz";
rs.executeQuery(sql);
rs.next();
tzdz=rs.getString("qzdz")+tzdz;
String urlNew = "";
Long time = Long.valueOf(100000);
try {
	EncryptionApiUtil encryptionApiUtil = new EncryptionApiUtil();
	urlNew = encryptionApiUtil.getSsoUrl(tzdz,workcode,time);
	System.out.println("登录url"+urlNew);
}catch (Exception e){
	 baseBean.writeLog("PC端跳转hec出错啦："+e);
}
%>
<script type="text/javascript">

    location.replace('<%=urlNew%>');

</script>
<%@ page language="java" contentType="text/html; charset=UTF-8" %>

<%@ page import="weaver.general.Util,weaver.conn.RecordSet" %>
<%@ page import="weaver.integration.logging.Logger"%>
<%@ page import="weaver.integration.logging.LoggerFactory"%>
<%@ page import="java.util.*" %>
<%@ page import="java.io.IOException" %>
<%@ page import="com.alibaba.fastjson.JSON" %>
<%@ page import="java.io.BufferedReader" %>
<%@ page import="java.io.OutputStreamWriter" %>
<%@ page import="java.net.URL" %>
<%@ page import="java.io.InputStreamReader" %>
<%@ page import="java.net.URLConnection" %>
<%@ page import="yitouniu.hec.util.EncryptionApiUtil" %>
<%@ page import="weaver.general.BaseBean" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.User" %>
<%
    User user = HrmUserVarify.getUser(request,response) ;
	String userid = user.getUID()+"";
	RecordSet recordSet = new RecordSet();
	recordSet.executeQuery("select workcode from hrmresource where id = ?",userid);
	recordSet.next();
	String workcode = recordSet.getString("workcode");	
	
	BaseBean baseBean = new BaseBean();
    RecordSet rs = new RecordSet();

    String tododataid = request.getParameter("tododataid");
    baseBean.writeLog("tododataid="+tododataid);
    rs.executeQuery("select * from ofs_todo_data where id = ?",tododataid);
    rs.next();
    String pcurlsrc =  Util.null2String(rs.getString("pcurlsrc"));
    String sysid = rs.getString("sysid");




    rs.executeQuery("select * from ofs_sysinfo where sysid = ?" , sysid);
    if(rs.next()){
        String Pcprefixurl = Util.null2String(rs.getString("Pcprefixurl"));
        String urlNew = "";
		String tourl=Pcprefixurl+pcurlsrc;
        //TODO 1 调用hec接口获取免登地址

		Long time = Long.valueOf(10000);
        EncryptionApiUtil encryptionApiUtil = new EncryptionApiUtil();
        
        try {
            urlNew = encryptionApiUtil.getSsoUrl(tourl,"c010151",time);
        }catch (Exception e){
             baseBean.writeLog("PC端跳转hec出错啦："+e);
        }

       
        baseBean.writeLog("PC端跳转hec地址："+tourl);
		//out.print(userid+"  ==>  "+workcode);
%>
<script type="text/javascript">

    location.replace('<%=urlNew%>');

</script>
<%
    }else{
        baseBean.writeLog("根据标识："+sysid+"未查询到数据");
        return;
    }
%>
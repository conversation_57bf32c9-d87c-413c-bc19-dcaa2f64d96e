<%@ page language="java" contentType="text/html; charset=UTF-8" %>
<%@ page language="java" import="java.util.*"%>
<%@ page import="java.sql.*" %>
<%@ page import="weaver.conn.RecordSet" %>
<%@ page import="weaver.general.BaseBean" %>

<%
RecordSet rs = new RecordSet();
String workflowid = request.getParameter("workflowid");
String sql = "select workflowname from workflow_base where id = '"+workflowid+"'";
rs.executeSql(sql);
String workflowname = "";
while(rs.next()){
    workflowname = rs.getString("workflowname")+"";
}

String returnStr = workflowname+"(HEC)";
out.print(returnStr);
%>
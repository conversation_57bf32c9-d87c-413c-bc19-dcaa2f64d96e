jQuery(document).ready(function () {
    var d1Field = {
        // 公司编码
        BUKRS: WfForm.convertFieldNameToId("BUKRS"),
        // 固定资产编码
        txt50: WfForm.convertFieldNameToId("txt50", "detail_1"),
        aktiv: WfForm.convertFieldNameToId("aktiv", "detail_1"),
        answl: WfForm.convertFieldNameToId("answl", "detail_1"),
        answx: WfForm.convertFieldNameToId("answx", "detail_1"),
        name1: WfForm.convertFieldNameToId("name1", "detail_1"),
        month: WfForm.convertFieldNameToId("month", "detail_1"),
        anln1: WfForm.convertFieldNameToId("anln1", "detail_1")

    };


    WfForm.bindDetailFieldChangeEvent(d1Field.txt50, function (id, rowIndex, value) {
        var BUKRS = WfForm.getFieldValue(d1Field.BUKRS);
        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF013',
                params: JSON.stringify({BUKRS: BUKRS, TXT50: '', ANLN1: value})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];

                WfForm.changeFieldValue(d1Field.anln1 + "_" + rowIndex, {

                    value: supplierAccount.ANLN1
                });

                WfForm.changeFieldValue(d1Field.aktiv + "_" + rowIndex, {

                    value: supplierAccount.AKTIV
                });
                WfForm.changeFieldValue(d1Field.answl + "_" + rowIndex, {

                    value: supplierAccount.ANSWL
                });
                WfForm.changeFieldValue(d1Field.answx + "_" + rowIndex, {

                    value: supplierAccount.ANSWX
                });
                WfForm.changeFieldValue(d1Field.name1 + "_" + rowIndex, {

                    value: supplierAccount.NAME1
                });
                WfForm.changeFieldValue(d1Field.month + "_" + rowIndex, {

                    value: supplierAccount.MONTH
                });
            }
        });
    });
});
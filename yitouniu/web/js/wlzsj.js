var d1Field = {
    // 公司
    sx: WfForm.convertFieldNameToId("sx"),
    // 事项
    szdw: WfForm.convertFieldNameToId("szdw"),
    // 事项
    szdwsj: WfForm.convertFieldNameToId("szdwsj"),

};

display();


WfForm.bindFieldChangeEvent(d1Field.sx+","+d1Field.szdw, function (obj,id,value) {
    var szdw = '';
    var szdwid= WfForm.getFieldValue(d1Field.szdw); // 所属公司
    var szdwsj= WfForm.getFieldValue(d1Field.szdwsj); // 所属公司

    if (szdwsj=='0'){
        szdw = szdwid;
    }else {
        szdw= szdwsj
    }
    var sx= WfForm.getFieldValue(d1Field.sx); // 事项

    if(sx =='2'){
        jQuery("#wl").show();
        jQuery("#cy").hide();
        jQuery("#my").hide();
        jQuery("#yx").hide();
    }else if ((sx!=null&&sx!='')&&sx !='2'&&(szdw=='17'||szdw=='18')){
        jQuery("#wl").hide();
        jQuery("#cy").hide();
        jQuery("#my").hide();
        jQuery("#yx").show();
    }else if((sx!=null&&sx!='')&&sx !='2'&&szdw=='33'){
        jQuery("#wl").hide();
        jQuery("#cy").show();
        jQuery("#my").hide();
        jQuery("#yx").hide();
    }else if ((sx!=null&&sx!='')&&sx !='2'&&szdw=='22'){
        jQuery("#wl").hide();
        jQuery("#cy").hide();
        jQuery("#my").show();
        jQuery("#yx").hide();
    }else {
        display();
    }

});

function display(){
    jQuery("#wl").hide();
    jQuery("#cy").hide();
    jQuery("#my").hide();
    jQuery("#yx").hide();
}
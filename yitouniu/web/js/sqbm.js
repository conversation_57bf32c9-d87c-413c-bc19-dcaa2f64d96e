var d1Field = {
    // 创建人
    UNAME: WfForm.convertFieldNameToId("UNAME"),
    // 申请部门
    sqbm: WfForm.convertFieldNameToId("sqbm"),


}

var nameValue= WfForm.getFieldValue(d1Field.UNAME); // 申请人
var requestid= WfForm.getBaseInfo().requestid; //



    var options = {
        url: '/niu/jsp/GetDeptID.jsp',
        method: 'POST',
        params: {
            id: nameValue,
            requestid: requestid,

        }
    };
    weaJs.callApi(options).then(function (res) {





    });








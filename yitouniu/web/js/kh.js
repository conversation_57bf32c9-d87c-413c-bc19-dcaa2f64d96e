jQuery(document).ready(function () {

    var d1Field = {
        // 供应商编码id
        PARTNER: WfForm.convertFieldNameToId("KUNNR"),
        // 供应商名称id
        NAME1: WfForm.convertFieldNameToId("NAME1"),
        // 贸易伙伴id
        VBUND: WfForm.convertFieldNameToId("VBUND")

    }

    var nameValue = WfForm.getFieldValue(d1Field.NAME1);
    //yhzh(nameValue);
    WfForm.bindFieldChangeEvent(d1Field.PARTNER, function (obj,id,value) {

        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF002',
                params: JSON.stringify({NAME1: '', PARTNER: value})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];
                var name = supplierAccount.NAME1;
                //yhzh(name);
                WfForm.changeFieldValue(d1Field.NAME1, {

                    value: supplierAccount.NAME1
                });
                WfForm.changeFieldValue(d1Field.VBUND, {

                    value: supplierAccount.VBUND
                });

            }
        });
    });


});
function yhzh(name){
    if(name.indexOf("一次性")!=-1){
        jQuery("#dg").hide();
        jQuery("#lj").show();
    }else {
        jQuery("#dg").show();
        jQuery("#lj").hide();
    }

}


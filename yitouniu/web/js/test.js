
jQuery(document).ready(function () {

    var d1Field = {
        // 内部订单
        AUFNR: WfForm.convertFieldNameToId("AUFNR", "detail_1"),
        // 订单类型
        AUART: WfForm.convertFieldNameToId("AUART", "detail_1"),
        // 税率
        taxRate1: WfForm.convertFieldNameToId("taxRate1", "detail_1"),
        // 价税合计
        taxRate: WfForm.convertFieldNameToId("taxRate", "detail_1"),
        // 价税合计
        fp: WfForm.convertFieldNameToId("fp", "detail_1"),
        // 供应商编码
        gysyxmc: WfForm.convertFieldNameToId("gysyxmc"),
        // 供应商账号
        yxmc: WfForm.convertFieldNameToId("yxmc"),
        // 供应商账号名称
        skzh: WfForm.convertFieldNameToId("skzh"),
        // 供应商
        PARTNER: WfForm.convertFieldNameToId("PARTNER"),

        // 科目
        hjkm: WfForm.convertFieldNameToId("hjkm", "detail_1"),
        // 科目
        zflx: WfForm.convertFieldNameToId("zflx"),
    }
// 判断是否有二次提交

    WfForm.registerCheckEvent(WfForm.OPER_SAVE+","+WfForm.OPER_SUBMIT, function(callback){
        var hs = WfForm.getDetailRowCount("detail_1");
        var result = 'true';
        for(var i = 0;i<hs;i++){
            var km= WfForm.getFieldValue(d1Field.hjkm+"_"+ i);

            if(km=='167'){
                for(var j = 0;j<hs;j++){
                    var kmlx= WfForm.getFieldValue(d1Field.hjkm+"_"+ j);
                    if(kmlx!=km){
                        result = 'false';
                        weaJs.alert("流程为二次提交不允许填其他科目，请重新选择。");
                        break;
                    }
                }

            }
        }
        if(result =='true'){
            callback();    //继续提交需调用callback，不调用代表阻断
        }


    });

// 供应商带出银行账号
    WfForm.bindFieldChangeEvent(d1Field.gysyxmc, function (obj,id,value) {
        var parValue= WfForm.getFieldValue(d1Field.PARTNER);
        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF002YH',
                params: JSON.stringify({BANKN: value, PARTNER: parValue})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];

                WfForm.changeFieldValue(d1Field.yxmc, {

                    value: supplierAccount.BANKA
                });
                WfForm.changeFieldValue(d1Field.skzh, {

                    value: supplierAccount.BANKN
                });

            }
        });
    });
// 根据税率文本带出税率
    WfForm.bindDetailFieldChangeEvent(d1Field.fp+","+d1Field.taxRate,
        function(id, rowIndex, value) {
            var taxRate= WfForm.getFieldValue(d1Field.taxRate+"_"+ rowIndex);

            if(taxRate>0){
                submit(taxRate,rowIndex,d1Field.taxRate1);
            } else{
                WfForm.changeFieldValue(d1Field.taxRate1+"_" + rowIndex, {
                    value:  ''
                });
            }



        });
// 内部订单获取订单类型
    WfForm.bindDetailFieldChangeEvent(d1Field.AUFNR, function (id, rowIndex, value) {
        if(value!=''){
            var options = {
                url: '/api/esb/execute',
                method: 'POST',
                params: {
                    timestamp: new Date().getTime(),
                    password: 'ryytnOA123',
                    username: 'sysadmin',
                    appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                    eventkey: 'ZINF014',
                    params: JSON.stringify({ AUFNR: value})

                }
            };
            weaJs.callApi(options).then(function (res) {
                if (res.data.list.length > 0) {
                    var supplierAccount = res.data.list[0];

                    WfForm.changeFieldValue(d1Field.AUART+"_"+rowIndex, {

                        value: supplierAccount.AUART
                    });

                }
            });
        }else{
            WfForm.changeFieldValue(d1Field.AUART+"_"+rowIndex, {

                value: ""
            });
        }
    });

    var  zflxValue = WfForm.getFieldValue(d1Field.zflx);
    play(d1Field.yxmc,d1Field.skzh,zflxValue);
    // 根据付款类型显示隐藏
    WfForm.bindFieldChangeEvent(d1Field.zflx, function (obj,id,value) {

        play(d1Field.yxmc,d1Field.skzh,value);
    });
});

function play(skdwkhx,skdwzh,value){
    if(value=='1'){
        jQuery("#dg").hide();
        jQuery("#lj").show();
        WfForm.changeFieldValue(skdwkhx, {

            value: '虚拟账号'
        });
        WfForm.changeFieldValue(skdwzh, {

            value: '999999999999999999'
        });
    }else {
        jQuery("#lj").hide();
        jQuery("#dg").show();
        WfForm.changeFieldValue(skdwkhx, {
            value: ''
        });
        WfForm.changeFieldValue(skdwzh, {

            value: ''
        });
    }
}
function submit(slwb,rowIndex,taxRate1) {
    var shuilv  = '';

    switch (slwb) {
        case '13.00':
            shuilv = 0;
            break;
        case '3.00':
            shuilv =  4;
            break;
        case '5.00':
            shuilv =  3;
            break;
        case '6.00':
            shuilv =  2;
            break;
        case '9.00':
            shuilv =  1;
            break;
        case '0.00':
            shuilv =  5;
            break;
        case '1.00':
            shuilv =  6;
            break;
    }

    WfForm.changeFieldValue(taxRate1+"_" + rowIndex, {
        value:  shuilv
    });
}




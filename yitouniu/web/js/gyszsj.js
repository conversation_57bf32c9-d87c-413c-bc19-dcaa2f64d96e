/**
 * 供应商主数据
 * @type {{sx: *, szdw: *, szdwsj: *}}
 */

var d1Field = {

    sx: WfForm.convertFieldNameToId("sx"),


};
var idValue = WfForm.getFieldValue(d1Field.sx);

display(idValue);


WfForm.bindFieldChangeEvent(d1Field.sx, function (obj,id,value) {

    display(value);

});

function display(value){
    if(value =='0'||value =='1'){
        jQuery("#mb1").show();
        jQuery("#mb2").hide();

    }else if (value =='2'){
        jQuery("#mb1").hide();
        jQuery("#mb2").show();

    }else {
        display();
    }

}
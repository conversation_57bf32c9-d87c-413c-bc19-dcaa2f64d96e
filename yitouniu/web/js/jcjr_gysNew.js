// 借出借入获取供应商

jQuery(document).ready(function () {

    var d1Field = {
        // 借出供应商编码id
        kunnr: WfForm.convertFieldNameToId("kunnr"),

        // 贸易伙伴名称
        NAME1: WfForm.convertFieldNameToId("NAME1"),
        // 贸易伙伴id
        BUKRS: WfForm.convertFieldNameToId("BUKRS"),
        // 还款单位名称
        NAME2: WfForm.convertFieldNameToId("NAME2"),
        // 还款单位公司代码
        VBUND: WfForm.convertFieldNameToId("VBUND"),
        // 还款单位
        partner: WfForm.convertFieldNameToId("partner"),
        // 还款单位开户行
        hkdwkhx: WfForm.convertFieldNameToId("hkdwkhx"),
        // 还款单位银行账号
        hkdwyxzh: WfForm.convertFieldNameToId("hkdwyxzh"),
        // 收款单位开户行
        skdwkhx: WfForm.convertFieldNameToId("skdwkhx"),
        // 收款单位银行账号
        skdwyxzh: WfForm.convertFieldNameToId("skdwyxzh"),
        //
        hkdwyxzh1: WfForm.convertFieldNameToId("hkdwyxzh1"),
        //
        skdwyxzh1: WfForm.convertFieldNameToId("skdwyxzh1"),


    };

// 收款单位
    WfForm.bindFieldChangeEvent(d1Field.kunnr, function (obj, id, value) {
        if (value != '') {
            var options = {
                url: '/api/esb/execute',
                method: 'POST',
                params: {
                    timestamp: new Date().getTime(),
                    password: 'ryytnOA123',
                    username: 'sysadmin',
                    appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                    eventkey: 'ZINF002GYS',
                    params: JSON.stringify({NAME1: '', PARTNER: value})

                }
            };
            weaJs.callApi(options).then(function (res) {
                if (res.data.list.length > 0) {
                    var supplierAccount = res.data.list[0];


                    WfForm.changeFieldValue(d1Field.BUKRS, {

                        value: supplierAccount.VBUND
                    });
                    WfForm.changeFieldValue(d1Field.NAME1, {

                        value: supplierAccount.NAME1
                    });

                }
            });

        }
    });

    // 还款单位
    WfForm.bindFieldChangeEvent(d1Field.partner, function (obj, id, value) {
        if (value != '') {
            var options = {
                url: '/api/esb/execute',
                method: 'POST',
                params: {
                    timestamp: new Date().getTime(),
                    password: 'ryytnOA123',
                    username: 'sysadmin',
                    appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                    eventkey: 'ZINF002GYS',
                    params: JSON.stringify({NAME1: '', PARTNER: value})

                }
            };
            weaJs.callApi(options).then(function (res) {
                if (res.data.list.length > 0) {
                    var supplierAccount = res.data.list[0];


                    WfForm.changeFieldValue(d1Field.VBUND, {

                        value: supplierAccount.VBUND
                    });
                    WfForm.changeFieldValue(d1Field.NAME2, {

                        value: supplierAccount.NAME1
                    });

                }
            });

        }
    });

    // 还款供应商带出银行账号
    WfForm.bindFieldChangeEvent(d1Field.hkdwyxzh1, function (obj, id, value) {
        var parValue = WfForm.getFieldValue(d1Field.partner);
        if (value != '') {
            var options = {
                url: '/api/esb/execute',
                method: 'POST',
                params: {
                    timestamp: new Date().getTime(),
                    password: 'ryytnOA123',
                    username: 'sysadmin',
                    appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                    eventkey: 'ZINF002YH',
                    params: JSON.stringify({BANKN: value, PARTNER: parValue})

                }
            };
            weaJs.callApi(options).then(function (res) {
                if (res.data.list.length > 0) {
                    var supplierAccount = res.data.list[0];

                    WfForm.changeFieldValue(d1Field.hkdwkhx, {

                        value: supplierAccount.BANKA
                    });
                    WfForm.changeFieldValue(d1Field.hkdwyxzh, {

                        value: supplierAccount.BANKN
                    });

                }
            });
        }
    });

// 供应商带出银行账号
    WfForm.bindFieldChangeEvent(d1Field.skdwyxzh1, function (obj, id, value) {
        var parValue = WfForm.getFieldValue(d1Field.kunnr);
        if (value != '') {
            var options = {
                url: '/api/esb/execute',
                method: 'POST',
                params: {
                    timestamp: new Date().getTime(),
                    password: 'ryytnOA123',
                    username: 'sysadmin',
                    appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                    eventkey: 'ZINF002YH',
                    params: JSON.stringify({BANKN: value, PARTNER: parValue})

                }
            };
            weaJs.callApi(options).then(function (res) {
                if (res.data.list.length > 0) {
                    var supplierAccount = res.data.list[0];

                    WfForm.changeFieldValue(d1Field.skdwkhx, {

                        value: supplierAccount.BANKA
                    });
                    WfForm.changeFieldValue(d1Field.skdwyxzh, {

                        value: supplierAccount.BANKN
                    });

                }
            });
        }
    });


});

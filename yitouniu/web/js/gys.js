jQuery(document).ready(function () {

    var d1Field = {
        // 供应商编码id
        PARTNER: WfForm.convertFieldNameToId("PARTNER"),
        // 供应商编码id
        KUNNR: WfForm.convertFieldNameToId("KUNNR"),
        // 供应商名称id
        NAME1: WfForm.convertFieldNameToId("NAME1"),
        // 贸易伙伴id
        VBUND: WfForm.convertFieldNameToId("VBUND"),
        // 类别
        xzskflx: WfForm.convertFieldNameToId("xzskflx"),
        gysyxmc: WfForm.convertFieldNameToId("gysyxmc"),
        // 付款方式
        fkfs: WfForm.convertFieldNameToId("fkfs"),
        // 原因代码
        RSTGR1: WfForm.convertFieldNameToId("RSTGR","detail_1"),
        // 原因代码
        RSTGR3: WfForm.convertFieldNameToId("RSTGR","detail_3"),


    };

    var fkfsValue = WfForm.getFieldValue(d1Field.fkfs);
    if(fkfsValue=='1'){ // 付款方式 原因代码为只读
        var hs1= WfForm.getDetailRowCount("detail_1");
        for (var i= 0;i<hs1;i++){
            WfForm.changeFieldAttr(d1Field.RSTGR1+"_"+i, 1);  //字段修改为只读
        }
        var hs3= WfForm.getDetailRowCount("detail_3");
        for (var i= 0;i<hs3;i++){
            WfForm.changeFieldAttr(d1Field.RSTGR3+"_"+i, 1);  //字段修改为只读
        }
    }
    jQuery("#qdg").hide();

    var xzskflxValue = WfForm.getFieldValue(d1Field.xzskflx);

    disPlayData(xzskflxValue,d1Field.PARTNER,d1Field.KUNNR);


    WfForm.bindFieldChangeEvent(d1Field.xzskflx, function (obj,id,value) {

        disPlayData(value,d1Field.PARTNER,d1Field.KUNNR);
    });

    if(WfForm.getGlobalStore().commonParam.currentnodetype!='0'){
        jQuery("#dg").hide();
    }
    WfForm.bindFieldChangeEvent(d1Field.PARTNER, function (obj,id,value) {

        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF002GYS',
                params: JSON.stringify({NAME1: '', PARTNER: value})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];
                var name = supplierAccount.NAME1;
                //yhzh(name,d1Field.gysyxmc);

                WfForm.changeFieldValue(d1Field.NAME1, {

                    value: supplierAccount.NAME1
                });
                WfForm.changeFieldValue(d1Field.VBUND, {

                    value: supplierAccount.VBUND
                });

            }
        });
    });


});

function yhzh(name,gysyxmc){
    if(name.indexOf("一次性")!=-1){
        WfForm.changeFieldAttr(gysyxmc, 2);
        jQuery("#qdg").hide();
        jQuery("#qlj").show();
    }else {

        jQuery("#qdg").show();
        jQuery("#qlj").hide();
    }

}

function disPlayData(value,PARTNER,KUNNR){
    if(value=='0'){
        jQuery(".gys").show();
        jQuery(".kh").hide();
        WfForm.changeFieldValue(KUNNR, {

            value: ''
        });
    }else if(value=='1'){
        jQuery(".gys").hide();
        jQuery(".kh").show();
        WfForm.changeFieldValue(PARTNER, {

            value: ''
        });
    }else{
        jQuery(".gys").hide();
        jQuery(".kh").show();
    }

}

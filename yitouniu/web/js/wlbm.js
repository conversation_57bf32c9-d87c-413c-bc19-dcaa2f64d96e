jQuery(document).ready(function () {

    var d1Field = {
        // 物料编码id
        MATNR: WfForm.convertFieldNameToId("MATNR", "detail_1"),
       // 销售单位id
        VRKME: WfForm.convertFieldNameToId("VRKME","detail_1"),
        // 物料名称id
        TXZ01: WfForm.convertFieldNameToId("TXZ01", "detail_1"),
        // 客户编码id
        KUNAG: WfForm.convertFieldNameToId("KUNAG", "detail_1"),
        // 客户名称id
        KUNWE: WfForm.convertFieldNameToId("KUNWE", "detail_1")
    };


    WfForm.bindDetailFieldChangeEvent(d1Field.MATNR, function (id, rowIndex, value) {

        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF005',
                params: JSON.stringify({MAKTX: '', MATNR: value})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];
               
                WfForm.changeFieldValue(d1Field.TXZ01+"_"+rowIndex, {
                   
                    value: supplierAccount.MAKTX
                });
              WfForm.changeFieldValue(d1Field.VRKME+"_"+rowIndex, {
                   
                    value: supplierAccount.MEINS
                });
            }
        });
    });

    WfForm.bindDetailFieldChangeEvent(d1Field.KUNAG, function (id, rowIndex, value) {

        var options = {
            url: '/api/esb/execute',
            method: 'POST',
            params: {
                timestamp: new Date().getTime(),
                password: 'ryytnOA123',
                username: 'sysadmin',
                appkey: '64caed2d-ab47-4116-b1be-6caec02a2fa1',
                eventkey: 'ZINF002',
                params: JSON.stringify({NAME1: '', PARTNER: value})

            }
        };
        weaJs.callApi(options).then(function (res) {
            if (res.data.list.length > 0) {
                var supplierAccount = res.data.list[0];

                WfForm.changeFieldValue(d1Field.KUNWE+"_"+rowIndex, {

                    value: supplierAccount.PARTNER
                });

            }
        });
    });
});

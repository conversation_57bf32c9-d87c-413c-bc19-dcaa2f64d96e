<%@page import="java.net.URLEncoder"%>
<%@page import="weaver.hrm.HrmUserVarify"%>
<%@page import="com.ryytn.WMSService"%>
<%@	page import="java.util.*"  %>
<%@	page import="weaver.general.Util" %>
<%@	page import="weaver.hrm.User" %>

<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page" />
<%@page contentType="text/html;charset=utf-8"%>
<!DOCTYPE html >
<html>
<head>
<meta http-equiv="Access-Control-Allow-Origin" content="*">
<script src="/ryytn/jquery.min.js" type="text/javascript"></script>
<script type="text/javascript">
jQuery(document).ready(function(){
	//window.location.href="/rrytn/LoginEHR.jsp?workflowid="+WfForm.BaseInfo().workflowid+"&mobile=1";
})

</script>
</head>
<body>
<%
User user = HrmUserVarify.getUser (request , response) ;
String userid = user.getUID()+"";
String workflowid = Util.null2String(request.getParameter("workflowid"));
String mobile = Util.null2String(request.getParameter("mobile"));

WMSService  wmsService = new WMSService();

String loginId = user.getLoginid();
String tokenId = wmsService.getToken(loginId);
out.println(loginId);
//String ret = wmsService.loginEHR(loginId, "");
//out.println(ret);
String ip = "http://**************:9090";
String url = "/g3-2/login/api?token="+tokenId+"&username="+loginId;
System.out.println("登录url"+ip+url);
response.sendRedirect(ip+url);

%>
</body>
</html>
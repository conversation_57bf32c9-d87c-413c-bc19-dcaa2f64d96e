package yitouniu;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import weaver.general.BaseBean;
import weaver.general.Util;
import yitouniu.util.OMSUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-01-18 10:20
 **/
public class TestGiftOrder {

    public static void main(String[] args) {


        BigDecimal bigDecimal = new BigDecimal("545.14").add(new BigDecimal("47.58")).divide(new BigDecimal(450),BigDecimal.ROUND_HALF_UP);
        System.out.println(bigDecimal.toString());
//        String resultStr = "{\n" +
//                "    \"CTRL\":{\n" +
//                "        \"MSGTY\":\"E\",\n" +
//                "        \"UNAME\":\"sysadmin\",\n" +
//                "        \"DATUM\":\"2024-01-18\",\n" +
//                "        \"SYSID\":\"OA\",\n" +
//                "        \"INFID\":\"13083191705573586640\",\n" +
//                "        \"PAGE_NO\":0,\n" +
//                "        \"FUNID\":\"ZOAINF003\",\n" +
//                "        \"PAGE_SIZE\":0,\n" +
//                "        \"METHOD\":\"\",\n" +
//                "        \"REVID\":\"SAP\",\n" +
//                "        \"TABIX\":0,\n" +
//                "        \"MSAGE\":\"数据检查不通过://'供应商名称已经存在'//'相同税号已经存在'\",\n" +
//                "        \"KEYID\":\"C06供应商主数据202401184942-1-1\",\n" +
//                "        \"UZEIT\":\"18:26:26\",\n" +
//                "        \"MD5\":\"\"\n" +
//                "    },\n" +
//                "    \"DATA\":[\n" +
//                "\n" +
//                "    ]\n" +
//                "}";
//        JSONObject resultJson1 = JSONObject.parseObject(resultStr);
//        JSONArray jsonArray1 = resultJson1.getJSONArray("data");
//        System.out.println(jsonArray1);
        JSONObject omsPlatformOrderRequest = new JSONObject();
        omsPlatformOrderRequest.put("platformOrderNos", Lists.newArrayList("1394302420230524174"));

        String token = getOmsToken();

        String result = HttpRequest
                .post("http://**************:21180/api/ip/oa/gift/order/info")
                .header("r3-api-token",token)
                .body(omsPlatformOrderRequest.toJSONString()).execute().body();
        JSONObject resultJson = JSONObject.parseObject(result);
        JSONArray jsonArray = resultJson.getJSONArray("data");
        new BaseBean().writeLog( "请求返回="+jsonArray);
        List<String> skuNos = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject orderJsonObj = (JSONObject) jsonArray.get(i);
            JSONArray orderItemArr = orderJsonObj.getJSONArray("giftOrderItems");
            for (int j = 0; j < orderItemArr.size(); j++) {
                JSONObject orderItemObj = orderItemArr.getJSONObject(j);
                skuNos.add(orderItemObj.getString("skuCode"));
            }
        }
        new BaseBean().writeLog( "skuNos="+skuNos);
        Double a = 0.00;
        new BaseBean().writeLog( "double judge = "+ (a == 0));


        List<List> list = Lists.newArrayList();
        List<List> subList = Lists.newArrayList();
        for (int i = 0; i < jsonArray.size(); i++) {
            List dataList = new ArrayList<>();
            JSONObject orderJsonObj = (JSONObject) jsonArray.get(i);
            dataList.add(orderJsonObj.getString("platformOrderNo"));
            dataList.add(orderJsonObj.getDouble("productAmt"));
            JSONArray orderItemArr = orderJsonObj.getJSONArray("giftOrderItems");

            BigDecimal ddzpze = new BigDecimal(0);
            for (int j = 0; j < orderItemArr.size(); j++) {
                List itemList = new ArrayList<>();
                JSONObject orderItemObj = orderItemArr.getJSONObject(j);
                String skuCode = orderItemObj.getString("skuCode");
                itemList.add(skuCode);
                itemList.add(orderItemObj.getInteger("quantity"));
                subList.add(itemList);
            }
            dataList.add(ddzpze);
            list.add(dataList);
        }
        new BaseBean().writeLog( "lists="+list);
        new BaseBean().writeLog( "sublists="+subList);


    }

    public static String getOmsToken(){
        String accessToken = "";
        try {
//            String userKeyMd5 = DigestUtils.md5Hex(OMS_USERKEY);
//            String requestSign = DigestUtils.md5Hex(OMS_USERNAME + userKeyMd5 +OMS_BURGEON_SECRET);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("userName", "erp");

//            paramMap.put("userKey", userKeyMd5);
//            paramMap.put("requestSign", requestSign);
            paramMap.put("userKey", "e10adc3949ba59abbe56e057f20f883e");
            paramMap.put("requestSign", "cd70c798a580c5dbdd765690ab90b05b");
            String httpResponse = HttpRequest.get("http://**************:21180/api/auth/login").form(paramMap).execute().body();
            if(!"".equals(httpResponse)){
                JSONObject responseJson = JSONObject.parseObject(httpResponse);
                boolean successFlag = responseJson.getBoolean("success");
                if(successFlag){
                    accessToken = responseJson.getString("loginToken");
                }
            }
            return accessToken;
        }catch (Exception e){
            e.printStackTrace();
        }
        return accessToken;
    }
}

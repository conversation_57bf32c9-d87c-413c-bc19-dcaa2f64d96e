package yitouniu.util;

import org.codehaus.xfire.client.Client;
import weaver.general.BaseBean;
import java.net.URL;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 17:52 2021/10/29
 * @Modified By:
 */
public class ZJPTUtil {
    //资金生产环境 内网地址
//    public final static String URL = "http://***************:8083/webservers/outService.ws?wsdl";

    //资金生产环境 外网地址
//    public final static String URL = "http://************:38083/webservers/outService.ws?wsdl";

    //资金测试环境，本地与正式服务器可用
    public final static String URL = "http://************:8083/webservice/outService.ws?wsdl";

    //测试机上使用内网地址
//    public final static String URL = "http://**************:8083/webservice/outService.ws?wsdl";

    public static String WebServiceZJPT(String xmlReq) throws Exception {
        new BaseBean().writeLog("入参 : "+xmlReq);
        String result = "";
        Client c=new Client(new URL(URL));
        new BaseBean().writeLog("************* : "+URL);
        Object[] o=c.invoke("outSystemWS",new String[]{xmlReq});
        result = (String) o[0];
        new BaseBean().writeLog("资金平台返回 : "+o[0]);
        return result;
    }
}

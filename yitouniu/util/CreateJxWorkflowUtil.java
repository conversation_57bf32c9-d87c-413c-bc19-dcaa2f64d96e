package yitouniu.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.workflow.webservices.*;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/11/15 16:57
 * @Description TODO
 * @Version 1.0
 */
public class CreateJxWorkflowUtil {

    //季度&月度制定流程workflowid
    //测试
//    public static final String WORKFLOWID_ARCHIVE = "953";
//    正式
    public static final String WORKFLOWID_ARCHIVE = "1228";

    //季度&月度评价流程workflowid
    //测试
//    public static final String WORKFLOWID_EVALUATE = "958";
    //正式
    public static final String WORKFLOWID_EVALUATE = "1230";

    //部门评价流程workflowid
    //测试
//    public static final String WORKFLOWID_DEP_EVALUATE = "960";
    //正式
    public static final String WORKFLOWID_DEP_EVALUATE = "1231";


    public static final String JDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","jdjxformmodeid"));
    public static final String YDJXFORMMODEID = Util.null2String(new BaseBean().getPropValue("JDAndYDJX","ydjxformmodeid"));

    /**
     * 获取所有杭州一级部门和负责人
     * @return
     */
    public static Map<String,String> getYjbmfzrMap(){
        RecordSet rs = new RecordSet();
        Map<String,String> yjbmfzrMap = new HashMap<>();
        String yjbmfzrSql = "select b.* from (select a.id, b.fgzj from hrmdepartment a " +
                " LEFT JOIN Matrixtable_1 b on a.id= b.bm" +
                " where (a.supdepid= 0 or a.supdepid = 1448 or a.supdepid = 3106)" +
                "   and (a.canceled is null or a.canceled = 0)" +
                "   and b.fgzj is not null) b" +
                "   where b.fgzj is not null ";
        rs.executeQuery(yjbmfzrSql);
        while (rs.next()){
            yjbmfzrMap.put(rs.getString("id"),rs.getString("fgzj"));
        }
        return yjbmfzrMap;
    }

    /**
     * 获取部门的一级部门id
     * @param departmentIdSet
     * @return
     */
    public static Map<String,String> getYjbmMap(HashSet<String> departmentIdSet){
        Map<String,String> yjbmMap = new HashMap<>();
        for(String o : departmentIdSet){
            String firstDepartmentId = null;
            String departmentId = o;
            while(true){
                RecordSet rs = new RecordSet();
                rs.executeQuery("select id, supdepid from hrmdepartment where id = ? ", departmentId);
                if(rs.next()){
                    String supdepId = rs.getString("supdepid");
                    departmentId = rs.getString("supdepid");
                    String depId = rs.getString("id");
                    //判断0或空为再往上找不到级联，即当前部门为为一级部门，常温1448，冰淇淋餐饮3047，低温 3106，下面的为一级部门
                    if("0".equals(supdepId) || "".equals(supdepId) || Lists.newArrayList("1448","3047", "3106").contains(supdepId)){
                        //判断山东的用户服务，将他改成认养的用户服务部一级部门
                        if("907".equals(depId)){
                            depId = "138";
                        }
                        firstDepartmentId = depId;
                        break;
                    }
                }
            }
            yjbmMap.put(o,firstDepartmentId);
        }
        return yjbmMap;
    }

    /**
     * 创建绩效制定流程
     * @param ryList
     * @param gjzgMap
     * @param nowTime
     * @param yjbmMap
     * @param yjbmfzrMap
     * @param jxlxValue
     * @param jxlxName
     * @param tsryListMap
     */
    public static void createWorkflow(List<Map<String,String>> ryList, Map<String,String> gjzgMap, LocalDate nowTime, Map<String,String> yjbmMap, Map<String,String> yjbmfzrMap, String jxlxValue, String jxlxName, Map<String,List<Map<String,String>>> tsryListMap){
        RecordSet rs = new RecordSet();
        for(Map<String,String> o :ryList){
            try {
                JSONObject result = new JSONObject();

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0]  = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqrq"); //申请日期
                wrti[0].setFieldValue(nowTime.toString());
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1]  = new WorkflowRequestTableField();
                wrti[1].setFieldName("gjzg"); //隔级主管
                wrti[1].setFieldValue(gjzgMap.get(o.get("managerid"))) ;
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("szyjbm"); //所属一级部门
                wrti[2].setFieldValue(yjbmMap.get(o.get("departmentid")));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("szyjbmfzr"); //所属一级部门负责人
                wrti[3].setFieldValue(yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("jxlx"); //绩效类型
                wrti[4].setFieldValue(jxlxValue);
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                int year = -1;
                String requestName = "";
                int value;
                if("0".equals(jxlxValue)){
                    int nowQuarter = (nowTime.getMonthValue()+2)/3;
                    int nextQuarter = (nowTime.plusMonths(3).getMonthValue()+2)/3;
                    if(nowQuarter>nextQuarter){
                        year = nowTime.plusYears(1).getYear();
                    } else {
                        year = nowTime.getYear();
                    }
//                    value = nextQuarter-1;
                    value = nowQuarter-1;

                    wrti[5]  = new WorkflowRequestTableField();
                    wrti[5].setFieldName("jd"); //季度
                    wrti[5].setFieldValue(String.valueOf(value));
                    wrti[5].setView(true);
                    wrti[5].setEdit(true);

                    requestName = "认养一头牛"+year+"年Q"+nowQuarter+"季度员工绩效目标制定流程-" + o.get("lastname");

                } else {
                    int nowMonthly = nowTime.getMonthValue();
                    int nextMonthly = nowTime.plusMonths(1).getMonthValue();
                    if(nowMonthly > nextMonthly){
                        year = nowTime.plusYears(1).getYear();
                    } else {
                        year = nowTime.getYear();
                    }
//                    value = nextMonthly-1;
                    value = nowMonthly-1;
                    wrti[5]  = new WorkflowRequestTableField();
                    wrti[5].setFieldName("yd"); //月度
                    wrti[5].setFieldValue(String.valueOf(value));
                    wrti[5].setView(true);
                    wrti[5].setEdit(true);

                    requestName = "认养一头牛"+year+"年"+nowMonthly+"月员工绩效目标制定流程-" + o.get("lastname");

                }

                wrti[6]  = new WorkflowRequestTableField();
                wrti[6].setFieldName("nf"); //年份
                wrti[6].setFieldValue(String.valueOf(year));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7]  = new WorkflowRequestTableField();
                wrti[7].setFieldName("xm"); //姓名
                wrti[7].setFieldValue(o.get("id"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8]  = new WorkflowRequestTableField();
                wrti[8].setFieldName("gh"); //工号
                wrti[8].setFieldValue(o.get("workcode"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9]  = new WorkflowRequestTableField();
                wrti[9].setFieldName("bm"); //部门
                wrti[9].setFieldValue(o.get("departmentid"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                wrti[10]  = new WorkflowRequestTableField();
                wrti[10].setFieldName("gw"); //岗位
                wrti[10].setFieldValue(o.get("jobtitle"));
                wrti[10].setView(true);
                wrti[10].setEdit(true);

                wrti[11]  = new WorkflowRequestTableField();
                wrti[11].setFieldName("zjsj"); //直接上级
                wrti[11].setFieldValue(o.get("managerid"));
                wrti[11].setView(true);
                wrti[11].setEdit(true);

                wrti[12]  = new WorkflowRequestTableField();
                wrti[12].setFieldName("dqgwrzsj"); //入职时间
                wrti[12].setFieldValue(o.get("createdate"));
                wrti[12].setView(true);
                wrti[12].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //季度&&特殊人员，需要插入明细表
                if("0".equals(jxlxValue) && "0".equals(o.get("sftsry"))){
                    new BaseBean().writeLog("进入添加明细");
                    List<Map<String,String>> tsryList = tsryListMap.get(o.get("okrmb"));
                    int detailrows = tsryList.size() ;//添加指定条数明细
                    //添加明细数据
                    wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
                    for(int i = 0 ; i < detailrows ; i++){
                        wrti = new WorkflowRequestTableField[5]; //字段个数   **************

                        wrti[0]  = new WorkflowRequestTableField();
                        wrti[0].setFieldName("mbxh"); //目标序号
                        wrti[0].setFieldValue(tsryList.get(i).get("mqbs"));
                        wrti[0].setView(true);//字段是否可见
                        wrti[0].setEdit(true);//字段是否可编辑

                        wrti[1]  = new WorkflowRequestTableField();
                        wrti[1].setFieldName("mbobjective"); //目标(Objective)
                        wrti[1].setFieldValue(tsryList.get(i).get("mb"));
                        wrti[1].setView(true);
                        wrti[1].setEdit(true);

                        wrti[2]  = new WorkflowRequestTableField();
                        wrti[2].setFieldName("gjjgkeyresults"); //关键结果（Key?Results）
                        wrti[2].setFieldValue(tsryList.get(i).get("gjjg"));
                        wrti[2].setView(true);
                        wrti[2].setEdit(true);

                        wrti[3]  = new WorkflowRequestTableField();
                        wrti[3].setFieldName("qzbfb"); //权重（百分比）
                        wrti[3].setFieldValue(tsryList.get(i).get("qzbfb"));
                        wrti[3].setView(true);
                        wrti[3].setEdit(true);

                        wrti[4]  = new WorkflowRequestTableField();
                        wrti[4].setFieldName("smbdmb"); //使命必达目标（3.5分）
                        wrti[4].setFieldValue(tsryList.get(i).get("smbdmb"));
                        wrti[4].setView(true);
                        wrti[4].setEdit(true);


                        wrtri[i] = new WorkflowRequestTableRecord();
                        wrtri[i].setWorkflowRequestTableFields(wrti);
                    }

                }

                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID_ARCHIVE);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(o.get("id"));//创建人id
                wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName(requestName);//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("id"))));
                if(!"".equals(requestid)){
                    if(Integer.parseInt(requestid) >0){
                        String sql = "";
                        if("0".equals(jxlxValue)){
                            sql = "insert into uf_jxzdhdb (xm, jd, sfycj) values(?,?,?)";
                        } else {
                            sql = "insert into uf_jxzdhdb (xm, yd, sfycj) values(?,?,?)";
                        }
                        boolean flag = rs.executeUpdate(sql, o.get("id"), value, 0);

                        result.put("status", "0");
                        result.put("requestid", requestid);
                        if(!flag){
                            new BaseBean().writeLog("createWorkflow:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果成功但是插入核对表失败，请手动插入，数据为绩效为类型=" + jxlxValue+", 月度or季度=" + value + ", 姓名=" + o.get("id"));
                        }
                        //制定成功后直接加入信息表
                        String insertTableName = "";
                        if("0".equals(jxlxValue)){
                            insertTableName = "uf_bmjdjx";
                        } else {
                            insertTableName = "uf_bmydjxxx";
                        }
                        String insertMainSql = "";
                        insertMainSql = "insert into " + insertTableName + " (yjbmmc, bmfzr, jxsznf, jxszyf, formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values(?,?,?,?,?,?,?,?,?)  ";
                        Date date = new Date();
                        int mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                        if("0".equals(jxlxValue)) {
                            if(mainId < 0){
                                boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), String.valueOf(year), String.valueOf(value), JDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                                new BaseBean().writeLog("CreateJxWorkflowUtil insertFlag = " + insertFlag);

                                if(insertFlag){
                                    mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                                    //修改建模模块权限
                                    ModeRightInfo modeRightInfo = new ModeRightInfo();
                                    modeRightInfo.setNewRight(true);
                                    modeRightInfo.editModeDataShare(1, Integer.parseInt(JDJXFORMMODEID),mainId);
                                } else {
                                    new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");
                                }
                            }
                        } else {
                            if(mainId < 0){
                                // 插入主表数据
                                boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), String.valueOf(year), String.valueOf(value), YDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                                if(insertFlag){
                                    mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                                    //修改建模模块权限
                                    ModeRightInfo modeRightInfo = new ModeRightInfo();
                                    modeRightInfo.setNewRight(true);
                                    modeRightInfo.editModeDataShare(1, Integer.parseInt(YDJXFORMMODEID),mainId);
                                } else {
                                    new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");

                                }
                            }
                        }
                        //不管是否存在，先将原来的删除了
                        rs.executeUpdate("delete from " + insertTableName + "_dt1 where mainid = ? and xm = ?", mainId, o.get("id"));
                        //插入明细数据
                        String insertDetailSql = "insert into " + insertTableName + "_dt1  (mainid, xm, jxzdlc, bm, sfzdwc, gh) values(?,?,?,?,?,?)";
                        boolean insertDetailFlag = rs.executeUpdate(insertDetailSql, mainId, o.get("id"), requestid, o.get("departmentid"), 1, o.get("workcode"));
                        new BaseBean().writeLog("CreateJxWorkflowUtil insertDetailFlag = " + insertDetailFlag);

                    }else{
                        result.put("status", "1");
                        result.put("requestid", requestid);
                    }
                }
                new BaseBean().writeLog("CreateJxWorkflowUtil:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果"+result.toJSONString());
            } catch (Exception e) {
                new BaseBean().writeLog("createWorkflow : 异常错误" + e);
                new BaseBean().writeLog(e);
            }
        }

    }


    /**
     * 创建绩效制定流程
     * @param ryList
     * @param gjzgMap
     * @param nowTime
     * @param yjbmMap
     * @param yjbmfzrMap
     * @param jxlxValue
     * @param jxlxName
     * @param tsryListMap
     */
    public static void createWorkflowV1(List<Map<String,String>> ryList, Map<String,String> gjzgMap, LocalDate nowTime, Map<String,String> yjbmMap, Map<String,String> yjbmfzrMap, String jxlxValue, String jxlxName, Map<String,List<Map<String,String>>> tsryListMap){
        RecordSet rs = new RecordSet();
        for(Map<String,String> o :ryList){
            try {
                JSONObject result = new JSONObject();

                WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
                //赋值方式
                wrti[0]  = new WorkflowRequestTableField();
                wrti[0].setFieldName("sqrq"); //申请日期
                wrti[0].setFieldValue(nowTime.toString());
                wrti[0].setView(true);
                wrti[0].setEdit(true);

                wrti[1]  = new WorkflowRequestTableField();
                wrti[1].setFieldName("gjzg"); //隔级主管
                wrti[1].setFieldValue(gjzgMap.get(o.get("managerid"))) ;
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("szyjbm"); //所属一级部门
                wrti[2].setFieldValue(yjbmMap.get(o.get("departmentid")));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("szyjbmfzr"); //所属一级部门负责人
                wrti[3].setFieldValue(yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("jxlx"); //绩效类型
                wrti[4].setFieldValue(jxlxValue);
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                int year = -1;
                String requestName = "";
                int value;
                if("0".equals(jxlxValue)){
                    int nowQuarter = (nowTime.getMonthValue()+2)/3;
//                    int nextQuarter = (nowTime.plusMonths(3).getMonthValue()+2)/3;
//                    if(nowQuarter>nextQuarter){
//                        year = nowTime.plusYears(1).getYear();
//                    } else {
                        year = nowTime.getYear();
//                    }
//                    value = nextQuarter-1;
                    value = nowQuarter-1;

                    wrti[5]  = new WorkflowRequestTableField();
                    wrti[5].setFieldName("jd"); //季度
                    wrti[5].setFieldValue(String.valueOf(value));
                    wrti[5].setView(true);
                    wrti[5].setEdit(true);

                    requestName = "认养一头牛"+year+"年Q"+nowQuarter+"季度员工绩效目标制定流程-" + o.get("lastname");

                } else {
                    int nowMonthly = nowTime.getMonthValue();
                    int nextMonthly = nowTime.plusMonths(1).getMonthValue();
                    if(nowMonthly > nextMonthly){
                        year = nowTime.plusYears(1).getYear();
                    } else {
                        year = nowTime.getYear();
                    }
//                    value = nextMonthly-1;
                    value = nowMonthly-1;
                    wrti[5]  = new WorkflowRequestTableField();
                    wrti[5].setFieldName("yd"); //月度
                    wrti[5].setFieldValue(String.valueOf(value));
                    wrti[5].setView(true);
                    wrti[5].setEdit(true);

                    requestName = "认养一头牛"+year+"年"+nowMonthly+"月员工绩效目标制定流程-" + o.get("lastname");

                }

                wrti[6]  = new WorkflowRequestTableField();
                wrti[6].setFieldName("nf"); //年份
                wrti[6].setFieldValue(String.valueOf(year));
                wrti[6].setView(true);
                wrti[6].setEdit(true);

                wrti[7]  = new WorkflowRequestTableField();
                wrti[7].setFieldName("xm"); //姓名
                wrti[7].setFieldValue(o.get("id"));
                wrti[7].setView(true);
                wrti[7].setEdit(true);

                wrti[8]  = new WorkflowRequestTableField();
                wrti[8].setFieldName("gh"); //工号
                wrti[8].setFieldValue(o.get("workcode"));
                wrti[8].setView(true);
                wrti[8].setEdit(true);

                wrti[9]  = new WorkflowRequestTableField();
                wrti[9].setFieldName("bm"); //部门
                wrti[9].setFieldValue(o.get("departmentid"));
                wrti[9].setView(true);
                wrti[9].setEdit(true);

                wrti[10]  = new WorkflowRequestTableField();
                wrti[10].setFieldName("gw"); //岗位
                wrti[10].setFieldValue(o.get("jobtitle"));
                wrti[10].setView(true);
                wrti[10].setEdit(true);

                wrti[11]  = new WorkflowRequestTableField();
                wrti[11].setFieldName("zjsj"); //直接上级
                wrti[11].setFieldValue(o.get("managerid"));
                wrti[11].setView(true);
                wrti[11].setEdit(true);

                wrti[12]  = new WorkflowRequestTableField();
                wrti[12].setFieldName("dqgwrzsj"); //入职时间
                wrti[12].setFieldValue(o.get("createdate"));
                wrti[12].setView(true);
                wrti[12].setEdit(true);

                WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
                wrtri[0] = new WorkflowRequestTableRecord();
                wrtri[0].setWorkflowRequestTableFields(wrti);
                WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
                wmi.setRequestRecords(wrtri);

                //季度&&特殊人员，需要插入明细表
                if("0".equals(jxlxValue) && "0".equals(o.get("sftsry"))){
                    new BaseBean().writeLog("进入添加明细");
                    List<Map<String,String>> tsryList = tsryListMap.get(o.get("okrmb"));
                    int detailrows = tsryList.size() ;//添加指定条数明细
                    //添加明细数据
                    wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
                    for(int i = 0 ; i < detailrows ; i++){
                        wrti = new WorkflowRequestTableField[5]; //字段个数   **************

                        wrti[0]  = new WorkflowRequestTableField();
                        wrti[0].setFieldName("mbxh"); //目标序号
                        wrti[0].setFieldValue(tsryList.get(i).get("mqbs"));
                        wrti[0].setView(true);//字段是否可见
                        wrti[0].setEdit(true);//字段是否可编辑

                        wrti[1]  = new WorkflowRequestTableField();
                        wrti[1].setFieldName("mbobjective"); //目标(Objective)
                        wrti[1].setFieldValue(tsryList.get(i).get("mb"));
                        wrti[1].setView(true);
                        wrti[1].setEdit(true);

                        wrti[2]  = new WorkflowRequestTableField();
                        wrti[2].setFieldName("gjjgkeyresults"); //关键结果（Key?Results）
                        wrti[2].setFieldValue(tsryList.get(i).get("gjjg"));
                        wrti[2].setView(true);
                        wrti[2].setEdit(true);

                        wrti[3]  = new WorkflowRequestTableField();
                        wrti[3].setFieldName("qzbfb"); //权重（百分比）
                        wrti[3].setFieldValue(tsryList.get(i).get("qzbfb"));
                        wrti[3].setView(true);
                        wrti[3].setEdit(true);

                        wrti[4]  = new WorkflowRequestTableField();
                        wrti[4].setFieldName("smbdmb"); //使命必达目标（3.5分）
                        wrti[4].setFieldValue(tsryList.get(i).get("smbdmb"));
                        wrti[4].setView(true);
                        wrti[4].setEdit(true);


                        wrtri[i] = new WorkflowRequestTableRecord();
                        wrtri[i].setWorkflowRequestTableFields(wrti);
                    }

                }

                //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
                WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
                WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
                //流程基本信息设置
                WorkflowBaseInfo wbi = new WorkflowBaseInfo();
                wbi.setWorkflowId(WORKFLOWID_ARCHIVE);//流程请求id
                WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
                wri.setCreatorId(o.get("id"));//创建人id
                wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
                wri.setRequestLevel("0");//0 正常，1重要，2紧急

                wri.setRequestName(requestName);//流程标题
                //添加主字段数据
                wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
                wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
                wri.setWorkflowBaseInfo(wbi);
                WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
                String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("id"))));
                if(!"".equals(requestid)){
                    if(Integer.parseInt(requestid) >0){
                        String sql = "";
                        if("0".equals(jxlxValue)){
                            sql = "insert into uf_jxzdhdb (xm, jd, sfycj) values(?,?,?)";
                        } else {
                            sql = "insert into uf_jxzdhdb (xm, yd, sfycj) values(?,?,?)";
                        }
                        boolean flag = rs.executeUpdate(sql, o.get("id"), value, 0);

                        result.put("status", "0");
                        result.put("requestid", requestid);
                        if(!flag){
                            new BaseBean().writeLog("createWorkflow:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果成功但是插入核对表失败，请手动插入，数据为绩效为类型=" + jxlxValue+", 月度or季度=" + value + ", 姓名=" + o.get("id"));
                        }
                        //制定成功后直接加入信息表
                        String insertTableName = "";
                        if("0".equals(jxlxValue)){
                            insertTableName = "uf_bmjdjx";
                        } else {
                            insertTableName = "uf_bmydjxxx";
                        }
                        String insertMainSql = "";
                        insertMainSql = "insert into " + insertTableName + " (yjbmmc, bmfzr, jxsznf, jxszyf, formmodeid,modedatacreater,modedatacreatertype,modedatacreatedate,modedatacreatetime) values(?,?,?,?,?,?,?,?,?)  ";
                        Date date = new Date();
                        int mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                        if("0".equals(jxlxValue)) {
                            if(mainId < 0){
                                boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), String.valueOf(year), String.valueOf(value), JDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                                new BaseBean().writeLog("CreateJxWorkflowUtil insertFlag = " + insertFlag);

                                if(insertFlag){
                                    mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                                    //修改建模模块权限
                                    ModeRightInfo modeRightInfo = new ModeRightInfo();
                                    modeRightInfo.setNewRight(true);
                                    modeRightInfo.editModeDataShare(1, Integer.parseInt(JDJXFORMMODEID),mainId);
                                } else {
                                    new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");
                                }
                            }
                        } else {
                            if(mainId < 0){
                                // 插入主表数据
                                boolean insertFlag = rs.executeUpdate(insertMainSql, yjbmMap.get(o.get("departmentid")), yjbmfzrMap.get(yjbmMap.get(o.get("departmentid"))), String.valueOf(year), String.valueOf(value), YDJXFORMMODEID, 1, 0, TimeUtils.getTimeStr(date, "yyyy-MM-dd"), TimeUtils.getTimeStr(date, "HH:mm:ss"));
                                if(insertFlag){
                                    mainId = CreateJxWorkflowUtil.select(insertTableName, yjbmMap.get(o.get("departmentid")), String.valueOf(year), String.valueOf(value));
                                    //修改建模模块权限
                                    ModeRightInfo modeRightInfo = new ModeRightInfo();
                                    modeRightInfo.setNewRight(true);
                                    modeRightInfo.editModeDataShare(1, Integer.parseInt(YDJXFORMMODEID),mainId);
                                } else {
                                    new BaseBean().writeLog("CreateJxWorkflowUtil "+o.get("lastname")+"的"+jxlxName+"绩效指定流程 插入主表数据错误");

                                }
                            }
                        }
                        //不管是否存在，先将原来的删除了
                        rs.executeUpdate("delete from " + insertTableName + "_dt1 where mainid = ? and xm = ?", mainId, o.get("id"));
                        //插入明细数据
                        String insertDetailSql = "insert into " + insertTableName + "_dt1  (mainid, xm, jxzdlc, bm, sfzdwc, gh) values(?,?,?,?,?,?)";
                        boolean insertDetailFlag = rs.executeUpdate(insertDetailSql, mainId, o.get("id"), requestid, o.get("departmentid"), 1, o.get("workcode"));
                        new BaseBean().writeLog("CreateJxWorkflowUtil insertDetailFlag = " + insertDetailFlag);

                    }else{
                        result.put("status", "1");
                        result.put("requestid", requestid);
                    }
                }
                new BaseBean().writeLog("CreateJxWorkflowUtil:"+o.get("lastname")+"的"+jxlxName+"绩效流程创建结果"+result.toJSONString());
            } catch (Exception e) {
                new BaseBean().writeLog("createWorkflow : 异常错误" + e);
                new BaseBean().writeLog(e);
            }
        }

    }




    public static Map<String,List<Map<String,String>>> getTemplateByName(){
        RecordSet rs = new RecordSet();
        Map<String,List<Map<String,String>>> resultMap = new HashMap<>();

        String sqlMain = "select id from uf_okrmbk";
        List<String> ids = new ArrayList<>();
        rs.executeQuery(sqlMain);
        while (rs.next()) {
            ids.add(rs.getString("id"));
        }

        for(String id : ids){
            String sql = "select a.*,b.id from uf_okrmbk_dt1 a left join uf_okrmbk b on a.mainid = b.id where b.id = ?";
            rs.executeQuery(sql, id);
            List<Map<String,String>> result = new ArrayList<>();
            while (rs.next()){
                Map<String,String> map = new HashMap<>();
                map.put("mb", rs.getString("mb"));
                map.put("gjjg", rs.getString("gjjg"));
                map.put("qzbfb", rs.getString("qzbfb"));
                map.put("smbdmb", rs.getString("smbdmb"));
                map.put("mqbs", rs.getString("mqbs"));
                result.add(map);
            }
            resultMap.put(id,result);
        }
        return resultMap;
    }

    /**
     * 创建个人绩效评价流程
     * @param grjxxxList
     * @param itemListMap
     * @param jxlxValue
     */
    public static void createWorkflowForPersonalEvaluate(List<Map<String,String>> grjxxxList, Map<String, List<Map<String, String>>> itemListMap, String jxlxValue){
        RecordSet rs = new RecordSet();
        LocalDate nowDate = LocalDate.now();
        for(Map<String, String> o : grjxxxList){
            JSONObject result = new JSONObject();

            String id = o.get("mainId");
            WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
            //赋值方式
            wrti[0]  = new WorkflowRequestTableField();
            wrti[0].setFieldName("jxlx"); //绩效类型
            wrti[0].setFieldValue(jxlxValue);
            wrti[0].setView(true);
            wrti[0].setEdit(true);

            wrti[1]  = new WorkflowRequestTableField();
            wrti[1].setFieldName("yjbmfzr"); //一级部门负责人
            wrti[1].setFieldValue(o.get("yjbmfzr"));
            wrti[1].setView(true);
            wrti[1].setEdit(true);

            wrti[2]  = new WorkflowRequestTableField();
            wrti[2].setFieldName("gjzg"); //隔级主管
            wrti[2].setFieldValue(o.get("gjzg"));
            wrti[2].setView(true);
            wrti[2].setEdit(true);

            wrti[3]  = new WorkflowRequestTableField();
            wrti[3].setFieldName("nf"); //年份
            wrti[3].setFieldValue(o.get("nf"));
            wrti[3].setView(true);
            wrti[3].setEdit(true);

            if("0".equals(jxlxValue)){
                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("jd"); //季度
                wrti[4].setFieldValue(o.get("jd"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);
            } else {
                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("yd"); //月度
                wrti[4].setFieldValue(o.get("yd"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);
            }

            wrti[5]  = new WorkflowRequestTableField();
            wrti[5].setFieldName("xm"); //姓名
            wrti[5].setFieldValue(o.get("xm"));
            wrti[5].setView(true);
            wrti[5].setEdit(true);

            wrti[6]  = new WorkflowRequestTableField();
            wrti[6].setFieldName("bm"); //部门
            wrti[6].setFieldValue(o.get("bm"));
            wrti[6].setView(true);
            wrti[6].setEdit(true);

            wrti[7]  = new WorkflowRequestTableField();
            wrti[7].setFieldName("gw"); //岗位
            wrti[7].setFieldValue(o.get("gw"));
            wrti[7].setView(true);
            wrti[7].setEdit(true);

            wrti[8]  = new WorkflowRequestTableField();
            wrti[8].setFieldName("dqgwrzsj"); //入职时间
            wrti[8].setFieldValue(o.get("dqgwrzsj"));
            wrti[8].setView(true);
            wrti[8].setEdit(true);

            wrti[9]  = new WorkflowRequestTableField();
            wrti[9].setFieldName("zjsj"); //直接上级
            wrti[9].setFieldValue(o.get("zjsj"));
            wrti[9].setView(true);
            wrti[9].setEdit(true);

            wrti[10]  = new WorkflowRequestTableField();
            wrti[10].setFieldName("zjygjsfxt"); //直接与隔级是否相同
            wrti[10].setFieldValue(o.get("zjygjsfxt"));
            wrti[10].setView(true);
            wrti[10].setEdit(true);

            wrti[11]  = new WorkflowRequestTableField();
            wrti[11].setFieldName("gh"); //工号
            wrti[11].setFieldValue(o.get("gh"));
            wrti[11].setView(true);
            wrti[11].setEdit(true);

            wrti[12]  = new WorkflowRequestTableField();
            wrti[12].setFieldName("zdlc"); //制定流程
            wrti[12].setFieldValue(o.get("zdlc"));
            wrti[12].setView(true);
            wrti[12].setEdit(true);

            wrti[13]  = new WorkflowRequestTableField();
            wrti[13].setFieldName("yjbmmc"); //一级部门名称
            wrti[13].setFieldValue(o.get("szyjbm"));
            wrti[13].setView(true);
            wrti[13].setEdit(true);

            wrti[14]  = new WorkflowRequestTableField();
            wrti[14].setFieldName("sqrq"); //申请日期
            wrti[14].setFieldValue(nowDate.toString());
            wrti[14].setView(true);
            wrti[14].setEdit(true);

            WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
            wrtri[0] = new WorkflowRequestTableRecord();
            wrtri[0].setWorkflowRequestTableFields(wrti);
            WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
            wmi.setRequestRecords(wrtri);

            //插入明细表
            new BaseBean().writeLog("进入添加明细");
            List<Map<String, String>> itemList = itemListMap.get(id);
            int detailrows = itemList.size() ;//添加指定条数明细
            //添加明细数据
            wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
            for(int i = 0 ; i < detailrows ; i++){
                wrti = new WorkflowRequestTableField[21]; //字段个数   **************

                wrti[0]  = new WorkflowRequestTableField();
                wrti[0].setFieldName("mbxh"); //目标序号
                wrti[0].setFieldValue(itemList.get(i).get("mbxh"));
                wrti[0].setView(true);//字段是否可见
                wrti[0].setEdit(true);//字段是否可编辑

                wrti[1]  = new WorkflowRequestTableField();
                wrti[1].setFieldName("mbobjective"); //目标(Objective)
                wrti[1].setFieldValue(itemList.get(i).get("mbobjective"));
                wrti[1].setView(true);
                wrti[1].setEdit(true);

                wrti[2]  = new WorkflowRequestTableField();
                wrti[2].setFieldName("gjjgkeyresults"); //关键结果（Key?Results）
                wrti[2].setFieldValue(itemList.get(i).get("gjjgkeyresults"));
                wrti[2].setView(true);
                wrti[2].setEdit(true);

                wrti[3]  = new WorkflowRequestTableField();
                wrti[3].setFieldName("qzbfb"); //权重（百分比）
                wrti[3].setFieldValue(itemList.get(i).get("qzbfb"));
                wrti[3].setView(true);
                wrti[3].setEdit(true);

                wrti[4]  = new WorkflowRequestTableField();
                wrti[4].setFieldName("smbdmb"); //使命必达目标（3.5分）
                wrti[4].setFieldValue(itemList.get(i).get("smbdmb"));
                wrti[4].setView(true);
                wrti[4].setEdit(true);

                wrti[5]  = new WorkflowRequestTableField();
                wrti[5].setFieldName("jxmxbid"); //绩效明细id
                wrti[5].setFieldValue(itemList.get(i).get("itemId"));
                wrti[5].setView(true);
                wrti[5].setEdit(true);

                wrtri[i] = new WorkflowRequestTableRecord();
                wrtri[i].setWorkflowRequestTableFields(wrti);
            }

            //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
            WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
            WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
            WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
            //流程基本信息设置
            WorkflowBaseInfo wbi = new WorkflowBaseInfo();
            wbi.setWorkflowId(WORKFLOWID_EVALUATE);//流程请求id
            WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
            wri.setCreatorId(o.get("xm"));//创建人id
            wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
            wri.setRequestLevel("0");//0 正常，1重要，2紧急
            String requestName = "";
            if("0".equals(jxlxValue)){
                requestName = "认养一头牛"+nowDate.getYear()+"年Q"+(nowDate.getMonthValue()+2)/3+"季度员工绩效评价审批流程-"+o.get("lastname");
            } else {
                requestName = "认养一头牛"+nowDate.getYear()+"年"+(nowDate.getMonthValue()-1)+"月员工绩效评价审批流程-"+o.get("lastname");
            }
            wri.setRequestName(requestName);//流程标题
            //添加主字段数据
            wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
            wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
            wri.setWorkflowBaseInfo(wbi);
            WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
            String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(o.get("xm"))));
            if(!"".equals(requestid)){
                if(Integer.parseInt(requestid) >0){
                    result.put("status", "0");
                    result.put("requestid", requestid);
                    //修改是否触发评价流程为是
                    rs.executeUpdate("update uf_grjxxx set sfcfpjlc = 0 where id = ? ", o.get("mainId"));
                }else{
                    result.put("status", "1");
                    result.put("requestid", requestid);
                }
            }
            new BaseBean().writeLog("CreateJxWorkflowUtil:"+o.get("gh")+"的"+jxlxValue+"（0：季度，1：月度）绩效评价流程创建结果"+result.toJSONString());
        }




    }

    /**
     * 创建部门绩效评价流程
     * @param bmJXMap
     * @param itembmJxList
     * @param jxlxValue
     */
    public static boolean createWorkflowForFirstDepartmentEvaluate(Map<String, String> bmJXMap, List<Map<String, String>> itembmJxList, String jxlxValue){
        RecordSet rs = new RecordSet();
        JSONObject result = new JSONObject();
        LocalDate nowDate = LocalDate.now();

        WorkflowRequestTableField[] wrti = new WorkflowRequestTableField[26]; //字段信息****************
        //赋值方式
        wrti[0]  = new WorkflowRequestTableField();
        wrti[0].setFieldName("jxlx"); //绩效类型
        wrti[0].setFieldValue(jxlxValue);
        wrti[0].setView(true);
        wrti[0].setEdit(true);

        wrti[1]  = new WorkflowRequestTableField();
        wrti[1].setFieldName("szyjbmfzr"); //一级部门负责人
        wrti[1].setFieldValue(bmJXMap.get("yjbmfzr"));
        wrti[1].setView(true);
        wrti[1].setEdit(true);

        wrti[2]  = new WorkflowRequestTableField();
        wrti[2].setFieldName("bmjxxxhdbid"); //部门绩效信息核对表id
        wrti[2].setFieldValue(bmJXMap.get("bmjxxxhdbid"));
        wrti[2].setView(true);
        wrti[2].setEdit(true);

        wrti[3]  = new WorkflowRequestTableField();
        wrti[3].setFieldName("nf"); //年份
        wrti[3].setFieldValue(bmJXMap.get("nf"));
        wrti[3].setView(true);
        wrti[3].setEdit(true);

        if("0".equals(jxlxValue)){
            wrti[4]  = new WorkflowRequestTableField();
            wrti[4].setFieldName("jd"); //季度
            wrti[4].setFieldValue(bmJXMap.get("time"));
            wrti[4].setView(true);
            wrti[4].setEdit(true);
        } else {
            wrti[4]  = new WorkflowRequestTableField();
            wrti[4].setFieldName("yd"); //月度
            wrti[4].setFieldValue(bmJXMap.get("time"));
            wrti[4].setView(true);
            wrti[4].setEdit(true);
        }

        wrti[5]  = new WorkflowRequestTableField();
        wrti[5].setFieldName("szyjbm"); //一级部门名称
        wrti[5].setFieldValue(bmJXMap.get("yjbmmc"));
        wrti[5].setView(true);
        wrti[5].setEdit(true);

        wrti[6]  = new WorkflowRequestTableField();
        wrti[6].setFieldName("sqrq"); //申请日期
        wrti[6].setFieldValue(nowDate.toString());
        wrti[6].setView(true);
        wrti[6].setEdit(true);

//        wrti[7]  = new WorkflowRequestTableField();
//        wrti[7].setFieldName("yjbmfzrDepId"); //申请人部门
//        wrti[7].setFieldValue(bmJXMap.get("yjbmfzrDepId"));
//        wrti[7].setView(true);
//        wrti[7].setEdit(true);

        WorkflowRequestTableRecord[] wrtri = new WorkflowRequestTableRecord[1];//主字段只有一行数据
        wrtri[0] = new WorkflowRequestTableRecord();
        wrtri[0].setWorkflowRequestTableFields(wrti);
        WorkflowMainTableInfo wmi = new WorkflowMainTableInfo();
        wmi.setRequestRecords(wrtri);

        //插入明细表
        new BaseBean().writeLog("进入添加明细");
        int detailrows = itembmJxList.size() ;//添加指定条数明细
        //添加明细数据
        wrtri = new WorkflowRequestTableRecord[detailrows];//添加指定条数行明细数据
        for(int i = 0 ; i < detailrows ; i++){
            wrti = new WorkflowRequestTableField[21]; //字段个数   **************

            wrti[0]  = new WorkflowRequestTableField();
            wrti[0].setFieldName("xm"); //姓名
            wrti[0].setFieldValue(itembmJxList.get(i).get("xm"));
            wrti[0].setView(true);//字段是否可见
            wrti[0].setEdit(true);//字段是否可编辑

            wrti[1]  = new WorkflowRequestTableField();
            wrti[1].setFieldName("jzgdf"); //价值观得分
            wrti[1].setFieldValue(itembmJxList.get(i).get("jzgdf"));
            wrti[1].setView(true);
            wrti[1].setEdit(true);

            wrti[2]  = new WorkflowRequestTableField();
            wrti[2].setFieldName("grjxdf"); //个人绩效得分
            wrti[2].setFieldValue(itembmJxList.get(i).get("okrdf"));
            wrti[2].setView(true);
            wrti[2].setEdit(true);

            wrti[3]  = new WorkflowRequestTableField();
            wrti[3].setFieldName("grjxjg"); //个人绩效结果
            wrti[3].setFieldValue(itembmJxList.get(i).get("xtjg"));
            wrti[3].setView(true);
            wrti[3].setEdit(true);

            //初始化使用个人绩效结果，
            wrti[4]  = new WorkflowRequestTableField();
            wrti[4].setFieldName("ylfhjg"); //一轮复核结果
            wrti[4].setFieldValue(itembmJxList.get(i).get("xtjg"));
            wrti[4].setView(true);
            wrti[4].setEdit(true);

            wrti[5]  = new WorkflowRequestTableField();
            wrti[5].setFieldName("szbm"); //所属部门
            wrti[5].setFieldValue(itembmJxList.get(i).get("bm"));
            wrti[5].setView(true);
            wrti[5].setEdit(true);

            wrti[6]  = new WorkflowRequestTableField();
            wrti[6].setFieldName("pjlc"); //评价流程
            wrti[6].setFieldValue(itembmJxList.get(i).get("jxpjlc"));
            wrti[6].setView(true);
            wrti[6].setEdit(true);

            wrti[7]  = new WorkflowRequestTableField();
            wrti[7].setFieldName("mxid"); //明细id
            wrti[7].setFieldValue(itembmJxList.get(i).get("mxid"));
            wrti[7].setView(true);
            wrti[7].setEdit(true);

            wrti[8]  = new WorkflowRequestTableField();
            wrti[8].setFieldName("gw"); //岗位
            wrti[8].setFieldValue(itembmJxList.get(i).get("gw"));
            wrti[8].setView(true);
            wrti[8].setEdit(true);

            wrti[9]  = new WorkflowRequestTableField();
            wrti[9].setFieldName("ygxm"); //员工姓名
            wrti[9].setFieldValue(itembmJxList.get(i).get("lastname"));
            wrti[9].setView(true);
            wrti[9].setEdit(true);

            wrti[10]  = new WorkflowRequestTableField();
            wrti[10].setFieldName("sfyfsddxx");
            wrti[10].setFieldValue("1");
            wrti[10].setView(true);
            wrti[10].setEdit(true);

            wrtri[i] = new WorkflowRequestTableRecord();
            wrtri[i].setWorkflowRequestTableFields(wrti);
        }

        //指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
        WorkflowDetailTableInfo[] WorkflowDetailTableInfo = new WorkflowDetailTableInfo[1];//指定明细表的个数，多个明细表指定多个，顺序按照明细的顺序
        WorkflowDetailTableInfo[0] = new WorkflowDetailTableInfo();
        WorkflowDetailTableInfo[0].setWorkflowRequestTableRecords(wrtri);
        //流程基本信息设置
        WorkflowBaseInfo wbi = new WorkflowBaseInfo();
        wbi.setWorkflowId(WORKFLOWID_DEP_EVALUATE);//流程请求id
        WorkflowRequestInfo wri = new WorkflowRequestInfo();//流程基本信息
        wri.setCreatorId(bmJXMap.get("yjbmfzr"));//创建人id
        wri.setIsnextflow("1"); //0：停留在创建节点，1：流转到下一个节点
        wri.setRequestLevel("0");//0 正常，1重要，2紧急
        String requestName = "";
        if("0".equals(jxlxValue)){
            requestName = "认养一头牛"+bmJXMap.get("nf")+"年Q"+(Integer.parseInt(bmJXMap.get("time")) + 1)+"季度部门绩效评价审批流程";
        } else {
            requestName = "认养一头牛"+bmJXMap.get("nf")+"年"+(Integer.parseInt(bmJXMap.get("time")) + 1)+"月部门绩效评价审批流程";
        }
        wri.setRequestName(requestName);//流程标题
        //添加主字段数据
        wri.setWorkflowMainTableInfo(wmi);//添加主字段数据
        wri.setWorkflowDetailTableInfos(WorkflowDetailTableInfo);//添加明细数据
        wri.setWorkflowBaseInfo(wbi);
        WorkflowServiceImpl workflowService=new WorkflowServiceImpl();
        String requestid = Util.null2String(workflowService.doCreateWorkflowRequest(wri,Integer.parseInt(bmJXMap.get("yjbmfzr"))));
        if(!"".equals(requestid)){
            if(Integer.parseInt(requestid) >0){
                result.put("status", "0");
                result.put("requestid", requestid);
            }else{
                result.put("status", "1");
                result.put("requestid", requestid);
            }
        }
        new BaseBean().writeLog("CreateJxWorkflowUtil: "+bmJXMap.get("yjbmfzr")+" 的 "+jxlxValue+"（0：季度，1：月度）部门绩效评价流程创建结果"+result.toJSONString());
        if("1".equals(result.getString("status"))){
            return false;
        }
        return true;
    }

    /**
     * 根据条件查到这个年份这个季度或月度这个一级部门的的下的绩效信息归档id
     * @param tableName
     * @param szyjbm
     * @param nf
     * @param jxszyf
     * @return
     */
    public static int select(String tableName, String szyjbm, String nf, String jxszyf){
        RecordSet rs = new RecordSet();
        int id = -1;
        rs.executeQuery("select id from "+tableName+" where yjbmmc = ? and jxsznf = ? and jxszyf = ? ", szyjbm , nf, jxszyf);
        if(rs.next()){
            id = rs.getInt("id");
        }
        return id;
    }

    public static List<Integer> selectHdRyList(int quarter, String field){
        RecordSet rs = new RecordSet();
        List<Integer> hdRyList = new ArrayList<>();
        String sql = "select xm from uf_jxzdhdb where "+field+" = ? and sfycj = 0";
        rs.executeQuery(sql, quarter);
        while (rs.next()){
            hdRyList.add(rs.getInt("xm"));
        }
        return hdRyList;
    }

    public static boolean deleteResignPeoples(String hdbTableName, String jxlx){
        RecordSet rs = new RecordSet();
        //删除核对表离职人员
        boolean deleteBmjdjxSql = rs.executeUpdate("delete a from "+ hdbTableName +" a inner join HrmResource b on a.xm = b.id where b.status >= 4");
        //删除个人基础表
        boolean deleteGrjxxxSql = rs.executeUpdate("delete a from uf_grjxxx a inner join HrmResource b on a.xm = b.id where b.status >= 4 b.jxlx = ?", jxlx);
        if(!deleteBmjdjxSql || !deleteGrjxxxSql){
            new BaseBean().writeLog("EvaluateForJDJXWorkflowCron.execute 删除离职人员失败");
            return false;
        }

        return true;
    }




}


package yitouniu.util;


import com.sap.conn.jco.JCoDestination;
import com.sap.conn.jco.JCoDestinationManager;
import com.sap.conn.jco.JCoFunction;
import com.sap.conn.jco.ext.DestinationDataProvider;
import weaver.file.Prop;
import weaver.general.BaseBean;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Properties;

/**
 * 连接sap
 */

public class ConnectPooled {
    private static final String DESTINATION_NAME = "ABAP_AS_WITH_POOL";



/*    private static Properties connectProperties;
    private static FileOutputStream fos;
    private static JCoDestination destination;
    private static JCoFunction function;*/
    private static volatile ConnectPooled connectPooled;
    private static Properties connectProperties;
    public ConnectPooled() {

    }
// 初始化参数
    static {
        initDestinationProperty();
    }

    public static ConnectPooled getInstance() {
        if (connectPooled == null) { // 其中一个线程进入该分支，另外一个线程则不会进入该分支

            if (connectPooled == null) {  // 线程A和线程B同时看到singleton = null，如果不为null，则直接返回singleton
                synchronized (ConnectPooled.class) { // 线程A或线程B获得该锁进行初始化

                    connectPooled = new ConnectPooled();

                }


            }
        }
        //connectPooled.initDestinationProperty(type, sapParams);
        return connectPooled;
    }

   /* public void init(String type, Map<String, String> sapParams) {
        String typedata = (String) typeMap.get("typedata");
        if (typedata != null && !"".equals(typedata)) {
            if (typedata.equals(type)) {
                if (connectProperties == null) {
                    initDestinationProperty(type, sapParams);
                } else {
                    createDestinationDataFile(DESTINATION_NAME, connectProperties);//创建文件

                }
            } else {
                initDestinationProperty(type, sapParams);
            }
        } else {
            typeMap.put("typedata", type);
            initDestinationProperty(type, sapParams);
        }
    }*/


    //初始化
    public static  void initDestinationProperty() {


           connectProperties = new Properties();

            // 正式环境使用负载均衡服务器访问
           connectProperties.setProperty(DestinationDataProvider.JCO_MSHOST, Prop.getPropValue("sap", "MSHOST"));
            connectProperties.setProperty(DestinationDataProvider.JCO_MSSERV, Prop.getPropValue("sap","MSSERV"));
            connectProperties.setProperty(DestinationDataProvider.JCO_GROUP, Prop.getPropValue("sap","GROUP"));
            connectProperties.setProperty(DestinationDataProvider.JCO_R3NAME, Prop.getPropValue("sap","R3NAME"));

        connectProperties.setProperty(DestinationDataProvider.JCO_SAPROUTER, Prop.getPropValue("sap","ROUTER"));
        connectProperties.setProperty(DestinationDataProvider.JCO_CLIENT, Prop.getPropValue("sap","CLIENT"));
        connectProperties.setProperty(DestinationDataProvider.JCO_USER, Prop.getPropValue("sap","USER"));
        connectProperties.setProperty(DestinationDataProvider.JCO_PASSWD,Prop.getPropValue("sap","PASSWD"));
        connectProperties.setProperty(DestinationDataProvider.JCO_POOL_CAPACITY, Prop.getPropValue("sap","POOL_CAPACITY"));
        connectProperties.setProperty(DestinationDataProvider.JCO_PEAK_LIMIT, Prop.getPropValue("sap","PEAK_LIMIT"));
        connectProperties.setProperty(DestinationDataProvider.JCO_LANG, Prop.getPropValue("sap","LANG"));


////            测试
//            connectProperties.setProperty(DestinationDataProvider.JCO_ASHOST, Prop.getPropValue("sap-cs","ASHOST"));
//            connectProperties.setProperty(DestinationDataProvider.JCO_SYSNR, Prop.getPropValue("sap-cs","SYSNR"));
//
//        connectProperties.setProperty(DestinationDataProvider.JCO_SAPROUTER, Prop.getPropValue("sap-cs","ROUTER"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_CLIENT, Prop.getPropValue("sap-cs","CLIENT"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_USER, Prop.getPropValue("sap-cs","USER"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_PASSWD,Prop.getPropValue("sap-cs","PASSWD"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_POOL_CAPACITY, Prop.getPropValue("sap-cs","POOL_CAPACITY"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_PEAK_LIMIT, Prop.getPropValue("sap-cs","PEAK_LIMIT"));
//        connectProperties.setProperty(DestinationDataProvider.JCO_LANG, Prop.getPropValue("sap-cs","LANG"));


        createDestinationDataFile(DESTINATION_NAME, connectProperties);//创建文件


    }

    //连接SAP
    private static void createDestinationDataFile(String destinationName, Properties connectProperties) {
        File destCfg = new File(destinationName + ".jcoDestination");
        new BaseBean().writeLog("Java客户端和SAP服务器尝试通信!");
        try {
            FileOutputStream fos = new FileOutputStream(destCfg, false);
            connectProperties.store(fos, "Java和SAP的连接！");
            fos.close();
            // System.out.println("Java客户端和SAP服务器通信成功!");
            new BaseBean().writeLog("Java客户端和SAP服务器通信成功!");
        } catch (Exception e) {
            new BaseBean().writeLog("SAP和Java无法正常通信，请检查！" + e);
        }
    }

    //调用函数
    public  String consumeABAPFM(String datum) {
        //initDestinationProperty();
        new  BaseBean().writeLog("consumeABAPFM sap获取数据开始");
        JCoDestination destination;
        JCoFunction function = null;
        try {
             destination = JCoDestinationManager.getDestination(DESTINATION_NAME);
            //函数名称--输入日期可以算出星期几  比如20160801  输出1
            function = destination.getRepository().getFunction("ZSYS_MAIN_FUNCTION_PROCESS");

            if (function == null) {
                new BaseBean().writeLog("SAP中不存在您调用的函数，请确认！");
            }


            function.getImportParameterList().setValue("IN_JSON", datum);
            function.execute(destination);
        } catch (Exception e) {
            StackTraceElement ste = e.getStackTrace()[0];
            new BaseBean().writeLog("异常信息: " + e);
            new BaseBean().writeLog("PurchaseContractCalibrationControl接口异常错误ste："+ste);
        }

        String out_json = function.getExportParameterList().getString("OUT_JSON");
        new BaseBean().writeLog("consumeABAPFM返回数据:"+out_json);
        new BaseBean().writeLog("请求数据:"+datum);
        return out_json;//结果
    }


}

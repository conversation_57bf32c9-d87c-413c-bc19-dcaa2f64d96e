package yitouniu.util;

import weaver.general.BaseBean;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 17:34 2022/3/16
 */
public class TimeUtils {
    /**
     * 时间戳转换为yyyy-MM-dd HH:mm:ss格式
     */
    public static String StampToTime() {
        //设置你的时间
        long stamp = 1535091681L;
        //新建一个时间对象
        final Date date = new Date(stamp);
        //你要转换成的时间格式,大小写不要变
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //转换你的时间
        final String yourtime = sdf.format(date);
        //打印出你转换后的时间
        new BaseBean().writeLog(stamp+" 转换后是："+yourtime);
        return yourtime;
    }


    /**
     * yyyy-MM-dd HH:mm:ss格式转换为时间戳 /
     */
    public static Long TimeToStamp(String changeTime) throws Exception {
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //将你的日期转换为时间戳
        changeTime = changeTime+" 00:00:00";
        final Date datetime = sdf.parse(changeTime);
        final long time = datetime.getTime();
        new BaseBean().writeLog(changeTime+"转换后是："+time);
        return time;
    }

    public static String changeTimeStr(String time, String afterFormat, String frontFormat) throws ParseException {
        SimpleDateFormat frontSdf = new SimpleDateFormat(frontFormat);
        SimpleDateFormat afterSdf = new SimpleDateFormat(afterFormat);
        return afterSdf.format(frontSdf.parse(time));
    }

    public static Date changeTimeToDate(String time, String afterFormat) throws ParseException {
        SimpleDateFormat afterSdf = new SimpleDateFormat(afterFormat);
        return afterSdf.parse(time);
    }

    public static String getTimeStr(Date time, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }


}

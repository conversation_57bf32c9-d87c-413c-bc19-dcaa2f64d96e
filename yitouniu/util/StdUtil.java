package yitouniu.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClient;
import com.google.common.base.Throwables;
import com.google.common.util.concurrent.RateLimiter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpHeaders;
import weaver.general.BaseBean;
import weaver.general.Util;
import yitouniu.thirdparty.std.dto.StdBaseResponse;

import javax.ws.rs.core.MediaType;
import javax.xml.bind.DatatypeConverter;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-05-22 10:06
 **/
public class StdUtil {
    private static final Log LOG = LogFactory.getLog(StdUtil.class);
    private static final String SUCCESS_CODE = "200";


    public static final String GATEWAY_URL = StrUtil.blankToDefault(new BaseBean().getPropValue("std_configuration", "gatewayUrl"),
            "https://std-test.ryytngroup.com");
    public static final String STD_AK = StrUtil.blankToDefault(new BaseBean().getPropValue("std_configuration", "accessKey"),
            "GR^&s&EQ*9Chz$Ag");
    public static final String STD_SK = StrUtil.blankToDefault(new BaseBean().getPropValue("std_configuration", "secretKey"),
            "uCCx6B#3eFyFgRnJEkd*64gV");
    public static final String STD_ORG_LEVEL_API = Util.null2String(new BaseBean().getPropValue("std_configuration", "stdOrgLevelApi"));
    public static final String STD_PRODUCT_LEVEL_API = Util.null2String(new BaseBean().getPropValue("std_configuration", "stdProductLevelApi"));

    private static final String STD_OSS_BUCKET_NAME =
            Util.null2String(new BaseBean().getPropValue("std_configuration", "stdOssBucketName"));

    private static final String STD_OSS_AK = "LTAI5tMJo1i4YGrBvS3KHv97";
    private static final String STD_OSS_SK = "******************************";
    private static final String STD_OSS_ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";

    private static final Map<String, RateLimiter> RATE_LIMITER_MAPTER_MAP = new HashMap<>();
    private static final int RATE_LIMIT = 15;


    private static OSSClient ossClient;

    private static OSS getOssClient() {
        if (Objects.isNull(ossClient)) {
            ossClient = new OSSClient(STD_OSS_ENDPOINT, STD_OSS_AK, STD_OSS_SK);
        }

        return ossClient;
    }


    public static void push2Oss(File file, String ossKey) {
        long start = System.currentTimeMillis();
        getOssClient().putObject(STD_OSS_BUCKET_NAME, ossKey, file);
        LOG.info("上传文件到OSS完成,源文件：" + file.getAbsolutePath() + "，文件大小:" + file.length()
                + ",目标地址:" + ossKey + ",耗时：" + (System.currentTimeMillis() - start));
    }

    /**
     * 请求STD接口-GET
     *
     * @param url          参数路径
     * @param formParamMap 请求体
     * @return 结果的字符串
     */
    public static String executeGet(String url, Map<String, Object> formParamMap) {
        if (StringUtils.isEmpty(url)) {
            LOG.warn("请求地址不能为空");
            throw new RuntimeException("请求地址不能为空");
        }

        RateLimiter rateLimiter = RATE_LIMITER_MAPTER_MAP.get(url);
        if (Objects.isNull(rateLimiter)) {
            rateLimiter = RateLimiter.create(RATE_LIMIT);
            RATE_LIMITER_MAPTER_MAP.put(url, rateLimiter);
        }
        rateLimiter.acquire();

        String timestamp = String.valueOf(System.currentTimeMillis());
        String responseBody = null;
        long startTime = System.currentTimeMillis();
        try (HttpResponse response = HttpRequest.get(GATEWAY_URL + url)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .header("timestamp", timestamp)
                .header("ak", STD_AK)
                .header("token", getApiSign(timestamp))
                .form(formParamMap)
                .execute()) {

            responseBody = response.body();
            LOG.info("STD接口GET请求完成,接口:" + url + ",入参:"
                    + JSONObject.toJSONString(formParamMap) + ",响应结果:" + responseBody
                    + ",耗时：" + (System.currentTimeMillis() - startTime));

            StdBaseResponse<String> resp = JSONObject.parseObject(responseBody, new TypeReference<StdBaseResponse<String>>(String.class) {
            });
            if (Objects.isNull(resp) || !SUCCESS_CODE.equals(resp.getCode())) {
                String msg = Objects.isNull(resp) ? "" : resp.getMessage();
                throw new RuntimeException("STD接口GET请求出错!" + msg);
            }
            return resp.getResult();
        } catch (Exception e) {
            LOG.warn("STD接口GET请求异常,接口:" + url + ",入参:"
                    + JSONObject.toJSONString(formParamMap) + ",响应结果:" + responseBody
                    + ",异常信息" + Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

    /**
     * 请求STD接口-POST
     *
     * @param url  参数路径
     * @param body 请求体
     * @return 结果的字符串
     */
    public static String executePost(String url, String body) {
        if (StringUtils.isEmpty(url)) {
            LOG.warn("请求地址不能为空");
            throw new RuntimeException("请求地址不能为空");
        }

        String timestamp = String.valueOf(System.currentTimeMillis());
        String responseBody = null;
        long startTime = System.currentTimeMillis();
        try (HttpResponse response = HttpRequest.post(GATEWAY_URL + url)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .header("timestamp", timestamp)
                .header("ak", STD_AK)
                .header("token", getApiSign(timestamp))
                .body(body)
                .execute()) {

            responseBody = response.body();
            LOG.info("STD接口POST请求完成,接口:" + url + ",入参:" + body + ",响应结果:" + responseBody
                    + ",耗时：" + (System.currentTimeMillis() - startTime));

            StdBaseResponse<String> resp = JSONObject.parseObject(responseBody, new TypeReference<StdBaseResponse<String>>(String.class) {
            });
            if (Objects.isNull(resp) || !SUCCESS_CODE.equals(resp.getCode())) {
                String msg = Objects.isNull(resp) ? "" : resp.getMessage();
                throw new RuntimeException("STD接口POST请求出错!" + msg);
            }
            return resp.getResult();
        } catch (Exception e) {
            LOG.warn("STD接口POST请求异常,接口:" + url + ",入参:" + body + ",响应结果:" + responseBody
                    + ",异常信息" + Throwables.getStackTraceAsString(e));
            throw e;
        }
    }


    public static String getApiSign(String timeStamp) {
        MessageDigest md5 = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest((timeStamp + STD_AK + STD_SK).getBytes(StandardCharsets.UTF_8));
            return DatatypeConverter.printHexBinary(digest).toLowerCase();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}

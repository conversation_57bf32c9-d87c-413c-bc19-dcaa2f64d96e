package yitouniu.util;

import com.alibaba.fastjson.JSONObject;
import com.ryytn.http.HttpClientResult;
import com.ryytn.http.HttpClientUtils;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/4 16:50
 * @Description TODO
 * @Version 1.0
 */
public class HecUtils {
    public static final String HEC_API_URL = Util.null2String(new BaseBean().getPropValue("hec","hecApiUrl"));
    public static final String HEC_TOKEN_URL = Util.null2String(new BaseBean().getPropValue("hec","hecTokenUrl"));
    public static final String CLIENT_SECRET = Util.null2String(new BaseBean().getPropValue("hec","clientSecret"));
    public static final String OA_URL = Util.null2String(new BaseBean().getPropValue("travelInfoAction","oaUrl"));
    public static final String FORMMODEID = Util.null2String(new BaseBean().getPropValue("travelInfoAction","dczformmodeid"));


    public static String getToken() throws Exception {
        Map<String,String> params = new HashMap<>();
        params.put("client_id","ry1tn-hec");
        params.put("grant_type","client_credentials");
        params.put("client_secret",CLIENT_SECRET);
        HttpClientResult result = HttpClientUtils.doPost(HEC_TOKEN_URL, params);
        new BaseBean().writeLog("travelInfoToHec :result " + result);

        return JSONObject.parseObject(result.getContent()).getString("access_token");
    }

}

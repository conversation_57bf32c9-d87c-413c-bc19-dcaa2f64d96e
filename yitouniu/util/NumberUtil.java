package yitouniu.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;

public class NumberUtil {

    /**
     * 千分位格式
     */
    public static final DecimalFormat df = new DecimalFormat(",###,##0.00");
    public static final int scale=2;

    /**
     * 加
     *
     * @param a  a
     * @param bs b
     * @return 结果
     */
    public static double add(String a, String... bs) {
        BigDecimal result = new BigDecimal(a);
        if (bs.length == 0) {
            return result.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        } else {
            for (String b : bs) {
                result = result.add(new BigDecimal(b));
            }
            return result.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    /**
     * 减
     *
     * @param a a
     * @param b b
     * @return 结果
     */
    public static double subtract(String a, String b) {
        BigDecimal aBD = new BigDecimal(a);
        BigDecimal bBD = new BigDecimal(b);
        return aBD.subtract(bBD).setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    /**
     * 乘
     *
     * @param a
     * @param bs
     * @return
     */
    public static double multiply(String a, String... bs) {
        BigDecimal result = new BigDecimal(a);
        if (bs.length == 0) {
            return result.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        } else {
            for (String b : bs) {
                result = result.multiply(new BigDecimal(b));
            }
            return result.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    /**
     * 除
     *
     * @param a a
     * @param b b
     * @return 结果
     */
    public static double divide(double a, double b, int length) {
        return divide(Double.toString(a), Double.toString(a), length);
    }

    public static double divide(double a, double b) {
        return divide(Double.toString(a), Double.toString(a));
    }

    public static double divide(String a, String b) {
        return divide(a, b, scale);
    }

    public static double divide(String a, String b, int length) {
        BigDecimal aBD = new BigDecimal(a);
        BigDecimal bBD = new BigDecimal(b);
        return aBD.divide(bBD, length, BigDecimal.ROUND_HALF_UP).doubleValue();
    }


    public static String format(double num) {
        return df.format(num);
    }

    public static double parse(String num) {
        double result = 0.00;
        try {
            result = df.parse(num).doubleValue();
        } catch (ParseException e) {

        }
        return result;
    }

}

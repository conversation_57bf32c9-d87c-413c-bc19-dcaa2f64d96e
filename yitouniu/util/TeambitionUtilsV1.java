package yitouniu.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/24 18:19
 * @Description TODO
 * @Version 1.0
 */
public class TeambitionUtilsV1 {

    public static JSONArray returnTaskUsers(Map<String, String> headers, List<String> userIds) {
        JSONObject params = new JSONObject();
        params.put("orgId", TeambitionBaseConfig.COMPANY_ID);
        params.put("userIds", userIds);
        String httpResponse = HttpClientUtils.doPostJson("https://open.teambition.com/api/org/member/batchGet", headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        JSONArray result = new JSONArray();
        int code = resp.getInteger("code");
        if(200 == code){
            result = resp.getJSONArray("result");
        } else {
            result = null;
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskUsers：查询任务返回json"+resp);
        //返回查到的task的信息
        return result;
    }

    /**
     *
     * @param headers
     * @param cfIds 逗号分割的字符串 例如 a,b,c
     * @return
     */
    public static JSONArray returnTaskCustomField(Map<String, String> headers, String cfIds, String projectId) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("orgId", TeambitionBaseConfig.COMPANY_ID);
        params.put("cfIds", cfIds);
        String httpResponse = HttpClientUtils.doGet("https://open.teambition.com/api/v3/project/" + projectId + "/customfield/search", headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        JSONArray result = new JSONArray();
        int code = resp.getInteger("code");
        if(200 == code){
            result = resp.getJSONArray("result");
        } else {
            result = null;
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskCustomField：查询任务返回json"+resp);
        //返回查到的task的信息
        return result;
    }

}

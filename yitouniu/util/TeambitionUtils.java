package yitouniu.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;

import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/10/24 18:19
 * @Description TODO
 * @Version 1.0
 */
public class TeambitionUtils {


    /**
     * 返回人员在teambition的id
     *
     * @param loginId 员工工号
     * @return
     */
    public static String returnUserId(String loginId, Map<String,String> headers) {
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：返回员工id=loginId=" + loginId);
        String user = null;
        String pageToken = null;
        for(int i= 0;i<10000;i++){
            Map<String, String> params = new HashMap<String, String>();
            params.put("orgId", TeambitionBaseConfig.COMPANY_ID);
            params.put("pageSize", String.valueOf(500));
            params.put("pageToken", pageToken);
            String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_ORG_MEMBER_LIST_GET, headers, params);
            JSONObject resp = JSONObject.parseObject(httpResponse);

            String code = resp.getString("code");
            List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
            if ("200".equals(code)) {
                for (JSONObject object : resultList) {
                    String employeeNumber = object.getString("employeeNumber");
                    if (loginId.equals(employeeNumber)) {
                        user = object.getString("userId");
                        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：查询人员id返回的json" + object.toJSONString());
                        break;
                    }
                }
                pageToken = resp.getString("nextPageToken");
                if(StringUtils.isBlank(pageToken)){
                    break;
                }
            }
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：返回员工在tb的id" + user + ";以及loginId=" + loginId);
        return user;

    }

    /**
     * 搜索返回人在teambition的id  by 登录名和登录id
     * @param loginName 员工名称
     * @param loginId 员工工号
     * @return
     */
    public static String returnUserIdByName(String loginName, String loginId, Map<String,String> headers) {
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：返回员工名字=loginName=" + loginName);
        String userId = null;
        Map<String, String> params = new HashMap<String, String>();
        params.put("orgId", TeambitionBaseConfig.COMPANY_ID);
        params.put("query", loginName);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：查询人员id请求params" + params);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_ORG_MEMBER_SEARCH, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：查询人员id返回的httpResponse" + httpResponse);


        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if ("200".equals(code)) {
            for (JSONObject object : resultList) {
                String employeeNumber = object.getString("employeeNumber");
                String name = object.getString("name");
                if (loginId.equals(employeeNumber) && loginName.equals(name)) {
                    userId = object.getString("userId");
                    new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：查询人员id返回的json" + object.toJSONString());
                    break;
                }
            }
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnUserId：返回员工在tb的id" + userId + ";以及loginId=" + loginId);
        return userId;
    }

    /**
     * 返回项目的一些信息，现在返回项目id、创建人id、项目编号前缀
     * 因为不建议我们oa存表，有点固定死了，看后期是否其他部门也需要在进行更新，
     *
     * @param projectName 开发语言
     * @return
     */
    public static Map<String, String> returnProject(String projectName, Map<String, String> headers) {
        Map<String, String> projectMap = new HashMap<String, String>();
        JSONObject params = new JSONObject();
        params.put("name", projectName);
        params.put("pageSize", 1000);
        String project = null;
        String creator = null;
        String uniqueIdPrefix = null;
        //查询企业的所有项目，根据一些条件去得到唯一的项目id
        String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfig.URL_API_PROJECT_QUERY_POST, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnProject：查询项目信息返回json" + resp);

        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if ("200".equals(code)) {
            for (JSONObject object : resultList) {
                if (projectName.equals(object.getString("name"))) {
                    project = object.getString("projectId");
                    creator = object.getString("creatorId");
                    uniqueIdPrefix = object.getString("uniqueIdPrefix");
                }
            }
        }
        projectMap.put("projectId", project);
        projectMap.put("creatorId", creator);
        projectMap.put("uniqueIdPrefix", uniqueIdPrefix);

        new BaseBean().writeLog("ESB-TeambitionUtils-returnProject：项目信息处理后返回的" + projectMap);

        return projectMap;
    }

    /**
     * 返回任务分组id
     * @param year        年
     * @param projectName 系统名称
     * @param projId      项目id
     * @return
     */
    public static String returntasklistId(String year, String projectName, String projId, Map<String, String> headers) {
        Map<String, String> params = new HashMap<String, String>();
        new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistId：需要比较的信息 year=" + year + ",devLanguage=" + projectName);

        params.put("projectId", projId);
        String taskgroup = null;
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASKGROUP_QUERY_GET, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if ("200".equals(code)) {
            for (JSONObject object : resultList) {
                String name = object.getString("name");
                new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistId：name=" + name);
                if (name.contains(year) && name.contains(projectName)) {
                    taskgroup = object.getString("taskgroupId");
                    new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistId：查询项目分组返回匹配上的json" + object.toJSONString());
                    break;
                }
                if ("数据中台项目".equals(projectName)) {
                    if (name.contains(year) && name.contains("迭代")) {
                        taskgroup = object.getString("taskgroupId");
                        new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistId：查询项目分组返回匹配上的json" + object.toJSONString());
                        break;
                    }
                } else if ("RPA".equals(projectName)) {
                    if (name.contains(year) && name.contains("XX")) {
                        taskgroup = object.getString("taskgroupId");
                        new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistId：查询项目分组返回匹配上的json" + object.toJSONString());
                        break;
                    }
                }
            }
        }
        return taskgroup;
    }

    /**
     * 返回任务分组id
     * @param year        年
     * @param projectTasklistName 开发语言
     * @param projId      项目id
     * @param headers      请求头
     * @return
     */
    public static String returntasklistIdV3(String year, String projectTasklistName, String projId, Map<String, String> headers) {
        Map<String, String> params = new HashMap<String, String>();
        new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistIdV3：需要比较的信息 year=" + year + ",projectTasklistName=" + projectTasklistName);

//        params.put("projectId", projId);
        String taskListId = null;
        String httpResponse = HttpClientUtils.doGet(String.format(TeambitionBaseConfig.URL_API_PROJECT_TASKLIST_SEARCH_V3, projId), headers, params);
        new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistIdV3：查询项目分组返回的json" + httpResponse);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        int code = resp.getInteger("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if (code == 200) {
            for (JSONObject object : resultList) {
                String title = object.getString("title");
                new BaseBean().writeLog("ESB-TeambitionUtils-returntasklistIdV3：title=" + title);
                if (title.contains(year) && title.contains(projectTasklistName)) {
                    taskListId = object.getString("id");
                    break;
                }
            }
        }
        return taskListId;
    }

    /**
     * 返回任务列表id
     * @param month      月份
     * @param projId     项目id
     * @param tasklistId 任务分组id
     * @return
     */
    public static String returnStageIdV3(String month, String day, String projId, String tasklistId, Map<String, String> headers) {
        String searchName = "";
        String defaultSearchName = "未分类";
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH,Integer.parseInt(day));
        int week = calendar.get(Calendar.WEEK_OF_MONTH);

        searchName = Integer.parseInt(month) + "月第" + convert(week) + "周";
        new BaseBean().writeLog("ESB-TeambitionUtils-returnStageIdV3 返回 searchName " + searchName);

        Map<String, String> params = new HashMap<String, String>();
        params.put("tasklistId", tasklistId);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnStageIdV3：请求params = " + params);
        String rspStageId = null;
        String defaultStageId = null;
        String httpResponse = HttpClientUtils.doGet(String.format(TeambitionBaseConfig.URL_API_PROJECT_STAGE_SEARCH_V3, projId), headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnStageIdV3：返回resp " + resp.toJSONString());
        int code = resp.getInteger("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if (code == 200) {
            for (JSONObject object : resultList) {
                String name = object.getString("name");
                if (name.contains(searchName)) {
                    rspStageId = object.getString("id");
                    break;
                } else if (name.contains(defaultSearchName)) {
                    defaultStageId = object.getString("id");
                }
            }
        }
        return StringUtils.isNotEmpty(rspStageId)? rspStageId : defaultStageId;
    }

    public static String returnStageNameV3(String projId, String stageIds, Map<String, String> headers) {
        String stageName = "";
        Map<String, String> params = new HashMap<String, String>();
        params.put("stageIds", stageIds);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnStageNameV3：请求params = " + params);
        String httpResponse = HttpClientUtils.doGet(String.format(TeambitionBaseConfig.URL_API_PROJECT_STAGE_SEARCH_V3, projId), headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnStageNameV3：返回resp " + resp.toJSONString());
        int code = resp.getInteger("code");
        if (code == 200) {
            stageName = resp.getJSONArray("result").getJSONObject(0).getString("name");
        }
        return stageName;
    }

    /**
     * 查询任务类型，通过Map返回任务模板id，工作流id，自定义字段id
     *
     * @param project 项目id
     * @return
     */
    public static Map returnTaskTypes(String project, Map<String, String> headers) {
        Map taskTypeMap = new HashMap();
        Map<String, String> params = new HashMap();
        params.put("projectId", project);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TEMPLATE_QUERY_GET, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskTypes：查询任务类型后返回json" + resp);
        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if ("200".equals(code)) {
            for (JSONObject object : resultList) {
                String name = object.getString("name");
                //应该确保name为任务的这个工作类型只有一个，否则需要修改
                Map<String, String> resultMap = new HashMap();
                //模板id
                resultMap.put("templateId", object.getString("templateId"));
                //工作流id
                resultMap.put("taskflowId", object.getString("taskflowId"));
                List<JSONObject> customfieldsList = (List<JSONObject>) object.get("customfields");
                for (JSONObject obj : customfieldsList) {
                    String fieldType = obj.getString("fieldType");
                    //额外的自定义字段
                    if ("customfield".equals(fieldType)) {
                        resultMap.put(obj.getString("name"), obj.getString("cfId"));
                        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskTypes：name:" + obj.getString("name") + ";cfId:" + obj.getString("cfId"));
                    }
                }
                taskTypeMap.put(name, resultMap);

            }
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskTypes：处理后的任务类型返回Map" + taskTypeMap);

        return taskTypeMap;
    }

    /**
     * 查询任务类型，返回自定义字段
     *
     * @param project 项目id
     * @return
     */
    public static Map<String, String> returnTaskCfTypes(String project, Map<String, String> headers, String taskTypeName) {
        Map<String, String> taskTypeMap = new HashMap();
        Map<String, String> params = new HashMap();
        params.put("projectId", project);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TEMPLATE_QUERY_GET, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskCfTypes：查询任务类型后返回json" + resp);
        int code = resp.getInteger("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if (200 == code) {
            for (JSONObject object : resultList) {
                String name = object.getString("name");
                //应该确保name为任务的这个工作类型只有一个，否则需要修改
                if(taskTypeName.equals(name)){
                    List<JSONObject> customfieldsList = (List<JSONObject>) object.get("customfields");
                    for (JSONObject obj : customfieldsList) {
                        String fieldType = obj.getString("fieldType");
                        //额外的自定义字段
                        if ("customfield".equals(fieldType)) {
                            taskTypeMap.put(obj.getString("cfId"), obj.getString("name"));
                        }
                    }
                }
            }
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskTypes：处理后的任务类型返回Map" + taskTypeMap);

        return taskTypeMap;
    }

    /**
     * 查询返回状态的id，一般性初始化为，后期优化
     *
     * @param taskflow 工作流id
     * @return
     */
    public static String returnTaskflowStatusId(String taskflow, String status, Map<String, String> headers) {
        String taskflowStatusId = null;
        Map<String, String> params = new HashMap<String, String>();
        params.put("taskflowId", taskflow);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASKFLOW_QUERY_GET, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        String code = resp.getString("code");
        JSONObject resultObject = resp.getJSONObject("result");
        List<JSONObject> statusesList = (List<JSONObject>) resultObject.get("statuses");
        if ("200".equals(code)) {
            for (JSONObject stat : statusesList) {
                String name = stat.getString("name");
                if (status.equals(name)) {
                    taskflowStatusId = stat.getString("id");
                    break;
                }
            }
        }
        return taskflowStatusId;
    }

    /**
     * 查询返回状态的id，一般性初始化为，后期优化
     *
     * @param taskflow 工作流id
     * @return
     */
    public static String returnTaskflowStatusIdV3(String taskflow, String status, String projId, Map<String, String> headers) {
        String taskflowStatusId = null;
        Map<String, String> params = new HashMap<String, String>();
        params.put("taskflowId", taskflow);
        new BaseBean().writeLog("TeambitionUtils:returnTaskflowStatusIdV3:"+params);
        String httpResponse = HttpClientUtils.doGet(String.format(TeambitionBaseConfig.URL_API_PROJECT_TASKFLOWSTARUS_SEARCH_V3, projId), headers, params);
        new BaseBean().writeLog("TeambitionUtils:returnTaskflowStatusIdV3:"+httpResponse);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        int code = resp.getInteger("code");
        List<JSONObject> result = (List<JSONObject>)resp.get("result");
        if (code == 200) {
            for (JSONObject stat : result) {
                String name = stat.getString("name");
                if (status.equals(name)) {
                    taskflowStatusId = stat.getString("id");
                    break;
                }
            }
        }
        return taskflowStatusId;
    }

    /**
     * 查询工作流状态的名称
     *
     * @param tfsIds 工作流状态id
     * @return
     */
    public static String returnTaskflowStatusNameIdV3(String tfsIds, String projId, Map<String, String> headers) {
        String taskflowStatusName = null;
        Map<String, String> params = new HashMap<String, String>();
        params.put("tfsIds", tfsIds);
        new BaseBean().writeLog("TeambitionUtils:returnTaskflowStatusNameIdV3:"+params);
        String httpResponse = HttpClientUtils.doGet(String.format(TeambitionBaseConfig.URL_API_PROJECT_TASKFLOWSTARUS_SEARCH_V3, projId), headers, params);
        new BaseBean().writeLog("TeambitionUtils:returnTaskflowStatusNameIdV3:"+httpResponse);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        int code = resp.getInteger("code");
        List<JSONObject> result = (List<JSONObject>)resp.get("result");
        if (code == 200) {
            taskflowStatusName = result.get(0).getString("name");
        }
        return taskflowStatusName;
    }




    /**
     * 返回单个任务信息
     * @param taskId 任务id
     * @return
     */
    public static JSONObject returnTaskInfo(String taskId, Map<String, String> headers){
        Map<String, String> params = new HashMap<String, String>();
        params.put("taskId", taskId);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASK_QUERY_GET_V3, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        String code = resp.getString("code");
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskInfo：查询任务返回json"+resp);
        //返回查到的task的信息
        return ((List<JSONObject>) resp.get("result")).get(0);
    }

    /**
     * 返回所有任务信息
     * @param taskIds 任务id
     * @return
     */
    public static JSONArray returnTaskInfos(String taskIds, Map<String, String> headers){
        Map<String, String> params = new HashMap<String, String>();
        params.put("taskId", taskIds);
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskInfo：taskIds"+taskIds);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASK_QUERY_GET_V3, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        JSONArray result = new JSONArray();
        int code = resp.getInteger("code");
        if(200 == code){
            result = resp.getJSONArray("result");
        } else {
            result = null;
        }
        new BaseBean().writeLog("ESB-TeambitionUtils-returnTaskInfo：查询任务返回json"+resp);
        //返回查到的task的信息
        return result;
    }

    public static String convert(int number) {
        //数字对应的汉字
        String[] num = {"一", "二", "三", "四", "五", "六", "七", "八", "九"};
        //单位
        String[] unit = {"", "十", "百", "千", "万", "十", "百", "千", "亿", "十", "百", "千", "万亿"};
        //将输入数字转换为字符串
        String result = String.valueOf(number);
        //将该字符串分割为数组存放
        char[] ch = result.toCharArray();
        //结果 字符串
        String str = "";
        int length = ch.length;
        for (int i = 0; i < length; i++) {
            int c = (int) ch[i] - 48;
            if (c != 0) {
                str += num[c - 1] + unit[length - i - 1];
            }
        }
        return str;
    }


}

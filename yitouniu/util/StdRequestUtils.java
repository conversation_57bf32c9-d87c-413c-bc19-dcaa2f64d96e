package yitouniu.util;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpHeaders;
import yitouniu.thirdparty.std.dto.StdBaseRequest;
import yitouniu.thirdparty.std.dto.StdBaseResponse;

import javax.ws.rs.core.MediaType;
import javax.xml.bind.DatatypeConverter;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * STD接口调用工具类,线上未使用，详见{@link StdUtil}
 *
 * <AUTHOR>
 * @since 2024-06-24 14:07
 */
@Deprecated
public class StdRequestUtils {
    private static final Log LOG = LogFactory.getLog(StdRequestUtils.class);


    /*测试环境配置信息*/
    private static final String DOMAIN = "https://std-test.ryytngroup.com";
    private static final String ACCESS_KEY = "GR^&s&EQ*9Chz$Ag";
    private static final String SECRET_KEY = "uCCx6B#3eFyFgRnJEkd*64gV";


    @Deprecated
    public static <T> StdBaseResponse<T> executePost(StdBaseRequest request) {
        return executePost(request.apiUrl(), JSONObject.toJSONString(request));
    }

    /**
     * 请求STD接口
     *
     * @param url  参数路径
     * @param body 请求体
     * @return 返回体
     */
    public static <T> StdBaseResponse<T> executePost(String url, String body) {
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(body)) {
            LOG.warn("请求地址或参数不能为空");
            throw new RuntimeException("请求地址或参数不能为空");
        }

        String timestamp = String.valueOf(System.currentTimeMillis());
        String responseBody = null;
        try (HttpResponse response = HttpRequest.post(DOMAIN + url)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON)
                .header("timestamp", timestamp)
                .header("ak", ACCESS_KEY)
                .header("token", sign(timestamp))
                .body(body)
                .execute()) {

            responseBody = response.body();
            LOG.info("STD接口POST请求完成,接口:" + url + ",入参:" + body + ",响应结果:" + responseBody);
            return JSONObject.parseObject(responseBody, StdBaseResponse.class);
        } catch (Exception e) {
            LOG.warn("STD接口POST请求异常,接口:" + url + ",入参:" + body + ",响应结果:" + responseBody
                    + ",异常信息" + Throwables.getStackTraceAsString(e));
            throw new RuntimeException(e);
        }
    }


    private static String sign(String timestamp) {
        String signStr = timestamp + ACCESS_KEY + SECRET_KEY;
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] digest = md5.digest(signStr.getBytes(StandardCharsets.UTF_8));
            return DatatypeConverter.printHexBinary(digest).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            LOG.warn("STD接口对接签名时MD5加密失败：{}", e);
            throw new RuntimeException("STD接口对接签名时MD5加密失败");
        }
    }


}

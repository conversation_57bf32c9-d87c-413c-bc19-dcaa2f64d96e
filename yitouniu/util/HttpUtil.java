package yitouniu.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;

public class HttpUtil {

    public static String httpJsonPost(String uri, JSONObject param) throws IOException {
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        HttpPost httppost = new HttpPost(uri);
        httppost.addHeader("charset", "utf-8");
        try {
            StringEntity entity = new StringEntity(param.toString(), "utf-8");// 解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httppost.setEntity(entity);
            HttpResponse httpResponse = closeableHttpClient.execute(httppost);
            String json = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            return json;
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                // 关闭流并释放资源
                closeableHttpClient.close();
            } catch (IOException e) {
                //throw e;
            }
        }
    }

    public static String httpXmlPost(String uri, String param) throws IOException {
        // 创建HttpClientBuilder
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        HttpPost httppost = new HttpPost(uri);
        httppost.addHeader("charset", "utf-8");
        try {
            StringEntity entity = new StringEntity(param, "utf-8");// 解决中文乱码问题
            entity.setContentEncoding("UTF-8");
            entity.setContentType("text/xml");
            httppost.setEntity(entity);
            HttpResponse httpResponse = closeableHttpClient.execute(httppost);
            String xmlResp = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            return xmlResp;
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            try {
                // 关闭流并释放资源
                closeableHttpClient.close();
            } catch (IOException e) {
                //throw e;
            }
        }
    }


}

package yitouniu.util;

import com.cloudstore.dev.api.bean.MessageBean;
import com.cloudstore.dev.api.bean.MessageType;
import com.cloudstore.dev.api.util.Util_Message;

import java.lang.reflect.Constructor;
import java.util.Set;


// 发送钉钉消息
public class SendDDMsgUtil {

    /**
     *
     * @param id  消息来源的绑定标识
     * @param userIdList 要发送用户的id集合
     * @param title  标题
     * @param context  内容
     * @param linkUrl  PC端链接 纯文本就传空字符串
     * @param linkMobileUrl 移动端链接 纯文本就传空字符串
     * @return
     */
    public static boolean send(int id,Set<String> userIdList,String title,String context,String linkUrl,String linkMobileUrl){

        try {
            Class c = Class.forName("com.cloudstore.dev.api.bean.MessageType");

            Constructor declaredConstructor = c.getDeclaredConstructor(int.class, int.class);
            declaredConstructor.setAccessible(true);
            MessageType messageType = (MessageType) declaredConstructor.newInstance(id, 0);
            MessageBean messageBean = Util_Message.createMessage(messageType, userIdList, title, context, linkUrl, linkMobileUrl);
            messageBean.setCreater(1);//创建人id
            // messageBean.setTargetId("121|22"); //消息来源code +“|”+业务id  需要修改消息状态时传入
            boolean b = Util_Message.store(messageBean);
            return b;
        } catch (Exception e) {

        }
        return  true;
    }
}

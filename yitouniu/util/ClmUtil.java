package yitouniu.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CLM (Contract Lifecycle Management) 工具类
 * 用于处理甄零接口的OAuth 2.0授权和API调用
 */
public class ClmUtil {
    
    private static final Log logger = LogFactory.getLog(ClmUtil.class);
    
    // CLM环境配置
    // UAT环境
    public static final String CLM_UAT_BASE_URL = "https://oc-uat.onecontract-cloud.com";
    public static final String CLM_UAT_CLIENT_ID_BI = "ryytn-biadmin";
    public static final String CLM_UAT_CLIENT_SECRET_BI = "ryytn-biadminryytn-biadminryytn-biadmin";

    // PROD环境 - 需要根据实际情况配置
    public static final String CLM_PROD_BASE_URL = "https://oc-prod.onecontract-cloud.com";
    // PROD环境的client_id和client_secret需要从甄零获取
    
    // OAuth相关常量
    public static final String OAUTH_TOKEN_PATH = "/oauth/oauth/token";
    public static final String GRANT_TYPE_CLIENT_CREDENTIALS = "client_credentials";
    public static final String DEFAULT_SCOPE = "default";
    public static final String TOKEN_TYPE_BEARER = "bearer";
    
    // 内存中的token缓存
    private static Map<String, TokenInfo> tokenCache = new HashMap<>();
    
    /**
     * Token信息内部类
     */
    private static class TokenInfo {
        private String accessToken;
        private String tokenType;
        private long expiresAt; // 过期时间戳
        
        public TokenInfo(String accessToken, String tokenType, int expiresIn) {
            this.accessToken = accessToken;
            this.tokenType = tokenType;
            // 提前30秒过期，避免边界情况
            this.expiresAt = System.currentTimeMillis() + (expiresIn - 30) * 1000L;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() >= expiresAt;
        }
        
        public String getAuthorizationHeader() {
            return tokenType + " " + accessToken;
        }
    }
    
    /**
     * 获取访问令牌 - 客户端模式
     * @param baseUrl CLM基础URL
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return 访问令牌信息
     */
    public static TokenInfo getAccessToken(String baseUrl, String clientId, String clientSecret) {
        String cacheKey = baseUrl + ":" + clientId;
        
        // 检查缓存中是否有有效的token
        TokenInfo cachedToken = tokenCache.get(cacheKey);
        if (cachedToken != null && !cachedToken.isExpired()) {
            logger.info("使用缓存的access_token");
            return cachedToken;
        }
        
        String tokenUrl = baseUrl + OAUTH_TOKEN_PATH;
        
        try {
            // 构建请求参数
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("grant_type", GRANT_TYPE_CLIENT_CREDENTIALS));
            params.add(new BasicNameValuePair("client_id", clientId));
            params.add(new BasicNameValuePair("client_secret", clientSecret));
            params.add(new BasicNameValuePair("scope", DEFAULT_SCOPE));
            
            // 发送POST请求
            String response = httpFormPost(tokenUrl, params);
            
            // 解析响应
            JSONObject responseJson = JSONObject.parseObject(response);
            
            if (responseJson.containsKey("access_token")) {
                String accessToken = responseJson.getString("access_token");
                String tokenType = responseJson.getString("token_type");
                int expiresIn = responseJson.getIntValue("expires_in");
                
                TokenInfo tokenInfo = new TokenInfo(accessToken, tokenType, expiresIn);
                
                // 缓存token
                tokenCache.put(cacheKey, tokenInfo);
                
                logger.info("成功获取access_token，有效期: " + expiresIn + "秒");
                new BaseBean().writeLog("CLM获取token成功: " + tokenType + " " + accessToken);
                
                return tokenInfo;
            } else {
                logger.error("获取access_token失败: " + response);
                new BaseBean().writeLog("CLM获取token失败: " + response);
                throw new RuntimeException("获取access_token失败: " + response);
            }
            
        } catch (Exception e) {
            logger.error("获取access_token异常", e);
            new BaseBean().writeLog("CLM获取token异常: " + e.getMessage());
            throw new RuntimeException("获取access_token异常", e);
        }
    }
    
    /**
     * 获取UAT环境BI客户端的访问令牌
     * @return 访问令牌信息
     */
    public static TokenInfo getUatBiToken() {
        return getAccessToken(CLM_UAT_BASE_URL, CLM_UAT_CLIENT_ID_BI, CLM_UAT_CLIENT_SECRET_BI);
    }
    
    /**
     * 发送带授权的GET请求到CLM
     * @param url 请求URL
     * @param token 访问令牌信息
     * @return 响应内容
     */
    public static String httpGetWithAuth(String url, TokenInfo token) throws IOException {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        
        try {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Authorization", token.getAuthorizationHeader());
            httpGet.setHeader("Content-Type", "application/json;charset=utf-8");
            
            HttpResponse httpResponse = closeableHttpClient.execute(httpGet);
            String response = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            
            logger.info("CLM GET请求成功: " + url);
            return response;
            
        } finally {
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                logger.warn("关闭HttpClient异常", e);
            }
        }
    }
    
    /**
     * 发送带授权的JSON POST请求到CLM
     * @param url 请求URL
     * @param jsonData JSON数据
     * @param token 访问令牌信息
     * @return 响应内容
     */
    public static String httpJsonPostWithAuth(String url, JSONObject jsonData, TokenInfo token) throws IOException {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", token.getAuthorizationHeader());
            httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
            
            StringEntity entity = new StringEntity(jsonData.toString(), "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            
            HttpResponse httpResponse = closeableHttpClient.execute(httpPost);
            String response = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            
            logger.info("CLM JSON POST请求成功: " + url);
            new BaseBean().writeLog("CLM请求: " + url + ", 参数: " + jsonData.toString());
            new BaseBean().writeLog("CLM响应: " + response);
            
            return response;
            
        } finally {
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                logger.warn("关闭HttpClient异常", e);
            }
        }
    }
    
    /**
     * 发送表单POST请求
     * @param url 请求URL
     * @param params 表单参数
     * @return 响应内容
     */
    private static String httpFormPost(String url, List<NameValuePair> params) throws IOException {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
            
            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(params, "utf-8");
            httpPost.setEntity(entity);
            
            HttpResponse httpResponse = closeableHttpClient.execute(httpPost);
            String response = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            
            return response;
            
        } finally {
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                logger.warn("关闭HttpClient异常", e);
            }
        }
    }
    
    /**
     * 清除指定客户端的token缓存
     * @param baseUrl CLM基础URL
     * @param clientId 客户端ID
     */
    public static void clearTokenCache(String baseUrl, String clientId) {
        String cacheKey = baseUrl + ":" + clientId;
        tokenCache.remove(cacheKey);
        logger.info("已清除token缓存: " + cacheKey);
    }
    
    /**
     * 清除所有token缓存
     */
    public static void clearAllTokenCache() {
        tokenCache.clear();
        logger.info("已清除所有token缓存");
    }

}

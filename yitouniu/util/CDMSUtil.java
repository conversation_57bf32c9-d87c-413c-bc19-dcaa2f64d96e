package yitouniu.util;


import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.general.BaseBean;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class CDMSUtil {

    //盘点接口
    public static final String INVENTORY = "inventory";
    //报废接口
    public static final String SCRAP = "scrap";

    private static final String FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";

    //测试环境
    public final static String URL ="http://115.238.39.2:27001/retail-app/oa/";

    //正式环境
//    public final static String URL ="http://47.96.239.54:17001/retail-app/oa/";

    private static String getUrl(String method) {
        String url = URL +method +"/";
        return url;
    }

    public static String pushCdms( String method, JSONObject jsonObject) {
        new BaseBean().writeLog("method : "+method);
        new BaseBean().writeLog("body : "+jsonObject);
        String result = "";
        try {
            String url = getUrl(method);
            result = HttpUtil.httpJsonPost(url, jsonObject);
            new BaseBean().writeLog("中间件返回 : "+result);
        } catch (IOException e) {
            new BaseBean().writeLog("调用中间件失败 : "+method);
            e.printStackTrace();
        }
        return result;
    }









}

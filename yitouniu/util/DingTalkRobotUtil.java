package yitouniu.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkrobot_1_0.Client;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOHeaders;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponse;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponseBody;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.taobao.api.ApiException;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/6 11:11
 * @Description 钉钉机器人
 * @Version 1.0
 */
public class DingTalkRobotUtil {
    /**
     * CorpId
     */
    public final static String CORPID = "dinga9e6db0a7491905135c2f4657eb6378f";

    public final static String OA_AGENTID = "1033996594";
    public final static String OA_ROBOT_APPKEY = "ding370bf6nydf1ztuo7";
    public final static String OA_ROBOT_APPSECRET = "ar71FOl64xb7UTxvylPdw0VkBRXD5qdgD7gEg6CWiAoe0OW1MeMnYsRu5E-SsRmd";


    /**
     * 使用 Token 初始化账号Client
     * @return Client
     * @throws Exception
     */
    public static Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    public static String userGetByMobile(String mobile, String token) {
        String userId = null;
        OapiV2UserGetbymobileResponse response = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
            OapiV2UserGetbymobileRequest request = new OapiV2UserGetbymobileRequest();
            request.setMobile(mobile);
            new BaseBean().writeLog("userGetByMobile  req = {}" + JSON.toJSONString(request));
            response = client.execute(request, token);
            new BaseBean().writeLog("userGetByMobile  response = {}"+ JSON.toJSONString(response));
            if(response != null && response.isSuccess()){
                return response.getResult().getUserid();
            }
            for (int i = 1; i < 4; i++) {
                Thread.sleep(1000);
                new BaseBean().writeLog("userGetByMobile 请求失败，重试第{"+i+"}次...");
                response = client.execute(request);
                if(response != null){
                    return response.getResult().getUserid();
                }
            }
            new BaseBean().writeLog("userGetByMobile 请求异常 request："+JSON.toJSON(request)+"，response = " + JSON.toJSON(response));
        } catch (Exception e) {
            new BaseBean().writeLog("DingTalkSender   userGetByMobile 异常 = ", e);
        }
        return null;
    }

    /**
     * 获取token
     * @param appKey
     * @param appSecret
     * @return
     */
    public static String getToken(String appKey, String appSecret) {
        OapiGettokenResponse response = null;
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(appKey);
            request.setAppsecret(appSecret);
            request.setHttpMethod("GET");
            new BaseBean().writeLog("getToken  request = {}"+ JSON.toJSONString(request));
            response = client.execute(request);
            new BaseBean().writeLog("getToken  response = {}"+ JSON.toJSONString(response));
            if(response != null && StringUtils.isNotBlank(response.getAccessToken())){
                return response.getAccessToken();
            }
            for (int i = 1; i < 4; i++) {
                Thread.sleep(1000);
                new BaseBean().writeLog("getToken 请求失败，重试第{"+i+"}次...");
                response = client.execute(request);
                if(response != null && StringUtils.isNotBlank(response.getAccessToken())){
                    return response.getAccessToken();
                }
            }
            new BaseBean().writeLog("getToken 请求异常 request："+JSON.toJSON(request)+"，response = " + JSON.toJSON(response));
        } catch (Exception e) {
            new BaseBean().writeLog("DingTalkRobotUtil getToken 异常 = ", e);
        }
        return null;
    }


    /**
     * 机器人发送钉钉私聊消息
     * @param appKey
     * @param userIds
     * @param msgKey
     * @param msgParam
     * @param token
     * @return
     */
    public static BatchSendOTOResponseBody batchSend(String appKey, List<String> userIds, String msgKey, String msgParam, String token) {
        BatchSendOTOResponseBody rspBody = null;
        BatchSendOTOResponse rsp = null;
        try {
            Client client = createClient();
            BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
            batchSendOTOHeaders.xAcsDingtalkAccessToken = token;
            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest()
                    .setRobotCode(appKey)
                    .setUserIds(userIds)
                    .setMsgKey(msgKey)
                    .setMsgParam(msgParam);
            new BaseBean().writeLog("batchSend  batchSendOTORequest = {}"+ JSON.toJSONString(batchSendOTORequest));
            rsp = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
            new BaseBean().writeLog("batchSend  BatchSendOTOResponse = {}"+ JSON.toJSONString(rsp));
            if(rsp != null && rsp.getBody() != null){
                return rsp.getBody();
            }
            for (int i = 1; i < 4; i++) {
                Thread.sleep(1000);
                new BaseBean().writeLog("batchSend 请求失败，重试第{"+i+"}次...");
                rsp = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
                if (rsp != null && rsp.getBody() != null) {
                    return rsp.getBody();
                }
            }
            new BaseBean().writeLog("batchSend 请求异常，request："+JSON.toJSON(batchSendOTORequest)+"，response = " + JSON.toJSON(rsp));
        } catch (TeaException err) {
            new BaseBean().writeLog("DingTalkSender batchSend TeaException  err = "+err);

//            rspBody = batchSend(appKey, userIds, msgKey, msgParam, token);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("DingTalkSender batchSend TeaException  err.code = "+err.code+", err.message = " + err.message + err);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        } catch (Exception _err) {
            new BaseBean().writeLog("DingTalkSender batchSend Exception  _err = "+_err);
//            rspBody = batchSend(appKey, userIds, msgKey, msgParam, token);
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("DingTalkSender  batchSend Exception  err.code = " + err.code + ", err.message =  " + err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return rspBody;
    }






}

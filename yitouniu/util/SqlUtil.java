package yitouniu.util;

import weaver.conn.RecordSet;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-03-01 23:33
 */
public class SqlUtil {
    /**
     * @param sqlfiield 字段名
     * @param sqltable  数据库表名
     * @param sqlwhere  条件
     * @param obj       参数
     * @return
     */
    public static String query(String sqlfiield, String sqltable, String sqlwhere, Object... obj) {
        try {
            RecordSet rs = new RecordSet();
            String sql = "select " + sqlfiield + " from " + sqltable + " where " + sqlwhere;
            rs.executeQuery(sql, obj);
            if (rs.next()) {
                return rs.getString(1);
            }
        } catch (Exception e) {
            return "";
        }
        return "";
    }

    public static List<Map<String, String>> getTableDate(String sql, Object... parameter) {
        List<Map<String, String>> result = new ArrayList<>();
        Map<String, String> row;
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, parameter);
        String[] columnNameArray = rs.getColumnName();

        while (rs.next()) {
            row = new HashMap<String, String>(15);
            for (String columnName : columnNameArray) {
                row.put(columnName.toUpperCase(), rs.getString(columnName));
            }
            result.add(row);
        }

        return result;
    }
}

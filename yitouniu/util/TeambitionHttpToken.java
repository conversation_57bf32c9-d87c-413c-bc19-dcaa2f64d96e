package yitouniu.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 15:01 2021/12/13
 * @Modified By:
 */
public class TeambitionHttpToken {
    //过期时间，应为1小时=1*3600 * 1000L
    public static final Long   EXPIRES_IN = 24 * 3600 * 1000L;
    public static final String TOKEN_APPID = "_appId";

    public static String genAppToken(String appId, String appSecret) throws UnsupportedEncodingException {
        if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
            return null;
        }

        Algorithm algorithm = Algorithm.HMAC256(appSecret);
        long timestamp = System.currentTimeMillis();
        Date issuedAt = new Date(timestamp);
        Date expiresAt = new Date(timestamp + EXPIRES_IN);

        return JWT.create()
                .withClaim(TOKEN_APPID, appId)
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .sign(algorithm);
    }

    /**
     * 返回统一的Teambition的HttpHeader请求头
     * @return headers
     */
    public static Map<String, String> returnHeaders(String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type","application/json;charset=utf-8");
        headers.put("Authorization","Bearer "+ token);
        headers.put("X-Tenant-Id",TeambitionBaseConfig.COMPANY_ID);
        headers.put("X-Tenant-Type","organization");
        new BaseBean().writeLog("TeambitionHttpToken:returnHeaders:headers = "+headers);
        return headers;
    }

    /**
     * 返回统一的Teambition的HttpHeader请求头
     * @return headers
     */
    public static Map<String, String> getHeaders(String operatorId, String token) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type","application/json;charset=utf-8");
        headers.put("Authorization","Bearer "+ token);
        headers.put("X-Tenant-Id",TeambitionBaseConfig.COMPANY_ID);
        headers.put("X-Tenant-Type","organization");
        headers.put("x-operator-id",operatorId);
        new BaseBean().writeLog("TeambitionHttpToken:getHeaders:headers = "+headers);
        return headers;
    }


}

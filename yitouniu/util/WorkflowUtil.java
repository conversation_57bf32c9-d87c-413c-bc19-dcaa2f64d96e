package yitouniu.util;

import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import yitouniu.util.SqlUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @description:
 * @date 2019/6/14
 */
public class WorkflowUtil {

    /**
     * 权限重构
     */
    public static void ModeDataShare(String tablename) {
        String formmodeid = getFormidByTableid(getTableidByTablename(tablename));

        ModeRightInfo ModeRightInfo = new ModeRightInfo();
        ModeRightInfo.setNewRight(true);
        RecordSet rs = new RecordSet();
        String sql =
                "select * from " + tablename + " where id not in (select sourceid from modeDataShare_" + formmodeid + ")";
        rs.executeQuery(sql);
        while (rs.next()) {
            ModeRightInfo.editModeDataShare(Integer.parseInt("1"), Integer.parseInt(formmodeid), rs.getInt(
                    "id"));
        }
    }


    /**
     * 根据表名查表id
     *
     * @param tabename 表名
     * @return 表id
     */
    public static String getTableidByTablename(String tabename) {
        String result = "";
        String sql = "select * from workflow_bill where tablename=?";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, tabename);
        if (rs.next()) {
            return rs.getString("id");
        }
        return result;
    }

    /**
     * 根据表id查模块id
     *
     * @param tabeid 表id
     * @return 模块id
     */
    public static String getFormidByTableid(String tabeid) {
        String result = "";
        String sql = "select * from modeinfo where formid=?";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, tabeid);
        if (rs.next()) {
            return rs.getString("id");
        }
        return result;
    }

    /**
     * 建模更新数据
     * 权限重构
     */
    public static void ModeDataShare(String tablename, String sqlwhere) {

        ModeRightInfo ModeRightInfo = new ModeRightInfo();

        String sql = "select id,MODEDATACREATER,formmodeid from " + tablename;
        if (!"".equals(sqlwhere)) {
            sql += " where " + sqlwhere;
        }

        for (Map<String, String> row : SqlUtil.getTableDate(sql)) {
            ModeRightInfo.editModeDataShare(Integer.parseInt(row.get("MODEDATACREATER")),
                    Integer.parseInt(row.get("FORMMODEID")), Integer.parseInt(row.get("ID")));
        }

    }

}

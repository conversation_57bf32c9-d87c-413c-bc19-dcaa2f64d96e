package yitouniu.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import weaver.general.BaseBean;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/7/26 16:31
 * @Description wms调用工具类
 * @Version 1.0
 */
public class WMSUtils {

    BaseBean baseBean = new BaseBean();

    //测试
//    private static final String URL = "http://***********:9090/interfaces-web/api?";
//    private static final String SECRET = "hf8ar6ouwoia6pi6syelfm51pv6d6bxg";
//    private static final String APP_KEY = "ADOPT_A_COW_TEST";
//    private static final String CUSTOMERID = "246";
//    private static final String FORMAT = "json";
//    private static final String SIGN_METHOD = "md5";
//    private static final String VERSION = "2.0";

    //生产
    private static final String URL = "http://**************:9090/g3-2/api?";
    private static final String SECRET = "hf8ar6ouwoia6pi6syelfm51pv6d6bxg";
    private static final String APP_KEY = "ADOPT_A_COW_TEST";
    private static final String CUSTOMERID = "646";
    private static final String FORMAT = "json";
    private static final String SIGN_METHOD = "md5";
    private static final String VERSION = "2.0";

    public static final String YES = "Y";
    public static final String NO = "N";

    public static String SUCCESS = "success";
    public static String FAILURE = "failure";

    private static final String FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";

    //盘点状态返回wms
    public final static String INVENTORYORDER_AUDITING ="inventoryorder.auditing";



    public static String getSign(String method, String body) {
        SimpleDateFormat sdf = new SimpleDateFormat(FORMAT_TIME);
        String dateTime = sdf.format(new Date());
        String signString = SECRET + "app_key" + APP_KEY + "customerId" + CUSTOMERID + "format" + FORMAT + "method" + method + "sign_method" + SIGN_METHOD + "timestamp" + dateTime + "v" + VERSION + body + SECRET;
        return DigestUtils.md5Hex(signString);
    }

    public static String getUrl(String method, String body) {
        String url = URL + "customerId=" + CUSTOMERID + "&format="+FORMAT+"&app_key=" + APP_KEY + "&sign_method="+SIGN_METHOD+"&sign=" + getSign(method, body) + "&method=" + method;
        return url;
    }

    public static String pushInventoryWms(String orderCode, String status) {

        JSONObject inventoryRequest = new JSONObject();
        inventoryRequest.put("orderCode",orderCode);
        inventoryRequest.put("status","0".equals(status) ? "3":"2");
        inventoryRequest.put("remark","");
        return pushWms(INVENTORYORDER_AUDITING, (JSONObject) JSONObject.toJSON(inventoryRequest));
    }

    public static String pushWms(String method, JSONObject jsonObject) {
        String result = "";
        try {
            new BaseBean().writeLog("调用wms接口 request body: " + jsonObject.toJSONString());
            new BaseBean().writeLog("调用wms接口 request method: " + method);
            String url = getUrl(method, jsonObject.toJSONString());
            new BaseBean().writeLog("调用wms接口 request url: "+url);
            result = HttpRequest.post(url).body(jsonObject.toJSONString()).execute().body();
            new BaseBean().writeLog("调用wms接口 response: " + result);
        } catch (Exception e) {
            new BaseBean().writeLog("调用wms"+method+"接口失败");

            e.printStackTrace();
        }
        return result;
    }

}

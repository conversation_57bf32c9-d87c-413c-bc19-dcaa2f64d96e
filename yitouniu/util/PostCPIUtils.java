package yitouniu.util;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.methods.StringRequestEntity;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;

public class PostCPIUtils {

    private static String USERNAME="sb-da696a61-5e68-4994-8e2f-0d06d3e7490d!b1172|it-rt-ci-dev-eeiprb0s!b39";
    private static String PASSWORD="54e5ca55-110a-4bb1-a38c-3af85ef3d0d6$DLFIl-s3zi8H6oHr_rXEN9x7YlFjLPazb9n2MyQMwrA=";
    private static String requestUrl ="https://ci-dev-eeiprb0s.it-cpi010-rt.cpi.cn40.apps.platform.sapcloud.cn/http/rfc/testcall";
    /**
     * 发送HttpClient请求
     * @param params
     * @return
     */
    public static String sendPost( String params ) {
        InputStream inputStream = null;
        try {
            HttpClient httpClient = new HttpClient();
            PostMethod postMethod = new PostMethod(requestUrl);
            // 设置请求头  Content-Type
            postMethod.setRequestHeader("Content-Type", "application/json");
            postMethod.setRequestHeader("Authorization", "Basic " + Base64.getUrlEncoder().encodeToString((USERNAME + ":" + PASSWORD).getBytes()));
            RequestEntity requestEntity = new StringRequestEntity(params,"application/json","UTF-8");
            postMethod.setRequestEntity(requestEntity);
            httpClient.executeMethod(postMethod);// 执行请求
            inputStream =  postMethod.getResponseBodyAsStream();// 获取返回的流
            BufferedReader br = null;
            StringBuffer buffer = new StringBuffer();
            // 将返回的输入流转换成字符串
            br = new BufferedReader(new InputStreamReader(inputStream, Charset.forName("UTF-8")));
            String temp;
            while ((temp = br.readLine()) != null) {
                buffer.append(temp);
            }
            new weaver.general.BaseBean().writeLog("接口返回内容为:" + buffer);
            return buffer.toString();
        } catch (Exception e){
            new weaver.general.BaseBean().writeLog("请求异常" +e.getMessage());
            throw new RuntimeException(e.getMessage());
        } finally {
            if(inputStream != null) {
                try{
                    inputStream.close();
                } catch (IOException e){
                    e.printStackTrace();
                }
            }
        }
    }
}

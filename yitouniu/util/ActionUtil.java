package yitouniu.util;

import com.alibaba.fastjson.JSONObject;
import com.weaver.esb.client.EsbClient;
import com.weaver.esb.spi.EsbManager;
import com.weaver.esb.spi.EsbService;
import weaver.conn.RecordSet;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class ActionUtil {

    /**
     * 获取表明
     *
     * @param requestid
     * @return
     */
    public static Map getTableNameByRequestId(String requestid) {
        RecordSet recordSet = new RecordSet();
        Map<String,String> map = new HashMap();
        String selectSql = "select * from WORKFLOW_BILL where id = (" +
                "select formid from WORKFLOW_BASE where id = (" +
                "select WORKFLOWID from WORKFLOW_REQUESTBASE where REQUESTID = ?))";
        recordSet.executeQuery(selectSql,requestid);

        while (recordSet.next()) {
            map.put("tableName",recordSet.getString("TABLENAME"));
            map.put("id",recordSet.getString("id"));
        }

        return map;

    }

    /**
     * 获取总账科目
     */
    public static Map findKm(String ysid) {
        RecordSet recordSet = new RecordSet();
        Map map = new HashMap();
        String sql = "select * from fnabudgetfeetype where id = ?";
        recordSet.executeQuery(sql, ysid);
        if (recordSet.next()) {
            map.put("codeName", recordSet.getString("subjectnote"));
            //map.put("codeName", recordSet.getString("codeName"));
            map.put("name", recordSet.getString("name"));
            map.put("supsubject", recordSet.getString("supsubject"));
        }
        return map;
    }

    /**
     * 更新数据
     */
    public static String insertData(String tableName, String KOSTL, String HKONT, String RSTGR, String AUFNR, String SGTXT, String WRBTR, String KUNNR, String BSCHL,
                                    String UMSKZ, String EBELN, String ZUONR, String LIFNR, String VBUND, String mainid, String BLART) {
        RecordSet recordSet = new RecordSet();
        String insertSql = "insert into " + tableName + " (KOSTL,HKONT,RSTGR,AUFNR,SGTXT,WRBTR,KUNNR,BSCHL,UMSKZ,EBELN,ZUONR,LIFNR,VBUND,mainid,BLART) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        recordSet.executeUpdate(insertSql, KOSTL, HKONT, RSTGR, AUFNR, SGTXT, WRBTR, KUNNR, BSCHL, UMSKZ, EBELN, ZUONR, LIFNR, VBUND, mainid, BLART);
        return "";
    }

    /**
     * 根据申请人查询数据
     */
    public static Map findData(String sqr) {
        RecordSet recordSet = new RecordSet();
        Map map = new HashMap();
        String sql = "select * from uf_SAPrenyuan where oary = ?";
        recordSet.executeQuery(sql, sqr);

        if (recordSet.next()) {
            map.put("rybh", recordSet.getString("rybh"));
            map.put("KOSTL", recordSet.getString("KOSTL"));
            map.put("KUNNR", recordSet.getString("KUNNR"));
            map.put("BUKRS", recordSet.getString("BUKRS"));
            map.put("rymc", recordSet.getString("rymc"));
        }
        return map;
    }

    /**
     * 根据申请人查询数据
     */
    public static String findBMData(String bm) {
        RecordSet recordSet = new RecordSet();
        Map map = new HashMap();
        String sql = "select * from formtable_main_306 where oabm = ?";
        recordSet.executeQuery(sql, bm);
        String sapbmbm = "";
        if (recordSet.next()) {
            sapbmbm = recordSet.getString("sapbmbm");

        }
        return sapbmbm;
    }

    /**
     * 加
     * @param a
     * @param bs
     * @return
     */
    public static String bigDecimalAdd(String a, String bs) {
        BigDecimal result = new BigDecimal(a);

        result = result.add(new BigDecimal(bs));

        return result.toString();

    }

    /**
     * 减
     * @param a
     * @param b
     * @return
     */
    public static String subtract(String a, String b) {
        BigDecimal aBD = new BigDecimal(a);
        BigDecimal bBD = new BigDecimal(b);
        return aBD.subtract(bBD).toString();
    }

    // 获取下拉框的名称
    public static String getSelectName(String billedId,String  fieldname, String selectvalue){
        RecordSet recordSet = new RecordSet();
        String sql = "select * from workflow_SelectItem where fieldid = " +
                "(select id  from  workflow_billfield where billid = ? and fieldname = ?) and selectvalue = ?";
        recordSet.executeQuery(sql,billedId,fieldname,selectvalue);
        String selectname = null;
        if (recordSet.next()){
            selectname =  recordSet.getString("selectname");
        }
        return selectname;
    }

    // 获取员工名称


    public static String getHrmName(String userid){
        RecordSet recordSet = new RecordSet();
        String sql = "select *  from  hrmresource where id = ?";
        recordSet.executeQuery(sql,userid);
        String lastname = null;
        if (recordSet.next()){
            lastname =  recordSet.getString("lastname");
        }
        return lastname;
    }


    // 获取分部名称


    public static String getSubName(String subId){
        RecordSet recordSet = new RecordSet();
        String sql = "select *  from  HrmSubCompany where id = ?";
        recordSet.executeQuery(sql,subId);
        String subcompanyname = null;
        if (recordSet.next()){
            subcompanyname =  recordSet.getString("subcompanyname");
        }
        return subcompanyname;
    }

    /**
     * 借款金额
     * @param id
     * @return
     */
    public static  Map  findJKJE(String id){
        RecordSet recordSet = new RecordSet();
        String sql = "select * from uf_jiekuanku where id= ?";
        Map map = new HashMap();
        recordSet.executeQuery(sql, id);
        if (recordSet.next()) {
            map.put("jkwhxje",recordSet.getString("jkwhxje"));
            map.put("bxlc",recordSet.getString("bxlc"));
            map.put("jkjebgx",recordSet.getString("jkjebgx"));
            map.put("djje",recordSet.getString("djje"));


        }
        return map;
    }

    /**
     * 预付款金额
     * @param id
     * @return
     */
    public static  Map  findyfje(String id){
        RecordSet recordSet = new RecordSet();
        String sql = "select * from uf_yufukuanku where id= ?";
        Map map = new HashMap();
        recordSet.executeQuery(sql, id);
        if (recordSet.next()) {

            map.put("djje",recordSet.getString("djje"));
            map.put("yfksyje",recordSet.getString("yfksyje"));


        }
        return map;
    }

    /**
     * 调用了esb
     */
    //  执行esb事件
    public static  String esb(String event, String params, String outString) {
        EsbService service = EsbClient.getService(); // 调用esb方法
        String response = service.execute(event, params); //触发 ESB 事件
        JSONObject jsonObject = JSONObject.parseObject(response);
        JSONObject dataJSONObject = (JSONObject) jsonObject.get("data");
        String out = dataJSONObject.getString(outString);
        return out;
    }
}

package yitouniu.util;

import com.aliyun.dingtalkalitrip_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.*;
import com.aliyun.teautil.models.*;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.taobao.api.ApiException;
import weaver.general.BaseBean;

/**
 * <AUTHOR>
 * @Date 2022/10/10 14:37
 * @Description TODO
 * @Version 1.0
 */
public class DingTalkTripUtil {

    private final static String APPKEY = "";
    private final static String APPSECRET = "";
    private final static String CORPID = "";



    /**
     * 使用 Token 初始化账号Client
     *
     * @return Client
     * @throws Exception
     */
    public static com.aliyun.dingtalkalitrip_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkalitrip_1_0.Client(config);
    }

    /**
     * 获取token
     *
     * @return
     */
    public static String getToken() {
        String res = "";
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey("ding370bf6nydf1ztuo7");
        request.setAppsecret("ar71FOl64xb7UTxvylPdw0VkBRXD5qdgD7gEg6CWiAoe0OW1MeMnYsRu5E-SsRmd");
        request.setHttpMethod("GET");
        try {
            OapiGettokenResponse response = (OapiGettokenResponse) client.execute(request);
            res = response.getAccessToken();
            return res;
        } catch (ApiException var7) {
            var7.printStackTrace();
            return res;
        }
    }

    /**
     * 酒店账单
     *
     * @param startTime
     * @param endTime
     * @param pageNumber
     * @return
     */
    public static BillSettementHotelResponseBody getBillSettementHotel(String startTime, String endTime, long pageNumber) {
        BillSettementHotelResponse response = new BillSettementHotelResponse();
        try {
            com.aliyun.dingtalkalitrip_1_0.Client client = createClient();
            BillSettementHotelHeaders billSettementHotelHeaders = new BillSettementHotelHeaders();
            billSettementHotelHeaders.xAcsDingtalkAccessToken = getToken();
            BillSettementHotelRequest billSettementHotelRequest = new BillSettementHotelRequest()
                    .setCorpId("dinga9e6db0a7491905135c2f4657eb6378f")
                    .setCategory(0L)
                    .setPageSize(100L)
                    .setPeriodStart(startTime)
                    .setPageNumber(pageNumber)
                    .setPeriodEnd(endTime);
            response = client.billSettementHotelWithOptions(billSettementHotelRequest, billSettementHotelHeaders, new RuntimeOptions());
            return response.getBody();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementHotel, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementHotel, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementHotel, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementHotel, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return response.getBody();
    }

    /**
     * 火车票账单查询
     *
     * @param startTime
     * @param endTime
     * @param pageNumber
     * @return
     */
    public static BillSettementBtripTrainResponseBody getBillSettementBtripTrain(String startTime, String endTime, long pageNumber) {
        BillSettementBtripTrainResponse response = new BillSettementBtripTrainResponse();
        try {
            com.aliyun.dingtalkalitrip_1_0.Client client = createClient();
            BillSettementBtripTrainHeaders billSettementBtripTrainHeaders = new BillSettementBtripTrainHeaders();
            billSettementBtripTrainHeaders.xAcsDingtalkAccessToken = getToken();
            BillSettementBtripTrainRequest billSettementBtripTrainRequest = new BillSettementBtripTrainRequest()
                    .setCorpId("dinga9e6db0a7491905135c2f4657eb6378f")
                    .setCategory(0L)
                    .setPageSize(100L)
                    .setPeriodStart(startTime)
                    .setPageNumber(pageNumber)
                    .setPeriodEnd(endTime);
            response = client.billSettementBtripTrainWithOptions(billSettementBtripTrainRequest, billSettementBtripTrainHeaders, new RuntimeOptions());
            return response.getBody();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementBtripTrain, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementBtripTrain, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementBtripTrain, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementBtripTrain, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return response.getBody();
    }

    /**
     * 机票账单查询
     * @param startTime
     * @param endTime
     * @param pageNumber
     * @return
     */
    public static BillSettementFlightResponseBody getBillSettementFlight(String startTime, String endTime, long pageNumber) {
        BillSettementFlightResponse response = new BillSettementFlightResponse();
        try {
            com.aliyun.dingtalkalitrip_1_0.Client client = createClient();
            BillSettementFlightHeaders billSettementFlightHeaders = new BillSettementFlightHeaders();
            billSettementFlightHeaders.xAcsDingtalkAccessToken = getToken();
            BillSettementFlightRequest billSettementFlightRequest = new BillSettementFlightRequest()
                    .setCorpId("dinga9e6db0a7491905135c2f4657eb6378f")
                    .setCategory(0L)
                    .setPageSize(100L)
                    .setPeriodStart(startTime)
                    .setPageNumber(pageNumber)
                    .setPeriodEnd(endTime);
            response = client.billSettementFlightWithOptions(billSettementFlightRequest, billSettementFlightHeaders, new RuntimeOptions());
            return response.getBody();
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementFlight, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementFlight, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }

        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                new BaseBean().writeLog("getBillSettementFlight, err.code={}", err.code);
                new BaseBean().writeLog("getBillSettementFlight, err.message={}", err.message);
                // err 中含有 code 和 message 属性，可帮助开发定位问题
            }
        }
        return response.getBody();
    }


}

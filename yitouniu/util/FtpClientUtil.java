package yitouniu.util;

import sun.net.ftp.FtpClient;
import weaver.general.Base64;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.SocketAddress;

/**
 * 登录ftp系统
 */

public class FtpClientUtil {
    private String url;  // ip
    private int port; // 端口
    private String username; // 用户名
    private String password; // 密码
    private  FtpClient ftp  = null;

    public FtpClientUtil(String url,int port,String username,String password ){
        this.url =url;
        this.port =port;
        this.username =username;
        this.password =password;
        this.connectFTP();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public FtpClient getFtp() {
        return ftp;
    }

    public void setFtp(FtpClient ftp) {
        this.ftp = ftp;
    }

    /**
     * 连接ftp
     */
    private void connectFTP() {
        //创建ftp

        try {
            //创建地址
            SocketAddress addr = new InetSocketAddress(url, port);
            //连接
            ftp = FtpClient.create();
            ftp.connect(addr);
            //登陆
            ftp.login(username, password.toCharArray());
            ftp.setBinaryType();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 下载ftp文件
     * @param ftpFile
     * @return
     */
    public  String download(String ftpFile) {
        //List<String> list = new ArrayList<String>();
        String str = "";
        InputStream is = null;
        //BufferedReader br = null;
        try {
            int byteread;
            byte data[] = new byte[1024];
            // 获取ftp上的文件
            is = ftp.getFileStream(ftpFile);
            /*//转为字节流
            br = new BufferedReader(new InputStreamReader(is));
            while((str=br.readLine())!=null){
                list.add(str);
            }*/
            ByteArrayOutputStream out1 = new ByteArrayOutputStream();
            while ((byteread = is.read(data)) != -1) {
                out1.write(data, 0, byteread);

                out1.flush();
            }
            byte[] content = out1.toByteArray();

            byte[] encode = Base64.encode(content);
            is.close();
            out1.close();
            // br.close();
            return new String(encode);
        }catch (Exception e) {
            e.printStackTrace();

        }
        return "";
    }

    public  void closeFtp() {
        try {
            ftp.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

}

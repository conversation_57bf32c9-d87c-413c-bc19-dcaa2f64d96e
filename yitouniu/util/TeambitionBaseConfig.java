package yitouniu.util;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 16:52 2021/12/20
 */
public class TeambitionBaseConfig {

    /**
     * ryytn 应用ID、应用密钥、公司ID
     */
    public static final String APP_ID = "61c023c04a1bfe9e6dfac08d";
    public static final String APP_SECRET = "aolKLnKbNCCvBUWIxfToL5ZczPcugJ7r";
    public static final String COMPANY_ID = "5ee9af38d68eb6445a702b7d";

    /**
     * Teambition公共Api
     */
    //GET
    // 获取企业成员列表
    public static final String URL_API_ORG_MEMBER_LIST_GET = "https://open.teambition.com/api/org/member/list";
    // 查询任务分组
    public static final String URL_API_TASKGROUP_QUERY_GET = "https://open.teambition.com/api/taskgroup/query";
    // 查询任务列表
    public static final String URL_API_TASKLIST_QUERY_GET = "https://open.teambition.com/api/tasklist/query";
    // 查询任务类型
    public static final String URL_API_TEMPLATE_QUERY_GET = "https://open.teambition.com/api/template/query";
    // 查询工作流信息
    public static final String URL_API_TASKFLOW_QUERY_GET = "https://open.teambition.com/api/taskflow/query";
    // 查询任务
    public static final String URL_API_TASK_QUERY_GET = "https://open.teambition.com/api/task/query";
    // 查询项目
    public static final String URL_API_PROJECT_INFO = "https://open.teambition.com/api/project/info";
    // 搜索企业成员
    public static final String URL_API_ORG_MEMBER_SEARCH = "https://open.teambition.com/api/org/member/search";

    //POST
    // 查询项目
    public static final String URL_API_PROJECT_QUERY_POST = "https://open.teambition.com/api/project/query";
    // 创建任务
    public static final String URL_API_TASK_CREATE_POST = "https://open.teambition.com/api/task/create";
    // 创建评论
    public static final String URL_API_TASK_COMMENT_CREATE_POST = "https://open.teambition.com/api/task/comment/create";
    // 更新任务
    public static final String URL_API_TASK_UPDATE_POST = "https://open.teambition.com/api/task/update";
    //TQL查询任务
    public static final String URL_API_TASK_TQLSEARCH_POST = "https://open.teambition.com/api/task/tqlsearch";


    /**
     * Teambition v3api
     */
    //POST
    // 创建任务
    public static final String URL_API_TASK_CREATE_POST_V3 = "https://open.teambition.com/api/v3/task/create";

    //PUT
    // 更新任务状态
    public static final String URL_API_UPDATE_TASK_STATUS_V3 = "https://open.teambition.com/api/v3/task/{taskId}/taskflowstatus";

    //GET
    //查询任务
    public static final String URL_API_TASK_QUERY_GET_V3 = "https://open.teambition.com/api/v3/task/query";
    //搜索任务分组
    public static final String URL_API_PROJECT_TASKLIST_SEARCH_V3 = "https://open.teambition.com/api/v3/project/%s/tasklist/search";
    //搜索任务列表
    public static final String URL_API_PROJECT_STAGE_SEARCH_V3 = "https://open.teambition.com/api/v3/project/%s/stage/search";
    //查询项目
    public static final String URL_API_PROJECT_QUERY_V3 = "https://open.teambition.com/api/v3/project/query";
    //查询项目任务类型
    public static final String URL_API_PROJECT_SCENCONFIG_SEARCH_V3 = "https://open.teambition.com/api/v3/project/%s/scenariofieldconfig/search";
    //查询项目工作流状态
    public static final String URL_API_PROJECT_TASKFLOWSTARUS_SEARCH_V3 = "https://open.teambition.com/api/v3/project/%s/taskflowstatus/search";
    //查询项目任务
    public static final String URL_API_PROJECT_TASK_QUERY_V3 = "https://open.teambition.com/api/v3/project/%s/task/query";
//    //查询项目任务
//    public static final String URL_API_PROJECT_TASK_QUERY_V3 = "https://open.teambition.com/api/v3/project/%s/task/query";


}

package yitouniu.util;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import weaver.general.BaseBean;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

public class SRMUtil {

    //SRM测试环境
//    public final static String URL ="http://47.98.176.225:9086/CommonModule/OA/SRMM/";
    //SRM正式环境
    public final static String URL ="http://121.43.97.210:9086/CommonModule/OA/SRMM";

    public static String pushSRM(JSONObject jsonObject) {
        new BaseBean().writeLog("入参 : "+jsonObject);
        String result = "";
        try {
            result = httpJsonPost(URL, jsonObject);
//            new BaseBean().writeLog("SRM返回 : "+result);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String httpJsonPost(String uri, JSONObject param) throws IOException {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        CloseableHttpClient closeableHttpClient = httpClientBuilder.build();
        List<NameValuePair> params = new ArrayList<NameValuePair>();
        params.add(new BasicNameValuePair("IN_JSON",param.toString()));
        HttpPost httppost = new HttpPost(uri);
//        httppost.addHeader("charset", "utf-8");
        httppost.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");
        try {
            //错误方法，不可这样直接拼接，会导致如果值有&的内容的话就会报错
//            StringEntity entity = new StringEntity("IN_JSON="+param.toString(), "utf-8");// 解决中文乱码问题
//            entity.setContentEncoding("UTF-8");
//            entity.setContentType("application/x-www-form-urlencoded");
//            httppost.setEntity(entity);
            httppost.setEntity(new UrlEncodedFormEntity(params,"UTF-8"));
//            new BaseBean().writeLog("httppost =  : "+ httppost);
            HttpResponse httpResponse = closeableHttpClient.execute(httppost);
            String json = EntityUtils.toString(httpResponse.getEntity(), "utf-8");
            return json;
        } catch (IOException e) {
            throw e;
        } finally {
            try {
                closeableHttpClient.close();
            } catch (IOException e) {
                //throw e;
            }
        }
    }
}

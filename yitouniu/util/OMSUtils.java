package yitouniu.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import org.apache.commons.codec.digest.DigestUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/8/22 13:53
 * @Description TODO
 * @Version 1.0
 */
public class OMSUtils {


    //uat
    //userName
//    public static final String OMS_USERNAME = "erp";
//    //userKey
//    public static final String OMS_USERKEY = "123456";
//    //burgeonSecret
//    public static final String OMS_BURGEON_SECRET = "123456";
//    //userKeyMD5
//    public static final String OMS_USERKEY_MD5 = "e10adc3949ba59abbe56e057f20f883e";
//    //OMS_REQUEST_SIGN
//    public static final String OMS_REQUEST_SIGN = "cd70c798a580c5dbdd765690ab90b05b";
//    //token地址
//    public static final String OMS_TOKEN_URL = "http://**************:21180/api/auth/login";
//    //报废地址
//    public static final String OMS_SCRAP_URL = "http://**************:21180/api/ip/oa/router/service";

//    //生产
//    //userName
//    public static final String OMS_USERNAME = "RYYTN_API_OA_PRO";
//    //userKey
//    public static final String OMS_USERKEY = "b7dc1c5c3d0bd4c79ddff8a242fa9d9c";
//    //burgeonSecret
//    public static final String OMS_BURGEON_SECRET = "279834220aeccb163745fb8365817923";
//    //userKeyMD5
//    public static final String OMS_USERKEY_MD5 = "b7dc1c5c3d0bd4c79ddff8a242fa9d9c";
//    //OMS_REQUEST_SIGN
//    public static final String OMS_REQUEST_SIGN = "279834220aeccb163745fb8365817923";
//    //token地址
//    public static final String OMS_TOKEN_URL = "http://ywzt.ryytn.com:21180/api/auth/login";
//    //报废地址
//    public static final String OMS_SCRAP_URL = "http://ywzt.ryytn.com:21180/api/ip/oa/router/service";


    public static final String OMS_USERNAME = Util.null2String(new BaseBean().getPropValue("oms_configuration","username"));
    public static final String OMS_USERKEY = Util.null2String(new BaseBean().getPropValue("oms_configuration","userKey"));
    public static final String OMS_USERKEY_MD5 = Util.null2String(new BaseBean().getPropValue("oms_configuration","userKeyMd5"));
    public static final String OMS_BURGEON_SECRET = Util.null2String(new BaseBean().getPropValue("oms_configuration","burgeonSecret"));
    public static final String OMS_REQUEST_SIGN = Util.null2String(new BaseBean().getPropValue("oms_configuration","requestSign"));
    public static final String OMS_OMS_URL = Util.null2String(new BaseBean().getPropValue("oms_configuration","omsUrl"));
    public static final String OMS_TOKEN_API = Util.null2String(new BaseBean().getPropValue("oms_configuration","tokenApi"));
    public static final String OMS_SCRAP_API = Util.null2String(new BaseBean().getPropValue("oms_configuration","scrapApi"));

    BaseBean baseBean = new BaseBean();

    /**
     * 获取token , 为了解决偶发性的接口连接报错，获取到token为空时重新获取一次。
     * @return
     */
    public String getToken(){
        String accessToken  = getOmsToken();
        if("".equals(accessToken)){
            accessToken = getOmsToken();
        }
        return accessToken;
    }


    /**
     * 获取token
     * @return
     */
    public String getOmsToken(){
        String accessToken = "";
        try {
//            String userKeyMd5 = DigestUtils.md5Hex(OMS_USERKEY);
//            String requestSign = DigestUtils.md5Hex(OMS_USERNAME + userKeyMd5 +OMS_BURGEON_SECRET);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("userName", OMS_USERNAME);

//            paramMap.put("userKey", userKeyMd5);
//            paramMap.put("requestSign", requestSign);
            paramMap.put("userKey", OMS_USERKEY_MD5);
            paramMap.put("requestSign", OMS_REQUEST_SIGN);
            baseBean.writeLog("getOmsToken——paramMap="+ paramMap);
            String httpResponse = HttpRequest.get(OMS_OMS_URL+OMS_TOKEN_API).form(paramMap).execute().body();
            baseBean.writeLog("getOmsToken——httpResponse="+ httpResponse);
            if(!"".equals(httpResponse)){
                JSONObject responseJson = JSONObject.parseObject(httpResponse);
                boolean successFlag = responseJson.getBoolean("success");
                if(successFlag){
                    accessToken = responseJson.getString("loginToken");
                }
            }
            return accessToken;
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
        return accessToken;
    }


}

package yitouniu.util;

import c.f.y.i;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.io.*;
import java.nio.charset.Charset;
import java.sql.ResultSet;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 10:04 2021/8/12
 * @Modified By:
 */
public class ImageFileUtil {
    static Base64.Encoder encoder = Base64.getEncoder();
    static Base64.Decoder decoder = Base64.getDecoder();

    public Map<String, Object> base64Encoder(String fjsc) {
        Map<String, Object> resultMap = new HashMap<>();
        try{
            String[] fjscArr = fjsc.split(",");

            List<String> fjscList = new ArrayList<String>(fjscArr.length);
            Collections.addAll(fjscList,fjscArr);
            List<String> PICXTList = new ArrayList<String>();
            List<String> PICFLList = new ArrayList<String>();

            for(String str : fjscList){
                if(str.length()>0){
                    RecordSet recordSet = new RecordSet();
                    String sql = " SELECT ImgF.filerealpath,ImgF.imagefilename" +
                            " FROM imagefile ImgF " +
                            " LEFT JOIN docimagefile DIF ON DIF.imagefileid = ImgF.imagefileid " +
                            " WHERE DIF.docid = ? ";
                    recordSet.executeQuery(sql,str);
                    if(recordSet.next()){
                        new BaseBean().writeLog("开始转换附件或图片："+str);
                        File zipFile= new File(recordSet.getString("filerealpath"));
                        String realFileName = recordSet.getString("imagefilename");
                        String descDir = "D:\\SRMImage\\";
                        File pathFile = new File(descDir);
                        if(!pathFile.exists()){
                            pathFile.mkdirs();
                        }
                        ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
                        for(Enumeration<? extends ZipEntry> enumeration = zip.entries(); enumeration.hasMoreElements();){
                            ZipEntry zipEntry = enumeration.nextElement();
                            String zipEntryName = zipEntry.getName();
                            new BaseBean().writeLog("打印zipEntryName："+zipEntryName);
                            InputStream in = zip.getInputStream(zipEntry);
                            String outPath = (descDir+realFileName.substring(0,realFileName.lastIndexOf("."))).replaceAll("\\*", "/");
                            new BaseBean().writeLog("打印outPath："+outPath);
                            File file = new File(outPath);
                            if(!file.exists())
                            {
                                file.mkdir();
                            }
                            String fileRealPath = descDir+realFileName.substring(0,realFileName.lastIndexOf("."))+"\\"+realFileName;
                            FileOutputStream out = new FileOutputStream(new File(fileRealPath));
                            byte[] buf = new byte[1024];
                            int len;
                            while((len=in.read(buf))>0)
                            {
                                out.write(buf,0,len);
                            }
                            in.close();
                            out.close();
                            String realFileOutPath = fileRealPath.replaceAll("\\*", "/");
                            String fileIO = getFileBinary(realFileOutPath);
                            PICXTList.add(realFileName);
                            PICFLList.add(fileIO);
                        }
                        new BaseBean().writeLog("解压转换完毕");
                    }
                }

            }
            resultMap.put("MSGTY","S");
            resultMap.put("RESULT","成功");
            resultMap.put("PICXT",PICXTList);
            resultMap.put("PICFL",PICFLList);


        } catch (Exception e) {
            resultMap.put("MSGTY","F");
            resultMap.put("RESULT",e);
            resultMap.put("PICXT","");
            resultMap.put("PICFL","");
        }
        return resultMap;
    }


    static String getFileBinary(String path) {
        String base64 = null;
        InputStream in = null;
        File file = new File(path);
        try{
            in = new FileInputStream(file);
            byte[] bytes = new byte[in.available()];
            in.read(bytes);
            base64 = encoder.encodeToString(bytes);
            return base64;
        }catch (FileNotFoundException e){
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }







}

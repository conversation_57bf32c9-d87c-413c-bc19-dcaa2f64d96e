package yitouniu.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import weaver.general.BaseBean;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/2/13 16:59
 * @Description TODO
 * @Version 1.0
 */
public class KuaiDi100Utils {

    BaseBean baseBean = new BaseBean();

    public static JSONObject addressAnalyse(String address) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("content", address);
        queryParam.put("secret_key", "bwPr7Y33RHZbBsDjs7");
        queryParam.put("secret_code", "0db4f037e2094f15a811261722e8a6e9");
        queryParam.put("secret_sign", DigestUtils.md5Hex("bwPr7Y33RHZbBsDjs7876a0bec81cd455e98aa1eab1e1dc079").toUpperCase());
        String response = HttpRequest.post("http://cloud.kuaidi100.com/api").form(queryParam).execute().body();
        new BaseBean().writeLog("addressAnalyse：response="+response);
        return JSON.parseObject(response);
    }


}

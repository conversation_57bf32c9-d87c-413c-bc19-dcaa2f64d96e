package yitouniu.util;

import cn.hutool.http.HttpUtil;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/4/7 9:53
 * @Description TODO
 * @Version 1.0
 */
public class SmsUtils {

    public static final String YUNPIAN_SMS_APIKEY = Util.null2String(new BaseBean().getPropValue("yunPianSMS","SMSApiKey"));
    public static final String YUNPIAN_SMS_SINGLE_SEND_URL = Util.null2String(new BaseBean().getPropValue("yunPianSMS","SMSSingleSendUrl"));

    public static String singleSend(String apiKey, String text, String mobile) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("apikey", apiKey);
        params.put("text", text);
        params.put("mobile", mobile);
        return HttpUtil.post(YUNPIAN_SMS_SINGLE_SEND_URL, params);
    }

}

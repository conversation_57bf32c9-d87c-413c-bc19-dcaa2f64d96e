package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *  财务凭证推送
 */
public class ZINF011ESB extends ReturnMsgToSAP {


    public Map executeParam(String params) {

        JSONArray DATAArr = new JSONArray();

        //String paramString = JSON.toJSONString(params);

        //转换esb中数据
        JSONObject jsonObject = JSONObject.parseObject(params);
        if (jsonObject.containsKey("DATA")) {
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");
            // 根据凭证类型进行分组
            Map<String, List<JSONObject>> collect = dataList.stream().collect(Collectors.groupingBy(row -> row.getString("BLART")));

            Iterator<Map.Entry<String, List<JSONObject>>> iterator = collect.entrySet().iterator();

            // 整理数据,转换成sap所需要的的格式
            while (iterator.hasNext()) {
                Map.Entry<String, List<JSONObject>> next = iterator.next();
                List<JSONObject> detailDaoList = next.getValue();
                JSONArray ITEMArr = new JSONArray();
                for (int i = 0; i < detailDaoList.size(); i++) {
                    JSONObject ITEMJson = new JSONObject();
                    JSONObject object = detailDaoList.get(i);
                    ITEMJson.put("BSCHL", object.getString("BSCHL"));
                    ITEMJson.put("UMSKZ", object.getString("UMSKZ"));
                    ITEMJson.put("WRBTR", object.getString("WRBTR"));
                    ITEMJson.put("SGTXT", object.getString("SGTXT"));
                    ITEMJson.put("KOSTL", object.getString("KOSTL"));
                    ITEMJson.put("EBELN", object.getString("EBELN"));
                    ITEMJson.put("RSTGR", object.getString("RSTGR"));
                    ITEMJson.put("ZUONR", object.getString("ZUONR"));
                    ITEMJson.put("AUFNR", object.getString("AUFNR"));
                    ITEMJson.put("HKONT", object.getString("HKONT"));
                    ITEMJson.put("KUNNR", object.getString("KUNNR"));
                    ITEMJson.put("LIFNR", object.getString("LIFNR"));
                    ITEMJson.put("ZFKBS", object.getString("ZFKBS"));
                    ITEMJson.put("VBUND", object.getString("VBUND"));
                    ITEMJson.put("XREF3", object.getString("XREF3"));
                    ITEMJson.put("ZKUNNR", object.getString("ZXREF5"));
                    ITEMArr.add(ITEMJson);

                    if (i == detailDaoList.size() - 1) {
                        JSONObject DATAJson = new JSONObject();
                        DATAJson.put("ITEM", ITEMArr);

                        DATAJson.put("ZBUTYP", object.getString("ZBUTYP"));
                        DATAJson.put("ZXBLN", object.getString("ZXBLN"));
                        DATAJson.put("ZHDID", object.getString("ZHDID"));
                        DATAJson.put("ZHDZT", object.getString("ZHDZT"));
                        DATAJson.put("ZJYRQ", object.getString("ZJYRQ"));
                        DATAJson.put("ZOAID", object.getString("ZOAID"));
                        DATAJson.put("ZFKQQID", object.getString("ZFKQQID"));
                        DATAJson.put("ZFKZLID", object.getString("ZFKZLID"));
                        DATAJson.put("ZFKWCBS", object.getString("ZFKWCBS"));
                        DATAJson.put("BUKRS", object.getString("BUKRS"));
                        DATAJson.put("GJAHR", object.getString("GJAHR"));
                        DATAJson.put("MONAT", object.getString("MONAT"));
                        DATAJson.put("BLART", object.getString("BLART"));
                        DATAJson.put("BLDAT", object.getString("BLDAT"));
                        DATAJson.put("BUDAT", object.getString("BUDAT"));
                        DATAJson.put("XBLNR", object.getString("XBLNR"));
                        DATAJson.put("BKTXT", object.getString("BKTXT"));
                        DATAJson.put("WAERS", object.getString("WAERS"));
                        DATAArr.add(DATAJson);
                    }
                }
            }
        }
        jsonObject.put("DATA", DATAArr);
        // 推送凭证
        Map execute = super.execute(jsonObject.toString());
        return execute;
    }
}

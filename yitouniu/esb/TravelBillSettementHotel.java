package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkalitrip_1_0.models.BillSettementBtripTrainResponseBody;
import com.aliyun.dingtalkalitrip_1_0.models.BillSettementHotelResponseBody;
import com.engine.demo.cmd.DoBaseDemoCmd;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.DingTalkTripUtil;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:17
 * @Description TODO
 * @Version 1.0
 */
public class TravelBillSettementHotel {

    //出差审批单主表
    private final String dataBaseName = "uf_slccdz";
    //酒店账单
    private final String dataBaseNameItemHotel = "uf_slccdz_dt2";

    BaseBean baseBean = new BaseBean();


    public Map<String,String> executeParam(Map params) {
        Map<String,String> map = new HashMap<>();
        String execute = JSON.toJSONString(params);

        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject dataObject = jsonObject.getJSONObject("data");
        String startTime = dataObject.getString("startTime");
        String endTime = dataObject.getString("endTime");
        String pageNumber = dataObject.getString("pageNumber");

        baseBean.writeLog("TravelBillSettementHotel 日期"+startTime+"到"+endTime+"的酒店账单>>>start");


        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startTime);
            Date end = sdf.parse(endTime);
            long startTimeStamp = start.getTime();
            long endTimeStamp = end.getTime();
            long oneDay = 1000 * 60 * 60 * 24L;//One Day TimeStamp
            boolean flagAll = false;

            while (startTimeStamp < endTimeStamp) {
                Date newStartTime = new Date(startTimeStamp);
                Date newEndTime = new Date(startTimeStamp+oneDay);
                String newStartTimeFormat = sdf.format(newStartTime);
                String newEndTimeFormat = sdf.format(newEndTime);
                for(int i = 1;i<=10000;i++){
                    BillSettementHotelResponseBody responseBody = DingTalkTripUtil.getBillSettementHotel(newStartTimeFormat,newEndTimeFormat,i);
                    if(responseBody.getSuccess()){
                        List<List> insertAllData = new ArrayList<>();
                        List<List> updateAllData = new ArrayList<>();
                        StringBuilder flag = new StringBuilder();
                        List<BillSettementHotelResponseBody.BillSettementHotelResponseBodyModuleDataList> hotelDataList = responseBody.getModule().getDataList();
                        for(BillSettementHotelResponseBody.BillSettementHotelResponseBodyModuleDataList o : hotelDataList){
                            String applyId = o.getApplyId();
                            if(StringUtils.isBlank(applyId) || (!"20103".equals(o.getFeeType()) && !"20101".equals(o.getFeeType())) || !"4".equals(o.getSettlementType())){
                                baseBean.writeLog("TravelBillSettementHotel 费用类型为"+o.getFeeType()+" 的酒店账单被跳过");
                                continue;
                            }
                            String primaryId = String.valueOf(o.getPrimaryId());
                            String id = selectId(applyId);
                            List insertData = new ArrayList<>();
                            List updateData = new ArrayList<>();
                            if(StringUtils.isNotBlank(id)){
                                boolean b = selectPrimaryId(dataBaseNameItemHotel, primaryId);
                                String feeName = "";
                                switch (o.getFeeType()){
                                    case "20103":
                                        feeName = "酒店退款";
                                        break;
                                    case "20101":
                                        feeName = "酒店预订";
                                        break;
                                    default:
                                        feeName = o.getFeeType();
                                        break;
                                }

                                if(b){
                                    updateData.add(applyId);
                                    updateData.add(o.getTravelerName());
                                    updateData.add(o.getTravelerJobNo());
                                    updateData.add(o.getCascadeDepartment());
                                    updateData.add(changeTimeStr(o.getCheckInDate(), "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"));
                                    updateData.add(changeTimeStr(o.getCheckoutDate(), "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"));
                                    updateData.add(o.getFeeType());
                                    updateData.add(feeName);
                                    updateData.add(o.getCity());
                                    updateData.add(o.getHotelName());
                                    updateData.add(String.valueOf(o.getOrderPrice()));
                                    updateData.add("");
                                    updateData.add(primaryId);
                                    updateAllData.add(updateData);
                                } else {
                                    insertData.add(id);
                                    insertData.add(applyId);
                                    insertData.add(o.getAlipayTradeNo());
                                    insertData.add(primaryId);
                                    insertData.add(o.getTravelerName());
                                    insertData.add(o.getTravelerJobNo());
                                    insertData.add(o.getCascadeDepartment());
                                    insertData.add(changeTimeStr(o.getCheckInDate(), "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"));
                                    insertData.add(changeTimeStr(o.getCheckoutDate(), "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"));
                                    insertData.add(o.getFeeType());
                                    insertData.add(feeName);
                                    insertData.add(o.getCity());
                                    insertData.add(o.getHotelName());
                                    insertData.add(String.valueOf(o.getOrderPrice()));
                                    insertData.add("");
                                    insertAllData.add(insertData);
                                }
                            }
                        }
                        if(insertAllData.size()>0){
                            flag.append(insert(insertAllData)).append(",");
                        }

                        if(updateAllData.size()>0){
                            flag.append(update(updateAllData)).append(",");
                        }
                        if (flag.toString().contains("false")) {
                            baseBean.writeLog("TravelBillSettementHotel 日期"+newStartTimeFormat+"到"+newEndTimeFormat+"的酒店账单拉取失败");
                            flagAll = true;
                            break;
                        }

                        if(hotelDataList.size()<100){
                            baseBean.writeLog("TravelBillSettementHotel 日期"+newStartTimeFormat+"到"+newEndTimeFormat+"的酒店账单拉取成功完毕");
                            break;
                        }
                    }
                }
                if(flagAll){
                    break;
                }
                startTimeStamp += oneDay;
            }



            if(flagAll){
                map.put("MSGTY","F");
                map.put("MSAGE","处理数据失败");
                map.put("RESULT","");
            } else {
                map.put("MSGTY","S");
                map.put("MSAGE","处理数据成功");
                map.put("RESULT","");
            }
        } catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT", JSON.toJSONString(e));
        }
        return map;
    }

    public String selectId(String applyId) {
        String id = "";
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select id from "+dataBaseName+" where spdh =? ", applyId);
        if (rs1.next()){
            id = rs1.getString("id");
        }
        return id;
    }

    private static String changeTimeStr(String time, String afterFormat, String frontFormat) throws ParseException {
        SimpleDateFormat frontSdf = new SimpleDateFormat(frontFormat);
        SimpleDateFormat afterSdf = new SimpleDateFormat(afterFormat);

        return afterSdf.format(frontSdf.parse(time));
    }

    public boolean selectPrimaryId(String dataBase, String primaryId) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+dataBase+" where primary_id =? ", primaryId);
        return rs1.next();
    }

    public boolean update(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("update "+dataBaseNameItemHotel+" " +
                "set spdh =?,cxr=?,cxrgh=?,bm=?,rzrq= ?," +
                "ldrq=?,fylx=?,fylxmc=?,rzcs=?,jdmc=?,ddje=?,clbz=? where primary_id =? ",list);
    }

    public boolean insert(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("insert into "+dataBaseNameItemHotel+" " +
                "(mainid, spdh, alipay_trade_no, primary_id, cxr,cxrgh," +
                "bm,rzrq,ldrq,fylx,fylxmc,rzcs,jdmc,ddje,clbz) " +
                "values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",list);
    }

    private static String getNowTimeStr(Date time, String format){
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(time);
    }









}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkalitrip_1_0.models.BillSettementBtripTrainResponseBody;
import com.aliyun.dingtalkalitrip_1_0.models.BillSettementBtripTrainResponseBody;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.DingTalkTripUtil;
import yitouniu.util.TimeUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/10/10 16:17
 * @Description TODO
 * @Version 1.0
 */
public class TravelBillSettementBtripTrain {

    //出差审批单主表
    private final String dataBaseName = "uf_slccdz";
    //火车票账单
    private final String dataBaseNameItemBtripTrain = "uf_slccdz_dt3";

    BaseBean baseBean = new BaseBean();


    public Map<String,String> executeParam(Map params){
        Map<String,String> map = new HashMap<>();
        String execute = JSON.toJSONString(params);

        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject dataObject = jsonObject.getJSONObject("data");
        String startTime = dataObject.getString("startTime");
        String endTime = dataObject.getString("endTime");
        String pageNumber = dataObject.getString("pageNumber");

        try{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date start = sdf.parse(startTime);
            Date end = sdf.parse(endTime);
            long startTimeStamp = start.getTime();
            long endTimeStamp = end.getTime();
            long oneDay = 1000 * 60 * 60 * 24L;//One Day TimeStamp
            boolean flagAll = false;

            while (startTimeStamp < endTimeStamp) {
                Date newStartTime = new Date(startTimeStamp);
                Date newEndTime = new Date(startTimeStamp+oneDay);
                String newStartTimeFormat = sdf.format(newStartTime);
                String newEndTimeFormat = sdf.format(newEndTime);
                for(int i = 1;i<=10000;i++){
                    BillSettementBtripTrainResponseBody responseBody = DingTalkTripUtil.getBillSettementBtripTrain(newStartTimeFormat,newEndTimeFormat,i);
                    if(responseBody.getSuccess()){
                        long totalNum = responseBody.getModule().getTotalNum();
                        StringBuilder flag = new StringBuilder();
                        List<List> insertAllData = new ArrayList<>();
                        List<List> updateAllData = new ArrayList<>();
                        List<BillSettementBtripTrainResponseBody.BillSettementBtripTrainResponseBodyModuleDataList> BtripTrainDataList = responseBody.getModule().getDataList();
                        for(BillSettementBtripTrainResponseBody.BillSettementBtripTrainResponseBodyModuleDataList o : BtripTrainDataList){
                            String applyId = o.getApplyId();
                            if(StringUtils.isBlank(applyId) || (!"6001".equals(o.getFeeType()) && !"6003".equals(o.getFeeType()) && !"6005".equals(o.getFeeType()) && !"6009".equals(o.getFeeType())  && !"6010".equals(o.getFeeType())) || !"4".equals(o.getSettlementType())){
                                continue;
                            }
                            String primaryId = String.valueOf(o.getPrimaryId());
                            String id = selectId(applyId);
                            List insertData = new ArrayList<>();
                            List updateData = new ArrayList<>();
                            if(StringUtils.isNotBlank(id)){
                                boolean b = selectPrimaryId(dataBaseNameItemBtripTrain, primaryId);
                                String feeName = "";
                                switch (o.getFeeType()){
                                    case "6001":
                                        feeName = "火车票预订";
                                        break;
                                    case "6003":
                                        feeName = "火车票改签差价";
                                        break;
                                    case "6005":
                                        feeName = "火车票退票";
                                        break;
                                    case "6009":
                                        feeName = "火车票预订退款";
                                        break;
                                    case "6010":
                                        feeName = "火车票改签退款";
                                        break;
                                    default:
                                        feeName = o.getFeeType();
                                        break;
                                }
                                if(b){
                                    updateData.add(o.getTravelerName());
                                    updateData.add(o.getTravelerJobNo());
                                    updateData.add(o.getCascadeDepartment());
                                    updateData.add(o.getDeptDate());
                                    updateData.add(o.getArrDate());
                                    updateData.add(o.getFeeType());
                                    updateData.add(feeName);
                                    updateData.add(o.getDeptStation());
                                    updateData.add(o.getArrStation());
                                    updateData.add(o.getSeatType());
                                    updateData.add(String.valueOf(o.getOrderPrice()));
                                    updateData.add(primaryId);
                                    updateAllData.add(updateData);
                                } else {
                                    insertData.add(id);
                                    insertData.add(applyId);
                                    insertData.add(o.getAlipayTradeNo());
                                    insertData.add(primaryId);
                                    insertData.add(o.getTravelerName());
                                    insertData.add(o.getTravelerJobNo());
                                    insertData.add(o.getCascadeDepartment());
                                    insertData.add(o.getDeptDate());
                                    insertData.add(o.getArrDate());
                                    insertData.add(o.getFeeType());
                                    insertData.add(feeName);
                                    insertData.add(o.getDeptStation());
                                    insertData.add(o.getArrStation());
                                    insertData.add(o.getSeatType());
                                    insertData.add(String.valueOf(o.getOrderPrice()));
                                    insertAllData.add(insertData);
                                }
                            }
                        }
                        if(insertAllData.size()>0){
                            flag.append(insert(insertAllData)).append(",");
                        }

                        if(updateAllData.size()>0){
                            flag.append(update(updateAllData)).append(",");
                        }
                        if (flag.toString().contains("false")) {
                            baseBean.writeLog("TravelBillSettementBtripTrain 日期"+newStartTimeFormat+"到"+newEndTimeFormat+"的酒店账单拉取失败");
                            flagAll = true;
                            break;
                        }

                        if(BtripTrainDataList.size()<100){
                            baseBean.writeLog("TravelBillSettementBtripTrain 日期"+newStartTimeFormat+"到"+newEndTimeFormat+"的酒店账单拉取成功完毕");
                            break;
                        }
                    }
                }
                if(flagAll){
                    break;
                }
                startTimeStamp += oneDay;
            }


            if(flagAll){
                map.put("MSGTY","F");
                map.put("MSAGE","处理数据失败");
                map.put("RESULT","");
            } else {
                map.put("MSGTY","S");
                map.put("MSAGE","处理数据成功");
                map.put("RESULT","");
            }
        } catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT", JSON.toJSONString(e));
        }
        return map;
    }

    public String selectId(String applyId) {
        String id = "";
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select id from "+dataBaseName+" where spdh =? ", applyId);
        if (rs1.next()){
            id = rs1.getString("id");
        }
        return id;
    }


    public boolean selectPrimaryId(String dataBase, String primaryId) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+dataBase+" where primary_id =? ", primaryId);
        return rs1.next();
    }

    public boolean update(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("update "+dataBaseNameItemBtripTrain+" " +
                "set cxr=?,cxrgh=?,bm=?,fcrq= ?," +
                "ddrq=?,fylx=?,fylxmc=?,fcz=?,ddz=?,zx=?,ddje=? where primary_id =? ",list);
    }

    public boolean insert(List<List> list) {
        RecordSet rs1 = new RecordSet();
        return rs1.executeBatchSql("insert into "+dataBaseNameItemBtripTrain+" " +
                "(mainid, spdh, alipay_trade_no,primary_id, cxr,cxrgh," +
                "bm,fcrq,ddrq,fylx,fylxmc,fcz,ddz,zx,ddje) " +
                "values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",list);
    }










}

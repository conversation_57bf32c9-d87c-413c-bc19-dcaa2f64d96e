package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ZJPTUtil;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: Moss
 * @Description: 获取资金平台联行号
 * @Date: Create in 15:18 2021/11/1
 * @Modified By:
 */
public class BankAccountESB {
    public final static String SRCOUTSYSTEMCODE = "OA";
    public final static String SRCBATCHNO = "sandy20190314";
    public final static String TRANSCODE = "PBQB01";
    public final static String COMMANDCODE = "GETBANKLOCATIONINFO";
    public final static String PAGEINDEX = "1";
    public static String DATACOUNT = null;
    public static String PAGECOUNT = "1";


    public Map executeParam(Map params) {
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        String formatDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        new BaseBean().writeLog("BankAccountESB : ");

        try {
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
            String startTime = dataObject.getString("startTime");
            String endTime = dataObject.getString("endTime");
            boolean flag = false;

            int pageNum = Integer.parseInt(PAGECOUNT);
            for (int i = 1;;i++){
                if (i > Integer.parseInt(PAGECOUNT)){
                    break;
                }
                StringBuilder sb = new StringBuilder();
                sb.append("<?xml version = '1.0'encoding='utf-8'?>\n" +
                        "<MBS>\n" +
                        "    <pub>\n" +
                        "        <SRCOUTSYSTEMCODE>" + SRCOUTSYSTEMCODE + "</SRCOUTSYSTEMCODE>\n" +
                        "        <SRCBATCHNO>" + SRCBATCHNO + "</SRCBATCHNO>\n" +
                        "        <TRANSCODE>" + TRANSCODE + "</TRANSCODE>\n" +
                        "        <TRANSDATETIME>" + formatDate + "</TRANSDATETIME>\n" +
                        "        <MD5>158cfd893b0598f078f7efa4e6b11696</MD5>\n" +
                        "    </pub>\n" +
                        "    <req>\n" +
                        "        <head>\n" +
                        "            <COMMANDCODE>" + COMMANDCODE + "</COMMANDCODE>\n" +
                        "            <PAGEINDEX>" + i + "</PAGEINDEX>\n" +
                        "            <PAGESIZE>10000</PAGESIZE>\n" +
                        "        </head>\n" +
                        "        <searchconditions>\n" +
                        "            <LASTMODIFIEDONSTART>" + startTime + "</LASTMODIFIEDONSTART>\n" +
                        "            <LASTMODIFIEDONEND>" + endTime + "</LASTMODIFIEDONEND>\n" +
                        "        </searchconditions>\n" +
                        "    </req>\n" +
                        "</MBS>\n");
                String xmlReq = sb.toString();
                String result = ZJPTUtil.WebServiceZJPT(xmlReq);

                flag = flagXmlDataToDatabase(xmlParse(result));
            }

            if(flag){
                map.put("MSGTY","S");
                map.put("MSAGE","处理数据成功");
                map.put("RESULT",flag);
            } else {
                map.put("MSGTY","F");
                map.put("MSAGE","处理数据失败");
                map.put("RESULT",flag);
            }
        } catch (Exception e) {
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT",e);
        }


        return map;
    }


    /**
     * 数据存入数据库
     * @param dataMap
     * @return
     */
    public boolean flagXmlDataToDatabase(Map<String,List<List>> dataMap) {
        List<List> updateDetailsList = dataMap.get("update");
        List<List> insertDetailsList = dataMap.get("insert");
        String flag = "";
        if(insertDetailsList.size()>0){
            List<List<List>> insertDataList =split(insertDetailsList, 500);
            for(List<List> i : insertDataList) {
                String insertFlag = String.valueOf(insert(i));
                flag += insertFlag + ",";
                new BaseBean().writeLog("BankAccountESB : 插入insertFlag = " + insertFlag);
            }
        }
        if(updateDetailsList.size()>0){
            List<List<List>> updateDataList =split(updateDetailsList, 500);
            for(List<List> i : updateDataList){
                String updateFlag = String.valueOf(update(i));
                flag += updateFlag + ",";
                new BaseBean().writeLog("BankAccountESB : 更新updateFlag= " + updateFlag);
            }
        }
        new BaseBean().writeLog("BankAccountESB : 更新flag= " + flag);
        if (flag.contains("false")) {
            return false;
        }
        return true;
    }


    /**
     * 解析得到的N条数据
     * @param xml
     * @throws DocumentException
     */
    public Map xmlParse(String xml) throws DocumentException {
        Map<String,List<List>> dataMap = new HashMap<>();
        List<List> updateDetailsList = new ArrayList<>();
        List<List> insertDetailsList = new ArrayList<>();
        Document doc = null;
        //字符串转换为XML
        doc = DocumentHelper.parseText(xml);
        //获取根节点
        Element root = doc.getRootElement();
        //获取根节点下的子节点pub
        Iterator iter = root.elementIterator("pub");
        while (iter.hasNext()) {
            Element recordElement = (Element) iter.next();
            //获取指令返回码，0为成功
            String RESPCODE = recordElement.elementText("RESPCODE");
            if (!"0".equals(RESPCODE)) {
                return dataMap;
            }
        }
        Iterator iter2 = root.elementIterator("resp");
        while (iter2.hasNext()) {
            Element recordElement = (Element) iter2.next();
            DATACOUNT = recordElement.elementText("DATACOUNT");
            new BaseBean().writeLog("BankAccountESB : xmlParse: "+DATACOUNT);
            PAGECOUNT = recordElement.elementText("PAGECOUNT");
            new BaseBean().writeLog("BankAccountESB : xmlParse: "+PAGECOUNT);

            Iterator iterLists = recordElement.elementIterator("list");
            while (iterLists.hasNext()) {
                Element ListElement = (Element) iterLists.next();
                Iterator iterDetails = ListElement.elementIterator("detail");

                while (iterDetails.hasNext()) {
                     List<String> updateDetail = new ArrayList<>();
                    List<String> insertDetail = new ArrayList<>();
                    Element detailElement = (Element) iterDetails.next();
                    //遍历节点获取各子节点数据
                    String lastmodifiedon = detailElement.elementText("lastmodifiedon");
                    String swiftname = detailElement.elementText("swiftname");
                    String lxh = detailElement.elementText("code");
                    String rtgscode = detailElement.elementText("rtgscode");
                    String isactive = detailElement.elementText("isactive");
                    String countrycode = detailElement.elementText("countrycode");
                    String description = detailElement.elementText("description");
                    String areacode = detailElement.elementText("areacode");
                    String countryid = detailElement.elementText("countryid");
                    String bankid = detailElement.elementText("bankid");
                    String qy = detailElement.elementText("areaid");
                    String swiftcode = detailElement.elementText("swiftcode");
                    String khx = detailElement.elementText("name");
                    String rtgsname = detailElement.elementText("rtgsname");
                    String yxmc = detailElement.elementText("bankcode");
                    boolean b = select(lxh);
                    if (b) {
                        updateDetail.add(lastmodifiedon);
                        updateDetail.add(swiftname);

                        updateDetail.add(rtgscode);
                        updateDetail.add(isactive);
                        updateDetail.add(countrycode);
                        updateDetail.add(description);
                        updateDetail.add(areacode);
                        updateDetail.add(countryid);
                        updateDetail.add(bankid);
                        updateDetail.add(qy);
                        updateDetail.add(swiftcode);
                        updateDetail.add(khx);
                        updateDetail.add(rtgsname);
                        updateDetail.add(yxmc);

                        updateDetail.add(lxh);
                        //将得到的一条detail加入updateDetailsList中
                        updateDetailsList.add(updateDetail);
                    } else {
                        insertDetail.add(lastmodifiedon);
                        insertDetail.add(swiftname);
                        insertDetail.add(lxh);
                        insertDetail.add(rtgscode);
                        insertDetail.add(isactive);
                        insertDetail.add(countrycode);
                        insertDetail.add(description);
                        insertDetail.add(areacode);
                        insertDetail.add(countryid);
                        insertDetail.add(bankid);
                        insertDetail.add(qy);
                        insertDetail.add(swiftcode);
                        insertDetail.add(khx);
                        insertDetail.add(rtgsname);
                        insertDetail.add(yxmc);
                        //将得到的一条detail加入updateDetailsList中
                        insertDetailsList.add(insertDetail);
                    }
                }
                dataMap.put("insert",insertDetailsList);
                dataMap.put("update",updateDetailsList);
            }

        }
        return dataMap;
    }

    /**
     * 正式环境中用已有的表，需要更改字段
     * 转回测试还要改回去，下次备份一份，这次先改了
     *
     * 测试———————————正式
     * 联行号 code——lxh 联行号
     * 名称 name——khx 开户行
     * 银行代码 bankcode——yxmc 银行名称
     * 区域ID areaid——qy 区域
     *
     * 表
     * uf_YHZSJ_KHYH——————uf_lhh
     */

    public boolean select(String lxh) {
        RecordSet rs = new RecordSet();
        //正式
        String sql = "select * from uf_lhh where lxh =? ";
        //测试
//        String sql = "select * from uf_YHZSJ_KHYH where code =? ";
        rs.executeQuery(sql, lxh);
        return rs.next();
    }

    public boolean update(List<List> list) {
        RecordSet rs = new RecordSet();
        //正式
        String sql = "update uf_lhh " +
                " set lastmodifiedon =?,swiftname=?,rtgscode=?,isactive=?,countrycode=?,description=?,areacode=?,countryid=?,bankid=?,qy=?,swiftcode=?,khx=?,rtgsname=?,yxmc=? " +
                " where lxh =? ";
        //测试
//        String sql = "update uf_YHZSJ_KHYH " +
//                " set lastmodifiedon =?,swiftname=?,rtgscode=?,isactive=?,countrycode=?,description=?,areacode=?,countryid=?,bankid=?,areaid=?,swiftcode=?,name=?,rtgsname=?,bankcode=? " +
//                " where code =? ";
        return rs.executeBatchSql(sql,list);
    }

    public boolean insert(List<List> list) {
        RecordSet rs = new RecordSet();
        //正式
        String sql = "insert into uf_lhh " +
                "    (lastmodifiedon, swiftname, lxh, rtgscode, isactive, countrycode, description, areacode, countryid, bankid, qy, swiftcode, khx, rtgsname, yxmc) " +
                "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        //测试
//        String sql = "insert into uf_YHZSJ_KHYH " +
//                "    (lastmodifiedon, swiftname, code, rtgscode, isactive, countrycode, description, areacode, countryid, bankid, areaid, swiftcode, name, rtgsname, bankcode) " +
//                "values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ";
        return rs.executeBatchSql(sql,list);
    }

    public static List<List<List>> split(List dataList, int splitDataSize) {
        List<List<List>> resultList = new ArrayList();

        int listSize = dataList.size();
        if (listSize <= splitDataSize) {
            resultList.add(dataList);
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            int splitListCount = (int) Math.ceil(Double.valueOf(df.format((float) listSize / splitDataSize)));
            for (int i = 0; i < splitListCount; i++) {
                int beginIndex = (i + 1 - 1) * splitDataSize;
                int endIndex = (i + 1) * splitDataSize <= listSize ? (i + 1) * splitDataSize : listSize;
                resultList.add(dataList.subList(beginIndex, endIndex));
            }
        }
        return resultList;
    }


}

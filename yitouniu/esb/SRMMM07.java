package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ImageFileUtil;
import yitouniu.util.SRMUtil;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Moss
 * @Description:
 * SRM接收OA审批通过的采购申请（会包含附件或图片）,包括类型为：
 * 一般申购
 * 紧急申购
 * 无料号紧急申购
 * 资产类申购
 * 费用类申购
 * @Date: Create in 10:00 2021/8/11
 * @Modified By:
 */
public class SRMMM07 {
    public  Map executeParam(Map params){
        Map map = new HashMap();

        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
            JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
            List<JSONObject> itemFileList = (List<JSONObject>) jsonObject.get("ITEM");

            JSONArray itemArr = new JSONArray();

            JSONObject inJson = new JSONObject();
            JSONObject dataJson = new JSONObject();

            //data为主表数据，唯一一条
            //
            String WERKS = dataObject.getString("WERKS");
            dataJson.put("WERKS",dataObject.getString("WERKS"));
            dataJson.put("NAME1",dataObject.getString("NAME1"));
            dataJson.put("ERDAT",dataObject.getString("ERDAT"));

            String cglx = dataObject.getString("cglx");
            switch(cglx){
                case "0":
                    dataJson.put("ZPRIO", "普通");
                    dataJson.put("ZUNMAT", "否");
                    break;
                case "1":
                    dataJson.put("ZPRIO", "紧急");
                    dataJson.put("ZUNMAT", "否");
                    break;
                case "2":
                    dataJson.put("ZPRIO", "紧急");
                    dataJson.put("ZUNMAT", "是");
                    break;
                default:
                    dataJson.put("ZPRIO", "");
                    dataJson.put("ZUNMAT", "");
                    break;
            }

            dataJson.put("EMLIF",dataObject.getString("EMLIF"));
            dataJson.put("ZNAME",dataObject.getString("ZNAME"));
            //设备资产编号
            dataJson.put("sbzcbh",dataObject.getString("sbzcbh"));

            switch(dataObject.getString("BSART")){
                case "0":
                    dataJson.put("BSART", "NB");
                    break;
                case "1":
                    dataJson.put("BSART", "NBA");
                    break;
                case "2":
                    dataJson.put("BSART", "NBK");
                    break;
                case "3":
                    dataJson.put("BSART", "NBL");
                    break;
                case "4":
                    dataJson.put("BSART", "NBJ");
                    break;
                default:
                    dataJson.put("BSART", "");
                    break;
            }
            dataJson.put("KOSTL",dataObject.getString("KOSTL"));
            dataJson.put("KOSTL_TEXT",dataObject.getString("KOSTL_TEXT"));
            dataJson.put("ZSUMPR",dataObject.getString("ZSUMPR"));
            dataJson.put("AFNAM",dataObject.getString("AFNAM"));

            String ZNOTE = dataObject.getString("ZNOTE");
            ZNOTE = ZNOTE.replaceAll("</?[^>]+>", "").replaceAll("&nbsp;","").replaceAll("\\s*|\t|\r|\n","");
            new BaseBean().writeLog("SRMMM07：以下为一条数据的ZNOTE："+ZNOTE);
            dataJson.put("ZNOTE",ZNOTE);
            dataJson.put("ZOANO",dataObject.getString("ZOANO"));

            //item和file各自对应的多条明细数据
            for(JSONObject object : itemFileList) {
                JSONObject itemJson = new JSONObject();

                itemJson.put("BNFPO",object.getString("BNFPO"));
                itemJson.put("ZSRTYP",object.getString("ZSRTYP"));
                itemJson.put("ZSRTYPDE",object.getString("ZSRTYPDE"));
                itemJson.put("ZMATNR",object.getString("ZMATNR"));
                itemJson.put("MATNR",object.getString("MATNR"));

                String MAKTX = object.getString("MAKTX");
                String MAKTXEW = object.getString("MAKTXEW");
                if("".equals(MAKTX) && !"".equals(MAKTXEW)){
                    itemJson.put("MAKTX",object.getString("MAKTXEW"));
                } else {
                    itemJson.put("MAKTX",object.getString("MAKTX"));
                }

                itemJson.put("MODEL",object.getString("MODEL"));
                itemJson.put("BRAND",object.getString("BRAND"));
                itemJson.put("MEINS",object.getString("MEINS"));
                itemJson.put("MENGE",object.getString("MENGE"));
                itemJson.put("ZNETPR",object.getString("ZNETPR"));
                itemJson.put("ZTOLPR",object.getString("ZTOLPR"));
                itemJson.put("EEIND",object.getString("EEIND"));
                itemJson.put("ZSPDE",object.getString("ZSPDE"));

                String MATKL = object.getString("MATKL");
                String MATKLE = object.getString("MATKLE");
                if("".equals(MATKL) && !"".equals(MATKLE)){
                    itemJson.put("MATKL",MATKLE);
                } else{
                    itemJson.put("MATKL",MATKL);
                }

                itemJson.put("WGBEZ",object.getString("WGBEZ"));

                String EKGRP = object.getString("EKGRP");
                String EKGRPE = object.getString("EKGRPE");
                if("".equals(EKGRP) && !"".equals(EKGRPE)){
                    itemJson.put("EKGRP",EKGRPE);
                } else{
                    itemJson.put("EKGRP",EKGRP);
                }

                itemJson.put("EKNAM",object.getString("EKNAM"));
                itemJson.put("ccostcenter",dataObject.getString("ccostcenter"));

                String ZCKBM = object.getString("ZCKBM");
                itemJson.put("ZCKBM",object.getString("ZCKBM"));
                RecordSet rs3 = new RecordSet();
                String sqlZCKMC = "select LGOBE from uf_ck where LGORT = ? and WERKS = ?";
                rs3.executeQuery(sqlZCKMC,ZCKBM,WERKS);
                itemJson.put("ZCKMC", "");
                if(rs3.next()){
                    itemJson.put("ZCKMC",rs3.getString("LGOBE"));
                }


                //有多条明细，需要对应的mainid和序号BNFPO
                //多条数据在循环中会自动next
                //TODO:这里好奇怪，不明白啥意思
                RecordSet rs = new RecordSet();
                if (rs.next()) {
                    itemJson.put("ZOAID", rs.getString("id"));
                } else {
                    itemJson.put("ZOAID", "");
                }

                itemArr.add(itemJson);

            }
            inJson.put("CTRL",ctrlObject);
            inJson.put("DATA",dataJson);
            inJson.put("ITEM",itemArr);

            new BaseBean().writeLog("SRMMM07最终传入参数："+inJson);
            //推送到SRM
            String result = SRMUtil.pushSRM(inJson);
            new BaseBean().writeLog("SRMMM07以下为result："+result);

            JSONObject resultObject = JSONObject.parseObject(result);
            JSONObject outObject = (JSONObject) resultObject.get("OUT_JSON");
            JSONObject ctrlResultObject = (JSONObject) outObject.get("CTRL");
//            JSONObject dataObject2 = (JSONObject) outObject.get("DATA");
            String successFlag = ctrlResultObject.getString("success");
            String messageFlag = ctrlResultObject.getString("message");
            new BaseBean().writeLog("返回message："+messageFlag);

            if(successFlag.equals("true")){
                map.put("MSGTY","S");
                map.put("MSAGE","推送成功");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",inJson);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","推送失败");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",inJson);
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","推送失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
            map.put("IN_JSON","");
        }
        return map;
    }
}

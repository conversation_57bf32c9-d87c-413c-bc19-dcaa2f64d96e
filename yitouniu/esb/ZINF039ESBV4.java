package yitouniu.esb;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2023-12-05 18:00
 **/
public class ZINF039ESBV4 extends ReturnMsgToSAP {
    //正式
//    public final static String dataBaseName = "formtable_main_499";
//    public final static String detailDataBaseName = "formtable_main_499_dt1";

    public Map executeParam(String params) {
        new BaseBean().writeLog("039日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        Map execute = new HashMap();

        //测试还是正式创建写死，T为测试，C为创建，后面的大哥再来想想能不能合并
        String MATNRFlag = "C";

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject CTRLObject = (JSONObject) jsonObject.get("CTRL");
        String requestid = CTRLObject.getString("INFID");
        Map tableNameByRequestID = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String) tableNameByRequestID.get("tableName");

//        RecordSet recordSet = new RecordSet();
//        String sql = "SELECT id FROM "+detailDataBaseName+" WHERE mainid = ( SELECT id FROM "+dataBaseName+" WHERE requestid = ?)";
//        recordSet.executeQuery(sql, requestid);

        if (jsonObject.containsKey("DATA")) {
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");

//            new BaseBean().writeLog("物料数据推送回传-ZINF039ESB-所有数据DATAs = " + dataList);

            JSONArray dataArr = new JSONArray();
            for (int i = 0; i < dataList.size(); i++) {
                JSONObject dataJSON = new JSONObject();
                JSONObject object = dataList.get(i);

                dataJSON.put("ZD_PLB", object.getString("ZD_PLB"));

                String MAKTX = object.getString("MAKTX") == null ? "":object.getString("MAKTX");
                MAKTX = MAKTX.trim();
                dataJSON.put("ZID", MAKTX);
                //记录下循环的数据
//                new BaseBean().writeLog("物料数据推送回传-ZINF039ESB-循环的i和ZID和dataJSON和本次数据 i = "+ i +",ZID = "+dataJSON.getString("ZID")+",dataJSON = "+dataJSON+", data = "+object);

                //品类
                String ZPL = object.getString("ZPL") == null ? "":object.getString("ZPL");
                if(ZPL.equals("0")){
                    dataJSON.put("ZPL","01");
                }else if(ZPL.equals("1")){
                    dataJSON.put("ZPL","02");
                }else if(ZPL.equals("2")){
                    dataJSON.put("ZPL","03");
                }else if(ZPL.equals("3")){
                    dataJSON.put("ZPL","04");
                }else if(ZPL.equals("4")){
                    dataJSON.put("ZPL","05");
                }else if(ZPL.equals("5")){
                    dataJSON.put("ZPL","06");
                }else if(ZPL.equals("6")){
                    dataJSON.put("ZPL","07");
                }else if(ZPL.equals("7")){
                    dataJSON.put("ZPL","10");
                }else if(ZPL.equals("8")){
                    dataJSON.put("ZPL","99");
                }else if(ZPL.equals("9")){
                    dataJSON.put("ZPL","98");
                }else if(ZPL.equals("10")){
                    dataJSON.put("ZPL","97");
                }else if (ZPL.equals("11")){
                    dataJSON.put("ZPL", "11");
                }else{
                    dataJSON.put("ZPL","");
                }

                //品牌1
                String ZPP = object.getString("ZPP") == null ? "":object.getString("ZPP");
                if (ZPP.equals("0")) {
                    dataJSON.put("ZPP", "01");
                } else if (ZPP.equals("1")) {
                    dataJSON.put("ZPP", "02");
                } else if (ZPP.equals("2")) {
                    dataJSON.put("ZPP", "03");
                } else if (ZPP.equals("3")) {
                    dataJSON.put("ZPP", "04");
                } else if (ZPP.equals("4")) {
                    dataJSON.put("ZPP", "88");
                } else if (ZPP.equals("5")) {
                    dataJSON.put("ZPP", "99");
                } else if (ZPP.equals("6")) {
                    dataJSON.put("ZPP", "77");
                } else {
                    dataJSON.put("ZPP", "");
                }

                String XCHPF = object.getString("XCHPF") == null ? "":object.getString("XCHPF");
                if (XCHPF.equals("0")) {
                    dataJSON.put("XCHPF", "X");
                } else {
                    dataJSON.put("XCHPF", "");
                }

                String PRMOD = object.getString("PRMOD") == null ? "":object.getString("PRMOD");
                if (PRMOD.equals("0")) {
                    dataJSON.put("PRMOD", "01");
                } else if (PRMOD.equals("1")) {
                    dataJSON.put("PRMOD", "02");
                } else {
                    dataJSON.put("PRMOD", "");
                }

                String TAKLM = object.getString("TAKLM") == null ? "":object.getString("TAKLM");
                if (TAKLM.equals("0")) {
                    dataJSON.put("TAKLM", "0");
                } else if (TAKLM.equals("1")) {
                    dataJSON.put("TAKLM", "1");
                } else if (TAKLM.equals("2")) {
                    dataJSON.put("TAKLM", "2");
                } else if (TAKLM.equals("3")) {
                    dataJSON.put("TAKLM", "3");
                } else if (TAKLM.equals("4")) {
                    dataJSON.put("TAKLM", "4");
                } else if (TAKLM.equals("5")) {
                    dataJSON.put("TAKLM", "5");
                } else {
                    dataJSON.put("TAKLM", "");
                }

                String INSMK = object.getString("INSMK") == null ? "":object.getString("INSMK");
                if (INSMK.equals("0")) {
                    dataJSON.put("INSMK", "X");
                } else {
                    dataJSON.put("INSMK", "");
                }

                String YZ_QM = object.getString("YZ_QM") == null ? "":object.getString("YZ_QM");
                if (YZ_QM.equals("0")) {
                    dataJSON.put("YZ_QM", "X");
                } else {
                    dataJSON.put("YZ_QM", "");
                }

                String SC_QM = object.getString("SC_QM") == null ? "":object.getString("SC_QM");
                if (SC_QM.equals("0")) {
                    dataJSON.put("SC_QM", "X");
                } else {
                    dataJSON.put("SC_QM", "");
                }

                String XK_QM = object.getString("XK_QM") == null ? "":object.getString("XK_QM");
                if (XK_QM.equals("0")) {
                    dataJSON.put("XK_QM", "X");
                } else {
                    dataJSON.put("XK_QM", "");
                }

                String XS_QM = object.getString("XS_QM") == null ? "":object.getString("XS_QM");
                if (XS_QM.equals("0")) {
                    dataJSON.put("XS_QM", "X");
                } else {
                    dataJSON.put("XS_QM", "");
                }

                String CG_QM = object.getString("CG_QM") == null ? "":object.getString("CG_QM");
                if (CG_QM.equals("0")) {
                    dataJSON.put("CG_QM", "X");
                } else {
                    dataJSON.put("CG_QM", "");
                }

                String VPRSV = object.getString("VPRSV") == null ? "":object.getString("VPRSV");
                if (VPRSV.equals("0")) {
                    dataJSON.put("VPRSV", "S");
                } else if (VPRSV.equals("1")) {
                    dataJSON.put("VPRSV", "V");
                } else {
                    dataJSON.put("VPRSV", "");
                }

                String SPART = object.getString("SPART") == null ? "":object.getString("SPART");
                if (SPART.equals("0")) {
                    dataJSON.put("SPART", "00");
                } else if (SPART.equals("1")) {
                    dataJSON.put("SPART", "10");
                } else if (SPART.equals("2")) {
                    dataJSON.put("SPART", "11");
                } else if (SPART.equals("3")) {
                    dataJSON.put("SPART", "12");
                } else if (SPART.equals("4")) {
                    dataJSON.put("SPART", "13");
                } else if (SPART.equals("5")) {
                    dataJSON.put("SPART", "50");
                } else if (SPART.equals("6")) {
                    dataJSON.put("SPART", "51");
                } else if (SPART.equals("7")) {
                    dataJSON.put("SPART", "52");
                } else if (SPART.equals("8")){
                    dataJSON.put("SPART","14");
                } else if (SPART.equals("9")){
                    dataJSON.put("SPART","15");
                } else if (SPART.equals("10")){
                    dataJSON.put("SPART","16");
                } else{
                    dataJSON.put("SPART","");
                }

                String KTGRM = object.getString("KTGRM") == null ? "":object.getString("KTGRM");
                if (KTGRM.equals("0")) {
                    dataJSON.put("KTGRM", "10");
                } else if (KTGRM.equals("1")) {
                    dataJSON.put("KTGRM", "20");
                } else if (KTGRM.equals("2")) {
                    dataJSON.put("KTGRM", "30");
                } else if (KTGRM.equals("3")) {
                    dataJSON.put("KTGRM", "40");
                } else if (KTGRM.equals("4")) {
                    dataJSON.put("KTGRM", "50");
                } else {
                    dataJSON.put("KTGRM", "");
                }

                RecordSet recordSet2 = new RecordSet();
                String sql2 = "select LGORT from uf_ck where id = ?";
                String LGFSB = object.getString("LGFSB") == null ? "":object.getString("LGFSB");
                recordSet2.executeQuery(sql2, LGFSB);
                if (recordSet2.next()) {
                    dataJSON.put("LGFSB", recordSet2.getString("LGORT"));
                } else {
                    dataJSON.put("LGFSB", "");
                }

                dataJSON.put("WERKS", object.getString("WERKS") == null ? "":object.getString("WERKS"));

                String MTART = object.getString("MTART") == null ? "":object.getString("MTART");
                switch (MTART) {
                    case "0":
                        dataJSON.put("MTART", "ZERT");
                        break;
                    case "1":
                        dataJSON.put("MTART", "ZROH");
                        break;
                    case "2":
                        dataJSON.put("MTART", "ZERP");
                        break;
                    case "3":
                        dataJSON.put("MTART", "ZWZC");
                        break;
                    case "4":
                        dataJSON.put("MTART", "ZRSA");
                        break;
                    case "5":
                        dataJSON.put("MTART", "ZHDP");
                        break;
                    case "6":
                        dataJSON.put("MTART", "ZERV");
                        break;
                    default:
                        dataJSON.put("MTART", "");
                }

                String DISPO = object.getString("DISPO") == null ? "":object.getString("DISPO");
                switch (DISPO) {
                    case "0":
                        dataJSON.put("DISPO", "M01");
                        break;
                    case "1":
                        dataJSON.put("DISPO", "M02");
                        break;
                    case "2":
                        dataJSON.put("DISPO", "M03");
                        break;
                    case "3":
                        dataJSON.put("DISPO", "M04");
                        break;
                    case "4":
                        dataJSON.put("DISPO", "M05");
                        break;
                    case "5":
                        dataJSON.put("DISPO", "M06");
                        break;
                    case "6":
                        dataJSON.put("DISPO", "M07");
                        break;
                    case "7":
                        dataJSON.put("DISPO", "M08");
                        break;
                    case "8":
                        dataJSON.put("MTART", "ZERT");
                        dataJSON.put("DISPO", "P01");
                        break;
                    default:
                        dataJSON.put("DISPO", "");
                        break;
                }

                dataJSON.put("MATNR", object.getString("MATNR") == null ? "":object.getString("MATNR"));
                dataJSON.put("MAKTX", object.getString("MAKTX") == null ? "":object.getString("MAKTX"));
                dataJSON.put("MEINS", object.getString("MEINS") == null ? "":object.getString("MEINS"));
                dataJSON.put("MATKL", object.getString("MATKL") == null ? "":object.getString("MATKL"));
                dataJSON.put("BRGEW", object.getString("BRGEW") == null ? "":object.getString("BRGEW"));
                dataJSON.put("NTGEW", object.getString("NTGEW") == null ? "":object.getString("NTGEW"));
                dataJSON.put("GEWEI", object.getString("GEWEI") == null ? "":object.getString("GEWEI"));
                dataJSON.put("BCODE", object.getString("BCODE") == null ? "":object.getString("BCODE"));
                dataJSON.put("BRAND", object.getString("BRAND") == null ? "":object.getString("BRAND"));
                dataJSON.put("BRNAM", object.getString("BRNAM") == null ? "":object.getString("BRNAM"));
                dataJSON.put("MODEL", object.getString("MODEL") == null ? "":object.getString("MODEL"));
                dataJSON.put("BOXNO", object.getString("BOXNO") == null ? "":object.getString("BOXNO"));
                dataJSON.put("TASTE", object.getString("TASTE") == null ? "":object.getString("TASTE"));
                dataJSON.put("PUPSE", object.getString("PUPSE") == null ? "":object.getString("PUPSE"));
                dataJSON.put("VNDOR", object.getString("VNDOR") == null ? "":object.getString("VNDOR"));
                dataJSON.put("VNDNM", object.getString("VNDNM") == null ? "":object.getString("VNDNM"));
                dataJSON.put("MEINH", object.getString("MEINH") == null ? "":object.getString("MEINH"));
                dataJSON.put("UMREZ", object.getString("UMREZ") == null ? "":object.getString("UMREZ"));
                dataJSON.put("UMREN", object.getString("UMREN") == null ? "":object.getString("UMREN"));
                dataJSON.put("VKORG", object.getString("VKORG") == null ? "":object.getString("VKORG"));
                dataJSON.put("VTWEG", object.getString("VTWEG") == null ? "":object.getString("VTWEG"));
                dataJSON.put("VRKME", object.getString("VRKME") == null ? "":object.getString("VRKME"));
                dataJSON.put("MTPOS", object.getString("MTPOS") == null ? "":object.getString("MTPOS"));


                String MVGR1 = object.getString("MVGR1") == null ? "":object.getString("MVGR1");
                if("0".equals(MVGR1)){
                    dataJSON.put("MVGR1","002");
                }else if("1".equals(MVGR1)){
                    dataJSON.put("MVGR1","003");
                }else if("2".equals(MVGR1)){
                    dataJSON.put("MVGR1","004");
                }else if("3".equals(MVGR1)){
                    dataJSON.put("MVGR1","005");
                }else if("4".equals(MVGR1)){
                    dataJSON.put("MVGR1","006");
                }else if("5".equals(MVGR1)){
                    dataJSON.put("MVGR1","007");
                }else if("9".equals(MVGR1)){
                    dataJSON.put("MVGR1","011");
                }else {
                    dataJSON.put("MVGR1","");
                }
                dataJSON.put("MTVFV", object.getString("MTVFV") == null ? "":object.getString("MTVFV"));
                dataJSON.put("EKGRP", object.getString("EKGRP") == null ? "":object.getString("EKGRP"));//采购组
                String scrkdd = object.getString("LGPRO") == null ? "":object.getString("LGPRO");
                switch (scrkdd){
                    case "0":
                        dataJSON.put("LGPRO","1001");//生产入库地点
                        break;
                    default:
                        dataJSON.put("LGPRO",scrkdd);//生产入库地点
                }

                dataJSON.put("PLIFZ", object.getString("PLIFZ"));
                dataJSON.put("BSTME", object.getString("BSTME"));
                dataJSON.put("MAXLZ", object.getString("MAXLZ"));
                dataJSON.put("MHDRZ", object.getString("MHDRZ"));
                dataJSON.put("DISGR", object.getString("DISGR"));
                dataJSON.put("DISMM", object.getString("DISMM"));
                dataJSON.put("FXHOR", object.getString("FXHOR"));
                dataJSON.put("MINBE", object.getString("MINBE"));
                dataJSON.put("DISLS", object.getString("DISLS"));
                dataJSON.put("BSTMI", object.getString("BSTMI"));
                dataJSON.put("BSTMA", object.getString("BSTMA"));
                dataJSON.put("MABST", object.getString("MABST"));
                dataJSON.put("BSTRF", object.getString("BSTRF"));
                dataJSON.put("BESKZ", object.getString("BESKZ"));
                dataJSON.put("SOBSL", object.getString("SOBSL"));
                dataJSON.put("RGEKZ", object.getString("RGEKZ"));
                dataJSON.put("EPRIO", object.getString("EPRIO"));
                dataJSON.put("EISBE", object.getString("EISBE"));
                dataJSON.put("FHORI", object.getString("FHORI"));
                dataJSON.put("MTVFP", object.getString("MTVFP"));
                dataJSON.put("FRTME", object.getString("FRTME"));
                dataJSON.put("BKLAS", object.getString("BKLAS"));
                dataJSON.put("EKLAS", object.getString("EKLAS"));
                dataJSON.put("STPRS", object.getString("STPRS"));
                dataJSON.put("PEINH", object.getString("PEINH"));
                dataJSON.put("MLAST", object.getString("MLAST"));
                dataJSON.put("EKALR", object.getString("EKALR"));
                dataJSON.put("HKMAT", object.getString("HKMAT"));
                dataJSON.put("LOSGR", object.getString("LOSGR"));
                dataJSON.put("PRCTR", object.getString("PRCTR"));
                if ("0".equals(object.getString("MMSTA"))){
                    dataJSON.put("MMSTA", "Z1");
                } else if ("1".equals(object.getString("MMSTA"))){
                    dataJSON.put("MMSTA", "");
                } else {
                    dataJSON.put("MMSTA","");
                }

                //长宽高体积
                dataJSON.put("ZLENGTH", object.getString("ZLENGTH"));
                dataJSON.put("ZWIDTH", object.getString("ZWIDTH"));
                dataJSON.put("ZHIGH", object.getString("ZHIGH"));
                dataJSON.put("ZVOLUME", object.getString("ZVOLUME"));

                //新增的18个字段  2022/05/10
                //分类化视图
                //基价下限
                dataJSON.put("ZJJXX",object.getString("ZJJXX"));
                //基价上限
                dataJSON.put("ZJJSX",object.getString("ZJJSX"));
                //一级分类
                dataJSON.put("ZYJFL",object.getString("ZYJFL"));
                //二级分类
                dataJSON.put("ZEJFL",object.getString("ZEJFL"));
                //三级分类
                dataJSON.put("ZSJFL",object.getString("ZSJFL"));
                //成品四级-财务标记
                dataJSON.put("ZCPSJ_CW",object.getString("ZCPSJ_CW"));
                //成品简称-财务
                dataJSON.put("ZCPJC_CW",object.getString("ZCPJC_CW"));
                //成品四级-计划标记
                dataJSON.put("ZCPSJ_JH",object.getString("ZCPSJ_JH"));
                //成品简称-计划
                dataJSON.put("ZCPJS_JH",object.getString("ZCPJS_JH"));
                //税收分类
                dataJSON.put("ZSSFL",object.getString("ZSSFL"));
//                //是否溯源
//                dataJSON.put("ZSFSY",object.getString("ZSFSY"));
                //单位
                dataJSON.put("ZCW_UNIT",object.getString("ZCW_UNIT"));
                //入
                dataJSON.put("ZGUIGE",object.getString("ZGUIGE"));
                //提
                dataJSON.put("ZGUIGE2",object.getString("ZGUIGE2"));

                //产品小类    ZCPXL
                dataJSON.put("ZCPXL",object.getString("ZCPXL"));
                //转化值      ZZHZ
                dataJSON.put("ZZHZ",object.getString("ZZHZ"));

                //四级分类
                dataJSON.put("ZZSJFL",object.getString("ZZSJFL"));

                // 是否溯源
                dataJSON.put("ZSFSY", object.getString("ZSFSY"));

                //是否测试新建
                dataJSON.put("Zflag", MATNRFlag);

                dataArr.add(dataJSON);
            }
            jsonObject.put("DATA", dataArr);
            execute = super.execute(jsonObject.toString());

            Object msgty = execute.get("MSGTY");
            if (!"S".equals(msgty)) {     //失败
                return execute;
            }
        }
        if (execute.isEmpty()) {    //成功
            execute.put("MSGTY", "S");
            execute.put("MSAGE", "成功");
            execute.put("RESULT", execute);
            execute.put("PARAMS", params);
        }

        String execute2 = (String) execute.get("RESULT");
        JSONObject jsonObject2 = JSONObject.parseObject(execute2);
        List<JSONObject> dataObject = (List<JSONObject>) jsonObject2.get("DATA");
        if(dataObject.size()>0){
            for(JSONObject obj : dataObject){
                String ZID = obj.getString("ZID");
                String MATNR = obj.getString("MATNR");
                RecordSet rs3 = new RecordSet();
                String sql3 = " update " + tableName+"_dt1 set MATNR = ? where MAKTX = ? and mainid = (SELECT id FROM "+tableName+" WHERE requestid = ?)";
                rs3.executeUpdate(sql3, MATNR, ZID, requestid);
            }
        }

        return execute;
    }

}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.HttpClientUtils;
import yitouniu.util.TeambitionBaseConfig;
import yitouniu.util.TeambitionBaseConfigV1;
import yitouniu.util.TeambitionHttpToken;

import java.io.UnsupportedEncodingException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: Chen <PERSON>i
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 0:40 2022/1/4
 */
public class UEDTaskToTeambitionV1 {

    //正式
//    public final static String dataBaseName = "";
//    public final static String detailDataBaseName = "";
    //测试
    public final static String dataBaseName = "formtable_main_91";
    public final static String detailDataBaseName = "formtable_main_91_dt1";

    public final static String KEYWORD = "UED";

    /**
     * 获取固定headers请求头
     */
    Map<String, String> headers = TeambitionHttpToken.returnHeaders(TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID,TeambitionBaseConfig.APP_SECRET));
    public static String projectId;
    public static String taskgroupId;
    public static String tasklistId;
    public static String templateId;
    public static String taskflowId;
    public static String executorId;
    public static String creatorId;

    public UEDTaskToTeambitionV1() throws UnsupportedEncodingException {
    }

    public Map executeParam(Map params)  {
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-start");
        Map map = new HashMap();

        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
            //营销视觉需求的任务一次一条
            JSONObject taskObject = (JSONObject) jsonObject.get("TASK");

            //CTRL
            String requestId = ctrlObject.getString("requestId");
            String nodeId = ctrlObject.getString("nodeId");
            String demandType= ctrlObject.getString("demandType");
            if("1".equals(demandType)){
                map.put("MSGTY", "S");
                map.put("MSAGE", "类型为内容需求单，不予创建任务");
                map.put("RESULT", "");
                map.put("IN_JSON", "");
                return map;
            }

            //得到任务应放所在处的年月
            //表单上的申请日期
            String applyTime = ctrlObject.getString("applyTime");
            String deliverTime = ctrlObject.getString("deliverTime");
            //TODO:这里是将真正的创建流程时间取出来，但是因为tb那边是UTC时间
            String AfterTime = null;
            RecordSet rsStartTime = new RecordSet();
            String sqlStartTime = "select createtime from workflow_requestbase where requestid = ?";
            rsStartTime.executeQuery(sqlStartTime,requestId);
            if(rsStartTime.next()){
                AfterTime = rsStartTime.getString("createtime");
            }
            //将申请时间转换为Date
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date dBegin = sdf.parse(applyTime+" "+AfterTime);
            //生成UTC时间，需要加时区
            DateFormat UTCdateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
            TimeZone newYorkTimeZone1 = TimeZone.getTimeZone("UTC");
            UTCdateFormat.setTimeZone(newYorkTimeZone1);
            String TBStartTime = UTCdateFormat.format(dBegin);

//        String TBStartTime = applyTime +"T01:00:00Z";
            //当天的结尾时间
            String TBEndTime = deliverTime +"T15:59:59Z";

            String year = applyTime.substring(0,4);
            String month = applyTime.substring(5,7);
            String day = applyTime.substring(8,10);

            month = String.valueOf(Integer.parseInt(month));


            //查找部门，到二级甚至三级部门
            String taskApplyDepId = ctrlObject.getString("taskApplyDep");
            String taskApplyDep = null;
            RecordSet rsTaskApplyDep = new RecordSet();
            String sqlTaskApplyDep = "SELECT departmentname FROM HrmDepartment where id = ?";
            rsTaskApplyDep.executeQuery(sqlTaskApplyDep,taskApplyDepId);
            if(rsTaskApplyDep.next()) {
                taskApplyDep = rsTaskApplyDep.getString("departmentname");
            }
            String applyPeopleID = ctrlObject.getString("applyPeople");
            String applyPeople = null;
            RecordSet rsApplyPeople = new RecordSet();
            String applyPeopleSql = "select lastname from hrmresource where id = ?";
            rsApplyPeople.executeQuery(applyPeopleSql,applyPeopleID);
            if(rsApplyPeople.next()){
                applyPeople = rsApplyPeople.getString("lastname");
            }


            //TASK
            //region 开始创建任务

            //任务名称
            String taskName = taskObject.getString("taskName");
            //先查到项目id、创建人id、项目编号前缀
            Map<String, String> projectMap = returnProject(year,KEYWORD);
            projectId = projectMap.get("projectId");
            creatorId = projectMap.get("creatorId");
            //根据项目id以及今年的年份和月份找到相应的任务分组和任务列表
            taskgroupId = returnTaskgroupId(year, month, projectId);
            tasklistId = returnTasklistId(year, month, projectId, taskgroupId);
            //查询任务所需要的任务类型
            Map<String, String> taskType = returnTaskTypes(projectId);
            templateId = taskType.get("templateId");
            //TODO：这个UED项目有问题，没有taskflowId的ID，测测看是否有问题。
            taskflowId = taskType.get("taskflowId");
            //查询任务所需要的工作流
            String taskflowStatusId = "";
//        if(!"".equals(taskflowId)){
//            taskflowStatusId = returnTaskflowStatusId(taskflowId);
//        }
            //查询执行者（当前节点审批的人）
            String loginId = null;
            //通过requestid和nodeid查到审批人的工号，然后去企业成员列表中匹配得到执行者id
            RecordSet rs = new RecordSet();
//        String sql = "select loginid as loginId " +
//                "from HrmResource " +
//                "where id = (select userid from workflow_currentoperator where requestid = ? and nodeid = ? and viewtype = -2 )";
            String sql = "select loginid as loginId " +
                    "from HrmResource " +
                    "where id = (select userid from workflow_currentoperator where requestid = ? and nodeid = ? and isremark = 0 )";
            rs.executeQuery(sql, requestId, nodeId);
            if (rs.next()) {
                loginId = rs.getString("loginId");
            }
            executorId = returnUserId(loginId);

            //创建任务的req数据
            JSONObject taskReq = new JSONObject();
            //操作人用户id
            taskReq.put("operatorId", creatorId);
            //项目id
            taskReq.put("projectId", projectId);
            //任务类型模板id
            taskReq.put("templateId", templateId);
            //任务列表id
            taskReq.put("tasklistId", tasklistId);
            //任务分组id
            taskReq.put("taskgroupId", taskgroupId);
            //任务内容
            taskReq.put("content", taskName);
            //执行者id，不传表示待认领
            taskReq.put("executorId", executorId);
            //工作流状态id
            taskReq.put("statusId", taskflowStatusId);
            //开始日期，现在不传值
            taskReq.put("startDate", TBStartTime);
            //截止日期，现在不传值
            taskReq.put("dueDate", TBEndTime);
            //任务备注,现在不传值
            taskReq.put("note", "");
            //优先级,现在不传值
            taskReq.put("priority", "");
            //可见性,默认为projectMembers，项目成员可见，   任务参与者可见为participants
            taskReq.put("visible", "projectMembers");
            //父任务id，创建子任务的时候传
            taskReq.put("parentTaskId", "");
            //参与者id列表
            JSONArray participantsArray = new JSONArray();
            participantsArray.add(executorId);
            taskReq.put("participants", participantsArray);
            //自定义字段id
            JSONArray customfieldArr = new JSONArray();
            //taskType是任务类型返回的Map，其中除了模板id和工作流id其他都是自定义字段。
            for (Map.Entry<String, String> m : taskType.entrySet()) {
                new BaseBean().writeLog("ESB-UEDTaskToTeambition-taskType.entrySet()：" + m.getKey() + "。类型字段id为：" + m.getValue());
                JSONObject customfieldJson = new JSONObject();
                JSONArray value = new JSONArray();
                if (!"templateId-taskflowId".contains(m.getKey())) {
                    switch (m.getKey()) {
                        case "业务渠道":
                            value.add(taskApplyDep);
                            break;
                        case "需求对接人":
                            value.add(applyPeople);
                            break;
                        default:
                            break;
                    }
                    customfieldJson.put("cfId", m.getValue());
                    customfieldJson.put("value", value);
                }
                if (!customfieldJson.isEmpty()) {
                    customfieldArr.add(customfieldJson);
                }
            }
            taskReq.put("customfields", customfieldArr);
            new BaseBean().writeLog("ESB-UEDTaskToTeambition：主任务请求json" + taskReq);
            //发起请求
            String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfigV1.URL_API_TASK_CREATE_POST, headers, taskReq);
            JSONObject resp = JSONObject.parseObject(httpResponse);
            new BaseBean().writeLog("ESB-UEDTaskToTeambition：主任务返回json" + httpResponse);
            String code = resp.getString("code");
            JSONObject resultObject = (JSONObject) resp.get("result");
            if ("200".equals(code)) {
                map.put("MSGTY", "S");
                map.put("MSAGE", "创建任务成功，请到Teambition查看");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "任务请求失败");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
                return map;
            }


            //endregion


            new BaseBean().writeLog("ESB-UEDTaskToTeambition-end");
        }catch (Exception e){
            map.put("MSGTY", "E");
            map.put("MSAGE", "异常错误失败");
            map.put("RESULT", "");
            map.put("IN_JSON", "");
        }



        return map;

    }


    /**
     * 返回项目的一些信息，尽量通用点吧，试试看
     * 现在返回项目id、创建人id、
     * @param year 年份
     * @param keyWord 关键词：这里固定为UED先用着先
     * @return
     */
    public Map<String,String> returnProject(String year, String keyWord) {
        Map<String,String> projectMap = new HashMap<String,String>();
        JSONObject params = new JSONObject();
        String searchName = year + "年" + keyWord +"任务书";
        params.put("name",searchName);
        params.put("pageSize",1000);
        String project = null;
        String creator = null;
        //查询企业的所有项目，根据一些条件去得到唯一的项目id
        String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfig.URL_API_PROJECT_QUERY_POST, headers, params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnProject：查询项目信息返回json"+resp);

        String code = resp.getString("code");
//        JSONArray result = resp.getJSONArray("result");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if("200".equals(code)){
            for(JSONObject object :resultList){
                if(searchName.contains(object.getString("name"))){
                    project = object.getString("projectId");
                    creator = object.getString("creatorId");
                }
            }
        }
        projectMap.put("projectId",project);
        projectMap.put("creatorId",creator);

        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnProject：项目信息处理后返回的"+projectMap);

        return projectMap;

    }


    /**
     * 返回任务分组id
     * @param year 年
     * @param month 月
     * @param projId 项目id
     * @return
     */
    public String returnTaskgroupId(String year, String month,String projId)  {
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskgroupId：查询项目分组id的条件year="+year+",month="+month);
        String queryConditions = year+"年"+month+"月";
        Map<String,String> params = new HashMap<String,String>();
        params.put("projectId",projId);
        String taskgroup = null;
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASKGROUP_QUERY_GET, headers,params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskgroupId：查询项目分组id返回json"+resp);
        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if("200".equals(code)){
            for(JSONObject object :resultList){
                String name = object.getString("name");
                if(name.equals(queryConditions)){
                    taskgroup = object.getString("taskgroupId");
                }
            }
        }
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskgroupId：查询项目分组id的taskgroup="+taskgroup);
        return taskgroup;
    }

    /**
     * 返回任务列表id
     * @param year 年份
     * @param month 月份
     * @param projId 项目id
     * @param taskgropId 任务分组id
     * @return
     */
    public String returnTasklistId(String year, String month, String projId, String taskgropId)  {
        String queryConditions = year+"年"+month+"月";
        Map<String,String> params = new HashMap<String,String>();
        params.put("projectId",projId);
        params.put("taskgroupId",taskgropId);
        String tasklist = null;
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASKLIST_QUERY_GET, headers,params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTasklistId：查询项目列表id返回json"+resp);
        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if("200".equals(code)){
            for(JSONObject object :resultList){
                String name = object.getString("name");
                if(name.equals(queryConditions)){
                    tasklist = object.getString("tasklistId");
                }
            }
        }
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTasklistId：查询项目列表id的tasklist="+tasklist);
        return tasklist;
    }

    /**
     * 查询任务类型，通过Map返回任务模板id，工作流id，自定义字段id
     * @param project 项目id
     * @return
     */
    public Map<String,String> returnTaskTypes(String project){
        Map<String,String> taskTypeMap = new HashMap<String,String>();
        Map<String,String> params = new HashMap<String,String>();
        params.put("projectId",project);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TEMPLATE_QUERY_GET, headers,params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskTypes：查询任务类型后返回json"+resp);
        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if("200".equals(code)){
            for(JSONObject object :resultList){
                String name = object.getString("name");
                //应该确保name为任务的这个工作类型只有一个，否则需要修改
                if("任务".equals(name)){
                    //模板id
                    taskTypeMap.put("templateId",object.getString("templateId"));
                    //工作流id
                    taskTypeMap.put("taskflowId",object.getString("taskflowId"));
                    List<JSONObject> customfieldsList = (List<JSONObject>) object.get("customfields");
                    for(JSONObject obj : customfieldsList){
                        String fieldType = obj.getString("fieldType");
                        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskTypes：fieldType:"+fieldType);
                        //额外的自定义字段
                        if("customfield".equals(fieldType)){
                            taskTypeMap.put(obj.getString("name"),obj.getString("cfId"));
                            new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskTypes：name:"+obj.getString("name")+";cfId:"+obj.getString("cfId"));
                        }
                    }
                }
            }
        }
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnTaskTypes：处理后的人物类型返回Map"+taskTypeMap);

        return taskTypeMap;
    }

    /**
     * 查询返回未完成状态的id，一般性初始化为未完成，后期优化
     * @param taskflow 工作流id
     * @return
     */
    public String returnTaskflowStatusId(String taskflow){
        String taskflowStatusId = null;
        Map<String,String> params = new HashMap<String,String>();
        params.put("taskflowId",taskflow);
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_TASKFLOW_QUERY_GET, headers,params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        String code = resp.getString("code");
        JSONObject resultObject = resp.getJSONObject("result");
        List<JSONObject> statusesList = (List<JSONObject>) resultObject.get("statuses");
        if("200".equals(code)) {
            for (JSONObject stat : statusesList) {
                String name = stat.getString("name");
                if ("未完成".equals(name)) {
                    taskflowStatusId = stat.getString("id");
                    break;
                }
            }
        }
        return taskflowStatusId;
    }

    /**
     * 返回人员在teambition的id
     * @param loginId 员工工号
     * @return
     */
    public String returnUserId(String loginId){
        String user = null;
        Map<String,String> params = new HashMap<String,String>();
        params.put("orgId",TeambitionBaseConfig.COMPANY_ID);
//        params.put("pageToken","");
        params.put("pageSize",String.valueOf(4000));
//        params.put("filter","");
        String httpResponse = HttpClientUtils.doGet(TeambitionBaseConfig.URL_API_ORG_MEMBER_LIST_GET, headers,params);
        JSONObject resp = JSONObject.parseObject(httpResponse);
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnUserId：查询人员id返回的json"+resp);

        String code = resp.getString("code");
        List<JSONObject> resultList = (List<JSONObject>) resp.get("result");
        if("200".equals(code)){
            for(JSONObject object :resultList){
                String employeeNumber = object.getString("employeeNumber");
                if(loginId.equals(employeeNumber)){
                    user = object.getString("userId");
                    break;
                }
            }
        }
        new BaseBean().writeLog("ESB-UEDTaskToTeambition-returnUserId：返回员工在tb的id"+user+";以及loginId="+loginId);

        return user;

    }




}
package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import yitouniu.util.StdUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户关联职位
 *
 * <AUTHOR>
 * @since 2024-08-26 10:25
 */
public class STD003ESB {
    private final Log LOG = LogFactory.getLog(STD003ESB.class);

    private static final String USER_BIND_POSITION_URL = "/crm-mdm/v1/external/std/bindPositionToUser";

    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }

    public Map executeParam(String params) {
        LOG.info("进入STD003ESB.executeParam,入参:" + params);

        Map<String, String> retMap = new HashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONArray dataObject = jsonObject == null ? null : jsonObject.getJSONArray("DATA");
        if (dataObject == null) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "参数不能为空");
            return retMap;
        }

        try {
            StdUtil.executePost(USER_BIND_POSITION_URL, dataObject.toJSONString());
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", e.getMessage());
            return retMap;
        }

        retMap.put("MSGTY", "S");
        retMap.put("MSAGE", "回调成功");

        LOG.info("OA调用STD用户关联职位接口-完成，入参:" + params + "，结果：" + JSON.toJSONString(retMap));
        return retMap;
    }

}

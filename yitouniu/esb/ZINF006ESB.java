package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ActionUtil;


import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 免费订单和员工内购推送
 */
public class ZINF006ESB extends ReturnMsgToSAP {


    public Map executeParam(String params) {
        RecordSet recordSet = new RecordSet();
        String currDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        //JSONArray DATAArr = new JSONArray();

        //String paramString = JSON.toJSONString(params);
        // 获取esb中流程参数
        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject CTRLObj = (JSONObject) jsonObject.get("CTRL");

        String requestid = CTRLObj.getString("INFID"); // 获取requestid
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String  tableName = (String) tableNameByRequestId.get("tableName"); // 表明
        Map execute = new HashMap();
        if (jsonObject.containsKey("DATA")) {
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");

            // 根据编号分组
            Map<String, List<JSONObject>> collect = dataList.stream().collect(Collectors.groupingBy(row -> row.getString("VKGRP")));

            Iterator<Map.Entry<String, List<JSONObject>>> iterator = collect.entrySet().iterator();
            // 循环组织发送的数据
            while (iterator.hasNext()) {


                Map.Entry<String, List<JSONObject>> next = iterator.next();
                List<JSONObject> detailDaoList = next.getValue();
                String lcbhjxh = "";

                JSONArray DATAArr = new JSONArray();
                JSONArray ITEMArr = new JSONArray();
                for (int i = 0; i < detailDaoList.size(); i++) {
                    JSONObject ITEMJson = new JSONObject();

                    JSONObject object = detailDaoList.get(i);
                    lcbhjxh = object.getString("BZIRK");  // 行号加编码
                    ITEMJson.put("POSNR", object.getString("POSNR"));
                    ITEMJson.put("MATNR", object.getString("MATNR"));
                    ITEMJson.put("PSTYV", object.getString("PSTYV"));
                    ITEMJson.put("KWMENG", object.getString("KWMENG"));
                    ITEMJson.put("VRKME", object.getString("VRKME"));
                    ITEMJson.put("KBETR", object.getString("KBETR"));
                    ITEMJson.put("TEXT", object.getString("TEXT"));
                    ITEMJson.put("ETTYP", object.getString("ETTYP"));
                    ITEMJson.put("ZVALIDITY_L", object.getString("ZVALIDITY_L"));
                    ITEMJson.put("ZVALIDITY_D", object.getString("ZVALIDITY_D"));
                    ITEMJson.put("ZPINX", object.getString("ZPINX"));
                    ITEMArr.add(ITEMJson);

                    if (i == detailDaoList.size() - 1) {
                        JSONObject DATAJson = new JSONObject();
                        DATAJson.put("ITEM", ITEMArr);
                        DATAJson.put("OPERATION", object.getString("OPERATION"));
                        DATAJson.put("VBELN_OA", object.getString("VBELN_OA"));
                        DATAJson.put("AUART", object.getString("AUART"));
                        DATAJson.put("VKORG", object.getString("VKORG"));
                        DATAJson.put("VTWEG", object.getString("VTWEG"));
                        DATAJson.put("SPART", object.getString("SPART"));
                        DATAJson.put("KUNAG", object.getString("KUNAG"));
                        DATAJson.put("KUNWE", object.getString("KUNWE"));
                        DATAJson.put("VSART", object.getString("VSART"));
                        DATAJson.put("PRSDT", object.getString("PRSDT"));
                        DATAJson.put("KOSTL", object.getString("KOSTL"));
                        DATAJson.put("BNAME", object.getString("BNAME"));
                        String HTEXT = object.getString("HTEXT");
                        String htxt1 = HTEXT.replaceAll("<br>", "");
                        String htxt2 = htxt1.replaceAll("&nbsp;", "");
                        DATAJson.put("HTEXT", htxt2);
                        DATAJson.put("REGION", detailDaoList.get(0).getString("REGION"));
                        DATAJson.put("CITY", detailDaoList.get(0).getString("CITY"));
                        DATAJson.put("DISTRICT", detailDaoList.get(0).getString("DISTRICT"));
                        DATAJson.put("STREET", detailDaoList.get(0).getString("STREET"));
                        DATAJson.put("NAME_CO", detailDaoList.get(0).getString("NAME_CO"));
                        DATAJson.put("TEL_NUMBER", detailDaoList.get(0).getString("TEL_NUMBER"));
                        CTRLObj.put("CTRL",requestid+"-"+object.getString("VKGRP"));
                        DATAArr.add(DATAJson);
                    }
                }
                // 查询数据是否需要推送
                String isSend = selectSql(tableName, lcbhjxh);
                if ("0".equals(isSend)) { // 不推送
                    continue;
                }

                jsonObject.put("DATA", DATAArr);
                execute = super.execute(jsonObject.toString());

                Object msgty = execute.get("MSGTY");
                if (!"S".equals(msgty)){
                    return execute;
                }
                JSONObject response = JSONObject.parseObject((String) execute.get("RESULT"));
                String VBELN = ((JSONObject)response.getJSONArray("DATA").get(0)).getString("VBELN").replaceAll("^(0+)", "");



                updateSql(tableName, lcbhjxh, VBELN); // 成功更新不推送凭证
            }

        }

        if(execute.isEmpty()){
            execute.put("MSGTY", "S");
            execute.put("MSAGE", "成功");
            execute.put("RESULT", execute);
            execute.put("PARAMS", params);
        }
        new BaseBean().writeLog("数据结束时间: "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        return execute;

    }

    /**
     * 查询是否推送凭证
     * @param tableName
     * @param lcbhjxh
     * @return
     */
    public String selectSql(String tableName,String lcbhjxh){
        RecordSet recordSet = new RecordSet();
        String sql = "select  * from  "+tableName+"_dt1 where lcbhjxh = ?";
        recordSet.executeQuery(sql,lcbhjxh);
        String sftspz = ""; // 是否推送凭证
        if (recordSet.next()){
            sftspz = recordSet.getString("sftspz");
        }
        return sftspz;
    }

    /**
     * 更新数据
     * @param tableName
     * @param lcbhjxh
     */
    public void updateSql(String tableName,String lcbhjxh, String VBELN){
        RecordSet recordSet = new RecordSet();
        String sql = "";
        sql = "update "+tableName+"_dt1  set  sftspz = 0,hcysdh = ?  where lcbhjxh = ?";
        recordSet.executeUpdate(sql,VBELN,lcbhjxh);
    }

}

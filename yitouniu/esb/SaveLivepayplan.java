package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.CDMSUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SaveLivepayplan {    //直播付款信息存表 by 依依  0525

    private RecordSet rs = new RecordSet();

    public Map executeParam(String params){
        String requestId = "";
        String lcbh = "";
        String sqr = "";
        String zje = "";
        String kyje = "";
        String yfje = "";
        String djje = "";
        String zqye = "";
        String PARTNER = "";
        String skdwzh = "";

        Map map = new HashMap();
        new BaseBean().writeLog("11111111111");

        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            //List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
            JSONObject idata = (JSONObject) jsonObject.get("DATA");

            new BaseBean().writeLog("以下为data:"+idata.size());
            new BaseBean().writeLog("以下为data:"+idata);
            //JSONObject idata = data.get(0);
            requestId = idata.getString("requestId");
            lcbh = idata.getString("lcbh");
            sqr = idata.getString("sqr");
            zje = idata.getString("fkze");
            PARTNER = idata.getString("PARTNER");
            skdwzh = idata.getString("skdwzh");

            kyje = zje;
            djje = "0";
            yfje = "0";
            zqye = kyje;

            List<List> insertAllData = new ArrayList<>();
            List insertData =  new ArrayList<>();
            insertData.add(requestId);
            insertData.add(lcbh);
            insertData.add(sqr);
            insertData.add(zje);
            insertData.add(kyje);
            insertData.add(yfje);
            insertData.add(djje);
            insertData.add(zqye);
            insertData.add(PARTNER);
            insertData.add(skdwzh);

            insertAllData.add(insertData);
            new BaseBean().writeLog("以下为insertdata:"+insertAllData);

            if(insertAllData.size()>0){
                boolean b = rs.executeBatchSql("insert into live_pay_plan (requestId,lcbh,sqr,zje,kyje,yfje,djje,zqye,PARTNER,skdwzh) values (?,?,?,?,?,?,?,?,?,?)",insertAllData);
                if(b){
                    map.put("MSGTY", "S");
                    map.put("MSAGE", "成功");
                }else{
                    map.put("MSGTY", "F");
                    map.put("MSAGE", "失败");
                }
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
        }
        return map;
    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 账龄分析表推送
 */
public class AgingScheduleESB {
    public Map execute(String params){
        List<Map> dataList = new ArrayList();
        Map dataMap = new HashMap();
        Map resultMap = new HashMap();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String b ="";
        List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
        for (JSONObject dataJSONObject : data) {
            String BUKRS = dataJSONObject.getString("BUKRS");
            String LIFNR = dataJSONObject.getString("LIFNR");
            String BLART = dataJSONObject.getString("BLART");
            String BELNR = dataJSONObject.getString("BELNR");
            String GJAHR = dataJSONObject.getString("GJAHR");
            String MONAT = dataJSONObject.getString("MONAT");
            String ZFBDT = dataJSONObject.getString("ZFBDT");
            String ZTERM = dataJSONObject.getString("ZTERM");
            String OVDAYS = dataJSONObject.getString("OVDAYS");
            String WRBTR = dataJSONObject.getString("WRBTR");
            String WAERS = dataJSONObject.getString("WAERS");
            String BUDAT = dataJSONObject.getString("BUDAT");
            String BLDAT = dataJSONObject.getString("BLDAT");
            String EBELN = dataJSONObject.getString("EBELN");
            String REBZG = dataJSONObject.getString("REBZG");

             b += insertData(BUKRS, LIFNR, BLART, BELNR, GJAHR, MONAT, ZFBDT, ZTERM, OVDAYS, WRBTR, WAERS, BUDAT, BLDAT, EBELN, REBZG);

        }
        if (b.indexOf("false")==-1){
            resultMap.put("MSGTY","S");
            resultMap.put("MSAGE","成功");
            dataList.add(resultMap);
        }else {
            resultMap.put("MSGTY","F");
            resultMap.put("MSAGE","失败");
            dataList.add(resultMap);
        }
        String s = JSON.toJSONString(dataList);
        dataMap.put("DATA",s);
        return dataMap;
    }

    public boolean insertData(String BUKRS,String LIFNR,String BLART,String BELNR,String GJAHR,String MONAT,String ZFBDT,String ZTERM,String OVDAYS,String WRBTR,String WAERS,String BUDAT,String BLDAT,String EBELN,String REBZG){
        RecordSet rs = new RecordSet();
        String sql = "insert into uf_zhanglingfenxi (BUKRS,LIFNR,BLART,BELNR,GJAHR,MONAT,ZFBDT,ZTERM,OVDAYS,WRBTR,WAERS,BUDAT," +
                "BLDAT,EBELN,REBZG,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        boolean b = rs.executeUpdate(sql, BUKRS, LIFNR, BLART, BELNR, GJAHR, MONAT, ZFBDT, ZTERM, OVDAYS, WRBTR, WAERS, BUDAT, BLDAT, EBELN, REBZG,  "21", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare("uf_zhanglingfenxi");
        return b;
    }
}

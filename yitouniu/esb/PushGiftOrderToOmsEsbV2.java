package yitouniu.esb;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import yitouniu.constant.CommonConstant;
import yitouniu.util.OMSUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-01-25 14:10
 **/
public class PushGiftOrderToOmsEsbV2 {

    BaseBean baseBean = new BaseBean();

    public Map executeParam(Map params) {
        baseBean.writeLog("PushGiftOrderToOmsEsbParam:" + params);
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray  jsonArray = jsonObject.getJSONArray("DATA");
        List<JSONObject> dataListObject = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
        JSONArray requestArr = new JSONArray();
        dataListObject.forEach(item -> {
            JSONObject request = new JSONObject();
            request.put("ownername", item.getString("ownername"));
            request.put("isGift", ctrlObject.getString("isGift"));
            request.put("cpCShopTitle", item.getString("cpCShopTitle"));
            request.put("tid", item.getString("tid") + "-ozp");
            String zfddsycs = item.getString("zfddsycs");
            if (StringUtils.isNotEmpty(zfddsycs) && Integer.parseInt(zfddsycs) > 0) {
                request.put("tid", item.getString("tid") + "-ozp" + "-" + zfddsycs);
            }
            request.put("receiverName", item.getString("receiverName"));
            request.put("receiverMobile", item.getString("receiverMobile"));
            request.put("cpCRegionProvinceEname", item.getString("cpCRegionProvinceEname"));
            request.put("cpCRegionCityEname", item.getString("cpCRegionCityEname"));
            request.put("cpCRegionAreaEname", item.getString("cpCRegionAreaEname"));
            request.put("receiverAddress", item.getString("receiverAddress"));
            request.put("psCSkuEcode", item.getString("psCSkuEcode"));
            request.put("qty", item.getInteger("qty"));
            // 单价
            request.put("priceActual", 0);
            // 地址是否明文 0:否；1:是
            request.put("isPlainAddr", item.getInteger("isPlainAddr"));
//            request.put("priceActual", item.getDoubleValue("priceActual"));
//            request.put("realAmt", item.getDoubleValue("realAmt"));
            requestArr.add(request);
        });

        JSONObject requestObj = new JSONObject();
        requestObj.put("data", requestArr);
        baseBean.writeLog("赠品订单请求参数:"+ requestObj);

        OMSUtils omsUtils = new OMSUtils();
        String token = omsUtils.getToken();
        String api = Util.null2String(new BaseBean().getPropValue("oms_configuration","pushGiftOrderApi"));

        String result = HttpRequest
                .post(OMSUtils.OMS_OMS_URL + api)
                .header("r3-api-token",token)
                .body(requestObj.toJSONString()).execute().body();
        new BaseBean().writeLog( "请求返回="+result);

        JSONObject resultJson = JSONObject.parseObject(result);
        if(resultJson.getInteger("code") != 0){
            String errMsg = resultJson.getString("message");
            if(StringUtils.isEmpty(errMsg)){
                map.put("MSGTY","F");
                map.put("MSAGE","异常错误失败");
                map.put("RESULT","异常错误失败");
                map.put("PARAMS",params);
                return map;
            }
            map.put("MSGTY","F");
            map.put("MSAGE", errMsg);
            map.put("RESULT", errMsg);
            map.put("PARAMS",params);
            return map;
        }
        map.put("MSGTY","S");
        map.put("MSAGE","处理数据成功");
        map.put("RESULT",execute);
        map.put("PARAMS",params);

        return map;
    }


}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import weaver.general.BaseBean;
import yitouniu.util.ZJPTUtil;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 4:17 2021/12/27
 */
public class ZJPTFKESB {
    public final static String SRCOUTSYSTEMCODE = "OA";
    public final static String SRCBATCHNO = String.valueOf(System.currentTimeMillis());
    public final static String TRANSCODE = "PMSQ01";


    public Map executeParam(Map params) {
        new BaseBean().writeLog("ESB-ZJPTFKESB-start");
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
//        JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
        JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
        List<JSONObject> itemFileList = (List<JSONObject>) jsonObject.get("ITEM");

        String TRANSDATETIME = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String NowDay = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        String ALLAMOUNT = dataObject.getString("ALLAMOUNT");
        String ALLCOUNT = dataObject.getString("ALLCOUNT");

        StringBuilder sb = new StringBuilder();
        sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n"+
                "<MBS>\n" +
                "    <pub>\n" +
                "        <SRCOUTSYSTEMCODE>" + SRCOUTSYSTEMCODE + "</SRCOUTSYSTEMCODE>\n" +
                "        <SRCBATCHNO>" + SRCBATCHNO + "</SRCBATCHNO>\n" +
                "        <TRANSCODE>" + TRANSCODE + "</TRANSCODE>\n" +
                "        <TRANSDATETIME>" + TRANSDATETIME + "</TRANSDATETIME>\n" +
                "        <MD5>158cfd893b0598f078f7efa4e6b11696</MD5>\n" +
                "    </pub>\n" +
                "    <req>\n" +
                "        <head>\n" +
                "            <ALLAMOUNT>"+ALLAMOUNT+"</ALLAMOUNT>\n" +
                "            <ALLCOUNT>"+ALLCOUNT+"</ALLCOUNT>\n" +
                "        </head>\n" +
                "        <list>\n");
        for(JSONObject obj : itemFileList){
            //流水号，建议加上时间戳
            String SRCSERIALNO = obj.getString("SRCSERIALNO")+System.currentTimeMillis();;
            //OA单据号
            String SRCNOTECODE = obj.getString("SRCNOTECODE");
            //单据所属 组织代码
            String ORGCODE = obj.getString("ORGCODE");
            //付款申请 组织代码
            String APPLYORGCODE = obj.getString("APPLYORGCODE");
            //传入日期
            String PAYDATE = NowDay;
            //交易类型代码
            String PAYTYPECODE = obj.getString("PAYTYPECODE");
            //结算方式代码
            String SETTLEMENTMODECODE = obj.getString("SETTLEMENTMODECODE");
            //计划项目代码 可空。根据实际业务设定是否可空。
            String BUDGETITEMCODE = obj.getString("BUDGETITEMCODE");
            //摘要
            String ABSTRACTS = obj.getString("ABSTRACTS");
            //加急标识
            String ISURGENT = obj.getString("ISURGENT");
            //用途 实际付款用途 可空。根据实际业务设定是否可空。
            String PURPOSE = obj.getString("PURPOSE");
            //附言(对账码) 可不填
            String CHECKCODE = obj.getString("CHECKCODE");
            //代付用途 从工资、补贴、奖金几个字段中选择，不能随便填，建议做成勾选项
            String OFFICEPURPOSEID = obj.getString("OFFICEPURPOSE");
            String OFFICEPURPOSE = null;
            if("0".equals(OFFICEPURPOSEID)){
                OFFICEPURPOSE = "工资";
            } else if("1".equals(OFFICEPURPOSEID)){
                OFFICEPURPOSE = "补贴";
            } else if("2".equals(OFFICEPURPOSEID)){
                OFFICEPURPOSE = "奖金";
            }
            
            //备注
            String MEMO = obj.getString("MEMO");
            //组织代码
            String OURORGCODE = obj.getString("OURORGCODE");
            //由哪个银行的账户做代发填哪个
            String OURBANKACCOUNTNUMBER = obj.getString("OURBANKACCOUNTNUMBER");
            //付方币种代码
            String OURCURCODE = obj.getString("OURCURCODE");
            //单笔付款金额
            String OURAMOUNT = obj.getString("OURAMOUNT");
            //收方户名
            String OPPOBJECTNAME = obj.getString("OPPOBJECTNAME");
            //收方对象代码 可空。根据实际业务设定是否可空。
            String OPPOBJECTCODE = obj.getString("OPPOBJECTCODE");
            //联行号 收方开户银行代码
            String OPPBANKLOCATIONCODE = obj.getString("OPPBANKLOCATIONCODE");
            //开户行名称
            String OPPBANKLOCATIONS = obj.getString("OPPBANKLOCATIONS");
            //个人收款银行账号
            String OPPBANKACCOUNTNUMBER = obj.getString("OPPBANKACCOUNTNUMBER");
            //收方户名
            String OPPBANKACCOUNTNAME = obj.getString("OPPBANKACCOUNTNAME");
            //收方币种代码
            String OPPDIRECTCURCODE = obj.getString("OPPDIRECTCURCODE");
            //收方公私标识：1-对私 2-对公
            String OPPPRIVATEFLAG = obj.getString("OPPPRIVATEFLAG");

            sb.append("            <detail>\n" +
                    "                <SRCSERIALNO>"+SRCSERIALNO+"</SRCSERIALNO>\n" +
                    "                <SRCNOTECODE>"+SRCNOTECODE+"</SRCNOTECODE>\n" +
                    "                <ORGCODE>"+ORGCODE+"</ORGCODE>\n" +
                    "                <APPLYORGCODE>"+APPLYORGCODE+"</APPLYORGCODE>\n" +
                    "                <PAYDATE>"+PAYDATE+"</PAYDATE>\n" +
                    "                <PAYTYPECODE>"+PAYTYPECODE+"</PAYTYPECODE>\n" +
                    "                <SETTLEMENTMODECODE>"+SETTLEMENTMODECODE+"</SETTLEMENTMODECODE>\n" +
                    "                <BUDGETITEMCODE>"+BUDGETITEMCODE+"</BUDGETITEMCODE>\n" +
                    "                <ABSTRACTS>"+ABSTRACTS+"</ABSTRACTS>\n" +
                    "                <ISURGENT>"+ISURGENT+"</ISURGENT>\n" +
                    "                <PURPOSE>"+PURPOSE+"</PURPOSE>\n" +
                    "                <CHECKCODE>"+CHECKCODE+"</CHECKCODE>\n" +
                    "                <OFFICEPURPOSE>"+OFFICEPURPOSE+"</OFFICEPURPOSE>\n" +
                    "                <MEMO>"+MEMO+"</MEMO>\n" +
                    "                <OURORGCODE>"+OURORGCODE+"</OURORGCODE>\n" +
                    "                <OURCURCODE>"+OURCURCODE+"</OURCURCODE>\n" +
                    "                <OURAMOUNT>"+OURAMOUNT+"</OURAMOUNT>\n" +
                    "                <OURBANKACCOUNTNUMBER>"+OURBANKACCOUNTNUMBER+"</OURBANKACCOUNTNUMBER>\n" +
                    "                <OPPOBJECTNAME>"+OPPOBJECTNAME+"</OPPOBJECTNAME>\n" +
                    "                <OPPOBJECTCODE>"+OPPOBJECTCODE+"</OPPOBJECTCODE>\n" +
                    "                <OPPBANKLOCATIONCODE>"+OPPBANKLOCATIONCODE+"</OPPBANKLOCATIONCODE>\n" +
                    "                <OPPBANKLOCATIONS>"+OPPBANKLOCATIONS+"</OPPBANKLOCATIONS>\n" +
                    "                <OPPBANKACCOUNTNUMBER>"+OPPBANKACCOUNTNUMBER+"</OPPBANKACCOUNTNUMBER>\n" +
                    "                <OPPBANKACCOUNTNAME>"+OPPBANKACCOUNTNAME+"</OPPBANKACCOUNTNAME>\n" +
                    "                <OPPDIRECTCURCODE>"+OPPDIRECTCURCODE+"</OPPDIRECTCURCODE>\n" +
                    "                <OPPPRIVATEFLAG>"+OPPPRIVATEFLAG+"</OPPPRIVATEFLAG>\n" +
                    "            </detail>");



        }

        sb.append("        </list>\n" +
                "    </req>\n" +
                "</MBS>");

        String Req = sb.toString();

        try {
            String result = ZJPTUtil.WebServiceZJPT(Req);
            new BaseBean().writeLog("ESB-ZJPTFKESB-result:"+result);
            boolean flag = respcodeParse(result);
            if(flag){
                map.put("MSGTY","S");
                map.put("MSAGE","请求成功");
                map.put("RESULT",flag);
            } else {
                map.put("MSGTY","F");
                map.put("MSAGE","请求失败");
                map.put("RESULT",flag);
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","请求异常错误失败");
            map.put("RESULT",e);
        }

        new BaseBean().writeLog("ESB-ZJPTFKESB-end");

        return map;
    }


    /**
     * 解析请求返回的数据
     * @param xml
     * @throws DocumentException
     */
    public boolean respcodeParse(String xml) throws DocumentException {
        Document doc = null;
        boolean flag = false;
        //字符串转换为XML
        doc = DocumentHelper.parseText(xml);
        //获取根节点
        Element root = doc.getRootElement();
        //获取根节点下的子节点pub
        Iterator iter = root.elementIterator("pub");
        while (iter.hasNext()) {
            Element recordElement = (Element) iter.next();
            //获取指令返回码，0为成功
            String RESPCODE = recordElement.elementText("RESPCODE");
            if ("0".equals(RESPCODE)) {
                flag = true;
            } else {
                flag = false;
            }
        }
        return flag;
    }

}

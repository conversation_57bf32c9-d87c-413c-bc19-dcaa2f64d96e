package yitouniu.esb;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.*;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: oa
 * @description: 变更类创建tb任务v2版本
 * @author: haiyang
 * @create: 2023-08-28 14:38
 **/
public class AutoCreateTbTaskV3ESB {

    public AutoCreateTbTaskV3ESB() throws UnsupportedEncodingException {
    }

    // TB 费控[数智化财务项目] id
    private static final String PROD_FK_PROJECT_ID = "63760183b722ed7092ff6516";

    // TB OA[基础运维项目] id
    private static final String PROD_OA_PROJECT_ID = "63743a25fa424151c03cab1d";

    // TB 缺省项目id
    private static final String PROD_OA_DEFAULT_ID = "64c8600a605d8efffab63a00";

    // TB 数智化财务项目 任务执行人 id
    private static final String PROD_TB_JIAOZI_ID = "6168b44a80a928171b12e27a";

//    // TB 基础运维项目 任务执行人 id
//    private static final String PROD_TB_DANAN_ID = "5c946ff3632d9f0001b4d507";

    //基础运维项目 - OA
    private static final String PROD_TB_DAFA_ID = "6216d96c99426ea5d071a97a";

    // TB 陈海洋 id
    private static final String TB_HAIYANG_ID = "60c1c4af0a6d67f8bb868918";


    /**
     * 获取固定headers请求头
     */
    Map<String, String> headers = TeambitionHttpToken.returnHeaders(TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID,TeambitionBaseConfig.APP_SECRET));
    public Map executeParam(Map params)  {
        new BaseBean().writeLog("ESB-AutoCreateTbTaskESB-start");
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        String requestId = ctrlObject.getString("requestId");
        Map tableNameByRequestID = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String) tableNameByRequestID.get("tableName");
        try {
            //  1. 获取参数 + 构建请求
            List<JSONObject> taskReqList = assembleTbReq(params, headers);
            new BaseBean().writeLog("ESB-AutoCreateTbTaskESB：请求参数" + JSON.toJSONString(taskReqList));
            //  2. 接口调用
            taskReqList.forEach(taskReq -> {
                String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfig.URL_API_TASK_CREATE_POST_V3, headers, taskReq);
                JSONObject resp = JSONObject.parseObject(httpResponse);
                new BaseBean().writeLog("ESB-AutoCreateTbTaskESB：主任务返回json" + httpResponse);
                String code = resp.getString("code");
                JSONObject resultObject = (JSONObject) resp.get("result");
                String taskId = resultObject.getString("taskId");
                if (CommonConstant.SUCCESS_CODE.equals(code)) {
                    RecordSet rs1 = new RecordSet();
                    String sql1 = "SELECT Top 1 * FROM " + tableName + "_dt1 WHERE mainid = ( SELECT id FROM " + tableName + " WHERE requestid = ?) and taskId = '' ";
                    rs1.executeQuery(sql1, requestId);
                    if (rs1.next()) {
                        String dataBaseid = rs1.getString("id");
                        RecordSet rs2 = new RecordSet();
                        String sql2 = "update " + tableName + "_dt1 set taskId = ? where id = ? and mainid = (SELECT id FROM " + tableName + " WHERE requestid = ?)";
                        rs2.executeUpdate(sql2, taskId, dataBaseid, requestId);
                    }
                    map.put("MSGTY", "S");
                    map.put("MSAGE", "创建任务成功，请到Teambition查看");
                    map.put("RESULT", httpResponse);
                    map.put("IN_JSON", taskReq);
                } else {
                    map.put("MSGTY", "F");
                    map.put("MSAGE", "任务请求失败");
                    map.put("RESULT", httpResponse);
                    map.put("IN_JSON", taskReq);
                }
            });
            return map;
        }catch (Exception e) {
            map.put("MSGTY", "E");
            map.put("MSAGE", "异常错误失败");
            map.put("RESULT", e.getMessage());
            map.put("IN_JSON", JSON.toJSONString(params));
        }
        new BaseBean().writeLog("ESB-AutoCreateTbTaskESB-end");
        return map;
    }

    private List<JSONObject> assembleTbReq(Map params, Map<String, String> headers) {
        new BaseBean().writeLog("ESB-AutoCreateTbTaskESB:Params:" + params);
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray jsonArray = jsonObject.getJSONArray(CommonConstant.TASK);
        List<JSONObject> taskList = Lists.newArrayList();
        if (Objects.nonNull(jsonArray)) {
            taskList =  JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
        }
        new BaseBean().writeLog("ESB-AutoCreateTbTaskESB:taskList:" + JSON.toJSONString(taskList));
        // 获取申请人
        String applyPeopleName = getApplyPeopleName(ctrlObject);
        // 获取申请时间
        String applyTime = ctrlObject.getString("applyTime");
        String year = applyTime.substring(0, 4);
        String month = applyTime.substring(5, 7);
        String day = applyTime.substring(8, 10);
        // 获取所属部门
        String departmentName = getDepartmentName(ctrlObject);
        // 获取系统类型
        String systemType = ctrlObject.getString("xtlx");
        // 获取流程名称
        String processName = ctrlObject.getString("lcmc");
        // 流程新增/修改原因
        String processAddOrModifyReason = ctrlObject.getString("lcxzxgyy");
        List<JSONObject> taskReqList = Lists.newArrayList();
        String projectId = systemType.equals("OA")? PROD_OA_PROJECT_ID :
                systemType.equals("费控")? PROD_FK_PROJECT_ID : PROD_OA_DEFAULT_ID;
        String executorId = systemType.equals("OA")?  PROD_TB_DAFA_ID :
                systemType.equals("费控")? PROD_TB_JIAOZI_ID : "";
        String tasklistName = systemType.equals("OA")? "OA/钉钉需求清单" :
                systemType.equals("费控")? "费控需求清单" : "";
        headers.put("x-operator-id", executorId);
        Map taskTypes = TeambitionUtils.returnTaskTypes(projectId, this.headers);
        Map<String, String> taskTypeMap = (Map<String, String>) taskTypes.get("任务");
        String tasklistId = TeambitionUtils.returntasklistIdV3(year, tasklistName, projectId, this.headers);
        String stageId = TeambitionUtils.returnStageIdV3(month, day, projectId, tasklistId, this.headers);
        if (CollectionUtil.isNotEmpty(taskList)) {
            new BaseBean().writeLog("ESB-AutoCreateTbTaskESB:TASKS:FOREACH");
            taskList.forEach(e -> {
                JSONObject taskReq = new JSONObject();
                String currentProcessName = e.getString("lcmc");
                // 项目id
                taskReq.put("projectId", projectId);
                // 标题
                taskReq.put("content", currentProcessName);
                // 执行人
                taskReq.put("executorId", executorId);
                // 任务分组
                taskReq.put("tasklistId", tasklistId);
                // 任务列id
                taskReq.put("stageId", stageId);
                // 备注
                taskReq.put("note", processAddOrModifyReason);
                //自定义字段id
                JSONArray customfieldArr = assembleCustomFields(taskTypeMap, applyPeopleName, departmentName);
                taskReq.put("customfields", customfieldArr);
                taskReqList.add(taskReq);
            });
        } else {
            new BaseBean().writeLog("ESB-AutoCreateTbTaskESB:SINGLE:TASK");
            JSONObject taskReq = new JSONObject();
            // 项目id
            taskReq.put("projectId", projectId);
            // 标题
            taskReq.put("content", processName);
            // 执行人
            taskReq.put("executorId", executorId);
            // 任务分组
            taskReq.put("tasklistId", tasklistId);
            // 任务列id
            taskReq.put("stageId", stageId);
            // 备注
            taskReq.put("note", processAddOrModifyReason);
            //自定义字段id
            JSONArray customfieldArr = assembleCustomFields(taskTypeMap, applyPeopleName, departmentName);
            taskReq.put("customfields", customfieldArr);
            taskReqList.add(taskReq);
        }
        return taskReqList;
    }

    /**
     * 自定义参数
     * @param taskTypeMap
     * @param applyPeopleName
     * @param departmentName
     * @return
     */
    private JSONArray assembleCustomFields(Map<String, String> taskTypeMap, String applyPeopleName, String departmentName) {
        JSONArray customfieldArr = new JSONArray();
        //parentTaskType是任务类型返回的父任务类型Map，其中除了模板id和工作流id其他都是自定义字段。
        for (Map.Entry<String, String> m : taskTypeMap.entrySet()) {
            new BaseBean().writeLog("ESB-CreateToTbTask-taskType.entrySet()：" + m.getKey() + "。类型字段id为：" + m.getValue());
            JSONObject customfieldJson = new JSONObject();
            JSONArray value = new JSONArray();
            JSONObject valueJson = new JSONObject();
            if (!"templateId-taskflowId".contains(m.getKey())) {
                switch (m.getKey()) {
                    case "需求类型":
                        valueJson.put("title", "流程变更");
                        break;
                    case "优先级":
                        valueJson.put("title", "P2");
                        break;
                    case "需求方":
                        valueJson.put("title", applyPeopleName);
                        break;
                    case "需求部门":
                        valueJson.put("title", departmentName);
                        break;
                    default:
                        break;
                }
                customfieldJson.put("cfId", m.getValue());
                value.add(valueJson);
                customfieldJson.put("value", value);
            }
            if (!customfieldJson.isEmpty()) {
                customfieldArr.add(customfieldJson);
            }
        }
        return customfieldArr;
    }

    private String getApplyPeopleName(JSONObject ctrlObject) {
        String applyPeopleID = ctrlObject.getString("applyPeople");
        new BaseBean().writeLog("AutoCreateTbTaskESB:getApplyPeopleName:START:PeoPleId:" + applyPeopleID);
        String applyPeople = null;
        RecordSet rsApplyPeople = new RecordSet();
        String applyPeopleSql = "select lastname from hrmresource where id = ?";
        rsApplyPeople.executeQuery(applyPeopleSql, applyPeopleID);
        if (rsApplyPeople.next()) {
            applyPeople = rsApplyPeople.getString("lastname");
        }
        new BaseBean().writeLog("AutoCreateTbTaskESB:getApplyPeopleName:END:PeopleName:" + applyPeople);
        return applyPeople;
    }

    private String getDepartmentName(JSONObject ctrlObject) {
        String taskApplyDepId = ctrlObject.getString("taskApplyDep");
        new BaseBean().writeLog("AutoCreateTbTaskESB:getDepartmentName:START:taskApplyDepId:" + taskApplyDepId);
        String taskApplyDep = null;
        RecordSet rsTaskApplyDep = new RecordSet();
        String sqlTaskApplyDep = "SELECT departmentname FROM HrmDepartment where id = ?";
        rsTaskApplyDep.executeQuery(sqlTaskApplyDep, taskApplyDepId);
        if (rsTaskApplyDep.next()) {
            taskApplyDep = rsTaskApplyDep.getString("departmentname");
        }
        new BaseBean().writeLog("AutoCreateTbTaskESB:getDepartmentName:END:DepName:" + taskApplyDep);
        return taskApplyDep;
    }
}

package yitouniu.esb;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.OMSUtils;

import java.util.*;

/**
 * @auther zhangcong
 */
public class CreateDiaoboESB {

    private final String api = "/api/sg/share/sa/transfer/oa";

    private final BaseBean baseBean = new BaseBean();

    private final RecordSet recordSet = new RecordSet();

    public Map executeParam(Map params) {

        Map<String, Object> map = new HashMap<>();

        if (CollectionUtil.isEmpty(params)) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "ESB参数为空");
            map.put("RESULT", "");
            map.put("PARAMS", params);
            return map;
        }

        baseBean.writeLog("CreateDiaoboESB接受参数：" + JSON.toJSONString(params));

        if (!checkParams(params)) {
            baseBean.writeLog("CreateDiaoboESB接受参数必填项为空:" + JSON.toJSONString(params));
            map.put("MSGTY", "F");
            map.put("MSAGE", "参数必填项为空");
            map.put("RESULT", "");
            map.put("PARAMS", params);
            return map;
        }

        if (!checkSku(params)) {
            baseBean.writeLog("CreateDiaoboESB接受参数sku存在重复:" + JSON.toJSONString(params));
            map.put("MSGTY", "F");
            map.put("MSAGE", "sku存在重复");
            map.put("RESULT", "");
            map.put("PARAMS", params);
            return map;
        }

        JSONObject jsonObject = new JSONObject(params);
        JSONArray objects = new JSONArray();
        JSONArray items = jsonObject.getJSONArray("ITEMS");
        for (int i = 0; i < items.size(); i++) {
            objects.add(items.getJSONObject(i));
        }
        jsonObject.put("ITEMS", objects);
        // 获取token
        OMSUtils omsUtils = new OMSUtils();
        String token = omsUtils.getToken();

        baseBean.writeLog("CreateDiaoboESB请求OMS转化后参数:" + jsonObject.toJSONString());
        String result = HttpRequest
                .post(OMSUtils.OMS_OMS_URL + api)
                .header("r3-api-token", token)
                .body(jsonObject.toJSONString())
                .execute()
                .body();

        baseBean.writeLog("CreateDiaoboESB请求OMS返回结果:" + result);

        if (StringUtils.isBlank(result)) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "请求OMS返回结果为空");
            map.put("RESULT", "");
            map.put("PARAMS", params);
            return map;
        }

        JSONObject resultJson;
        try {
            resultJson = JSON.parseObject(result);
        }
        catch (Exception e) {
            baseBean.writeLog("CreateDiaoboESB解析返回结果异常:" + Throwables.getStackTraceAsString(e));
            map.put("MSGTY", "F");
            map.put("MSAGE", "解析返回结果异常");
            map.put("RESULT", result);
            map.put("PARAMS", params);
            return map;
        }
        if (resultJson.getBoolean("oK")) {
            map.put("MSGTY", "S");
        }
        else {
            map.put("MSGTY", "F");
        }
        map.put("MSAGE", resultJson.getString("message"));
        map.put("RESULT", result);
        map.put("PARAMS", params);

        try {
//            String sql = "update formtable_main_754 set omszxqk = '" + resultJson.getString("message") + "' where requestid = " + params.get("REQUESTID"); // 测试环境
            String sql = "update formtable_main_809 set omszxqk = '" + resultJson.getString("message") + "' where requestid = " + params.get("REQUESTID"); // 正式环境
            baseBean.writeLog("CreateDiaoboESB更新返回信息sql=" + sql);
            boolean b = recordSet.executeUpdate(sql);
            baseBean.writeLog("CreateDiaoboESB更新返回信息requestid=" + params.get("REQUESTID") + "结果=" + b);
        }
        catch (Exception e) {
            baseBean.writeLog("CreateDiaoboESB更新返回信息requestid=" + params.get("REQUESTID") + "结果异常=" + Throwables.getStackTraceAsString(e));
        }

        return map;
    }

    private boolean checkSku(Map params) {
        JSONObject jsonObject = new JSONObject(params);
        JSONArray items = jsonObject.getJSONArray("ITEMS");
        Set<String> keySet = new HashSet<>();
        for (int i = 0; i < items.size(); i++) {
            JSONObject item = items.getJSONObject(i);
            keySet.add(item.getString("PS_C_SKU_ECODE"));
        }
        return keySet.size() == items.size();
    }

    private boolean checkParams(Map params) {
        if (Objects.isNull(params.get("OA_PROCESS_NO"))
                || Objects.isNull(params.get("OA_USER_NO"))
                || Objects.isNull(params.get("RECEIVER_SA_STORE_ECODE"))
                || Objects.isNull(params.get("SENDER_SA_STORE_ECODE"))) {
            return false;
        }

        JSONObject jsonObject = new JSONObject(params);
        JSONArray items = jsonObject.getJSONArray("ITEMS");
        return !Objects.isNull(items) && items.size() != 0;
    }

}

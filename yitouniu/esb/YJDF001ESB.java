package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.google.common.collect.Sets;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.workflow.webservices.WorkflowBaseInfo;
import weaver.workflow.webservices.WorkflowMainTableInfo;
import weaver.workflow.webservices.WorkflowRequestInfo;
import weaver.workflow.webservices.WorkflowRequestTableField;
import weaver.workflow.webservices.WorkflowRequestTableRecord;
import weaver.workflow.webservices.WorkflowServiceImpl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 一件代发信贷超期预期提醒工单创建
 *
 * <AUTHOR>
 * @since 2023-07-14 16:15
 */
public class YJDF001ESB {
    public Map execute(Map<String, Object> params) {
        YJDF001ESB esb = new YJDF001ESB();
        return esb.executeParam(JSON.toJSONString(params));
    }

    private final Log LOG = LogFactory.getLog(YJDF001ESB.class);
    private static final Set<String> ILLEGAL_STATUS = Sets.newHashSet("4", "5", "6", "7");

    /**
     * 测试环境
     */
//    private static final String WORK_FLOW_ID = "992";
    /**
     * 生产环境
     */
    private static final String WORK_FLOW_ID = "1412";
    private static final String ZZBM_ID = "129";

    public Map<String, String> executeParam(String params) {
        LOG.info("一件代发信贷超期预期提醒工单创建-开始，请求入参：" + params);
        JSONObject jsonParam = JSONObject.parseObject(params);

        Map<String, String> retMap = new HashMap<>();
        String reqId;
        try {
            JSONObject dpMap = buildParamByDpbm(jsonParam);
            LOG.info("一件代发信贷超期预期提醒工单创建-查询映射结果，映射结果：" + dpMap);

            reqId = createWorkflow(jsonParam, dpMap);
        } catch (Exception e) {
            LOG.error("一件代发信贷超期预期提醒工单创建-异常,入参：" + jsonParam.toJSONString(), e);
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", e.getMessage());
            /*retMap.put("RESULT", "");*/
            return retMap;
        }

        retMap.put("MSGTY", "S");
        retMap.put("MSAGE", "处理数据成功");
        retMap.put("RESULT", reqId);

        LOG.info("一件代发信贷超期预期提醒工单创建-完成，结果：{}" + JSON.toJSONString(retMap));
        return retMap;
    }

    private String createWorkflow(JSONObject jsonParam, JSONObject dpMap) {
        /*工作流程请求信息*/
        WorkflowRequestInfo workflowRequestInfo = new WorkflowRequestInfo();
        //创建人id
        workflowRequestInfo.setCreatorId(dpMap.getString("xsy"));
        //0：停留在创建节点，1：流转到下一个节点
        workflowRequestInfo.setIsnextflow("0");
        //0 正常，1重要，2紧急
        workflowRequestInfo.setRequestLevel("0");
        //显示
        workflowRequestInfo.setCanView(true);
        //可编辑
        workflowRequestInfo.setCanEdit(true);
        //流程标题
        workflowRequestInfo.setRequestName("一件代发店铺信贷超期-" + jsonParam.getString("NAME1"));

        /*工作流信息*/
        WorkflowBaseInfo workflowBaseInfo = new WorkflowBaseInfo();
        workflowBaseInfo.setWorkflowId(WORK_FLOW_ID);
        /*workflowBaseInfo.setWorkflowName("一件代发店铺信贷超期");*/

        workflowRequestInfo.setWorkflowBaseInfo(workflowBaseInfo);

        /*主表*/
        WorkflowMainTableInfo workflowMainTableInfo = new WorkflowMainTableInfo();
        //主表字段只有一条记录
        WorkflowRequestTableRecord[] workflowRequestTableRecord = new WorkflowRequestTableRecord[1];

        // 主表字段
        WorkflowRequestTableField[] workflowRequestTableField = new WorkflowRequestTableField[11];

        // 标题
        workflowRequestTableField[0] = new WorkflowRequestTableField();
        workflowRequestTableField[0].setFieldName("bt");
        workflowRequestTableField[0].setFieldValue("一件代发店铺信贷超期-" + jsonParam.getString("NAME1"));
        workflowRequestTableField[0].setView(true);
        workflowRequestTableField[0].setEdit(true);

        // 申请人
        workflowRequestTableField[1] = new WorkflowRequestTableField();
        workflowRequestTableField[1].setFieldName("sqr");
        workflowRequestTableField[1].setFieldValue(dpMap.getString("xsy"));
        workflowRequestTableField[1].setView(true);
        workflowRequestTableField[1].setEdit(true);

        // 店铺编码
        workflowRequestTableField[2] = new WorkflowRequestTableField();
        workflowRequestTableField[2].setFieldName("dpbm");
        workflowRequestTableField[2].setFieldValue(dpMap.getString("dpbm"));
        workflowRequestTableField[2].setView(true);
        workflowRequestTableField[2].setEdit(true);

        // 店铺名称
        workflowRequestTableField[3] = new WorkflowRequestTableField();
        workflowRequestTableField[3].setFieldName("dpmc");
        workflowRequestTableField[3].setFieldValue(dpMap.getString("dpxx"));
        workflowRequestTableField[3].setView(true);
        workflowRequestTableField[3].setEdit(true);

        // 销售员
        workflowRequestTableField[4] = new WorkflowRequestTableField();
        workflowRequestTableField[4].setFieldName("xsy");
        workflowRequestTableField[4].setFieldValue(dpMap.getString("xsy"));
        workflowRequestTableField[4].setView(true);
        workflowRequestTableField[4].setEdit(true);
        // 提案部门=全员
        workflowRequestTableField[5] = new WorkflowRequestTableField();
        workflowRequestTableField[5].setFieldName("tabm");
        workflowRequestTableField[5].setFieldValue("全员");
        workflowRequestTableField[5].setView(true);
        workflowRequestTableField[5].setEdit(true);
        // 提案职级=全员
        workflowRequestTableField[6] = new WorkflowRequestTableField();
        workflowRequestTableField[6].setFieldName("tazj");
        workflowRequestTableField[6].setFieldValue("全员");
        workflowRequestTableField[6].setView(true);
        workflowRequestTableField[6].setEdit(true);
        // 职责部门=财务管理部
        workflowRequestTableField[7] = new WorkflowRequestTableField();
        workflowRequestTableField[7].setFieldName("zzbm");
        workflowRequestTableField[7].setFieldValue(ZZBM_ID);
        workflowRequestTableField[7].setView(true);
        workflowRequestTableField[7].setEdit(true);
        // 责任人及联系方式=朱劝17757129923
        workflowRequestTableField[8] = new WorkflowRequestTableField();
        workflowRequestTableField[8].setFieldName("zrrjlxfs");
        workflowRequestTableField[8].setFieldValue("朱劝17757129923");
        workflowRequestTableField[8].setView(true);
        workflowRequestTableField[8].setEdit(true);
        // 业务场景=适用于非自营店铺的一件代发信贷超期系统提醒
        workflowRequestTableField[9] = new WorkflowRequestTableField();
        workflowRequestTableField[9].setFieldName("ywcj");
        workflowRequestTableField[9].setFieldValue("适用于非自营店铺的一件代发信贷超期系统提醒");
        workflowRequestTableField[9].setView(true);
        workflowRequestTableField[9].setEdit(true);

        // 申请时间
        workflowRequestTableField[10] = new WorkflowRequestTableField();
        workflowRequestTableField[10].setFieldName("sqsj");
        workflowRequestTableField[10].setFieldValue(DateFormatUtils.format(new Date(), "yyyy-MM-dd"));
        workflowRequestTableField[10].setView(true);
        workflowRequestTableField[10].setEdit(true);


        /*添加主表字段*/
        workflowRequestTableRecord[0] = new WorkflowRequestTableRecord();
        workflowRequestTableRecord[0].setWorkflowRequestTableFields(workflowRequestTableField);
        workflowMainTableInfo.setRequestRecords(workflowRequestTableRecord);

        //添加主字段数据
        workflowRequestInfo.setWorkflowMainTableInfo(workflowMainTableInfo);

        LOG.info("一件代发信贷超期预期提醒工单-调用创建接口，入参：" + JSON.toJSONString(workflowRequestInfo));
        WorkflowServiceImpl workflowService = new WorkflowServiceImpl();
        return Util.null2String(workflowService.doCreateWorkflowRequest(workflowRequestInfo, Integer.parseInt(dpMap.getString("xsy"))));
    }

    private JSONObject buildParamByDpbm(JSONObject jsonParam) {
        String dpbm = jsonParam.getString("KUNNR");
        if (StringUtils.isEmpty(dpbm)) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，店铺编码未传，请求入参：" + jsonParam.toJSONString());
            throw new RuntimeException("店铺与销售员映射不存在");
        }

        RecordSet rs = new RecordSet();
        rs.executeQuery("SELECT id,dpxx,dpbm,xsy FROM uf_yjdfnfsyb WHERE dpbm= ? ", dpbm);
        JSONObject retMap = new JSONObject();
        boolean next = rs.next();
        if (!next || StringUtils.isEmpty(rs.getString("xsy"))) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，店铺与销售员映射不存在，请求入参：" + dpbm);
            throw new RuntimeException("店铺与销售员映射不存在");
        }

        retMap.put("id", rs.getString("id"));
        retMap.put("dpxx", rs.getString("dpxx"));
        retMap.put("dpbm", rs.getString("dpbm"));
        retMap.put("xsy", queryXsyOrManager(rs.getString("xsy")));
        return retMap;
    }


    private String queryXsyOrManager(String xsyId) {
        RecordSet rs = new RecordSet();
        rs.executeQuery("SELECT id,status,managerid FROM HrmResource WHERE id= ? ", xsyId);
        if (!rs.next()) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，销售员不存在，销售员ID：" + xsyId);
            throw new RuntimeException("销售员不存在");
        }
        if (!ILLEGAL_STATUS.contains(rs.getString("status"))) {
            return xsyId;
        }

        String managerId = rs.getString("managerid");
        if (StringUtils.isEmpty(managerId)) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，销售员离职且无对应上级，销售员ID：" + xsyId);
            throw new RuntimeException("销售员离职且无对应上级");
        }

        RecordSet managerRs = new RecordSet();
        managerRs.executeQuery("SELECT id,status FROM HrmResource WHERE id= ? ", managerId);
        if (!managerRs.next()) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，销售员离职且未找到上级，销售员ID：" + xsyId + "，上级ID" + managerId);
            throw new RuntimeException("销售员离职且未找到上级");
        }

        if (ILLEGAL_STATUS.contains(managerRs.getString("status"))) {
            LOG.warn("一件代发信贷超期预期提醒工单创建-失败，销售员与直接上级均离职，销售员ID：" + xsyId + "，上级ID" + managerId);
            throw new RuntimeException("销售员与直接上级均离职");
        }

        return managerId;
    }


}

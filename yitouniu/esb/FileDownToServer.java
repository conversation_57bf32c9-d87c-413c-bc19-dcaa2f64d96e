package yitouniu.esb;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 11:54 2021/9/3
 * @Modified By:
 */
public class FileDownToServer {

    public Map executeParam(Map params) {



//        final String ProcessDatabase ="formtable_main_29";
        final String ProcessDatabase =(String)params.get("dataBase");
        new BaseBean().writeLog("FileDownToServer测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        Map<String, Object> result = new HashMap<>();
        RecordSet recordSet = new RecordSet();
        String sqlFj = "SELECT fj FROM "+ProcessDatabase;
        recordSet.executeQuery(sqlFj);
        List<String> FjList = new ArrayList<>();
        while (recordSet.next()){
            String[] arr = recordSet.getString("fj").split(",");
            FjList.addAll(Arrays.asList(arr));
        }
//        String descDir = "D:\\ProcessFileDown\\"+ProcessDatabase+"\\";
        String descDir = "D:\\oa\\oa附件流程文件"+ProcessDatabase+"\\";
        File pathFile = new File(descDir);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        try{
            for(String str : FjList) {
                if (str.length() > 0) {
                    RecordSet rd = new RecordSet();
                    String sql = " SELECT ImgF.filerealpath,ImgF.imagefilename" +
                            " FROM imagefile ImgF " +
                            " LEFT JOIN docimagefile DIF ON DIF.imagefileid = ImgF.imagefileid " +
                            " WHERE DIF.docid = ? ";
                    rd.executeQuery(sql, str);
                    if (rd.next()) {
                        new BaseBean().writeLog("开始转换附件或图片：" + str);
                        File zipFile = new File(rd.getString("filerealpath"));
                        String realFileName = rd.getString("imagefilename");

                        if(zipFile.exists()){
                            ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
                            for (Enumeration<? extends ZipEntry> enumeration = zip.entries(); enumeration.hasMoreElements(); ) {
                                ZipEntry zipEntry = enumeration.nextElement();
                                String zipEntryName = zipEntry.getName();
                                new BaseBean().writeLog("打印zipEntryName：" + zipEntryName);
                                InputStream in = zip.getInputStream(zipEntry);
                                String outPath = descDir;
                                new BaseBean().writeLog("打印outPath：" + outPath);

                                String fileRealPath = descDir + realFileName;
                                FileOutputStream out = new FileOutputStream(new File(fileRealPath));
                                byte[] buf = new byte[1024];
                                int len;
                                while ((len = in.read(buf)) > 0) {
                                    out.write(buf, 0, len);
                                }
                                in.close();
                                out.close();
                            }
                        }
                        new BaseBean().writeLog("解压转换完毕");
                    }
                }
            }
            if(FjList.size()!=0) {
                result.put("msg", "1");
                result.put("list",FjList);
            }
        }catch (Exception e){
            result.put("msg","0");
            result.put("list",FjList);
        }
        return result;
    }
}

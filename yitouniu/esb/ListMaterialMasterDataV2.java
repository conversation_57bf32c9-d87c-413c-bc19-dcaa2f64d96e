package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import org.apache.commons.collections4.CollectionUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.SAPUtil;

import java.util.*;

/**
 * @Author: Moss
 * @Description: SAP物料主数据拉取接口
 * @Date: Create in 18:13 2021/9/9
 * @Modified By:
 */
public class ListMaterialMasterDataV2 {
    public Map executeParam(Map params) {

        Map map = new HashMap();
        String paramsString = JSON.toJSONString(params);
        try {

            String execute = SAPUtil.execute(paramsString);
            JSONObject jsonObject = JSONObject.parseObject(execute);

            //用来判断插入和更新数据是否成功
            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();

            //
            JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
            List<JSONObject> dataListObject = (List<JSONObject>) jsonObject.get("DATA");
            String msgType = ctrlObject.getString("MSGTY");
            if("S".equals(msgType)){
                new BaseBean().writeLog("ListMaterialMasterData本次拉取物料数据的数量为="+dataListObject.size());
                for(JSONObject obj : dataListObject){

                    List dataList = new ArrayList<>();
                    List insertData =  new ArrayList<>();
                    List updateData =  new ArrayList<>();

                    String MATNR = obj.getString("MATNR");
                    String MAKTX = obj.getString("MAKTX");
                    String WERKS = obj.getString("WERKS");
                    String NAME1 = obj.getString("NAME1");
                    String MEINS = obj.getString("MEINS");
                    String MTART = obj.getString("MTART");
                    String MATKL = obj.getString("MATKL");
                    String SPART = obj.getString("SPART");
                    String BRGEW = obj.getString("BRGEW");
                    String NTGEW = obj.getString("NTGEW");
                    String GEWEI = obj.getString("GEWEI");
                    String BCODE = obj.getString("BCODE");
                    String BRAND = obj.getString("BRAND");
                    String BRNAM = obj.getString("BRNAM");
                    String MODEL = obj.getString("MODEL");
                    String BOXNO = obj.getString("BOXNO");
                    String TASTE = obj.getString("TASTE");
                    String PUPSE = obj.getString("PUPSE");
                    String ZLENGTH = obj.getString("ZLENGTH");
                    String ZWIDTH = obj.getString("ZWIDTH");
                    String ZHIGH = obj.getString("ZHIGH");
                    String ZVOLUME = obj.getString("ZVOLUME");
                    String ZMATNR_JLB = obj.getString("ZMATNR_JLB");
                    String ZMAKTX_JLB = obj.getString("ZMAKTX_JLB");
                    String VNDOR = obj.getString("VNDOR");
                    String VNDNM = obj.getString("VNDNM");
                    String ZBZXQ = obj.getString("ZBZXQ");
                    String ZDBZL = obj.getString("ZDBZL");
                    String ZGBXQ = obj.getString("ZGBXQ");
                    String ZIQC = obj.getString("ZIQC");
                    String PRMOD = obj.getString("PRMOD");

                    //这两个字段保留但是没值，再说
                    String MEINH = obj.getString("MEINH");
                    String UMREN = obj.getString("UMREN");

                    String VRKME = obj.getString("VRKME");
                    String TAXM1 = obj.getString("TAXM1");
                    String KTGRM = obj.getString("KTGRM");
                    String MVGR1 = obj.getString("MVGR1");
                    String EKGRP = obj.getString("EKGRP");
                    String PLIFZ = obj.getString("PLIFZ");
                    String LGFSB = obj.getString("LGFSB");
                    String BSTME = obj.getString("BSTME");
                    String ZCGRK = obj.getString("ZCGRK");
                    String ZXSRK = obj.getString("ZXSRK");
                    String ZZKJ = obj.getString("ZZKJ");
                    String ZSCRK = obj.getString("ZSCRK");
                    String ZYZDJ = obj.getString("ZYZDJ");
                    String MAXLZ = obj.getString("MAXLZ");
                    String MHDRZ = obj.getString("MHDRZ");
                    String INSMK = obj.getString("INSMK");
                    String XCHPF = obj.getString("XCHPF");
                    String DISPO = obj.getString("DISPO");
                    String MINBE = obj.getString("MINBE");
                    String BSTMI = obj.getString("BSTMI");
                    String BSTMA = obj.getString("BSTMA");
                    String MABST = obj.getString("MABST");
                    String BSTRF = obj.getString("BSTRF");
                    String EISBE = obj.getString("EISBE");
                    String BKLAS = obj.getString("BKLAS");

                    String VPRSV = obj.getString("VPRSV");
                    String STPRS = obj.getString("STPRS");
                    String PEINH = obj.getString("PEINH");
                    String UEBTO = obj.getString("UEBTO");
                    String UNTTO = obj.getString("UNTTO");
                    String MSTAE = obj.getString("MSTAE");
                    //创建日期
                    String ERSDA = obj.getString("ERSDA");
                    String CREATED_AT_TIME = obj.getString("CREATED_AT_TIME");
                    String ERNAM = obj.getString("ERNAM");
                    //最后更改日期
                    String LAEDA = obj.getString("LAEDA");
                    String AENAM = obj.getString("AENAM");

                    String BUKRS = obj.getString("BUKRS");
                    String VKORG = obj.getString("VKORG");

                    //2022-10-17增加
                    //分类化视图
                    //基价下限
                    String ZJJXX = obj.getString("ZJJXX");
                    //基价上限
                    String ZJJSX = obj.getString("ZJJSX");
                    //一级分类
                    String ZYJFL = obj.getString("ZYJFL");
                    //二级分类
                    String ZEJFL = obj.getString("ZEJFL");
                    //三级分类
                    String ZSJFL = obj.getString("ZSJFL");
                    //成品四级-财务标记
                    String ZCPSJ_CW = obj.getString("ZCPSJ_CW");
                    //成品简称-财务
                    String ZCPJC_CW = obj.getString("ZCPJC_CW");
                    //成品四级-计划标记
                    String ZCPSJ_JH = obj.getString("ZCPSJ_JH");
                    //成品简称-计划
                    String ZCPJS_JH = obj.getString("ZCPJS_JH");
                    //税收分类
                    String ZSSFL = obj.getString("ZSSFL");
                    //单位
                    String ZCW_UNIT = obj.getString("ZCW_UNIT");
                    //入
                    String ZGUIGE = obj.getString("ZGUIGE");
                    //提
                    String ZGUIGE2 = obj.getString("ZGUIGE2");

                    //四级分类
                    String ZZSJFL = obj.getString("ZZSJFL");

                    //各个选项框ID组
                    String SPARTID;
                    String MTARTID;
                    String ZBZXQID;
                    String ZGBXQID;
                    String ZIQCID;
                    String PRMODID;
                    String KTGRMID;
                    String MVGR1ID;
                    String ZCGRKID;
                    String ZXSRKID;
                    String ZZKJID;
                    String ZSCRKID;
                    String ZYZDJID;
                    String INSMKID;
                    String XCHPFID;
                    String DISPOID;
                    String VPRSVID;

                    switch(SPART){
                        case "00": SPARTID = "0";
                            break;
                        case "10": SPARTID = "1";
                            break;
                        case "11": SPARTID = "2";
                            break;
                        case "12": SPARTID = "3";
                            break;
                        case "13": SPARTID = "4";
                            break;
                        case "50": SPARTID = "5";
                            break;
                        case "51": SPARTID = "6";
                            break;
                        case "52": SPARTID = "7";
                            break;
                        default: SPARTID = "";
                            break;
                    }

                    switch (MTART){
                        case "ZERT": MTARTID = "0";
                            break;
                        case "ZROH": MTARTID = "1";
                            break;
                        case "ZERP": MTARTID = "2";
                            break;
                        case "ZWZC": MTARTID = "3";
                            break;
                        case "ZRSA": MTARTID = "4";
                            break;
                        case "ZHDP": MTARTID = "5";
                            break;
                        case "ZERV": MTARTID = "6";
                            break;
                        default: MTARTID = "";
                            break;
                    }

                    if("X".equals(ZBZXQ)){
                        ZBZXQID = "0";
                    } else {
                        ZBZXQID = "1";
                    }

                    if("X".equals(ZGBXQ)){
                        ZGBXQID = "0";
                    } else {
                        ZGBXQID = "1";
                    }

                    if("X".equals(ZIQC)){
                        ZIQCID = "0";
                    } else {
                        ZIQCID = "1";
                    }

                    switch(PRMOD){
                        case "01": PRMODID = "0";
                            break;
                        case "02": PRMODID = "1";
                            break;
                        default: PRMODID = "";
                            break;
                    }

                    switch(KTGRM){
                        case "10": KTGRMID = "0";
                            break;
                        case "20": KTGRMID = "1";
                            break;
                        case "30": KTGRMID = "2";
                            break;
                        case "40": KTGRMID = "3";
                            break;
                        case "50": KTGRMID = "4";
                            break;
                        default: KTGRMID = "";
                            break;
                    }

                    switch (MVGR1){
                        case "001": MVGR1ID = "8";
                            break;
                        case "002": MVGR1ID = "0";
                            break;
                        case "003": MVGR1ID = "1";
                            break;
                        case "004": MVGR1ID = "2";
                            break;
                        case "005": MVGR1ID = "3";
                            break;
                        case "006": MVGR1ID = "4";
                            break;
                        case "007": MVGR1ID = "5";
                            break;
                        case "008": MVGR1ID = "6";
                            break;
                        case "009": MVGR1ID = "7";
                            break;
                        default: MVGR1ID = "";
                            break;
                    }

                    if("X".equals(ZCGRK)){
                        ZCGRKID = "0";
                    } else {
                        ZCGRKID = "1";
                    }
                    if("X".equals(ZXSRK)){
                        ZXSRKID = "0";
                    } else {
                        ZXSRKID = "1";
                    }
                    if("X".equals(ZZKJ)){
                        ZZKJID = "0";
                    } else {
                        ZZKJID = "1";
                    }
                    if("X".equals(ZSCRK)){
                        ZSCRKID = "0";
                    } else {
                        ZSCRKID = "1";
                    }
                    if("X".equals(ZYZDJ)){
                        ZYZDJID = "0";
                    } else {
                        ZYZDJID = "1";
                    }
                    if("X".equals(INSMK)){
                        INSMKID = "0";
                    } else {
                        INSMKID = "1";
                    }
                    if("X".equals(XCHPF)){
                        XCHPFID = "0";
                    } else {
                        XCHPFID = "1";
                    }

                    switch(DISPO){
                        case "M01": DISPOID = "0";
                            break;
                        case "M02": DISPOID = "1";
                            break;
                        case "M03": DISPOID = "2";
                            break;
                        case "M04": DISPOID = "3";
                            break;
                        case "M05": DISPOID = "4";
                            break;
                        case "M06": DISPOID = "5";
                            break;
                        case "M07": DISPOID = "6";
                            break;
                        case "M08": DISPOID = "7";
                            break;
                        case "P01": DISPOID = "8";
                            break;
                        default: DISPOID = "";
                            break;
                    }

                    if("S".equals(VPRSV)){
                        VPRSVID = "0";
                    } else {
                        VPRSVID = "1";
                    }


                    //物料编号+工厂编号确定数据是否存在
//                    dataList.add(MATNR);
                    dataList.add(MAKTX);
//                    dataList.add(WERKS);
                    dataList.add(NAME1);
                    dataList.add(MEINS);
                    dataList.add(MTART);
                    dataList.add(MATKL);
                    dataList.add(SPART);
                    dataList.add(BRGEW);
                    dataList.add(NTGEW);
                    dataList.add(GEWEI);
                    dataList.add(BCODE);
                    dataList.add(BRAND);
                    dataList.add(BRNAM);
                    dataList.add(MODEL);
                    dataList.add(BOXNO);
                    dataList.add(TASTE);
                    dataList.add(PUPSE);
                    dataList.add(ZLENGTH);
                    dataList.add(ZWIDTH);
                    dataList.add(ZHIGH);
                    dataList.add(ZVOLUME);
                    dataList.add(ZMATNR_JLB);
                    dataList.add(ZMAKTX_JLB);
                    dataList.add(VNDOR);
                    dataList.add(VNDNM);
                    dataList.add(ZBZXQ);
                    dataList.add(ZDBZL);
                    dataList.add(ZGBXQ);
                    dataList.add(ZIQC);
                    dataList.add(PRMOD);
                    dataList.add(MEINH);
                    dataList.add(UMREN);
                    dataList.add(VRKME);
                    dataList.add(TAXM1);
                    dataList.add(KTGRM);
                    dataList.add(MVGR1);
                    dataList.add(EKGRP);
                    dataList.add(PLIFZ);
                    dataList.add(LGFSB);
                    dataList.add(BSTME);
                    dataList.add(ZCGRK);
                    dataList.add(ZXSRK);
                    dataList.add(ZZKJ);
                    dataList.add(ZSCRK);
                    dataList.add(ZYZDJ);
                    dataList.add(MAXLZ);
                    dataList.add(MHDRZ);
                    dataList.add(INSMK);
                    dataList.add(XCHPF);
                    dataList.add(DISPO);
                    dataList.add(MINBE);
                    dataList.add(BSTMI);
                    dataList.add(BSTMA);
                    dataList.add(MABST);
                    dataList.add(BSTRF);
                    dataList.add(EISBE);
                    dataList.add(BKLAS);
                    dataList.add(VPRSV);
                    dataList.add(STPRS);
                    dataList.add(PEINH);
                    dataList.add(UEBTO);
                    dataList.add(UNTTO);
                    dataList.add(MSTAE);
                    dataList.add(ERSDA);
                    dataList.add(CREATED_AT_TIME);
                    dataList.add(ERNAM);
                    dataList.add(LAEDA);
                    dataList.add(AENAM);
                    dataList.add(BUKRS);
                    dataList.add(VKORG);
                    dataList.add(ZJJXX);
                    dataList.add(ZJJSX);
                    dataList.add(ZYJFL);
                    dataList.add(ZEJFL);
                    dataList.add(ZSJFL);
                    dataList.add(ZCPSJ_CW);
                    dataList.add(ZCPJC_CW);
                    dataList.add(ZCPSJ_JH);
                    dataList.add(ZCPJS_JH);
                    dataList.add(ZSSFL);
                    dataList.add(ZCW_UNIT);
                    dataList.add(ZGUIGE);
                    dataList.add(ZGUIGE2);
                    dataList.add(ZZSJFL);
                    //各个选项框ID组
                    dataList.add(SPARTID);
                    dataList.add(MTARTID);
                    dataList.add(ZBZXQID);
                    dataList.add(ZGBXQID);
                    dataList.add(ZIQCID);
                    dataList.add(PRMODID);
                    dataList.add(KTGRMID);
                    dataList.add(MVGR1ID);
                    dataList.add(ZCGRKID);
                    dataList.add(ZXSRKID);
                    dataList.add(ZZKJID);
                    dataList.add(ZSCRKID);
                    dataList.add(ZYZDJID);
                    dataList.add(INSMKID);
                    dataList.add(XCHPFID);
                    dataList.add(DISPOID);
                    dataList.add(VPRSVID);


                    int id = selectWLZSJ(MATNR, WERKS);
                    if(id > 0){
                        CollectionUtils.addAll(updateData,new Object[dataList.size()]);
                        Collections.copy(updateData,dataList);
                        updateData.add(id);
                        updateAllData.add(updateData);
                    } else {
                        CollectionUtils.addAll(insertData,new Object[dataList.size()]);
                        Collections.copy(insertData,dataList);
                        insertData.add(0,MATNR);
                        insertData.add(2,WERKS);
                        insertAllData.add(insertData);
                    }
                }

                if(insertAllData.size()>0){
                    new BaseBean().writeLog("ListMaterialMasterData: insertAllData新增：");
                    flag += insertWLZSJ(insertAllData) + ",";
                }

                if(updateAllData.size()>0){
                    new BaseBean().writeLog("ListMaterialMasterData: updateAllData更新：");
                    flag += updateWLZJS(updateAllData) + ",";
                }

                if (flag.contains("false")) {
                    map.put("MSGTY","F");
                    map.put("MSAGE","处理数据失败");
                    map.put("RESULT",execute);
                    map.put("PARAMS",params);
                }else{
                    map.put("MSGTY","S");
                    map.put("MSAGE","处理数据成功");
                    map.put("RESULT",execute);
                    map.put("PARAMS",params);
                }
            } else {
                map.put("MSGTY","F");
                map.put("MSAGE","读取失败");
                map.put("RESULT",execute);
                map.put("PARAMS",params);
            }
        }catch (Exception e){
            new BaseBean().writeLog("ListMaterialMasterData异常：" + Throwables.getStackTraceAsString(e));
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
        }
        return map;
    }


    public int selectWLZSJ(String MATNR, String WERKS){
        RecordSet rs = new RecordSet();
        int id = -1;
        rs.executeQuery("select id from uf_KCXGWLZSJSY where MATNR =? and WERKS =? ", MATNR, WERKS);
        if(rs.next()){
            id = rs.getInt("id");
        }
        new BaseBean().writeLog("selectWLZSJ:id = "+id);
        return id;
    }

    public boolean updateWLZJS(List<List> list){
        RecordSet rs = new RecordSet();
        String sql = "update uf_KCXGWLZSJSY set MAKTX =?, NAME1 =?, MEINS =?, MTART = ?, MATKL =?, SPART =?, BRGEW =?, NTGEW =?, GEWEI =?, BCODE =?" +
                ", BRAND =?, BRNAM =?, MODEL =?, BOXNO =?, TASTE =?, PUPSE =?, ZLENGTH =?, ZWIDTH =?, ZHIGH =?, ZVOLUME =?" +
                ", ZMATNR_JLB =?, ZMAKTX_JLB =?, VNDOR =?, VNDNM=?, ZBZXQ =?, ZDBZL =?, ZGBXQ =?, ZIQC =?, PRMOD =?, MEINH =?" +
                ", UMREN =?, VRKME =?, TAXM1 =?, KTGRM =?, MVGR1 =?, EKGRP =?, PLIFZ =?, LGFSB =?, BSTME =?, ZCGRK =?" +
                ", ZXSRK =?, ZZKJ =?, ZSCRK =?, ZYZDJ =?, MAXLZ =?, MHDRZ =?, INSMK =?, XCHPF =?, DISPO =?, MINBE =?" +
                ", BSTMI =?, BSTMA =?, MABST =?, BSTRF =?, EISBE =?, BKLAS =?, VPRSV =?, STPRS =?, PEINH =?, UEBTO =?" +
                ", UNTTO =?, MSTAE =?, ERSDA =?, CREATED_AT_TIME =?, ERNAM =?, LAEDA =?, AENAM =?, BUKRS =?, VKORG =?,ZJJXX =?" +
                ", ZJJSX =?,ZYJFL =?,ZEJFL =?,ZSJFL =?,ZCPSJ_CW =?,ZCPJC_CW =?, ZCPSJ_JH =?,ZCPJS_JH =?,ZSSFL =?,ZCW_UNIT =?" +
                ",ZGUIGE =?, ZGUIGE2 =?, ZZSJFL=?, SPARTID =?, MTARTID =?, ZBZXQID =?, ZGBXQID =?, ZIQCID =?, PRMODID =?, KTGRMID =?, MVGR1ID =?" +
                ", ZCGRKID =?, ZXSRKID =?, ZZKJID =?, ZSCRKID =?, ZYZDJID =?, INSMKID =?, XCHPFID =?, DISPOID =?, VPRSVID =?" +
                " where id = ?";
        return rs.executeBatchSql(sql,list);
    }

    public boolean insertWLZSJ(List<List> list){
        RecordSet rs = new RecordSet();
        String sql = "insert into uf_KCXGWLZSJSY (MATNR, MAKTX, WERKS, NAME1,MEINS, MTART, MATKL, SPART, BRGEW, NTGEW" +
                ", GEWEI, BCODE, BRAND, BRNAM, MODEL, BOXNO, TASTE, PUPSE, ZLENGTH, ZWIDTH" +
                ", ZHIGH, ZVOLUME, ZMATNR_JLB, ZMAKTX_JLB, VNDOR, VNDNM, ZBZXQ, ZDBZL, ZGBXQ, ZIQC" +
                ", PRMOD, MEINH, UMREN, VRKME, TAXM1, KTGRM, MVGR1, EKGRP, PLIFZ, LGFSB" +
                ", BSTME, ZCGRK, ZXSRK, ZZKJ, ZSCRK, ZYZDJ, MAXLZ, MHDRZ, INSMK, XCHPF" +
                ", DISPO, MINBE, BSTMI, BSTMA, MABST, BSTRF, EISBE, BKLAS, VPRSV, STPRS" +
                ", PEINH, UEBTO, UNTTO, MSTAE, ERSDA, CREATED_AT_TIME, ERNAM, LAEDA, AENAM, BUKRS" +
                ", VKORG,ZJJXX,ZJJSX,ZYJFL,ZEJFL,ZSJFL,ZCPSJ_CW,ZCPJC_CW,ZCPSJ_JH,ZCPJS_JH" +
                ", ZSSFL, ZCW_UNIT,ZGUIGE,ZGUIGE2, ZZSJFL, SPARTID, MTARTID, ZBZXQID, ZGBXQID, ZIQCID, PRMODID" +
                ", KTGRMID, MVGR1ID, ZCGRKID, ZXSRKID, ZZKJID, ZSCRKID, ZYZDJID, INSMKID, XCHPFID, DISPOID" +
                ", VPRSVID) " +
                " values (?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?,?,?,?,?,?,?,?,?" +
                ",?,?)";
        return rs.executeBatchSql(sql,list);
    }


}

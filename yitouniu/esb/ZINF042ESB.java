package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//员工主数据推送  by  依依
/**
 * @Author: Moss
 * @Description: 员工报销银行信息维护流程(作废)
 * @Modified By:v2.0
 */
public class ZINF042ESB extends ReturnMsgToSAP{   //不需要

    public Map executeParam(String params){
        new BaseBean().writeLog("员工报销银行信息维护流程测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        Map execute = new HashMap();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject CTRLObject = (JSONObject) jsonObject.get("CTRL");
        String requestid = CTRLObject.getString("INFID");



        if(jsonObject.containsKey("DATA")){
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");
            JSONArray dataArr = new JSONArray();
            for(int i= 0; i<dataList.size();i++){
                JSONObject dataJSON = new JSONObject();
                JSONObject object = dataList.get(i);

                /**
                 * 业务伙伴名称:NAME_ORG1
                 * 业务伙伴名称2:NAME_ORG2 (员工号)
                 * 公司代码:BUKRS
                 * 银行账户:BANKN   （前18位）
                 * 参考明细:BKREF    OA传输（第19位开始）
                 * 银行代码:BANKL
                 */
                dataJSON.put("NAME_ORG1",object.getString("NAME_ORG1"));
                dataJSON.put("NAME_ORG2",object.getString("NAME_ORG2"));
                dataJSON.put("BUKRS",object.getString("BUKRS"));
                String BANKN = object.getString("BANKN");
                int BANKLLength = BANKN.length();
                if(BANKLLength<=18){
                    dataJSON.put("BANKN",BANKN);
                    dataJSON.put("BKREF","");
                } else{
                    dataJSON.put("BANKN",BANKN.substring(0,18));
                    dataJSON.put("BKREF",BANKN.substring(18));
                }
                dataJSON.put("BANKL",object.getString("BANKL"));


                dataArr.add(dataJSON);
            }
            jsonObject.put("DATA",dataArr);
            execute = super.execute(jsonObject.toString());
            Object msgty = execute.get("MSGTY");
            if(!"S".equals(msgty)){
                return execute;   //失败
            }

        }

        if(execute.isEmpty()){
            execute.put("MSGTY","S");
            execute.put("MSAGE","成功");
            execute.put("RESULT",execute);
            execute.put("PARAMS",params);
        }
        //还需要接收返回信息
        String result = (String) execute.get("RESULT");
        JSONObject resultObject = JSONObject.parseObject(result);
        List<JSONObject> dataObject = (List<JSONObject>) resultObject.get("DATA");
        if (dataObject.size() > 0){
            for (JSONObject jsonObj : dataObject) {
                String KNA1_KUNNR = jsonObj.getString("KNA1-KUNNR");
                RecordSet recordSet = new RecordSet();
                String sql = "update formtable_main_350 set sapkhbh = ? WHERE requestid = ?";
                recordSet.executeQuery(sql, KNA1_KUNNR, requestid);
                new BaseBean().writeLog("以下为sql：" + sql);

            }
        }


        return execute;
    }
}

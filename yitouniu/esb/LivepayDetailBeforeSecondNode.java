package yitouniu.esb;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LivepayDetailBeforeSecondNode {     //直播预付表2  第二个节点操作前  数据更新

    public Map executeParam(String params){
        String requestId = "";
        String requestId1 = "";
        String WRBTR = "";
        String lcjd = "";
        String lcbh = "";

        Map map = new HashMap();
        new BaseBean().writeLog("11111111111");

        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            JSONObject idata = (JSONObject) jsonObject.get("DATA");
            new BaseBean().writeLog("以下为data:"+idata.size());
            new BaseBean().writeLog("以下为data:"+idata);
            //List<JSONObject> data = (List<JSONObject>) jsonObject.get("data");
            //JSONObject idata = data.get(0);
            requestId = idata.getString("requestId");
            requestId1 = idata.getString("glhtlc");
            WRBTR = idata.getString("fkze");
            lcjd = idata.getString("lcjd");
            lcbh = idata.getString("lcbh");

            RecordSet rs = new RecordSet();
            List<List> updateAllData = new ArrayList<>();
            List updateData =  new ArrayList<>();
            updateData.add(WRBTR);
            updateData.add(requestId1);
            updateAllData.add(updateData);
            boolean b = rs.executeBatchSql("update live_pay_plan set djje = ? where requestId = ? ",updateAllData);

            if(b){
                RecordSet rs2 = new RecordSet();
                rs2.executeQuery("select zje,yfje from live_pay_plan where requestId = ?", requestId1);
                if(rs2.next()){
                    String zje = rs2.getString("zje");
                    String yfje = rs2.getString("yfje");
                    String kyje = String.valueOf(Double.parseDouble(zje)-Double.parseDouble(WRBTR)-Double.parseDouble(yfje));
                    String zqye = kyje;
                    RecordSet rs3 = new RecordSet();
                    List<List> updateAllData2 = new ArrayList<>();
                    List updateData2 =  new ArrayList<>();
                    updateData2.add(kyje);
                    updateData2.add(zqye);
                    updateData2.add(requestId1);
                    updateAllData2.add(updateData2);
                    boolean b2 = rs3.executeBatchSql("update live_pay_plan set kyje = ?,zqye = ? where requestId = ? ",updateAllData2);

                    RecordSet rs4 = new RecordSet();
                    List<List> updateAllData3 = new ArrayList<>();
                    List updateData3 =  new ArrayList<>();
                    updateData3.add(lcjd);
                    updateData3.add(lcbh);
                    updateData3.add(requestId);
                    updateAllData3.add(updateData3);
                    boolean b3 = rs4.executeBatchSql("update live_pay_detail set lcjd = ?,lcbh = ? where requestId = ? ",updateAllData3);

                    if(b2 && b3){
                        map.put("MSGTY", "S");
                        map.put("MSAGE", "成功");
                    }else{
                        map.put("MSGTY", "F");
                        map.put("MSAGE", "数据更新失败");
                    }
                }else{
                    map.put("MSGTY", "F");
                    map.put("MSAGE", "不存在该直播付款计划");
                }
            }else{
                map.put("MSGTY", "F");
                map.put("MSAGE", "数据更新失败");
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
        }
        return map;
    }

}

package yitouniu.esb;

import com.alibaba.fastjson.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

//中间件调用OA 返回人员数据   by   依依
public class ReturnYGDataToZJJ {

    public Map executeParam(String params) {
        try {
            params= URLDecoder.decode(params, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        new BaseBean().writeLog("打印："+params);
        String userCode = "";
        String id = "";
        String departmentid = "";
        String subcompanyid1 = "";
        String gysbh = "";
        Map map = new HashMap();
        JSONObject jsonObject = JSONObject.parseObject(params);
        List<JSONObject> data = (List)jsonObject.get("data");
        JSONObject idata = (JSONObject) data.get(0);
        userCode = idata.getString("userCode");
        if(userCode.indexOf("-")>0){
            userCode = userCode.substring(0,userCode.indexOf("-"));
        }
        RecordSet rs1 = new RecordSet();
        RecordSet rs2 = new RecordSet();
        String sql = "select id,departmentid,subcompanyid1 from HrmResource where loginid = ?";
        rs1.executeQuery(sql, userCode);

        while(rs1.next()) {
            id = rs1.getString("id");
            departmentid = rs1.getString("departmentid");
            subcompanyid1 = rs1.getString("subcompanyid1");
            String sql2 = "select gysbh from uf_zzdmys where oagsmc = ?";
            rs2.executeQuery(sql2, subcompanyid1);

            while(rs2.next()) {
                gysbh = rs2.getString("gysbh");
            }
        }
        map.put("id", id);
        map.put("departmentid", departmentid);
        map.put("subcompanyid1", subcompanyid1);
        map.put("gysbh", gysbh);
        return map;
    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.workflow.webservices.WorkflowBaseInfo;
import weaver.workflow.webservices.WorkflowMainTableInfo;
import weaver.workflow.webservices.WorkflowRequestInfo;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 15:24 2022/1/18
 */
public class TestEsb extends ReturnMsgToSAP {
    //测试
    public final static String dataBaseName = "formtable_main_350";
    public final static String detailDataBaseName = "formtable_main_350_dt1";

    public Map executeParam(Map params) {
        Map map = new HashMap();
        boolean flag = false;

        RecordSet rs1 = new RecordSet();
        String sql1 = "select * from uf_SAPrenyuan where xglc IS NOT NULL and KUNNR = ''";
        rs1.executeQuery(sql1);
        while(rs1.next()){
            String xglc = rs1.getString("xglc");
            String sapkhbh = null;

            RecordSet rs2 = new RecordSet();
            String sql2 = "select sapkhbh from formtable_main_354 where requestid = ? ";
            rs2.executeQuery(sql2, xglc);
            if(rs2.next()){
                sapkhbh = rs2.getString("sapkhbh");
            }
            if(sapkhbh != null){
                RecordSet rs3 = new RecordSet();
                flag = rs3.executeUpdate("update uf_SAPrenyuan set rybh = ?,KUNNR = ? where xglc = ? ", sapkhbh, sapkhbh, xglc);
            }

            new BaseBean().writeLog("TestEsb：xglc = " + xglc);
            new BaseBean().writeLog("TestEsb：sapkhbh = " + sapkhbh);
        }

        if(flag){
            map.put("type", "true");
        }else {
            map.put("type", "false");
        }

        return map;


    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Moss
 * @Description: 查询oa用户数据接口
 * @Date: Create in 14:11 2021/8/11
 * @Modified By:
 */
public class ReturnOaUserData {
    public Map executeParam(){
        new BaseBean().writeLog("ReturnOaUserData测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));


        Map<String, Object> resultMap = new HashMap<>();
        RecordSet recordSet = new RecordSet();

        String sql = "select HR.id Id, HR.loginid username, HR.lastname fullname, " +
                " hd.departmentname deptname, hjt.jobtitlename position, HR.mobile, hd.id deptId, hd.supdepid fid" +
                " from HrmResource HR" +
                " left join hrmdepartment hd ON hd.id = HR.departmentid" +
                " left join hrmjobtitles hjt ON hjt.id = HR.jobtitle" +
                " where HR.status <=3";
        recordSet.executeQuery(sql);

        JSONArray dataArray = new JSONArray();

        while (recordSet.next()){
            JSONObject dataJson = new JSONObject();
            dataJson.put("Id",recordSet.getString("Id"));
            dataJson.put("username",recordSet.getString("username"));
            dataJson.put("fullname",recordSet.getString("fullname"));
            dataJson.put("deptname",recordSet.getString("deptname"));
            dataJson.put("position",recordSet.getString("position"));
            dataJson.put("mobile",recordSet.getString("mobile"));
            dataJson.put("deptId",recordSet.getString("deptId"));
            if("0".equals(recordSet.getString("fid"))){
                dataJson.put("fid"," ");
            } else {
                dataJson.put("fid",recordSet.getString("fid"));
            }
            dataArray.add(dataJson);
        }

        if(dataArray.size()!=0){
            resultMap.put("msg","成功");
            resultMap.put("data",dataArray);
        }else {
            resultMap.put("msg","失败");
            resultMap.put("data",null);
        }
        return resultMap;
    }
}

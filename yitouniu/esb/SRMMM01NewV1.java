package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ImageFileUtil;
import yitouniu.util.SRMUtil;

import java.io.IOException;
import java.util.*;

public class SRMMM01NewV1 {     //物料档案接收接口 OA->SRM

    RecordSet rs = new RecordSet();


    public Map executeParam(Map params) throws IOException {
        Map map = new HashMap();
        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject ctrlobject = (JSONObject) jsonObject.get("CTRL");
            JSONObject lxobject = (JSONObject) jsonObject.get("TYPE");

            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");
            JSONArray dataArr = new JSONArray();
            JSONArray dataArr2 = new JSONArray();
            JSONArray dataArr3 = new JSONArray();
            JSONObject injson = new JSONObject();
            if(lxobject.getString("LX").equals("1")){   //扩充，不传DATA
                new BaseBean().writeLog("不传data");
                for(int i = 0; i <dataList.size(); i++){
                    JSONObject dataJSON2 = new JSONObject();
                    JSONObject object = dataList.get(i);

                    dataJSON2.put("MATNR",object.getString("MATNR"));
                    dataJSON2.put("WERKS",object.getString("WERKS"));
                    dataJSON2.put("NAME1",object.getString("NAME1"));
                    dataJSON2.put("XCHPF",object.getString("XCHPF"));
                    dataJSON2.put("ZXQGL",object.getString("ZXQGL"));
                    dataJSON2.put("EKGRP",object.getString("EKGRP"));
                    dataJSON2.put("LGFSB",object.getString("LGFSB"));
                    dataJSON2.put("BSTMI",object.getString("BSTMI"));
                    dataJSON2.put("EISBE",object.getString("EISBE"));
                    dataJSON2.put("PLIFZ",object.getString("PLIFZ"));
                    dataJSON2.put("WEBAZ",object.getString("WEBAZ"));
                    dataJSON2.put("STPRS_1",object.getString("STPRS_1"));
                    dataJSON2.put("VPRSV_1",object.getString("VPRSV_1"));
                    dataJSON2.put("PEINH",object.getString("PEINH"));
                    // 收税分类和名称
                    dataJSON2.put("ssfl1", object.getString("ssfl1"));
                    dataJSON2.put("ssflmc", object.getString("ssflmc"));

                    if(object.getString("fjsc").length()>0 && !object.getString("fjsc").equals("?")){
                        ImageFileUtil imageFileUtil = new ImageFileUtil();
                        Map mapresult = imageFileUtil.base64Encoder(object.getString("fjsc"));
                        List PICXTList = (List) mapresult.get("PICXT");
                        List PICFLList = (List) mapresult.get("PICFL");
                        for(int j = 0;j < PICXTList.size();j++){
                            JSONObject dataJSON3 = new JSONObject();
                            String PICFLONE = (String) PICFLList.get(j);
                            dataJSON3.put("MATNR",object.getString("MATNR"));
                            dataJSON3.put("PICXT",PICXTList.get(j));
                            dataJSON3.put("PICFL",PICFLONE.replace("+","|"));
                            dataArr3.add(dataJSON3);
                        }
                    }
                    dataArr2.add(dataJSON2);

                }
            }else{
                for(int i = 0; i <dataList.size(); i++){
                    JSONObject dataJSON = new JSONObject();
                    JSONObject dataJSON2 = new JSONObject();

                    JSONObject object = dataList.get(i);

                    dataJSON.put("ZSRTYP",object.getString("ZSRTYP"));
                    dataJSON.put("ZOANO",object.getString("ZOANO"));

                    switch(object.getString("MTART")){
                        case "0":
                            dataJSON.put("MTART", "产成品");
                            break;
                        case "1":
                            dataJSON.put("MTART", "原辅料");
                            break;
                        case "2":
                            dataJSON.put("MTART", "包装物");
                            break;
                        case "3":
                            dataJSON.put("MTART", "生物资产");
                            break;
                        case "4":
                            dataJSON.put("MTART", "备品备件");
                            break;
                        case "5":
                            dataJSON.put("MTART", "活动品");
                            break;
                        case "6":
                            dataJSON.put("MTART", "服务物料");
                            break;
                        default:
                            dataJSON.put("MTART", "");
                            break;
                    }

                    dataJSON.put("MATKL",object.getString("MATKL"));
                    dataJSON.put("MMSTA",object.getString("MMSTA"));
                    dataJSON.put("MATNR",object.getString("MATNR"));
                    dataJSON.put("MAKTX",object.getString("MAKTX"));
                    dataJSON.put("MEINS",object.getString("MEINS"));
                    RecordSet rs = new RecordSet();
                    String sql = "select MSEH3 from uf_jbdwb where MEINS = ?";
                    rs.executeQuery(sql,object.getString("MEINS"));
                    dataJSON.put("MEINS_NAME","");
                    if(rs.next()){
                        dataJSON.put("MEINS_NAME",rs.getString("MSEH3"));
                    }
                    dataJSON.put("BSTME",object.getString("BSTME"));
                    dataJSON.put("MEINH",object.getString("MEINH"));
                    dataJSON.put("ZLENGTH",object.getString("ZLENGTH"));
                    dataJSON.put("ZWIDTH",object.getString("ZWIDTH"));
                    dataJSON.put("ZHIGH",object.getString("ZHIGH"));
                    dataJSON.put("ZVOLUME",object.getString("ZVOLUME"));
                    dataJSON.put("ZMATNR_JLB",object.getString("ZMATNR_JLB"));
                    dataJSON.put("ZMAKTX_JLB",object.getString("ZMAKTX_JLB"));
                    dataJSON.put("ZDBZL",object.getString("ZDBZL"));
                    dataJSON.put("ZBZXQ",object.getString("ZBZXQ"));
                    dataJSON.put("ZGBXQ",object.getString("ZGBXQ"));
                    dataJSON.put("ZIQC",object.getString("ZIQC"));
                    dataJSON.put("UEBTO",object.getString("UEBTO"));
                    dataJSON.put("UNTTO",object.getString("UNTTO"));
                    dataJSON.put("ZTEXT",object.getString("ZTEXT"));
                    dataJSON.put("MODEL",object.getString("MODEL"));
                    dataJSON.put("VNDOR",object.getString("VNDOR"));
                    dataJSON.put("BRAND",object.getString("BRAND"));
                    dataJSON.put("BRGEW",object.getString("BRGEW"));
                    dataJSON.put("NTGEW",object.getString("NTGEW"));
                    dataJSON.put("GEWEI",object.getString("GEWEI"));
                    dataJSON.put("ERSDA",object.getString("ERSDA"));
                    dataJSON.put("CREATED_AT_TIME",object.getString("CREATED_AT_TIME"));
                    dataJSON.put("ERNAM",object.getString("ERNAM"));
                    dataJSON.put("LAEDA",object.getString("LAEDA"));
                    dataJSON.put("AENAM",object.getString("AENAM"));
                    dataJSON.put("XCHPF",object.getString("XCHPF"));
                    dataJSON.put("jybz",object.getString("jybz"));
                    // 税收分类编码和名称
                    dataJSON.put("ssfl1", object.getString("ssfl1"));
                    dataJSON.put("ssflmc", object.getString("ssflmc"));
                    String txjl = object.getString("txjl");
                    String txjlName = "";
                    if (StringUtils.isNotBlank(txjl)){
                        rs.executeQuery("select lastname from HrmResource where id = ? ", txjl);
                        if(rs.next()){
                            txjlName = rs.getString("lastname");
                        }
                    }
                    dataJSON.put("txjl",txjlName);

                    dataJSON2.put("MATNR",object.getString("MATNR"));
                    dataJSON2.put("WERKS",object.getString("WERKS"));
                    dataJSON2.put("NAME1",object.getString("NAME1"));
                    dataJSON2.put("XCHPF",object.getString("XCHPF"));
                    dataJSON2.put("ZXQGL",object.getString("ZXQGL"));
                    dataJSON2.put("EKGRP",object.getString("EKGRP"));
                    dataJSON2.put("LGFSB",object.getString("LGFSB"));
                    dataJSON2.put("BSTMI",object.getString("BSTMI"));
                    dataJSON2.put("EISBE",object.getString("EISBE"));
                    dataJSON2.put("PLIFZ",object.getString("PLIFZ"));
                    dataJSON2.put("WEBAZ",object.getString("WEBAZ"));
                    dataJSON2.put("STPRS_1",object.getString("STPRS_1"));

                    switch(object.getString("VPRSV_1")){
                        case "0":
                            dataJSON2.put("VPRSV_1", "S");
                            break;
                        case "1":
                            dataJSON2.put("VPRSV_1", "V");
                            break;
                        default:
                            dataJSON2.put("VPRSV_1", "");
                            break;
                    }

                    dataJSON2.put("PEINH",object.getString("PEINH"));

                    if(object.getString("fjsc").length()>0 && !"?".equals(object.getString("fjsc"))){
                        ImageFileUtil imageFileUtil = new ImageFileUtil();
                        Map mapresult = imageFileUtil.base64Encoder(object.getString("fjsc"));
                        List PICXTList = (List) mapresult.get("PICXT");
                        List PICFLList = (List) mapresult.get("PICFL");
                        for(int j = 0;j < PICXTList.size();j++){
                            JSONObject dataJSON3 = new JSONObject();
                            String PICFLONE = (String) PICFLList.get(j);
                            dataJSON3.put("MATNR",object.getString("MATNR"));
                            dataJSON3.put("PICXT",PICXTList.get(j));
                            dataJSON3.put("PICFL",PICFLONE.replace("+","|"));
                            dataArr3.add(dataJSON3);
                        }
                    }

                    dataArr.add(dataJSON);
                    dataArr2.add(dataJSON2);
                }
                injson.put("DATA",dataArr);
            }

            injson.put("ITEM",dataArr2);
            injson.put("IMGS",dataArr3);
            injson.put("TYPE",lxobject);
            injson.put("CTRL",ctrlobject);

            new BaseBean().writeLog("SRMMM01传入参数："+injson);
            String result = SRMUtil.pushSRM(injson);
            new BaseBean().writeLog("以下为result："+result);

            JSONObject resultObject = JSONObject.parseObject(result);
            JSONObject outObject = (JSONObject) resultObject.get("OUT_JSON");
            JSONObject ctrlObject2 = (JSONObject) outObject.get("CTRL");
//            JSONObject dataObject2 = (JSONObject) outObject.get("DATA");
            String successflag = ctrlObject2.getString("success");
            String messageflag = ctrlObject2.getString("message");
            new BaseBean().writeLog("返回message："+messageflag);
//            String outMATNR = dataObject2.getString("cinvcode");
            new BaseBean().writeLog("返回SAP物料号cinvcode："+"outMATNR");

            if(successflag.equals("true")){
                map.put("MSGTY","S");
                map.put("MSAGE","推送成功");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",injson);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","推送失败");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",injson);
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","推送失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
            map.put("IN_JSON","");
        }
        return map;
    }

}

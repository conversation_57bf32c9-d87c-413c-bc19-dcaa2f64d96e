package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Moss
 * @Description: 采购申购单 申请人提交采购时间控制 （废弃）
 * @Date: Create in 14:42 2021/12/14
 * @Modified By:
 */
public class CGTimeControlV2 {
    public Map executeParam(Map params) {
        Map map = new HashMap();
        String companyId = null;
        int startDay = 0;
        int endDay = 0;
        String checkStatus = null;
        int nowDay = 0;
        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
            //申请公司，是id号32不是编号8700这种
            String applyCompanyId = dataObject.getString("SQGS");
            //申请类型
            String applyType = dataObject.getString("SQLX");
            RecordSet rs = new RecordSet();
            String sql = "select zzdm from uf_zzdmys where oagsmc = ?";
            rs.executeQuery(sql,applyCompanyId);
            while(rs.next()) {
                companyId = rs.getString("zzdm");
            }
            //查找控制日期和是否检查Status
            RecordSet rs2 = new RecordSet();
            String sql2 = "select start_day, end_day, check_status from uf_cg_time_control where company_id= ?";
            rs2.executeQuery(sql2,companyId);
            while(rs2.next()) {
                startDay = Integer.parseInt(rs2.getString("start_day"));
                endDay = Integer.parseInt(rs2.getString("end_day"));
                checkStatus = rs2.getString("check_status");
            }
            new BaseBean().writeLog("CGTimeControl :startDay "+startDay);
            new BaseBean().writeLog("CGTimeControl :endDay"+endDay);
            new BaseBean().writeLog("CGTimeControl :checkStatus "+checkStatus);

            boolean flag = true;
            if(!"4".equals(applyType) && !"2".equals(applyType)){
                String now = new SimpleDateFormat("dd").format(dataObject.getDate("nowDay"));
                nowDay = Integer.parseInt(now);
                if(nowDay < startDay || nowDay > endDay || !"X".equals(checkStatus)){
                    flag = false;
                }
            }
            new BaseBean().writeLog("CGTimeControl :flag "+flag);
            if(flag){
                map.put("MSGTY","S");
                map.put("MSAGE","进入下一流程");
                map.put("NowDay",nowDay);
                map.put("StartDay",startDay);
                map.put("EndDay",endDay);
            } else{
                map.put("MSGTY","F");
                map.put("MSAGE","不在采购申请日期内");
                map.put("NowDay",nowDay);
                map.put("StartDay",startDay);
                map.put("EndDay",endDay);
            }
        } catch(Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE",e);
            map.put("NowDay",nowDay);
            map.put("StartDay",startDay);
            map.put("EndDay",endDay);
        }
        return map;
    }
}

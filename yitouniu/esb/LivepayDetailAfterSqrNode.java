package yitouniu.esb;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LivepayDetailAfterSqrNode {      //直播预付表2 申请人操作后   校验&存表
    private RecordSet rs = new RecordSet();

    public Map executeParam(String params){
        String requestId = "";
        String lcbh = "";
        String requestId1 = "";
        String sqr = "";
        String WRBTR = "";
        String lcjd = "";

        Map map = new HashMap();
        new BaseBean().writeLog("11111111111");

        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            JSONObject idata = (JSONObject) jsonObject.get("DATA");
            new BaseBean().writeLog("以下为data:"+idata.size());
            new BaseBean().writeLog("以下为data:"+idata);
            //List<JSONObject> data = (List<JSONObject>) jsonObject.get("data");
            //JSONObject idata = data.get(0);
            requestId = idata.getString("requestId");
            lcbh = idata.getString("lcbh");
            requestId1 = idata.getString("glhtlc");
            sqr = idata.getString("sqr");
            WRBTR = idata.getString("fkze");
            lcjd = idata.getString("lcjd");

            rs.executeQuery("select zqye,kyje,zje,yfje from live_pay_plan where requestId = ?", requestId1);
            if(rs.next()){
                String zqye1 = rs.getString("zqye");
                if(Double.parseDouble(zqye1) == 0.00){
                    map.put("MSGTY","F");
                    map.put("RESULT","该直播付款计划已过期失效");
                }else{
                    String kyje = rs.getString("kyje");
                    if(Double.parseDouble(WRBTR) <= Double.parseDouble(kyje)){   //校验  本期应付小于等于可用金额时，才可提交
                        List<List> insertAllData = new ArrayList<>();
                        List insertData =  new ArrayList<>();
                        insertData.add(requestId);
                        insertData.add(lcbh);
                        insertData.add(requestId1);
                        insertData.add(sqr);
                        insertData.add(WRBTR);
                        insertData.add(lcjd);
                        insertAllData.add(insertData);

                        List<List> updateAllData = new ArrayList<>();
                        List updateData =  new ArrayList<>();
                        updateData.add(requestId1);
                        updateAllData.add(updateData);

                        if(insertAllData.size()>0){
                            boolean b = rs.executeBatchSql("insert into live_pay_detail (requestId,lcbh,requestId1,sqr,WRBTR,lcjd) values (?,?,?,?,?,?)",insertAllData);
                            if(b){
                                RecordSet rs2 = new RecordSet();
                                boolean b2 = rs2.executeBatchSql("update live_pay_plan set djje = 0 where requestId = ? ",updateAllData);

                                String zje = rs.getString("zje");
                                String yfje = rs.getString("yfje");
                                kyje = String.valueOf(Double.parseDouble(zje)-0.00-Double.parseDouble(yfje));
                                String zqye = kyje;
                                List<List> updateAllData2 = new ArrayList<>();
                                List updateData2 =  new ArrayList<>();
                                updateData2.add(kyje);
                                updateData2.add(zqye);
                                updateData2.add(requestId1);
                                updateAllData2.add(updateData2);
                                RecordSet rs3 = new RecordSet();
                                boolean b3 = rs3.executeBatchSql("update live_pay_plan set kyje = ?,zqye = ? where requestId = ? ",updateAllData2);

                                if(b2 && b3){
                                    map.put("MSGTY", "S");
                                    map.put("MSAGE", "成功");
                                }else{
                                    map.put("MSGTY", "F");
                                    map.put("MSAGE", "可用金额更新失败");
                                }

                            }else{
                                map.put("MSGTY", "F");
                                map.put("MSAGE", "存表失败");
                            }
                        }
                    }else{
                        map.put("MSGTY","F");
                        map.put("RESULT","本期应付金额超出可用金额");
                    }
                }

            }else{
                map.put("MSGTY","F");
                map.put("RESULT","关联合同不存在");
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
        }
        return map;
    }
}

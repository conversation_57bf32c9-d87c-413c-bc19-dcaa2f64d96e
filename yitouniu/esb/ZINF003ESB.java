package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import weaver.conn.RecordSet;
import yitouniu.util.SAPUtil;
import yitouniu.util.TimeUtils;

import java.util.*;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description:
 * @Date: Create in 9:46 2022/3/14
 */
public class ZINF003ESB {

    public static final String databaseName = "uf_costcenter";

    public Map executeParam(Map params) {
        Map map = new HashMap();
        String paramsString = JSON.toJSONString(params);
        try {
            String execute = SAPUtil.execute(paramsString);
            JSONObject jsonObject = JSONObject.parseObject(execute);

            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();
            JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
            List<JSONObject> dataListObject = (List<JSONObject>) jsonObject.get("DATA");
            String msgType = ctrlObject.getString("MSGTY");
            if("S".equals(msgType)) {
                for(JSONObject obj : dataListObject){
                    List dataList = new ArrayList<>();
                    List insertData =  new ArrayList<>();
                    List updateData =  new ArrayList<>();

                    String KOKRS = obj.getString("KOKRS");
                    String KOSTL = obj.getString("KOSTL");
                    String LTEXT = obj.getString("LTEXT");
                    String FKBER = obj.getString("FKBER");
                    String KOSAR = obj.getString("KOSAR");
                    String DATBI = obj.getString("DATBI");
                    String DATAB = obj.getString("DATAB");
                    String BUKRS = obj.getString("BUKRS");
                    String BKZKP = obj.getString("BKZKP");
                    String KHINR = obj.getString("KHINR");

                    Long nowTime = System.currentTimeMillis();
                    //有效截至日期
                    Long DATBITime = TimeUtils.TimeToStamp(DATBI);
                    //开始生效日期
                    Long DATABTime = TimeUtils.TimeToStamp(DATAB);
                    if(nowTime>DATABTime && nowTime <DATBITime){
                        dataList.add(KOKRS);
                        dataList.add(LTEXT);
                        dataList.add(FKBER);
                        dataList.add(KOSAR);
                        dataList.add(DATBI);
                        dataList.add(DATAB);
                        dataList.add(BUKRS);
                        dataList.add(BKZKP);
                        dataList.add(KHINR);
                    }

                    boolean typeFlag = selectKOSTL(KOSTL);
                    if(typeFlag){
                        CollectionUtils.addAll(updateData,new Object[dataList.size()]);
                        Collections.copy(updateData,dataList);
                        updateData.add(KOSTL);
                        updateAllData.add(updateData);
                    } else {
                        CollectionUtils.addAll(insertData,new Object[dataList.size()]);
                        Collections.copy(insertData,dataList);
                        insertData.add(1,KOSTL);
                        insertAllData.add(insertData);
                    }
                }

                if(insertAllData.size()>0){
                    flag += insertList(insertAllData) + ",";
                }

                if(updateAllData.size()>0){
                    flag += updateList(updateAllData) + ",";
                }
                if (flag.indexOf("false") != -1) {
                    map.put("MSGTY","F");
                    map.put("MSAGE","处理数据失败");
                    map.put("RESULT",execute);
                    map.put("PARAMS",params);
                }else{
                    map.put("MSGTY","S");
                    map.put("MSAGE","处理数据成功");
                    map.put("RESULT",execute);
                    map.put("PARAMS",params);
                }
            }else {
                map.put("MSGTY","F");
                map.put("MSAGE","读取失败");
                map.put("RESULT",execute);
                map.put("PARAMS",params);
            }
        } catch (Exception e) {
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
        }

        return map;
    }

    public boolean selectKOSTL(String KOSTL){
        RecordSet rs = new RecordSet();
        rs.executeQuery("select * from "+databaseName+" where KOSTL = ?",KOSTL);
        return rs.next();
    }

    public boolean updateList(List<List> list){
        RecordSet rs = new RecordSet();
        String sql = " update "+databaseName+" set KOKRS=?, LTEXT=?, FKBER=?, KOSAR=?, DATBI=?, DATAB=?, BUKRS=?, BKZKP=?, KHINR=? " +
                " where KOSTL=?";
        return rs.executeBatchSql(sql,list);
    }

    public boolean insertList(List<List> list){
        RecordSet rs = new RecordSet();
        String sql = " insert into "+databaseName+" (KOKRS, KOSTL, LTEXT, FKBER, KOSAR, DATBI, DATAB, BUKRS, BKZKP, KHINR)" +
                " values (?,?,?,?,?,?,?,?,?,?) ";
        return rs.executeBatchSql(sql,list);
    }







}

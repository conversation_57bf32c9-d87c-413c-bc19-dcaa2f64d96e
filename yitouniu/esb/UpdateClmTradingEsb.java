package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.general.BaseBean;
import yitouniu.util.ClmUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * CLM相对方业务信息更新接口ESB
 * 调用甄零系统的相对方业务信息更新接口
 * 
 * <AUTHOR>
 * @since 2025-08-18
 */
public class UpdateClmTradingEsb {
    
    private final Log LOG = LogFactory.getLog(UpdateClmTradingEsb.class);
    
    // CLM相对方业务信息更新接口路径
    private static final String CLM_UPDATE_TRADING_PATH = "/api/hitf/v2p/rest/invoke/SFpFUk86T1JDSEVTVFJBVElPTjpISVRGLkFQSS5TSU5HTEUtU0NSSVBU?domain=CUX.RYYTN.UPDATE_TRADING";
    
    /**
     * 执行相对方业务信息更新
     * @param params 参数Map
     * @return 执行结果
     */
    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }
    
    /**
     * 执行相对方业务信息更新
     * @param params JSON字符串参数
     * @return 执行结果
     */
    public Map executeParam(String params) {
        LOG.info("进入UpdateClmTradingEsb.executeParam，入参: " + params);
        new BaseBean().writeLog("CLM相对方业务信息更新ESB开始执行，入参: " + params);
        
        Map<String, String> retMap = new HashMap<>();
        
        try {
            // 解析输入参数
            JSONObject inputJson = JSONObject.parseObject(params);
            JSONObject dataObject = inputJson == null ? null : inputJson.getJSONObject("DATA");
            
            if (dataObject == null) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "参数DATA不能为空");
                LOG.error("参数DATA不能为空");
                return retMap;
            }
            
            // 验证必填字段
            String tradingUuid = dataObject.getString("tradingUuid");
            String partyNumber = dataObject.getString("partyNumber");
            String sapNumber = dataObject.getString("sapNumber");
            
            if (isEmpty(tradingUuid)) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "tradingUuid不能为空");
                LOG.error("tradingUuid不能为空");
                return retMap;
            }
            
            if (isEmpty(partyNumber)) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "partyNumber不能为空");
                LOG.error("partyNumber不能为空");
                return retMap;
            }
            
            if (isEmpty(sapNumber)) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "sapNumber不能为空");
                LOG.error("sapNumber不能为空");
                return retMap;
            }
            
            // 构建请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("tradingUuid", tradingUuid);
            requestData.put("partyNumber", partyNumber);
            requestData.put("sapNumber", sapNumber);
            
            LOG.info("构建CLM请求数据: " + requestData.toString());
            new BaseBean().writeLog("CLM请求数据: " + requestData.toString());
            
            // 获取访问令牌 - 默认使用UAT环境SRM客户端
            ClmUtil.TokenInfo token = ClmUtil.getUatSrmToken();
            
            // 构建完整的请求URL
            String requestUrl = ClmUtil.CLM_UAT_BASE_URL + CLM_UPDATE_TRADING_PATH;
            
            // 调用CLM接口
            String response = ClmUtil.httpJsonPostWithAuth(requestUrl, requestData, token);
            
            LOG.info("CLM接口响应: " + response);
            new BaseBean().writeLog("CLM接口响应: " + response);
            
            // 解析响应结果
            JSONObject responseJson = JSONObject.parseObject(response);
            
            if (responseJson == null) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "CLM接口响应格式错误");
                retMap.put("RESULT", response);
                retMap.put("PARAMS", params);
                return retMap;
            }
            
            // 检查响应是否成功
            Boolean success = responseJson.getBoolean("success");
            String message = responseJson.getString("message");
            JSONObject result = responseJson.getJSONObject("result");
            
            if (success != null && success) {
                // 检查业务执行结果
                if (result != null) {
                    Boolean failed = result.getBoolean("failed");
                    String resultMessage = result.getString("message");
                    
                    if (failed != null && !failed) {
                        // 执行成功
                        retMap.put("MSGTY", "S");
                        retMap.put("MSAGE", "相对方业务信息更新成功: " + resultMessage);
                        retMap.put("RESULT", response);
                        retMap.put("PARAMS", params);
                        
                        LOG.info("CLM相对方业务信息更新成功");
                        new BaseBean().writeLog("CLM相对方业务信息更新成功: " + resultMessage);
                    } else {
                        // 业务执行失败
                        retMap.put("MSGTY", "F");
                        retMap.put("MSAGE", "相对方业务信息更新失败: " + resultMessage);
                        retMap.put("RESULT", response);
                        retMap.put("PARAMS", params);
                        
                        LOG.error("CLM相对方业务信息更新失败: " + resultMessage);
                        new BaseBean().writeLog("CLM相对方业务信息更新失败: " + resultMessage);
                    }
                } else {
                    // 没有result字段，但success为true
                    retMap.put("MSGTY", "S");
                    retMap.put("MSAGE", "相对方业务信息更新成功: " + message);
                    retMap.put("RESULT", response);
                    retMap.put("PARAMS", params);
                    
                    LOG.info("CLM相对方业务信息更新成功");
                    new BaseBean().writeLog("CLM相对方业务信息更新成功: " + message);
                }
            } else {
                // 接口调用失败
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "CLM接口调用失败: " + message);
                retMap.put("RESULT", response);
                retMap.put("PARAMS", params);
                
                LOG.error("CLM接口调用失败: " + message);
                new BaseBean().writeLog("CLM接口调用失败: " + message);
            }
            
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "CLM相对方业务信息更新异常: " + e.getMessage());
            retMap.put("PARAMS", params);
            
            LOG.error("CLM相对方业务信息更新异常", e);
            new BaseBean().writeLog("CLM相对方业务信息更新异常: " + e.getMessage());
        }
        
        LOG.info("UpdateClmTradingEsb执行完成，结果: " + JSON.toJSONString(retMap));
        new BaseBean().writeLog("CLM相对方业务信息更新ESB执行完成，结果: " + JSON.toJSONString(retMap));
        
        return retMap;
    }
    
    /**
     * 使用指定环境和客户端执行相对方业务信息更新
     * @param params JSON字符串参数
     * @param baseUrl CLM基础URL
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return 执行结果
     */
    public Map executeWithCustomClient(String params, String baseUrl, String clientId, String clientSecret) {
        LOG.info("进入UpdateClmTradingEsb.executeWithCustomClient，入参: " + params);
        new BaseBean().writeLog("CLM相对方业务信息更新ESB(自定义客户端)开始执行，入参: " + params);
        
        Map<String, String> retMap = new HashMap<>();
        
        try {
            // 解析输入参数
            JSONObject inputJson = JSONObject.parseObject(params);
            JSONObject dataObject = inputJson == null ? null : inputJson.getJSONObject("DATA");
            
            if (dataObject == null) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "参数DATA不能为空");
                return retMap;
            }
            
            // 验证必填字段
            String tradingUuid = dataObject.getString("tradingUuid");
            String partyNumber = dataObject.getString("partyNumber");
            String sapNumber = dataObject.getString("sapNumber");
            
            if (isEmpty(tradingUuid) || isEmpty(partyNumber) || isEmpty(sapNumber)) {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "tradingUuid、partyNumber、sapNumber不能为空");
                return retMap;
            }
            
            // 构建请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("tradingUuid", tradingUuid);
            requestData.put("partyNumber", partyNumber);
            requestData.put("sapNumber", sapNumber);
            
            // 获取访问令牌
            ClmUtil.TokenInfo token = ClmUtil.getAccessToken(baseUrl, clientId, clientSecret);
            
            // 构建完整的请求URL
            String requestUrl = baseUrl + CLM_UPDATE_TRADING_PATH;
            
            // 调用CLM接口
            String response = ClmUtil.httpJsonPostWithAuth(requestUrl, requestData, token);
            
            // 解析响应结果
            JSONObject responseJson = JSONObject.parseObject(response);
            Boolean success = responseJson.getBoolean("success");
            
            if (success != null && success) {
                JSONObject result = responseJson.getJSONObject("result");
                if (result != null && !result.getBoolean("failed")) {
                    retMap.put("MSGTY", "S");
                    retMap.put("MSAGE", "相对方业务信息更新成功");
                } else {
                    retMap.put("MSGTY", "F");
                    retMap.put("MSAGE", "相对方业务信息更新失败");
                }
            } else {
                retMap.put("MSGTY", "F");
                retMap.put("MSAGE", "CLM接口调用失败");
            }
            
            retMap.put("RESULT", response);
            retMap.put("PARAMS", params);
            
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "CLM相对方业务信息更新异常: " + e.getMessage());
            retMap.put("PARAMS", params);
            
            LOG.error("CLM相对方业务信息更新异常", e);
        }
        
        return retMap;
    }
    
    /**
     * 检查字符串是否为空
     * @param str 字符串
     * @return 是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}

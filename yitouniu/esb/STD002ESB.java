package yitouniu.esb;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.utils.StringUtils;
import com.google.common.base.Throwables;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.conn.RecordSet;
import yitouniu.util.StdUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * TPM合同创建接口
 *
 * <AUTHOR>
 * @since 2024-06-25 15:25
 */
public class STD002ESB {
    private final Log LOG = LogFactory.getLog(STD002ESB.class);

    private static final String CREATE_CONTRACT_URL = "/crm-tpm/v1/external/tpm/createBatchContract";
    private static final String OSS_KEY_PREFIX = "oa/contract/";

    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }

    public Map executeParam(String params) {
        LOG.info("进入STD002ESB.executeParam,入参:" + params);
        long startTime = System.currentTimeMillis();
        Map<String, String> retMap = new HashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject dataObject = jsonObject == null ? null : jsonObject.getJSONObject("DATA");
        if (dataObject == null) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "参数不能为空");
            return retMap;
        }

        /*组装文件列表参数*/
        JSONObject paramObject = jsonObject.getJSONObject("PARAM");
        JSONArray fileUrlList;
        try {
            fileUrlList = docId2OssFileUrl(paramObject == null ? null : paramObject.getString("docIdStr"));
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "文件处理失败" + e.getMessage());
            return retMap;
        }
        dataObject.put("fileUrlList", fileUrlList);

        /*执行STD请求*/
        try {
            StdUtil.executePost(CREATE_CONTRACT_URL, dataObject.toJSONString());
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", e.getMessage());
            return retMap;
        }

        retMap.put("MSGTY", "S");
        retMap.put("MSAGE", "回调成功");

        LOG.info("合同创建接口-完成，入参:" + params + "，结果：" + JSON.toJSONString(retMap)
                + "，耗时:" + (System.currentTimeMillis() - startTime));
        return retMap;
    }

    /**
     * 根据docId查询文件并上传到OSS
     *
     * @param docIdStr 文档ID列表，多个会用英文逗号隔开
     * @return 文件ossObjectKey列表
     */
    private JSONArray docId2OssFileUrl(String docIdStr) {
        if (StringUtils.isEmpty(docIdStr)) {
            return null;
        }

        /*查询图片文件*/
        JSONArray imageFiles = queryImageFile(docIdStr);
        if (imageFiles.size() == 0) {
            LOG.warn("根据docId查询不到文件,docId:" + docIdStr);
            return null;
        }
        LOG.info("根据docId查询文件,入参:" + docIdStr + ",查询到文件记录数:" + imageFiles.size());
        JSONArray retArray = new JSONArray();
        for (int i = 0; i < imageFiles.size(); i++) {
            JSONObject imageFile = imageFiles.getJSONObject(i);

            String extName = FileUtil.extName(imageFile.getString("imagefilename"));
            String fileSuffix = StringUtils.isEmpty(extName) ? null : "." + extName;
            /*解压文件*/
            String unZipFile;
            try {
                unZipFile = unzip(imageFile.getString("filerealpath"), fileSuffix);
            } catch (Exception e) {
                LOG.warn("解压文件失败,文件对象:" + imageFile.toJSONString() + ",异常:" + Throwables.getStackTraceAsString(e));
                throw new RuntimeException(e);
            }

            /*将解压文件上传到OSS*/
            File file = new File(unZipFile);
            String ossKey = OSS_KEY_PREFIX
                    + DateUtil.format(new Date(), "yyyyMM")
                    + "/"
                    + UUID.randomUUID().toString().replace("-", "")
                    + fileSuffix;
            StdUtil.push2Oss(file, ossKey);

            /*删除解压的文件*/
            //TODO ************:手动删除文件，节约服务器存储空间,测试机路径：【C:\Windows\TEMP\】
            /*FileUtil.del(file);*/
            JSONObject fileInfo = new JSONObject();
            fileInfo.put("fileUrl", ossKey);
            fileInfo.put("fileName", imageFile.getString("imagefilename"));
            retArray.add(fileInfo);
        }
        return retArray;
    }


    /**
     * 通过docId查询文件信息
     *
     * @param docIdStr 文档ID列表，多个会用英文逗号隔开
     * @return 文件信息列表
     */
    private JSONArray queryImageFile(String docIdStr) {
        String sql = "SELECT imagefileid,imagefilename,filerealpath FROM imagefile WHERE imagefileid IN " +
                "(SELECT imagefileid FROM docimagefile WHERE docid IN ( " + docIdStr + "))";
        RecordSet rs = new RecordSet();
        rs.execute(sql);

        JSONArray jsonArray = new JSONArray();
        while (rs.next()) {
            JSONObject obj = new JSONObject();
            obj.put("imagefileid", rs.getString("imagefileid"));
            obj.put("imagefilename", rs.getString("imagefilename"));
            obj.put("filerealpath", rs.getString("filerealpath"));
            jsonArray.add(obj);
        }
        return jsonArray;
    }


    /**
     * 解压文件(OA的附件都是被单独压缩到一个zip的包里面，所以解压后里面就只会有一个文件)
     *
     * @param zipFilePath 源文件路径
     * @param fileSuffix  文件后缀
     * @return 解压后的文件路径
     */
    public String unzip(String zipFilePath, String fileSuffix) throws IOException {
        String filePath = null;
        File destDir = FileUtil.getTmpDir();
        if (!destDir.exists()) {
            destDir.mkdir();
        }
        ZipInputStream zipIn = new ZipInputStream(Files.newInputStream(Paths.get(zipFilePath)));
        ZipEntry entry = zipIn.getNextEntry();
        // 迭代解压每一个条目，根据OA的压缩规则，其实这里只有一个文件，也不会有文件夹的情况
        while (entry != null) {
            filePath = destDir + File.separator + entry.getName() + fileSuffix;
            if (!entry.isDirectory()) {
                // 如果条目是文件，创建输出流并写入文件
                try (FileOutputStream fos = new FileOutputStream(filePath)) {
                    byte[] bytes = new byte[1024];
                    int length;
                    while ((length = zipIn.read(bytes)) != -1) {
                        fos.write(bytes, 0, length);
                    }
                }
            } else {
                /*如果是文件夹，确保文件夹存在，否则创建之*/
                File dir = new File(filePath);
                dir.mkdir();
            }
            zipIn.closeEntry();
            entry = zipIn.getNextEntry();
        }
        zipIn.close();

        return filePath;
    }

}

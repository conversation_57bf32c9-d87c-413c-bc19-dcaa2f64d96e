package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import yitouniu.util.StdUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * OA回调
 *
 * <AUTHOR>
 * @since 2024-06-25 15:25
 */
public class STD001ESB {
    private final Log LOG = LogFactory.getLog(STD001ESB.class);

    private static final String OA_CALLBACK_URL = "/crm-mdm/v1/external/std/oaCallback";

    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }

    public Map executeParam(String params) {
        LOG.info("进入STD001ESB.executeParam,入参:" + params);

        Map<String, String> retMap = new HashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject dataObject = jsonObject == null ? null : jsonObject.getJSONObject("DATA");
        if (dataObject == null) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "参数不能为空");
            return retMap;
        }

        try {
            StdUtil.executePost(OA_CALLBACK_URL, dataObject.toJSONString());
        } catch (Exception e) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", e.getMessage());
            return retMap;
        }

        retMap.put("MSGTY", "S");
        retMap.put("MSAGE", "回调成功");

        LOG.info("STD调用OA回调接口-完成，入参:" + params + "，结果：" + JSON.toJSONString(retMap));
        return retMap;
    }
}

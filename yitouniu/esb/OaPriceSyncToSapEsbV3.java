package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.TimeUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: haiyang
 * @Date: 2025-07-11 09:41
 * @Desc:
 */
public class OaPriceSyncToSapEsbV3 extends ReturnMsgToSAP{

    BaseBean baseBean = new BaseBean();
    public Map executeParam(Map params) {
        baseBean.writeLog("OaPriceSyncToSapEsb.param:" + params);
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray dataArr = jsonObject.getJSONArray("DATA");
        List<JSONObject> dataListObject = JSONObject.parseArray(dataArr.toJSONString(), JSONObject.class);
        // 流程编号
        String lcbh = ctrlObject.getString("OAID");
        JSONObject reqJSON = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(lcbh);
        List<JSONObject> dataListReq = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataListObject)) {
            dataListObject.forEach(item -> {
                JSONObject dataReq = new JSONObject();
                dataReq.put("OAID", lcbh);
                // 物料编码
                dataReq.put("MATNR", item.getString("MATNR"));
                // 标准价
                dataReq.put("ZBZXSJ", item.getString("ZBZXSJ"));
                dataListReq.add(dataReq);
            });
        }

        reqJSON.put("CTRL", ctrlReq);
        reqJSON.put("DATA", dataListReq);
        baseBean.writeLog("OaPriceSyncToSapEsb.requestSapParam:" + reqJSON.toJSONString());
        Map executeResult = this.execute(reqJSON.toJSONString());
        return executeResult;
    }


    private static JSONObject getCtrlReq(String lcbh){
        JSONObject ctrlReq = new JSONObject();
        Date date = new Date();
        ctrlReq.put("SYSID", "OA");
        ctrlReq.put("REVID", "SAP");
        ctrlReq.put("FUNID", "ZOAINF009");
        ctrlReq.put("INFID", lcbh + System.currentTimeMillis());
        ctrlReq.put("UNAME", "sysadmin");
        ctrlReq.put("DATUM", TimeUtils.getTimeStr(date, "yyyy-MM-dd"));
        ctrlReq.put("UZEIT", TimeUtils.getTimeStr(date, "HH:mm:ss"));
        ctrlReq.put("KEYID", lcbh);

        return ctrlReq;
    }
}

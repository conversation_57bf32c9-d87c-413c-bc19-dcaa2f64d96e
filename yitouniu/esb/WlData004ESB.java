package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.response.CorpReportListResponse;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.*;

//物料数据获取 by 依依
public class WlData004ESB {

    private final String JMID="17";

    private RecordSet rs = new RecordSet();

    public Map execute(Map params){
        Map map = new HashMap();
        try{

            new BaseBean().writeLog("004测试日志"+new Date().getTime());
            String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String formatTime = new SimpleDateFormat("HH:mm:ss").format(new Date());

            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");

            String update = "";
            List<List> insertAllJBDWB = new ArrayList<>();
            List<List> updateAllJBDWB = new ArrayList<>();

            List<List>  insertAllWLZB = new ArrayList<>();
            List<List> updateAllWLZB = new ArrayList<>();

            List<List> insertAllCGZ = new ArrayList<>();
            List<List> updateAllCGZ = new ArrayList<>();

            List<List> insertAllGCB = new ArrayList<>();
            List<List> updateAllGCB = new ArrayList<>();

            List<List> insertAllPGL = new ArrayList<>();
            List<List> updateAllPGL = new ArrayList<>();

            List<List> insertAllCK = new ArrayList<>();
            List<List> updateAllCK = new ArrayList<>();

            for(JSONObject idata : data){
                List insertJBDWB = new ArrayList<>();
                List updateJBDWB = new ArrayList<>();

                List insertWLZB = new ArrayList<>();
                List updateWLZB = new ArrayList<>();

                List insertCGZ = new ArrayList<>();
                List updateCGZ = new ArrayList<>();

                List insertGCB = new ArrayList<>();
                List updateGCB = new ArrayList<>();

                List insertPGL = new ArrayList<>();
                List updatePGL = new ArrayList<>();

                List insertCK = new ArrayList<>();
                List updateCK = new ArrayList<>();

                String MEINS = idata.getString("MEINS");
                String MTART = idata.getString("MTART");
                String MATKL = idata.getString("MATKL");
                String WERKS = idata.getString("WERKS");
                String EKGRP = idata.getString("EKGRP");
                String NAME1 = idata.getString("NAME1");
                //String BWKEY = idata.getString("BWKEY");
                String BKLAS = idata.getString("BKLAS");
                String LGORT = idata.getString("LGORT");
                String LGOBE = idata.getString("LGOBE");
                String MTBEZ = idata.getString("MTBEZ");
                String WGBEZ = idata.getString("WGBEZ");
                String MSEH3 = idata.getString("MSEH3");
                String EKNAM = idata.getString("EKNAM");
                String BKBEZ = idata.getString("BKBEZ");



                if(MEINS.length()>0){
                    if(selectJBDWB(MEINS)){
                        updateJBDWB.add(MSEH3);
                        updateJBDWB.add(MEINS);
                        updateAllJBDWB.add(updateJBDWB);
                    }else{
                        insertJBDWB.add(MEINS);
                        insertJBDWB.add(MSEH3);
                        insertJBDWB.add(JMID);
                        insertJBDWB.add("0");
                        insertJBDWB.add("1");
                        insertJBDWB.add(formatDate);
                        insertJBDWB.add(formatTime);
                        insertAllJBDWB.add(insertJBDWB);
                    }
                }

                if(MATKL.length()>0){
                    if(selectWLZB(MATKL)){
                        updateWLZB.add(MTBEZ);
                        updateWLZB.add(WGBEZ);
                        updateWLZB.add(MTART);
                        updateWLZB.add(MATKL);
                        updateAllWLZB.add(updateWLZB);
                    }else{
                        insertWLZB.add(MTART);
                        insertWLZB.add(MATKL);
                        insertWLZB.add(MTBEZ);
                        insertWLZB.add(WGBEZ);
                        insertWLZB.add(JMID);
                        insertWLZB.add("0");
                        insertWLZB.add("1");
                        insertWLZB.add(formatDate);
                        insertWLZB.add(formatTime);
                        insertAllWLZB.add(insertWLZB);
                    }
                }

                if(WERKS.length()>0 && EKGRP.length()>0){
                    if(selectCGZ(WERKS,EKGRP)){
                        updateCGZ.add(EKNAM);
                        updateCGZ.add(WERKS);
                        updateCGZ.add(EKGRP);
                        updateAllCGZ.add(updateCGZ);
                    }else{
                        insertCGZ.add(WERKS);
                        insertCGZ.add(EKGRP);
                        insertCGZ.add(EKNAM);
                        insertCGZ.add(JMID);
                        insertCGZ.add("0");
                        insertCGZ.add("1");
                        insertCGZ.add(formatDate);
                        insertCGZ.add(formatTime);
                        insertAllCGZ.add(insertCGZ);
                    }
                }

                if(WERKS.length()>0 && NAME1.length()>0){
                    if(selectGCB(WERKS)){
                        updateGCB.add(NAME1);
                        updateGCB.add(WERKS);
                        updateAllGCB.add(updateGCB);
                    }else{
                        insertGCB.add(WERKS);
                        insertGCB.add(NAME1);
                        insertGCB.add(JMID);
                        insertGCB.add("0");
                        insertGCB.add("1");
                        insertGCB.add(formatDate);
                        insertGCB.add(formatTime);
                        insertAllGCB.add(insertGCB);
                    }
                }

                if(BKLAS.length()>0 && MTART.length()>0){
                    if(selectPGL(BKLAS,MTART)){
                        updatePGL.add(BKBEZ);
                        updatePGL.add(BKLAS);
                        updatePGL.add(MTART);
                        updateAllPGL.add(updatePGL);
                    }else{
                        insertPGL.add(MTART);
                        insertPGL.add(BKLAS);
                        insertPGL.add(BKBEZ);
                        insertPGL.add(JMID);
                        insertPGL.add("0");
                        insertPGL.add("1");
                        insertPGL.add(formatDate);
                        insertPGL.add(formatTime);
                        insertAllPGL.add(insertPGL);
                    }
                }

                if(LGORT.length()>0 && WERKS.length()>0){
                    if(selectCK(LGORT,WERKS)){
                        updateCK.add(LGOBE);
                        updateCK.add(LGORT);
                        updateCK.add(WERKS);
                        updateAllCK.add(updateCK);
                    }else{
                        insertCK.add(WERKS);
                        insertCK.add(LGORT);
                        insertCK.add(LGOBE);
                        insertCK.add(JMID);
                        insertCK.add("0");
                        insertCK.add("1");
                        insertCK.add(formatDate);
                        insertCK.add(formatTime);
                        insertAllCK.add(insertCK);
                    }
                }
            }

            if(insertAllJBDWB.size()>0){
                update += insertJBDWB(insertAllJBDWB) + ",";
            }

            if(updateAllJBDWB.size()>0){
                update += updateJBDWB(updateAllJBDWB) + ",";
            }

            if(insertAllWLZB.size()>0){
                update += insertWLZB(insertAllWLZB) + ",";
            }

            if(updateAllWLZB.size()>0){
                update += updateWLZB(updateAllWLZB)+",";
            }

            if(insertAllCGZ.size()>0){
                update += insertCGZ(insertAllCGZ)+",";
            }

            if(updateAllCGZ.size()>0){
                update += updateCGZ(updateAllCGZ) +",";
            }

            if(insertAllGCB.size()>0){
                update += insertGCB(insertAllGCB)+",";
            }

            if(updateAllGCB.size()>0){
                update += updateGCB(updateAllGCB)+",";
            }

            if(insertAllPGL.size()>0){
                update += insertPGL(insertAllPGL)+",";
            }

            if(updateAllPGL.size()>0){
                update += updatePGL(updateAllPGL)+",";
            }

            if(insertAllCK.size()>0){
                update += insertCK(insertAllCK)+",";
            }

            if(updateAllCK.size()>0){
                update += updateCK(updateAllCK)+",";
            }

            if(update.indexOf("false")!=-1){
                map.put("MSGTY","F");
                map.put("MSAGE","失败");
            }else{
                map.put("MSGTY","S");
                map.put("MSAGE","成功");
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE",e);
        }

        return map;
    }

    public boolean selectJBDWB(String MEINS){
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from uf_JBDWB where MEINS = ?",MEINS);
        return rs1.next();
    }

    public boolean insertJBDWB(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_JBDWB (MEINS,MSEH3,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updateJBDWB(List<List> list){
        boolean b = rs.executeBatchSql("update uf_JBDWB set MSEH3 = ? where MEINS = ?",list);
        return b;
    }

    public boolean selectWLZB(String MATKL){
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from uf_WLZB where MATKL = ?",MATKL);
        return rs1.next();
    }

    public boolean insertWLZB(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_WLZB (MTART,MATKL,MTBEZ,WGBEZ,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updateWLZB(List<List> list){
        boolean b = rs.executeBatchSql("update uf_WLZB set MTBEZ = ?,WGBEZ = ? where MTART =? and MATKL =?",list);
        return b;
    }

    public boolean selectCGZ(String WERKS,String EKGRP){
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from uf_CGZ where WERKS = ? and EKGRP = ?",WERKS,EKGRP);
        return rs1.next();
    }

    public boolean insertCGZ(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_CGZ (WERKS,EKGRP,EKNAM,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updateCGZ(List<List> list){
        boolean b = rs.executeBatchSql("update uf_CGZ set EKNAM = ? where WERKS = ? and EKGRP = ?",list);
        return b;
    }

    public boolean selectGCB(String WERKS){
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from uf_GCB where WERKS = ?",WERKS);
        return rs1.next();
    }

    public boolean insertGCB(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_GCB (WERKS,NAME1,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updateGCB(List<List> list){
        boolean b = rs.executeBatchSql("update uf_GCB set NAME1 = ? where WERKS =?",list);
        return b;
    }

    public boolean selectPGL(String BKLAS,String MTART){
        RecordSet rs1= new RecordSet();
        rs1.executeQuery("select * from uf_PGL where BKLAS = ? and MTART = ?",BKLAS,MTART);
        return rs1.next();
    }

    public boolean insertPGL(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_PGL (MTART,BKLAS,BKBEZ,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updatePGL(List<List> list){
        boolean b = rs.executeBatchSql("update uf_PGL set BKBEZ = ? where BKLAS = ? and MTART = ?",list);
        return b;
    }

    public boolean selectCK(String LGORT,String WERKS) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from uf_ck where LGORT =? and WERKS = ?",LGORT,WERKS);
        return rs1.next();
    }

    public boolean insertCK(List<List> list){
        boolean b = rs.executeBatchSql("insert into uf_CK (WERKS,LGORT,LGOBE,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?)",list);
        return b;
    }

    public boolean updateCK(List<List> list){
        boolean b = rs.executeBatchSql("update uf_CK set LGOBE = ? where LGORT =? and WERKS =?",list);
        return b;
    }

}

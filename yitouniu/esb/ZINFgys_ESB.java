package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//供应商主数据推送   by  依依
public class ZINFgys_ESB extends ReturnMsgToSAP{

    public Map executeParam(String params){
        new BaseBean().writeLog("供应商主数据测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        Map execute = new HashMap();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject CTRLObject = (JSONObject) jsonObject.get("CTRL");
        String requestid = CTRLObject.getString("INFID");

        RecordSet recordSet = new RecordSet();
        //这个表名要改
        String sql = "SELECT id FROM formtable_main_350_dt1 WHERE mainid = ( SELECT id FROM formtable_main_350 WHERE requestid = ?)";
        recordSet.executeQuery(sql,requestid);

        if(jsonObject.containsKey("DATA")){
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");

            JSONArray dataArr = new JSONArray();
            for(int i = 0;i <dataList.size();i++){
                JSONObject dataJSON = new JSONObject();
                JSONObject object = dataList.get(i);
                if(recordSet.next()){
                    dataJSON.put("ZID",recordSet.getString("id"));
                }else{
                    dataJSON.put("ZID","");
                }

                dataJSON.put("BU_GROUP",object.getString("BU_GROUP"));
                dataJSON.put("NAME_ORG1",object.getString("NAME_ORG1"));
                dataJSON.put("NAME_ORG2",object.getString("NAME_ORG2"));
                dataJSON.put("SORTL",object.getString("SORTL"));
                dataJSON.put("STRAS",object.getString("STRAS"));
                dataJSON.put("PSTL2",object.getString("PSTL2"));
                dataJSON.put("ORT02",object.getString("ORT02"));
                dataJSON.put("ORT01",object.getString("ORT01"));
                dataJSON.put("CONTACT",object.getString("CONTACT"));
                dataJSON.put("TELF1",object.getString("TELF1"));
                dataJSON.put("TELF2",object.getString("TELF2"));
                dataJSON.put("TELFX",object.getString("TELFX"));
                dataJSON.put("SMTP_ADDR",object.getString("SMTP_ADDR"));
                dataJSON.put("STCD5",object.getString("STCD5"));
                dataJSON.put("BUKRS",object.getString("BUKRS"));
                dataJSON.put("ZTERM",object.getString("ZTERM"));
                dataJSON.put("BANKL",object.getString("BANKL"));
                dataJSON.put("BANKN",object.getString("BANKN"));
                dataJSON.put("BKONT",object.getString("BKONT"));
                dataJSON.put("KOINH",object.getString("KOINH"));
                dataJSON.put("EKORG",object.getString("EKORG"));
                dataJSON.put("ZTERM",object.getString("ZTERM"));

                dataArr.add(dataJSON);
            }
            jsonObject.put("DATA",dataArr);
            execute = super.execute(jsonObject.toString());
            Object msgty = execute.get("MSGTY");
            if(!"S".equals(msgty)){    //失败
                return execute;
            }
        }
        if(execute.isEmpty()){
            execute.put("MSGTY","S");
            execute.put("MSAGE","成功");
            execute.put("RESULT",execute);
            execute.put("PARAMS",params);
        }

        return execute;
    }
}

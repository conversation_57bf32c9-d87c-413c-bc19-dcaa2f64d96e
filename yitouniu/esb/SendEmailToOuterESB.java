package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.email.EmailWorkRunnable;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: oa
 * @description: 给外部供应商发送邮件
 * @author: haiyang
 * @create: 2023-09-11 10:13
 **/
public class SendEmailToOuterESB {

    public Map executeParam(Map params) {
        Map map = new HashMap();

        String execute = JSON.toJSONString(params);
        new BaseBean().writeLog("ESB-SendEmailToOuterESB:" + execute);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        String email = ctrlObject.getString("email");
        String title = ctrlObject.getString("title");
        String content = ctrlObject.getString("content");
        String docIds = ctrlObject.getString("docIds");
        EmailWorkRunnable ewr = new EmailWorkRunnable(email, title,content);
        if (StringUtils.isNotEmpty(docIds)){
            ewr.setDocIds(docIds);
        }
        try {
            boolean result = ewr.emailCommonRemind(); //result:true投递成功，false投递失败
            if (result) {
                map.put("MSGTY", "S");
                map.put("MSAGE", "发送消息成功");
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "发送邮件失败");
            }
            map.put("RESULT", result);
            map.put("IN_JSON", execute);
        } catch (Exception e) {
            map.put("MSGTY", "E");
            map.put("MSAGE", "异常错误失败");
            map.put("RESULT", e.getMessage());
            map.put("IN_JSON", execute);
        }

        return map;
    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.h2.util.StringUtils;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 销售员OA-SAP接口
 *
 * <AUTHOR>
 * @since 2025-05-16 15:05
 */
public class SalesPersonToSapESBV1 {
    private static final String FUNID = "ZOAINF008";
    private final Log LOG = LogFactory.getLog(SalesPersonToSapESBV1.class);


    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }

    public Map executeParam(String params) {
        LOG.info("SalesPersonToSapESB.executeParam,入参:" + params);
        Map<String, String> retMap = new HashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject dataObject = jsonObject == null ? null : jsonObject.getJSONObject("DATA");
        if (dataObject == null) {
            retMap.put("MSGTY", "E");
            retMap.put("MSAGE", "参数不能为空");
            return retMap;
        }

        try {
            String sapParam = buildSapParam(dataObject);

            String respStr = SAPUtil.execute(sapParam);
            parseSapResp(respStr, retMap);
        } catch (Exception e) {
            retMap.put("MSGTY", "E");
            retMap.put("MSAGE", "执行异常:" + e.getMessage());
        }

        LOG.info("SalesPersonToSapESB.executeParam-完成，入参:" + params + "，结果：" + JSON.toJSONString(retMap));
        return retMap;
    }

    /**
     * 解析SAP返回结果
     *
     * @param respStr SAP执行结果
     * @param retMap  返回结果
     */
    private void parseSapResp(String respStr, Map<String, String> retMap) {
        LOG.info("SalesPersonToSapESB.buildSapResp,SAP执行结果:" + respStr);

        JSONObject resp = JSON.parseObject(respStr);
        if (Objects.isNull(resp)) {
            throw new RuntimeException("SAP执行结果为空");
        }

        JSONObject ctrl = resp.getJSONObject("CTRL");
        JSONArray data = resp.getJSONArray("DATA");
        /*外层成功标识*/
        String MSGTY = ctrl.getString("MSGTY");
        /*外层返回信息*/
        String MSAGE = ctrl.getString("MSAGE");

        if ("S".equals(MSGTY)) {
            JSONObject retObj = data.getJSONObject(0);
            if (Objects.isNull(retObj)
                    || StringUtils.isNullOrEmpty(retObj.getString("MSGTY"))) {
                throw new RuntimeException("未获取到销售员编码");
            }

            retMap.put("MSGTY", retObj.getString("MSGTY"));
            retMap.put("MSAGE", retObj.getString("MSAGE"));
            retMap.put("PERNR", retObj.getString("PERNR"));
        } else {
            throw new RuntimeException("SAP返回失败:" + MSAGE);
        }
    }

    /**
     * 构建SAP入参
     *
     * @param dataObject 入参
     * @return SAP入参
     */
    private static String buildSapParam(JSONObject dataObject) {
        JSONObject DATA = new JSONObject();
        /*姓名*/
        String name = dataObject.getString("Sqrwb");
        if (!StringUtils.isNullOrEmpty(name)) {
            /*姓*/
            DATA.put("NACHN", name.substring(0, 1));
            /*名*/
            DATA.put("VORNA", name.substring(1));
        }
        /*工号*/
        DATA.put("RUFNM", dataObject.getString("gh"));
        /*邮箱*/
        DATA.put("USRID", dataObject.getString("gryxh"));
        /*出生日期*/
        DATA.put("GBDAT", dataObject.getString("wsrq"));

        JSONObject CTRL = SAPUtil.getCtrlReq(FUNID, 1, dataObject.getString("lcbh"), FUNID, "1");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("CTRL", CTRL);
        jsonObject.put("DATA", DATA);
        return jsonObject.toJSONString();
    }
}

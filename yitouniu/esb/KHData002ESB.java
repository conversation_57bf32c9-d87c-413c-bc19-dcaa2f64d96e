package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.SAPUtil;
import yitouniu.util.WorkflowUtil;

import java.text.SimpleDateFormat;
import java.util.*;

// 客户/员工/供应商主数据获取
public class KHData002ESB {

    private final String TABLE_NAME_DETAIL = "uf_yhzsj";
    private final String TABLE_NAME = "uf_khzsj";
    private final String JMID = "14";
    private final String JMID_DETAIL = "1051";

    private RecordSet rs = new RecordSet();


    public Map execute(Map param) {
        Map map = new HashMap();
        String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        String formatTime = new SimpleDateFormat("HH:mm:ss").format(new Date());
        String update = "";
        try {
            new BaseBean().writeLog("开始处理之间: "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

            String params = JSON.toJSONString(param); // map转为json
            //String execute = SAPUtil.execute(params);
            JSONObject jsonObject = JSONObject.parseObject(params);
            List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
            List<List> insertAllKHData = new ArrayList<>(); // 客户
            List<List> updateAllKHData = new ArrayList<>(); // 客户
            List<List> insertAllYHData = new ArrayList<>(); // 银行
            List<List> updateAllYHData = new ArrayList<>(); // 银行



            for (JSONObject datum : data) { // 主数据
                List insertKHData =  new ArrayList<>();
                List updateKHData =  new ArrayList<>();
                String ZBUTYPE = datum.getString("ZBUTYPE");
                String NAME1 = datum.getString("NAME1");
                String BUGROUP = datum.getString("BUGROUP");
                String VBUND = datum.getString("VBUND");
                String PARTNER = datum.getString("PARTNER");
                String NAME2 = datum.getString("NAME2");
                String NAME3 = datum.getString("NAME3");
                String TXT15 = datum.getString("TXT15");

                List<JSONObject> kz_object = (List<JSONObject>) datum.get("IT_BANKS");

                for (int i = 0; i < kz_object.size(); i++) { // 明细数据
                    List insertYHData =  new ArrayList<>();
                    List updateYHData =  new ArrayList<>();
                    JSONObject kz = kz_object.get(i);
                    String PARTNER1 = kz.getString("PARTNER");
                    String BANKL = kz.getString("BANKL");
                    String BANKN = kz.getString("BANKN");
                    String BANKA = kz.getString("BANKA");
                    String KOINH = kz.getString("KOINH");
                    String KOVON = kz.getString("KOVON");
                    String KOBIS = kz.getString("KOBIS");
                    String BKREF = kz.getString("BKREF");

                    // jmid, "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date())


                    boolean mxsj = selectCbkz(PARTNER1, BANKN, TABLE_NAME_DETAIL);
                    //         boolean b = rs.executeBatchSql("update " + tableName + " set PARTNER =?,BANKL=?,BANKN=?,BANKA=?,KOINH=?,KOVON=?,KOBIS=? where PARTNER =? and BANKN = ? ", );
                    if (mxsj) {
                        updateYHData.add(PARTNER1);
                        updateYHData.add(BANKL);
                        if (BKREF!=null&&!"".equals(BKREF)){
                            updateYHData.add(BANKN+BKREF);
                        }else{
                            updateYHData.add(BANKN);

                        }
                        updateYHData.add(BANKA);
                        updateYHData.add(KOINH);
                        updateYHData.add(KOVON);
                        updateYHData.add(KOBIS);
                        updateYHData.add(PARTNER1);
                        updateYHData.add(BANKN);
                        updateAllYHData.add(updateYHData);

                        //update += "更新明细:" + updateCbkz(PARTNER1, BANKL, BANKN, BANKA, KOINH, KOVON, KOBIS, TABLE_NAME_DETAIL) + ",";

                    } else {
                        //PARTNER,BANKL,BANKN,BANKA,KOINH,KOVON,KOBIS,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME
                        // PARTNER,BANKL,BANKN,BANKA,KOINH,KOVON,KOBIS,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME
                        insertYHData.add(PARTNER1);
                        insertYHData.add(BANKL);
                        insertYHData.add(BANKN);
                        insertYHData.add(BANKA);
                        insertYHData.add(KOINH);
                        insertYHData.add(KOVON);
                        insertYHData.add(KOBIS);
                        insertYHData.add(JMID_DETAIL);
                        insertYHData.add("0");
                        insertYHData.add("1");
                        insertYHData.add(formatDate);
                        insertYHData.add(formatTime);
                        insertAllYHData.add(insertYHData);

                        //update += "新增明细:" + insertCbkz(PARTNER1, BANKL, BANKN, BANKA, KOINH, KOVON, KOBIS, TABLE_NAME_DETAIL, JMID_DETAIL) + ",";
                    }
                }

                boolean zsj = selectCb(PARTNER, TABLE_NAME,ZBUTYPE);
                if (zsj) {

                    updateKHData.add(ZBUTYPE);
                    updateKHData.add(NAME1);
                    updateKHData.add(BUGROUP);
                    updateKHData.add(VBUND);
                    updateKHData.add(NAME2);
                    updateKHData.add(PARTNER);
                    updateKHData.add(PARTNER);
                    updateKHData.add(ZBUTYPE);
                    updateAllKHData.add(updateKHData);

                    //update += "更新:" + updateCb(ZBUTYPE, NAME1, BUGROUP, PARTNER, VBUND, TABLE_NAME) + ",";

                } else {
                    insertKHData.add(ZBUTYPE);
                    insertKHData.add(NAME1);
                    insertKHData.add(BUGROUP);
                    insertKHData.add(PARTNER);
                    insertKHData.add(VBUND);
                    insertKHData.add(NAME2);
                    insertKHData.add(NAME3);
                    insertKHData.add(TXT15);
                    insertKHData.add(JMID);
                    insertKHData.add("0");
                    insertKHData.add("1");
                    insertKHData.add(formatDate);
                    insertKHData.add(formatTime);
                    insertAllKHData.add(insertKHData);

                   // update += "新增:" + insertCb(ZBUTYPE, NAME1, BUGROUP, PARTNER, VBUND, TABLE_NAME, JMID) + ",";
                }
            }
            new BaseBean().writeLog("数据处理时间: "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            if (insertAllKHData.size()>0){
                update += insertCb(TABLE_NAME, insertAllKHData)+",";
            }
            if (insertAllYHData.size()>0){
                update +=insertCbkz(TABLE_NAME_DETAIL,insertAllYHData)+",";
            }


            new BaseBean().writeLog("插入数据结束时间: "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));


            if (updateAllKHData.size()>0){
                update += updateCb(TABLE_NAME,updateAllKHData)+",";
            }
            if (updateAllYHData.size()>0){
                update +=updateCbkz(TABLE_NAME_DETAIL,updateAllYHData)+",";

            }

            new BaseBean().writeLog("更新数据结束时间: "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));


            if (update.indexOf("false") != -1) {
                map.put("MSGTY", "F");
                map.put("MSAGE", "失败");
            } else {
                map.put("MSGTY", "S");
                map.put("MSAGE", "成功");
            }
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", e);
        }

        return map;
    }


   /* // 修改主数据
    public boolean updateCb(String ZBUTYPE, String NAME1, String BUGROUP, String PARTNER, String VBUND, String tableName) {
        boolean b = rs.executeUpdate("update " + tableName + " set ZBUTYPE =?,NAME1=?,BUGROUP=?,VBUND=?,PARTNER=? where PARTNER = ? ",
                ZBUTYPE, NAME1, BUGROUP, VBUND, PARTNER, PARTNER);
        return b;
    }*/

    // 修改主数据
    public boolean updateCb( String tableName,List<List> list) {
        boolean b = rs.executeBatchSql("update " + tableName + " set ZBUTYPE =?,NAME1=?,BUGROUP=?,VBUND=?,NAME2 = ? ,PARTNER=? where PARTNER = ? and ZBUTYPE = ?", list);
        return b;
    }


    /*// 新增主数据
    public boolean insertCb(String ZBUTYPE, String NAME1, String BUGROUP, String PARTNER, String VBUND, String tableName, String jmid) {
        boolean b = rs.executeUpdate("insert into " + tableName + " ( ZBUTYPE,NAME1,BUGROUP,PARTNER,VBUND,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?) ",
                ZBUTYPE, NAME1, BUGROUP, PARTNER, VBUND, jmid, "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare(tableName);


        return b;
    }*/

        // 新增主数据
    public boolean insertCb( String tableName, List<List> list) {
        /*String sqlValue = "";
        for (List listDetail : list) {
            String listString = listDetail.toString();
            String substring = listString.substring(1, listString.length() - 1);
            sqlValue += "("+substring+"),";
        }
        String lastSqlValue = sqlValue.substring(0, sqlValue.length() - 1);*/
        boolean b = rs.executeBatchSql("insert into " + tableName + " ( ZBUTYPE,NAME1,BUGROUP,PARTNER,VBUND,NAME2,NAME3,TXT15,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?,?,?,?) ",list);
        //boolean b = rs.executeUpdate("insert into " + tableName + " ( ZBUTYPE,NAME1,BUGROUP,PARTNER,VBUND,NAME2,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values  "+lastSqlValue);
       // WorkflowUtil.ModeDataShare(tableName);


        return b;
    }

    // 查询主数据
    public boolean selectCb(String PARTNER, String tableName,String ZBUTYPE) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  " + tableName + " where PARTNER =? and ZBUTYPE = ?", PARTNER,ZBUTYPE);


        return rs1.next();
    }

   /* // PARTNER1, BANKL, BANKN, BANKA, KOINH,KOVON,KOBIS
    // 修改扩展数据
    public boolean updateCbkz(String PARTNER, String BANKL, String BANKN, String BANKA, String KOINH, String KOVON, String KOBIS, String tableName) {
        boolean b = rs.executeUpdate("update " + tableName + " set PARTNER =?,BANKL=?,BANKN=?,BANKA=?,KOINH=?,KOVON=?,KOBIS=? where PARTNER =? and BANKN = ? ",
                PARTNER, BANKL, BANKN, BANKA, KOINH, KOVON, KOBIS, PARTNER, BANKN);
        return b;
    }
*/
    // PARTNER1, BANKL, BANKN, BANKA, KOINH,KOVON,KOBIS
    // 修改扩展数据
    public boolean updateCbkz( String tableName,List<List> list) {
        boolean b = rs.executeBatchSql("update " + tableName + " set PARTNER =?,BANKL=?,BANKN=?,BANKA=?,KOINH=?,KOVON=?,KOBIS=? where PARTNER =? and BANKN = ? ",list );
        return b;
    }

   /* // 新增扩展数据
    public boolean insertCbkz(String PARTNER, String BANKL, String BANKN, String BANKA, String KOINH, String KOVON, String KOBIS, String tableName, String jmid) {
        boolean b = rs.executeUpdate("insert into " + tableName + " ( PARTNER,BANKL,BANKN,BANKA,KOINH,KOVON,KOBIS,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?,?,?) ",
                PARTNER, BANKL, BANKN, BANKA, KOINH, KOVON, KOBIS, jmid, "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
        WorkflowUtil.ModeDataShare(tableName);


        return b;
    }*/
    // 新增扩展数据
    public boolean insertCbkz( String tableName, List<List> list) {
        boolean b = rs.executeBatchSql("insert into " + tableName + " ( PARTNER,BANKL,BANKN,BANKA,KOINH,KOVON,KOBIS,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?,?,?) ", list);
       // WorkflowUtil.ModeDataShare(tableName);


        return b;
    }

    // 查询扩展数据
    public boolean selectCbkz(String PARTNER, String BANKN, String tableName) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  " + tableName + " where PARTNER =? and BANKN = ?", PARTNER, BANKN);


        return rs1.next();
    }


}

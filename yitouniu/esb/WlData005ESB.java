package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 物料主数据获取
 */
public class WlData005ESB {


    private RecordSet rs = new RecordSet();
    private final String JMID = "16";


    public Map execute(Map params) {
        Map map = new HashMap();
        try {
            params.forEach((k,v)->{
                System.out.println("bbbbbbbbb"+k+":"+v);
            });
            new BaseBean().writeLog("005测试日志"+new Date().getTime());
            String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            String formatTime = new SimpleDateFormat("HH:mm:ss").format(new Date());

            String execute = JSON.toJSONString(params);
            new BaseBean().writeLog("WlData005ESB param: "+ execute);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
            String update = "";
            List<List> insertAllWLData = new ArrayList<>(); // 新增物料
            List<List> updateAllWLData = new ArrayList<>(); // 更新物料

            for (JSONObject datum : data) {
                List insertWLHData =  new ArrayList<>();
                List updateWLHData =  new ArrayList<>();
                String MATNR = datum.getString("MATNR");
                String MAKTX = datum.getString("MAKTX");
                String MTART = datum.getString("MTART");
                String MATKL = datum.getString("MATKL");
                String MEINS = datum.getString("MEINS");
                String BWKEY = datum.getString("BWKEY");
                String NAME1 = datum.getString("NAME1");
                String WGBEZ = datum.getString("WGBEZ");
                String PEINH = datum.getString("PEINH");
                String STPRS = datum.getString("STPRS");
                String MTBEZ = datum.getString("MTBEZ");
                String STPRS2 = datum.getString("STPRS2");
                String ZBZXSJ = datum.getString("ZBZXSJ");
                String VTEXT = datum.getString("VTEXT");


                boolean b = selectCb(MATNR,BWKEY);

                if (b) {
                    //"update uf_wlzsj set MATNR =?,MAKTX=?,MTART=?,MEINS=?,MATKL = ?,BWKEY=?,NAME1=?,WGBEZ=?,PEINH=?,STPRS=?,MTBEZ=?,STPRS2=? where  MATNR =? and BWKEY = ? ",list);

                    updateWLHData.add(MATNR);
                    updateWLHData.add(MAKTX);
                    updateWLHData.add(MTART);
                    updateWLHData.add(MEINS);
                    updateWLHData.add(MATKL);
                    updateWLHData.add(BWKEY);
                    updateWLHData.add(NAME1);
                    updateWLHData.add(WGBEZ);
                    updateWLHData.add(PEINH);
                    updateWLHData.add(STPRS);
                    updateWLHData.add(MTBEZ);
                    updateWLHData.add(STPRS2);
                    updateWLHData.add(ZBZXSJ);
                    updateWLHData.add(VTEXT);
                    updateWLHData.add(MATNR);
                    updateWLHData.add(BWKEY);
                    updateAllWLData.add(updateWLHData);
                    //update += "更新:" + updateCb(updateAllWLData) + ",";

                } else {
                    //MATNR, MAKTX, MTART, MEINS,MATKL,BWKEY,NAME1,WGBEZ,PEINH,STPRS,MTBEZ,STPRS2
                    insertWLHData.add(MATNR);
                    insertWLHData.add(MAKTX);
                    insertWLHData.add(MTART);
                    insertWLHData.add(MEINS);
                    insertWLHData.add(MATKL);
                    insertWLHData.add(BWKEY);
                    insertWLHData.add(NAME1);
                    insertWLHData.add(WGBEZ);
                    insertWLHData.add(PEINH);
                    insertWLHData.add(STPRS);
                    insertWLHData.add(MTBEZ);
                    insertWLHData.add(STPRS2);
                    insertWLHData.add(JMID);
                    insertWLHData.add("0");
                    insertWLHData.add("1");
                    insertWLHData.add(formatDate);
                    insertWLHData.add(formatTime);
                    insertWLHData.add(ZBZXSJ);
                    insertWLHData.add(VTEXT);
                    insertAllWLData.add(insertWLHData);
                    //
                }

            }

            if(insertAllWLData.size()>0){
                update +=  insertCb(insertAllWLData) + ",";

            }

            if(updateAllWLData.size()>0){
                update +=  updateCb(updateAllWLData) + ",";

            }

            if (update.indexOf("false") != -1) {
                map.put("MSGTY", "F");
                map.put("MSAGE", "失败");
            } else {
                map.put("MSGTY", "S");
                map.put("MSAGE", "成功");
            }
           /* rs.executeUpdate("insert into uf_sap (qqcs,xycs,gxjg,qqsj,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?)",
                    params, execute, update, currDate, "17", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));
            WorkflowUtil.ModeDataShare("uf_sap");*/
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", e);
        }
        return map;
    }


    // 修改
    public boolean updateCb(List<List> list) {
        /*boolean b = rs.executeUpdate("update uf_wlzsj set MATNR =?,MAKTX=?,MTART=?,MEINS=?,MATKL = ? where MATNR = ? ",
                MATNR, MAKTX, MTART, MEINS, MATKL,MATNR);*/
        boolean b = rs.executeBatchSql("update uf_wlzsj set MATNR =?,MAKTX=?,MTART=?,MEINS=?,MATKL = ?,BWKEY=?,NAME1=?,WGBEZ=?,PEINH=?,STPRS=?,MTBEZ=?,STPRS2=?, bzsj = ?, sl = ? where  MATNR =? and BWKEY = ? ",list);
        return b;
    }


    // 新增
    public boolean insertCb(List<List> list) {
        /*boolean b = rs.executeUpdate("insert into uf_wlzsj ( MATNR, MAKTX, MTART, MEINS,MATKL,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME) values (?,?,?,?,?,?,?,?,?,?) ",
                MATNR, MAKTX, MTART, MEINS, MATKL, "16", "1", "0", new SimpleDateFormat("yyyy-MM-dd").format(new Date()), new SimpleDateFormat("HH:mm:ss").format(new Date()));*/
        //WorkflowUtil.ModeDataShare("uf_wlzsj");
        boolean b = rs.executeBatchSql("insert into uf_wlzsj ( MATNR, MAKTX, MTART, MEINS,MATKL,BWKEY,NAME1,WGBEZ,PEINH,STPRS,MTBEZ,STPRS2,FORMMODEID,MODEDATACREATER,MODEDATACREATERTYPE,MODEDATACREATEDATE,MODEDATACREATETIME,bzsj,sl) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",list);


        return b;
    }

    // 查询
    public boolean selectCb(String MATNR,String BWKEY) {
        RecordSet rs1 = new RecordSet();

        rs1.executeQuery("select  *  from  uf_wlzsj where MATNR =? and BWKEY = ?", MATNR,BWKEY);

        return rs1.next();
    }


}



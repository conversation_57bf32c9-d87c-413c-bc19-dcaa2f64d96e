package yitouniu.esb;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import weaver.general.Util;
import yitouniu.constant.CommonConstant;
import yitouniu.util.OMSUtils;

import java.util.*;


/**
 * @Author: haiyang
 * @Date: 2024-10-18 14:48
 * @Desc:
 */
public class SupplementOrderPushOmsESBV1 {

    BaseBean baseBean = new BaseBean();

    List<String> cardOrCycleSkuEcodes = Lists.newArrayList("10800", "10801","10802");

    public Map executeParam(Map params) {
        new BaseBean().writeLog("SupplementOrderPushOmsESB请求参数" + JSON.toJSONString(params));

        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        Map map = new HashMap();
        JSONArray  jsonArray = jsonObject.getJSONArray("DATA");
        List<JSONObject> dataListObject = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
        JSONArray requestArr = new JSONArray();
        List<String> skuEcodes = Lists.newArrayList();
        dataListObject.forEach(item -> {
            JSONObject request = new JSONObject();
            request.put("tid", item.getString("tid"));
            request.put("psCSkuEcode", item.getString("psCSkuEcode"));
            skuEcodes.add(item.getString("psCSkuEcode"));
            request.put("qty", item.getInteger("qty"));
            // 提交人
            request.put("ownername", item.getString("ownername"));
            requestArr.add(request);
        });

        JSONObject requestObj = new JSONObject();
        requestObj.put("data", requestArr);
        baseBean.writeLog("补发订单请求参数:"+ requestObj);

        try {
            OMSUtils omsUtils = new OMSUtils();
            String token = omsUtils.getToken();

            doQuerySku(token,skuEcodes);

            String api = Util.null2String(new BaseBean().getPropValue("oms_configuration","resissueOrderApi"));

            String result = HttpRequest
                    .post(OMSUtils.OMS_OMS_URL + api)
                    .header("r3-api-token",token)
                    .body(requestObj.toJSONString()).execute().body();
            new BaseBean().writeLog( "请求返回="+result);
            JSONObject resultJson = JSONObject.parseObject(result);
            if(resultJson.getInteger("code") != 0){
                String errMsg = resultJson.getString("message");
                if(StringUtils.isEmpty(errMsg)){
                    map.put("MSGTY","F");
                    map.put("MSAGE","异常错误失败");
                    map.put("RESULT","异常错误失败");
                    map.put("PARAMS",params);
                    return map;
                }
                map.put("MSGTY","F");
                map.put("MSAGE", errMsg);
                map.put("RESULT", errMsg);
                map.put("PARAMS",params);
                return map;
            }
            map.put("MSGTY","S");
            map.put("MSAGE","处理数据成功");
            map.put("RESULT",execute);
            map.put("PARAMS",params);
        } catch (Exception e) {
            e.printStackTrace();
            map.put("MSGTY","F");
            map.put("MSAGE", e.getMessage());
            map.put("RESULT","异常错误失败");
            map.put("PARAMS",params);
        }
        return map;

    }

    private void doQuerySku(String token, List<String> skuEcodes) throws JsonProcessingException {
        baseBean.writeLog("查询商品信息：" + skuEcodes);
        String queryApi = Util.null2String(new BaseBean().getPropValue("oms_configuration","skuQueryApi"));
        String result = HttpRequest
                .post(OMSUtils.OMS_OMS_URL + queryApi)
                .header("r3-api-token",token)
                .body(skuEcodes.toString()).execute().body();
        JSONObject resultJson = JSONObject.parseObject(result);
        new BaseBean().writeLog( "请求返回="+result);
        if(resultJson.getInteger("code") != 0){
            throw new RuntimeException("查询商品接口报错: "+ resultJson.getString("message"));
        }
        JSONObject data = resultJson.getJSONObject("data");
        if (null == data) {
            throw new RuntimeException("查询商品接口返回数据为空");
        }
        JSONArray proSkuList = data.getJSONArray("proSkuList");
        if (null == proSkuList) {
            throw new RuntimeException("所选商品编码在oms系统不存在，请检查");
        }
        List<String> dbSkuEcodes = Lists.newArrayList();
        for (int i = 0; i < proSkuList.size(); i++) {
            JSONObject jsonObject = proSkuList.getJSONObject(i);
            String psCProEcode = jsonObject.getString("PS_C_PRO_ECODE");
            dbSkuEcodes.add(psCProEcode);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, LinkedHashMap<String,Object>> proAttributeMap = objectMapper.readValue(jsonObject.getString("PRO_ATTRIBUTE_MAP"), Map.class);
            if (proAttributeMap.containsKey("M_DIM2_ID")) {
                LinkedHashMap<String, Object> mDim2IdMap = proAttributeMap.get("M_DIM2_ID");
                if (Lists.newArrayList("10800", "10801","10802").contains(mDim2IdMap.get("ecode"))) {
                    throw new RuntimeException("所选商品编码:"+psCProEcode+"为奶卡或周期商品，请检查");
                }
            }
        }
        if (!new HashSet<>(dbSkuEcodes).containsAll(skuEcodes)) {
            skuEcodes.removeAll(dbSkuEcodes);
            throw new RuntimeException("所选商品编码:" +skuEcodes.toString()+"在oms系统不存在，请检查");
        }
    }

    public static void main(String[] args) throws JsonProcessingException {

        List<String> skuCodes = Lists.newArrayList("104000000003","11","22");
        String result = HttpRequest
                .post("http://**************:21180/api/ip/oa/sku/query")
                .header("r3-api-token",getOmsToken())
                .body(skuCodes.toString()).execute().body();
        System.out.println(result);
        JSONObject resultJson = JSONObject.parseObject(result);
        if(resultJson.getInteger("code") != 0){
            throw new RuntimeException("查询商品接口报错: "+ resultJson.getString("message"));
        }
        JSONObject data = resultJson.getJSONObject("data");
        if (null == data) {
            throw new RuntimeException("查询商品接口返回数据为空");
        }
        JSONArray proSkuList = data.getJSONArray("proSkuList");
        if (null == proSkuList) {
            throw new RuntimeException("所选商品编码在oms系统不存在，请检查");
        }
        List<String> dbSkuEcodes = Lists.newArrayList();
        for (int i = 0; i < proSkuList.size(); i++) {
            JSONObject jsonObject = proSkuList.getJSONObject(i);
            String psCProEcode = jsonObject.getString("PS_C_PRO_ECODE");
            dbSkuEcodes.add(psCProEcode);
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, LinkedHashMap<String,Object>> proAttributeMap = objectMapper.readValue(jsonObject.getString("PRO_ATTRIBUTE_MAP"), Map.class);
            if (proAttributeMap.containsKey("M_DIM2_ID")) {
                LinkedHashMap<String, Object> mDim2IdMap = proAttributeMap.get("M_DIM2_ID");
                if (Lists.newArrayList("10800", "10801","10802").contains(mDim2IdMap.get("ecode"))) {
                    throw new RuntimeException("所选商品编码:"+psCProEcode+"为奶卡或周期商品，请检查");
                }
            }
        }
        if (!new HashSet<>(dbSkuEcodes).containsAll(skuCodes)) {
            skuCodes.removeAll(dbSkuEcodes);
            throw new RuntimeException("所选商品编码:" +skuCodes.toString()+"在oms系统不存在，请检查");
        }
    }

    public static String getOmsToken(){
        String accessToken = "";
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("userName", "erp");

            paramMap.put("userKey", "e10adc3949ba59abbe56e057f20f883e");
            paramMap.put("requestSign", "cd70c798a580c5dbdd765690ab90b05b");
            String httpResponse = HttpRequest.get("http://**************:21180/api/auth/login").form(paramMap).execute().body();
            if(!"".equals(httpResponse)){
                JSONObject responseJson = JSONObject.parseObject(httpResponse);
                boolean successFlag = responseJson.getBoolean("success");
                if(successFlag){
                    accessToken = responseJson.getString("loginToken");
                }
            }
            return accessToken;
        }catch (Exception e){
            e.printStackTrace();
        }
        return accessToken;
    }
}

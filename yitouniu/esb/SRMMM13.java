package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;
import yitouniu.util.SRMUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: Moss
 * @Description: 资产申购接口    资产购置流程
 * @Date: Create in 16:56 2021/10/25
 * @Modified By:
 */
public class SRMMM13 {
    public Map executeParam(Map params){
        Map map = new HashMap();

        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
            JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
            List<JSONObject> itemFileList = (List<JSONObject>) jsonObject.get("ITEM");

            JSONObject inJson = new JSONObject();

            JSONObject dataJson = new JSONObject();
            JSONArray itemArr = new JSONArray();

            dataJson.put("VBELN_OA",dataObject.getString("VBELN_OA"));
            dataJson.put("NAME1",dataObject.getString("NAME1"));
            dataJson.put("AFNAM",dataObject.getString("AFNAM"));
            dataJson.put("WERKS",dataObject.getString("WERKS"));

            for(JSONObject object : itemFileList) {
                JSONObject itemJson = new JSONObject();

                itemJson.put("TXZ01",object.getString("TXZ01"));
                itemJson.put("MEINS",object.getString("MEINS"));
                itemJson.put("MENGE",object.getString("MENGE"));
                itemJson.put("ANLN1",object.getString("ANLN1"));
                itemJson.put("AFNAM",object.getString("AFNAM"));
                itemJson.put("WERKS",object.getString("WERKS"));

                itemArr.add(itemJson);

            }

            inJson.put("CTRL",ctrlObject);
            inJson.put("DATA",dataJson);
            inJson.put("ITEM",itemArr);

            new BaseBean().writeLog("最终传入参数："+inJson);
            //推送到SRM
            String result = SRMUtil.pushSRM(inJson);
            new BaseBean().writeLog("以下为result："+result);
            JSONObject resultObject = JSONObject.parseObject(result);
            JSONObject outObject = (JSONObject) resultObject.get("OUT_JSON");
            JSONObject ctrlResultObject = (JSONObject) outObject.get("CTRL");
//            JSONObject dataObject2 = (JSONObject) outObject.get("DATA");
            String successFlag = ctrlResultObject.getString("success");
            String messageFlag = ctrlResultObject.getString("message");
            new BaseBean().writeLog("返回message："+messageFlag);

            if(successFlag.equals("true")){
                map.put("MSGTY","S");
                map.put("MSAGE","推送成功");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",inJson);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","推送失败");
                map.put("RESULT",result);
                map.put("PARAMS",params);
                map.put("IN_JSON",inJson);
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","推送失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
            map.put("IN_JSON","");
        }
        return map;
    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.TimeUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: haiyang
 * @Date: 2024-11-26 11:01
 * @Desc:
 */
public class MilkCardApplyOutWarehouseESBV3 extends ReturnMsgToSAP {
    BaseBean baseBean = new BaseBean();
    public Map executeParam(Map params) {
        baseBean.writeLog("MilkCardApplyOutWarehouseESB.param:" + params);
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray dataArr = jsonObject.getJSONArray("DATA");
        List<JSONObject> dataListObject = JSONObject.parseArray(dataArr.toJSONString(), JSONObject.class);

        // 流程编号
        String lcbh = ctrlObject.getString("OAID");
        // 过账日期
        String gzrq = ctrlObject.getString("BUDAT");
        // 移动类型 默认都是201
        String ydlx = ctrlObject.getString("ZTYP");
        JSONObject reqJSON = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(lcbh);
        List<JSONObject> dataListReq = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataListObject)) {
            JSONObject dataReq = new JSONObject();
            dataReq.put("OAID", lcbh);
            dataReq.put("ZTYP", ydlx);
            dataReq.put("BUDAT", gzrq);
            List<JSONObject> itemList = Lists.newArrayList();
            for (JSONObject object : dataListObject) {
                JSONObject itemObject = new JSONObject();
                // 物料编号
                itemObject.put("MATNR", object.getString("MATNR"));
                // 基本计量单位
                itemObject.put("MEINS", object.getString("MEINS"));
                // 工厂
                itemObject.put("WERKS", object.getString("WERKS"));
                // 库位
                itemObject.put("LGORT", object.getString("LGORT"));
                // 数量
                itemObject.put("MENGE", object.getString("MENGE"));
                // 成本中心
                itemObject.put("KOSTL", object.getString("KOSTL"));
                itemList.add(itemObject);
            }
            dataReq.put("ITEM", itemList);
            dataListReq.add(dataReq);
        }

        reqJSON.put("CTRL", ctrlReq);
        reqJSON.put("DATA", dataListReq);
        baseBean.writeLog("requestSapParam:" + reqJSON.toJSONString());
        Map executeResult = this.execute(reqJSON.toJSONString());
        return executeResult;
    }


    private static JSONObject getCtrlReq(String lcbh){
        JSONObject ctrlReq = new JSONObject();
        Date date = new Date();
        ctrlReq.put("SYSID", "OA");
        ctrlReq.put("REVID", "SAP");
        ctrlReq.put("FUNID", "ZOAINF006");
        ctrlReq.put("INFID", lcbh + System.currentTimeMillis());
        ctrlReq.put("UNAME", "sysadmin");
        ctrlReq.put("DATUM", TimeUtils.getTimeStr(date, "yyyy-MM-dd"));
        ctrlReq.put("UZEIT", TimeUtils.getTimeStr(date, "HH:mm:ss"));
        ctrlReq.put("KEYID", lcbh);

        return ctrlReq;
    }


}

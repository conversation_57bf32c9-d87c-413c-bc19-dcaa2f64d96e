package yitouniu.esb;

import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

/**
 * @Author: <PERSON>
 * @Version 1.0.0
 * @Description: 采购合同
 * @Date: Create in 16:22 2022/5/24
 */
public class PurchaseContractCalibrationControl {

    //正式
//    public final static String dataBaseName = "formtable_main_461";
//    public final static String detailDataBaseName = "formtable_main_461_dt1";
    //测试
//    public final static String dataBaseName = "formtable_main_411";
//    public final static String detailDataBaseName = "formtable_main_411_dt1";

    public final static String associatedContractLog = "关联相关合同未归档，需等关联相关合同走完后流程才能继续流转。";


    public Map executeParam(Map params) {
        Map map = new HashMap();

        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject dataObject = (JSONObject) jsonObject.get("DATA");
            String requestId = dataObject.getString("requestId");
            String htqyfdm = dataObject.getString("htqyfdm");
            String htqyfdmNew = null;
            String contractSourceType = dataObject.getString("contractSourceType");
            String glxght = dataObject.getString("glxght");
            String formId = dataObject.getString("formId");
            //多个流程共用一个接口，前台传递formid，拼接上去
            String dataBaseName = "formtable_main_"+formId.replace("-","");
            String currentnodetype = null;
            boolean flag = false;
            if(!"00".equals(htqyfdm.substring(0,2))){
                htqyfdmNew = "00" + Integer.parseInt(htqyfdm);
                RecordSet rs = new RecordSet();
                rs.executeUpdate("update "+dataBaseName+" set htqyfdm = ? where requestid = ?", htqyfdmNew, requestId);
            }
            if("1".equals(contractSourceType)){
                RecordSet rs1 = new RecordSet();
                String sql = "select currentnodetype from workflow_requestbase where requestid = ?";
                rs1.executeQuery(sql,glxght);
                if(rs1.next()){
                    currentnodetype = rs1.getString("currentnodetype");
                }
                if(!"3".equals(currentnodetype)){
                    RecordSet rs2 = new RecordSet();
                    rs2.executeUpdate("update "+dataBaseName+" set logOut  = ? where requestid = ?", associatedContractLog, requestId);
                } else{
                    flag = true;
                }
            }else{
                flag = true;
            }
            if(flag){
                map.put("MSGTY", "S");
                map.put("MSAGE", "流程id为 "+requestId+" 的合同的补充合同 "+glxght+" 节点状态为 " +currentnodetype);
                map.put("htqyfdm", "合同签约方代码 修改前= "+htqyfdm+" 修改后= " +htqyfdmNew);
            } else{
                map.put("MSGTY", "F");
                map.put("MSAGE", "流程id为 "+requestId+" 的合同的补充合同 "+glxght+" 节点状态为 " +currentnodetype);
                map.put("htqyfdm", "合同签约方代码 修改前= "+htqyfdm+" 修改后= " +htqyfdmNew);
            }
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "接口异常错误");
            map.put("htqyfdm", "");
            StackTraceElement ste = e.getStackTrace()[0];
            new BaseBean().writeLog("PurchaseContractCalibrationControl接口异常错误："+e);
            new BaseBean().writeLog("PurchaseContractCalibrationControl接口异常错误ste："+ste);
        }
        return map;

    }

}


package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.weaver.esb.client.EsbClient;
import com.weaver.esb.spi.EsbService;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import yitouniu.util.CDMSUtil;
import yitouniu.util.SRMUtil;

import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * OA返回审批结果给SRM
 */

public class ReturnMsgToSRM {

    public Map executeParam(Map params){
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlobject = (JSONObject) jsonObject.get("CTRL");
        JSONObject databject = (JSONObject) jsonObject.get("DATA");
        String ZOAID = databject.getString("ZOAID") == null ? "":databject.getString("ZOAID");
        String AGREE = databject.getString("AGREE") == null ? "":databject.getString("AGREE");
        String OPINION = databject.getString("OPINION") == null ? "":databject.getString("OPINION");
        String TYPE = databject.getString("TYPE") == null ? "":databject.getString("TYPE");
        String XJTYPE = databject.getString("XJTYPE") == null ? "":databject.getString("XJTYPE");
        String ZSRMID = databject.getString("ZSRMID") == null ? "":databject.getString("ZSRMID");
        String CONTRACTID = databject.getString("CONTRACTID") == null ? "":databject.getString("CONTRACTID");
        JSONObject dataJSON = new JSONObject();
        if(AGREE.length()>0){    //同意归档
            new BaseBean().writeLog("同意归档");
            dataJSON.put("AGREE",AGREE);
            dataJSON.put("OPINION",OPINION);
            dataJSON.put("TYPE",TYPE);
            //报价结果签报中使用，返回询价方式
            if(TYPE.equals("SRMMM08")){
                dataJSON.put("XJTYPE",XJTYPE);
            } else {
                dataJSON.put("XJTYPE","");
            }
            dataJSON.put("ZOAID",ZOAID);
            dataJSON.put("ZSRMID",ZSRMID);
            //合同结果报批中使用，返回合同编号
            if(TYPE.equals("SRMMM10")){
                dataJSON.put("CONTRACTID",CONTRACTID);
            } else{
                dataJSON.put("CONTRACTID","");
            }
        }else{   //退回
            new BaseBean().writeLog("退回");
            RecordSet recordSet = new RecordSet();
            String sql = "select top 1 * from workflow_requestlog wr " +
                    "where wr.requestid = ? " +
                    "order by wr.nodeid desc ";
            recordSet.executeQuery(sql,ZOAID);
            new BaseBean().writeLog(sql);
            if(recordSet.next()){
                dataJSON.put("AGREE",recordSet.getString("logtype"));
                dataJSON.put("OPINION",recordSet.getString("remark"));
                dataJSON.put("TYPE",TYPE);
                if(TYPE.equals("SRMMM08")){
                    dataJSON.put("XJTYPE",XJTYPE);
                } else {
                    dataJSON.put("XJTYPE","");
                }
                dataJSON.put("ZOAID",ZOAID);
                dataJSON.put("ZSRMID",ZSRMID);
                dataJSON.put("CONTRACTID",CONTRACTID);
            }
        }
        JSONObject jsonObject2  = new JSONObject();
        jsonObject2.put("CTRL",ctrlobject);
        jsonObject2.put("DATA",dataJSON);
        new BaseBean().writeLog("入参："+jsonObject2);

        String result = SRMUtil.pushSRM(jsonObject2);
        new BaseBean().writeLog("以下为result："+result);
        JSONObject resultObject = JSONObject.parseObject(result);
        JSONObject outObject = (JSONObject) resultObject.get("OUT_JSON");
        JSONObject ctrlObject2 = (JSONObject) outObject.get("CTRL");
        String successflag = ctrlObject2.getString("success");
        String messageflag = ctrlObject2.getString("message");
        if(successflag.equals("true")){
            map.put("MSGTY","S");
            map.put("MSAGE","推送成功");
            map.put("RESULT",result);
            map.put("IN_JSON",jsonObject2);
        } else {
            map.put("MSGTY","F");
            map.put("MSAGE","推送失败");
            map.put("RESULT",result);
            map.put("IN_JSON",jsonObject2);
        }
        return map;
    }

}

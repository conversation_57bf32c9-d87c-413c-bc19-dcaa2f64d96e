package yitouniu.esb;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import weaver.conn.RecordSet;
import yitouniu.util.ActionUtil;
import yitouniu.util.StdUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * OA创建退货单
 *
 * <AUTHOR>
 * @since 2024-09-24 09:39
 */
public class STD004ESB_V1 {
    private final Log LOG = LogFactory.getLog(STD004ESB_V1.class);

    private static final String PUSH_RETURNED_GOODS_URL = "/crm-dms/v1/external/std/pushReturnedGoods";

    public Map execute(Map<String, Object> params) {
        return executeParam(JSON.toJSONString(params));
    }

    public Map executeParam(String params) {
        LOG.info("OA调用STD创建退货单接口-开始,入参:" + params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        Map<String, Object> retMap = new HashMap<>();

        JSONObject dataObject = jsonObject == null ? null : jsonObject.getJSONObject("DATA");
        JSONObject ctrlObject = jsonObject == null ? null : jsonObject.getJSONObject("CTRL");
        if (dataObject == null || ctrlObject == null) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "参数不能为空");
            return retMap;
        }

        String requestId = ctrlObject.getString("requestId"); // 获取requestid
        if (StringUtils.isBlank(requestId)) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "requestId不能为空");
            return retMap;
        }
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String) tableNameByRequestId.get("tableName");
        if (StringUtils.isBlank(tableName) || StringUtils.isBlank(tableName)) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "表名不能为空");
            return retMap;
        }
        Integer mainId = queryMainId(tableName, requestId);
        if (Objects.isNull(mainId)) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "获取流程ID为空");
            return retMap;
        }

        JSONArray itemArray = dataObject.getJSONArray("productList");
        /*只处理未成功过的明细*/
        List<JSONObject> itemList = IntStream.range(0, itemArray.size())
                .mapToObj(itemArray::getJSONObject)
                .filter(item -> ObjectUtil.notEqual(item.getString("cgbs"), "S"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemList)) {
            retMap.put("MSGTY", "F");
            retMap.put("MSAGE", "需要推送的明细列表为空");
            return retMap;
        }

        /*明细唯一标识*/
        List<String> wybsList = itemList.stream()
                .map(item -> item.getString("mxhwybs")).collect(Collectors.toList());
        /*根据物流承运商分组，推送STD*/
        Map<String, List<JSONObject>> codeListMap = itemList.stream()
                .collect(Collectors.groupingBy(row ->
                        row.getString("logisticsCarrierCode")
                                + "_" + row.getString("logisticsCarrierName")
                                + "_" + Optional.ofNullable(row.getString("logisticsCode")).orElse("").trim()));

        String isAllSuccess = "S";
        String msg = "回调成功";
        Map<String, JSONObject> pushRetMap = new HashMap<>();
        for (Map.Entry<String, List<JSONObject>> codeListEntry : codeListMap.entrySet()) {
            List<String> wybsArrayList = codeListEntry.getValue().stream()
                    .map(o -> o.getString("mxhwybs")).collect(Collectors.toList());

            try {
                boolean isSuccess = push2StdAndUpdateSuccess(wybsArrayList, dataObject, codeListEntry.getValue(), tableName, mainId, pushRetMap);
                if (!isSuccess) {
                    isAllSuccess = "F";
                    msg = "有明细行处理失败";
                }
            } catch (Exception e) {
                LOG.info("STD004ESB.executeParam,推送STD失败,物流承运商编码:"
                        + codeListEntry.getKey() + "异常信息：" + Throwables.getStackTraceAsString(e));

                for (String wybs : wybsArrayList) {
                    JSONObject object = pushRetMap.get(wybs);
                    if (Objects.nonNull(object)) {
                        pushRetMap.put(wybs, new JSONObject()
                                .fluentPut("mxhwybs", wybs)
                                .fluentPut("cgbs", "F")
                                /*.fluentPut("thdbm", thdbm)*/
                                .fluentPut("fhxx", e.getMessage()));
                    }
                }
            }
        }

        /*返回给前端*/

        // 只有成功的明细才会被返回
//        retMap.put("RESULT", JSON.toJSONString(pushRetMap));
        JSONArray pushRetArray = new JSONArray();
        pushRetArray.addAll(pushRetMap.values());
        retMap.put("RESULT", pushRetArray);

        retMap.put("MSGTY", isAllSuccess);
        retMap.put("MSAGE", msg);

        LOG.info("OA调用STD创建退货单接口-完成，入参:" + params + "，结果：" + JSON.toJSONString(retMap));
        return retMap;
    }

    private Integer queryMainId(String tableName, String requestId) {
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT id FROM " + tableName + " WHERE requestid = ?";
        recordSet.executeQuery(sql, requestId);
        if (recordSet.next()) {
            return recordSet.getInt("id");
        }

        return null;
    }

    /**
     * 推送STD 成功时变更明细表推送状态
     *
     * @param wybsList   明细唯一标识集合
     * @param dataObject 主表对象
     * @param itemList   明细对象集合
     * @param tableName  表名
     * @param mainId     流程ID
     * @param pushRetMap 成功的明细推送结果
     */
    private boolean push2StdAndUpdateSuccess(List<String> wybsList, JSONObject dataObject, List<JSONObject> itemList,
                                             String tableName, Integer mainId, Map<String, JSONObject> pushRetMap) {
        JSONObject paramObj = convertParam(dataObject, itemList);
        String retJsonStr;
        String fhxx = "操作成功！";
        String thdbm = "";
        String cgbs = "S";
        try {
            retJsonStr = StdUtil.executePost(PUSH_RETURNED_GOODS_URL, paramObj.toJSONString());
            JSONObject jsonRet = JSONObject.parseObject(retJsonStr);
            thdbm = jsonRet == null ? null : jsonRet.getString("returnedGoodsCode");
            if (StringUtils.isEmpty(thdbm)) {
                throw new RuntimeException("推送返回的退货单编码为空!");
            }
        } catch (Exception e) {
            fhxx = e.getMessage();
            cgbs = "E";
            LOG.warn("STD004ESB.push2StdAndUpdateSuccess,推送STD失败,异常信息：" + Throwables.getStackTraceAsString(e));
        }

        for (String wybs : wybsList) {
            pushRetMap.put(wybs, new JSONObject()
                    .fluentPut("mxhwybs", wybs)
                    .fluentPut("cgbs", cgbs)
                    .fluentPut("thdbm", thdbm)
                    .fluentPut("fhxx", fhxx));
        }

        /*
        1、明细行唯一标识字段 ------mxhwybs
        2、成功标识字段：E  S -------cgbs
        3、返回信息  ------fhxx
        4、退货单编码------thdbm
        */
        boolean executeUpdate = false;
        try {
            String wybsString = wybsList.stream()
                    .filter(StringUtils::isNotBlank).map(o -> "'" + o + "'").collect(Collectors.joining(","));
            String sql = "UPDATE " + tableName + "_dt1  SET thdbm = ?,cgbs = ?,fhxx = ?,xgsj = ? " +
                    " WHERE mainId = ? AND cgbs <> 'S' AND mxhwybs IN (" + wybsString + ")";
            String modifyTime = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            executeUpdate = new RecordSet().executeUpdate(sql, thdbm, cgbs, fhxx, modifyTime, mainId);
        } catch (Exception e) {
            LOG.warn("执行推送后更新OA推送状态失败,异常信息：" + Throwables.getStackTraceAsString(e));
            throw e;
        }
        if (!executeUpdate) {
            throw new RuntimeException("执行推送后更新OA推送状态失败!");
        }

        return "S".equals(cgbs);
    }


    /**
     * 物流承运商信息从明细放到主表
     *
     * @param dataObject 主表对象
     * @param itemList   明细对象集合
     * @return JSONObject 组装后的结果
     */
    private JSONObject convertParam(JSONObject dataObject, List<JSONObject> itemList) {
        JSONObject retObj = JSONObject.parseObject(dataObject.toJSONString());
        retObj.remove("productList");
        retObj.put("logisticsCarrierCode", itemList.get(0).getString("logisticsCarrierCode"));
        retObj.put("logisticsCarrierName", itemList.get(0).getString("logisticsCarrierName"));
        if (StringUtils.isNotEmpty(itemList.get(0).getString("logisticsCode"))) {
            retObj.put("logisticsCode", itemList.get(0).getString("logisticsCode").trim());
        }

        JSONArray productList = new JSONArray();
        for (JSONObject item : itemList) {
            /*不移除也可以的*/
            item.remove("logisticsCarrierCode");
            item.remove("logisticsCarrierName");
            item.remove("logisticsCode");
            item.remove("mxhwybs");
            productList.add(item);
        }

        retObj.put("productList", productList);
        return retObj;
    }
}

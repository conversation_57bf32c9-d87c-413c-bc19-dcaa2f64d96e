package yitouniu.esb;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LivepatDetailBeforeSqrNode {    //直播预付表2退回  申请人操作前
    private RecordSet rs = new RecordSet();

    public Map executeParam(String params){
        String requestId = "";
        String requestId1 = "";
        String lcjd = "";

        Map map = new HashMap();
        new BaseBean().writeLog("11111111111");

        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            JSONObject idata = (JSONObject) jsonObject.get("DATA");
            new BaseBean().writeLog("以下为data:"+idata.size());
            new BaseBean().writeLog("以下为data:"+idata);

            requestId = idata.getString("requestId");
            requestId1 = idata.getString("glhtlc");
            lcjd = idata.getString("lcjd");

            rs.executeQuery("select zqye,kyje,zje,yfje from live_pay_plan where requestId = ?", requestId1);
            if(rs.next()){
                String zqye1 = rs.getString("zqye");
                if(Double.parseDouble(zqye1) == 0.00){
                    map.put("MSGTY","F");
                    map.put("RESULT","该直播付款计划已过期失效");
                }else{
                    List<List> updateAllData = new ArrayList<>();
                    List updateData =  new ArrayList<>();
                    updateData.add(requestId1);
                    updateAllData.add(updateData);
                    RecordSet rs2 = new RecordSet();
                    boolean b2 = rs2.executeBatchSql("update live_pay_plan set djje = 0 where requestId = ? ",updateAllData);
                    String zje = rs.getString("zje");
                    String yfje = rs.getString("yfje");
                    String kyje = String.valueOf(Double.parseDouble(zje)-0.00-Double.parseDouble(yfje));
                    String zqye = kyje;
                    List<List> updateAllData2 = new ArrayList<>();
                    List updateData2 =  new ArrayList<>();
                    updateData2.add(kyje);
                    updateData2.add(zqye);
                    updateData2.add(requestId1);
                    updateAllData2.add(updateData2);
                    RecordSet rs3 = new RecordSet();
                    boolean b3 = rs3.executeBatchSql("update live_pay_plan set kyje = ?,zqye = ? where requestId = ? ",updateAllData2);

                    List<List> updateAllData3 = new ArrayList<>();
                    List updateData3 =  new ArrayList<>();
                    updateData3.add(lcjd);
                    updateData3.add(requestId);
                    updateAllData3.add(updateData3);
                    RecordSet rs4 = new RecordSet();
                    boolean b4 = rs4.executeBatchSql("update live_pay_detail set lcjd = ? where requestId = ? ",updateAllData3);
                    if(b2 && b3 && b4){
                        map.put("MSGTY", "S");
                        map.put("MSAGE", "成功");
                    }else{
                        map.put("MSGTY", "F");
                        map.put("MSAGE", "可用金额更新失败");
                    }

                }

            }else{
                map.put("MSGTY","F");
                map.put("RESULT","关联合同不存在");
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
        }
        return map;
    }
}

package yitouniu.esb;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * @Author: Moss
 * @Description:
 * @Date: Create in 15:25 2021/12/14
 * @Modified By:
 */
public class FileDownToServerByName {
    public Map executeParam(Map params) {

        String workflowId = (String) params.get("workflowid");
        //流程所属数据库表的名字
        String fileFieldName = (String) params.get("fileFieldName");
        //开始时间
        String startTime = (String) params.get("startTime");
        //结束时间
        String endTime = (String) params.get("endTime");

        new BaseBean().writeLog("FileDownToServer日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        RecordSet rs1 = new RecordSet();
        //同一套流程不同版本的话会有不同的workflowid，这里只会查到一个，workflowid是唯一的
        //可以查到不同版本号下的完结状态流程的的附件
        String sqlWorkflowId = "select workflowname,formid from workflow_base where id = ?";
        rs1.executeQuery(sqlWorkflowId,workflowId);
        String formid = null;
        while (rs1.next()){
            //去除前面的-号
            formid = rs1.getString("formid").replaceAll("[^(0-9)]","");
        }
        final String ProcessDatabase = "formtable_main_" + formid;
        new BaseBean().writeLog("FileDownToServer日志:ProcessDatabase:" + ProcessDatabase);

        List<String> finishStatusList = new ArrayList();
        RecordSet rs2 = new RecordSet();
        //只取流程状态为3（归档）的流程的附件
        String sqlRequestId = "select requestid from workflow_requestbase where workflowid = ? and currentnodetype = 3 ";
        if(startTime != null && startTime.length() > 0){
            sqlRequestId += " and createdate >= '"+startTime+"' ";
        }
        if(endTime != null && endTime.length() > 0){
            sqlRequestId += " and createdate <= '"+endTime+"' ";
        }

        new BaseBean().writeLog("FileDownToServer日志:sqlRequestId = " + sqlRequestId);

        rs2.executeQuery(sqlRequestId,workflowId);
        while (rs2.next()) {
            finishStatusList.add(rs2.getString("requestid"));
        }
        Map<String, Object> result = new HashMap<>();
        //fj的字段名字让林张波自己填
        String sqlFj = "SELECT "+fileFieldName+" FROM "+ProcessDatabase+" where requestid = ?";
        //附件id的存放List
        List<String> FjList = new ArrayList<>();
        for(String requestId : finishStatusList) {
            RecordSet recordSet = new RecordSet();
            recordSet.executeQuery(sqlFj,requestId);
            while (recordSet.next()){
                String[] arr = recordSet.getString(fileFieldName).split(",");
                FjList.addAll(Arrays.asList(arr));
            }
        }
//        String descDir = "D:\\ProcessFileDown\\"+ProcessDatabase+"\\";
        String descDir = "D:\\oa\\oa附件流程文件\\"+workflowId+"\\"+startTime+"_"+endTime+"\\";
        File pathFile = new File(descDir);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        try{
            for(String str : FjList) {
                if (str.length() > 0) {
                    RecordSet rd = new RecordSet();
                    String sql = " SELECT ImgF.filerealpath,ImgF.imagefilename" +
                            " FROM imagefile ImgF " +
                            " LEFT JOIN docimagefile DIF ON DIF.imagefileid = ImgF.imagefileid " +
                            " WHERE DIF.docid = ? ";
                    rd.executeQuery(sql, str);
                    if (rd.next()) {
                        new BaseBean().writeLog("开始转换附件或图片：" + str);
                        File zipFile = new File(rd.getString("filerealpath"));
                        String realFileName = rd.getString("imagefilename");

                        if(zipFile.exists()){
                            ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
                            for (Enumeration<? extends ZipEntry> enumeration = zip.entries(); enumeration.hasMoreElements(); ) {
                                ZipEntry zipEntry = enumeration.nextElement();
                                String zipEntryName = zipEntry.getName();
                                new BaseBean().writeLog("打印zipEntryName：" + zipEntryName);
                                InputStream in = zip.getInputStream(zipEntry);
                                String outPath = descDir;
                                new BaseBean().writeLog("打印outPath：" + outPath);

                                String fileRealPath = descDir + realFileName;
                                FileOutputStream out = new FileOutputStream(new File(fileRealPath));
                                byte[] buf = new byte[1024];
                                int len;
                                while ((len = in.read(buf)) > 0) {
                                    out.write(buf, 0, len);
                                }
                                in.close();
                                out.close();
                            }
                        }
                        new BaseBean().writeLog("解压转换完毕");
                    }
                }
            }
            if(FjList.size()!=0) {
                result.put("msg", "1");
                result.put("list",FjList);
            } else{
                result.put("msg","0");
                result.put("list","该流程workflowid下没有归档状态的流程的附件");
            }
        }catch (Exception e){
            result.put("msg","0");
            result.put("list",e);
        }
        return result;
    }

}

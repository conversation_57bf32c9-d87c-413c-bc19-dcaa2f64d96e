package yitouniu.esb;

import com.alibaba.fastjson.JSONObject;
import org.json.XML;
import weaver.conn.RecordSet;
import yitouniu.util.ActionUtil;
import yitouniu.util.FtpClientUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 电子回单
 */
public class DZHDDataESB {

    private static RecordSet rs = new RecordSet();


    public static void execute(Map param) {
        Map map = new HashMap();
        FtpClientUtil ftpClientUtil = new FtpClientUtil("115.238.39.2", 1021, "YTN", "YTN1");;
        try {


            String requestid = (String) param.get("requestid"); // 流程id
            String nodtecode = (String) param.get("NOTECODE"); // 资金系统返回流水号

            // 通过接口获取电子路径
            String dzhdParams = "{\n" +
                    "\t\"notecode\": \"" + nodtecode + "\"\n" +
                    "}";
            String dzhd = ActionUtil.esb("dzhd", dzhdParams, "out"); // 获取esb中电子回单数据
            String dzhdXmlJsonString = xmlToString(dzhd); // 结果xml转为json
            JSONObject jsonObject = JSONObject.parseObject(dzhdXmlJsonString);
            JSONObject mbs = (JSONObject) jsonObject.get("MBS");
            JSONObject resp = (JSONObject) mbs.get("resp");
            JSONObject list = (JSONObject) resp.get("list");
            String creatDoc = "";
            if (list.containsKey("detail")) {


                JSONObject detail = (JSONObject) list.get("detail");
                //String filepath = detail.getString("filepath");
                String filename = detail.getString("filepath")+"/"+detail.getString("filename");

                // 创建ftp连接


                // 获取的附件转为base64编码
                String encode = ftpClientUtil.download("/"+filename);
                String[] split = filename.split("/");

                /**
                 * 创建文档接口
                 */
                // 获取session
                String loginParam = "{\n" +
                        "\t\"in0\": \"ceshi\",\n" +
                        "\t\"in2\": \"0\",\n" +
                        "\t\"in1\": \"1\",\n" +
                        "\t\"in3\": \"127.0.0.1\"\n" +
                        "}";
                String loginResult = ActionUtil.esb("login", loginParam, "out");
                // 创建文档
                String createDocParam = "{\n" +
                        "\t\"in0\": {\n" +
                        "\t\t\"attachments\": {\n" +
                        "\t\t\t\"DocAttachment\": [\n" +
                        "\t\t\t\t{\n" +
                        "\t\t\t\t\t\"filecontent\": \"" + new String(encode) + "\",\n" +
                        "\t\t\t\t\t\"filename\": \""+split[split.length - 1]+"\"\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t]\n" +
                        "\t\t},\n" +
                        "\t\t\"docStatus\": \"1\",\n" +
                        "\t\t\"docSubject\": \"" + split[split.length - 1] + "\",\n" +
                        "\t\t\"docType\": \"2\",\n" +
                        "\t\t\"doccontent\": \"\",\n" +
                        "\t\t\"doccreaterid\": \"1\",\n" +
                        "\t\t\"doccreatertype\": \"0\",\n" +
                        "\t\t\"ownerid\": \"1\",\n" +
                        "\t\t\"seccategory\": \"290\"\n" +
                        "\t},\n" +
                        "\t\"in1\": \"" + loginResult + "\"\n" +
                        "}";
                creatDoc = ActionUtil.esb("CreatDoc", createDocParam, "out");

            }
            if (creatDoc != null && !"".equals(creatDoc)) {

                if (Integer.parseInt(creatDoc) > 0) {
                    insertDoc(requestid, creatDoc);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ftpClientUtil.closeFtp();
        }

    }


    /**
     * xml格式转化为json
     *
     * @param xml
     * @return
     */
    public static String xmlToString(String xml) {
        //将xml转为json
        org.json.JSONObject xmlJSONObj = null;
        try {
            xmlJSONObj = XML.toJSONObject(xml);

        } catch (org.json.JSONException e) {
            e.printStackTrace();
        }
        //设置缩进
        String jsonPrettyPrintString = xmlJSONObj.toString();
        //输出格式化后的json
        return jsonPrettyPrintString;
    }

    public static void insertDoc(String requestid, String docId) {
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestid);
        String tableName = (String) tableNameByRequestId.get("tableName");
        rs.executeUpdate("update " + tableName + " set dzhd = ? where  requestid = ?", docId, requestid);

    }

}

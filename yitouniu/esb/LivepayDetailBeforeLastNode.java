package yitouniu.esb;

import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LivepayDetailBeforeLastNode {     //直播预付表2  归档节点操作前  数据更新

    public Map executeParam(String params){
        String requestId = "";
        String requestId1 = "";
        String lcjd = "";
        String lcbh = "";
        String bqfkje = "";

        Map map = new HashMap();
        new BaseBean().writeLog("11111111111");

        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            JSONObject idata = (JSONObject) jsonObject.get("DATA");
            new BaseBean().writeLog("以下为data:"+idata.size());
            new BaseBean().writeLog("以下为data:"+idata);
            //List<JSONObject> data = (List<JSONObject>) jsonObject.get("data");
            //JSONObject idata = data.get(0);
            requestId = idata.getString("requestId");
            requestId1 = idata.getString("glhtlc");
            lcjd = idata.getString("lcjd");
            lcbh = idata.getString("lcbh");
            bqfkje = idata.getString("fkze");

            RecordSet rs = new RecordSet();
            List<List> updateAllData = new ArrayList<>();
            List updateData =  new ArrayList<>();
            updateData.add(lcjd);
            updateData.add(requestId);
            updateAllData.add(updateData);
            boolean b = rs.executeBatchSql("update live_pay_detail set lcjd = ? where requestId = ? ",updateAllData);

            if(b){
                RecordSet rs2 = new RecordSet();
                rs2.executeQuery("select sum(WRBTR) as sum from live_pay_detail where lcjd = 56 and requestId1 = ?",requestId1);
                String yfje = "";
                if(rs2.next()){
                    new BaseBean().writeLog(rs2.getString("sum"));
                    if(rs2.getString("sum") == null){
                        new BaseBean().writeLog("为空");
                        yfje = "0";
                    }else{
                        new BaseBean().writeLog("不为空");
                        yfje = rs2.getString("sum");
                    }

                }else{
                    yfje = "0";
                }

                RecordSet rs3 = new RecordSet();
                rs3.executeQuery("select zje,lcbh from live_pay_plan where requestId = ?" ,requestId1);
                if(rs3.next()){
                    String zje = rs3.getString("zje");
                    String lcbh1 = rs3.getString("lcbh");
                    String kyje = String.valueOf(Double.parseDouble(zje) - 0.00 - Double.parseDouble(yfje));
                    String zqye = kyje;

                    RecordSet rs4 = new RecordSet();
                    List<List> updateAllData2 = new ArrayList<>();
                    List updateData2 =  new ArrayList<>();
                    updateData2.add(kyje);
                    updateData2.add(zqye);
                    updateData2.add(yfje);
                    updateData2.add(requestId1);
                    updateAllData2.add(updateData2);
                    boolean b2 = rs4.executeBatchSql("update live_pay_plan set djje = 0,kyje = ?,zqye = ?,yfje = ? where requestId = ? ",updateAllData2);

                    List<List> insertAllData = new ArrayList<>();
                    List insertData =  new ArrayList<>();
                    insertData.add(requestId1);
                    insertData.add(lcbh1);
                    insertData.add(requestId);
                    insertData.add(lcbh);
                    insertData.add(zje);
                    insertData.add(yfje);
                    insertData.add(bqfkje);
                    insertAllData.add(insertData);
                    RecordSet rs5 = new RecordSet();
                    rs5.executeBatchSql("insert into live_pay_record (requestId1,lcbh1,requestId2,lcbh2,zje,yfje,bqfkje) values (?,?,?,?,?,?,?)",insertAllData);


                    if(b2){
                        map.put("MSGTY", "S");
                        map.put("MSAGE", "成功");
                    }else{
                        map.put("MSGTY", "F");
                        map.put("MSAGE", "失败");
                    }

                }else{
                    map.put("MSGTY", "F");
                    map.put("MSAGE", "不存在该直播付款计划");
                }

            }else{
                map.put("MSGTY", "F");
                map.put("MSAGE", "数据更新失败");
            }

        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
        }
        return map;
    }

}

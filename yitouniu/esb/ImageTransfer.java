package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class ImageTransfer {

    static BASE64Encoder encoder = new sun.misc.BASE64Encoder();

    public Map executeParam(String fc) throws IOException {   //传入附件字段 fjsc
        Map map = new HashMap();
        try{
            List<String> list = Arrays.asList(fc.split(","));
            List PICXTList = new ArrayList();
            List PICFLList = new ArrayList();
            for(String i : list){
                if(i.length()>0){
                    RecordSet rs = new RecordSet();
                    rs.executeQuery("select filerealpath  from ImageFile a left join DocImageFile b  on a.imagefileid =b.imagefileid " +
                            " where b.docid= ? ",i);
                    if(rs.next()){
                        new BaseBean().writeLog("开始转换图片："+i);
                        File zipFile = new File(rs.getString("filerealpath"));
                        String descDir = "D:\\SRMImage\\";
                        File pathFile = new File(descDir);
                        if(!pathFile.exists())
                        {
                            pathFile.mkdirs();
                        }
                        ZipFile zip = new ZipFile(zipFile, Charset.forName("GBK"));
                        for(Enumeration entries = zip.entries(); entries.hasMoreElements();)
                        {
                            ZipEntry entry = (ZipEntry)entries.nextElement();
                            String zipEntryName = entry.getName();
                            new BaseBean().writeLog("打印zipEntryName："+zipEntryName);
                            InputStream in = zip.getInputStream(entry);
                            String outPath = (descDir+zipEntryName).replaceAll("\\*", "/");
                            new BaseBean().writeLog("打印outPath："+outPath);
                            //判断路径是否存在,不存在则创建文件路径
                            File file = new File(outPath);
                            if(!file.exists())
                            {
                                file.mkdir();
                            }
                            File file1 = new File(descDir+zipEntryName+"\\"+zipEntryName+".jpg");
                            file1.createNewFile();
                            OutputStream out = new FileOutputStream(file1);
                            byte[] buf1 = new byte[1024];
                            int len;
                            while((len=in.read(buf1))>0)
                            {
                                out.write(buf1,0,len);
                            }
                            in.close();
                            out.close();
                            String outPath2 = (descDir+zipEntryName+"\\"+zipEntryName+".jpg").replaceAll("\\*", "/");
                            String imageio= getImageBinary(outPath2);
                            PICXTList.add(zipEntryName+".jpg");
                            PICFLList.add(imageio);
                        }
                        new BaseBean().writeLog("解压转换完毕");
                    }
                }
            }
            map.put("MSGTY","S");
            map.put("RESULT","成功");
            map.put("PICXT",PICXTList);
            map.put("PICFL",PICFLList);
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",e);
            map.put("PICXT","");
            map.put("PICFL","");
        }
        return map;
    }

    /**
     * 将图片转换成二进制
     * @return
     */
    static String getImageBinary(String path){
        File f = new File(path);
        BufferedImage bi;
        try {
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);
            byte[] bytes = baos.toByteArray();
            return encoder.encodeBuffer(bytes).trim();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }


}

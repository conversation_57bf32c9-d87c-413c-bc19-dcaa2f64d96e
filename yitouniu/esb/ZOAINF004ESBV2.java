package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/12/26
 */
public class ZOAINF004ESBV2 {

    private static final BaseBean LOGGER = new BaseBean();

    public Map<Object, Object> execute(String params) {

        LOGGER.writeLog("ZOAINF004ESB,参数 ===> " + params);

        if (!StringUtils.isNotBlank(params)) {
            return genResult("E", "请查看流程参数是否正确");
        }

        String resp; // 调用sap返回结果
        try {
            resp = SAPUtil.execute(modifyParams(params));
            LOGGER.writeLog("ZOAINF004ESB调用sap返回结果 ===> " + resp);
        }
        catch (Exception e) {
            LOGGER.writeLog("ZOAINF004ESB执行异常");
            e.printStackTrace();
            return genResult("E", "SAP执行异常");
        }

        if (StringUtils.isBlank(resp)) {
            return genResult("E", "SAP响应失败");
        }

        JSONObject jsonObject = JSON.parseObject(resp);
//        JSONObject data = jsonObject.getJSONObject("CTRL");
//        JSONArray DATA = jsonObject.getJSONArray("DATA");
//
//        Map<Object, Object> objectObjectMap = genResult(data.getString("MSGTY"), data.getString("MSAGE"));
//        objectObjectMap.put("KUNNR", DATA.getString("KUNNR"));
//        objectObjectMap.put("ZPX", DATA.getString("ZPX"));

        JSONArray data = jsonObject.getJSONArray("DATA");

        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject1 = data.getJSONObject(i);
            if ("S".equals(jsonObject1.getString("MSGTY"))) {
                jsonObject1.put("status", "S");
            } else {
                jsonObject1.put("status", "E");
            }
        }
        Map<Object, Object> respJ = new HashMap<>();
        respJ.put("CTRL", jsonObject.getJSONObject("CTRL"));
        respJ.put("DATA", data);
        return respJ;
    }

    /**
     * 校验必填参数
     *
     * @param params
     * @return
     */
    private boolean paramsCheck(String params) {
        JSONObject CTRL = JSON.parseObject(params).getJSONObject("CTRL");
        JSONArray DATA = JSON.parseObject(params).getJSONArray("DATA");

        if (null == CTRL || null == DATA)
            return Boolean.FALSE;
        else return Boolean.TRUE;
    }

    private String modifyParams(String params) {
        JSONObject paramsJsonObject = JSON.parseObject(params);
        JSONObject ctrl = paramsJsonObject.getJSONObject("CTRL");
        JSONArray data = paramsJsonObject.getJSONArray("DATA");

        if (null != data) {
            JSONArray dataArr = new JSONArray();
            for (int i = 0; i < data.size(); i++) {
                JSONObject jsonObject = data.getJSONObject(i);
                String status = jsonObject.getString("status");
                if ("E".equals(status)) {
                    dataArr.add(jsonObject);
                }
            }
            paramsJsonObject.put("DATA", dataArr);
        }

        paramsJsonObject.put("CTRL", ctrl);

        String jsonString = paramsJsonObject.toJSONString();

        LOGGER.writeLog("ZOAINF004ESB,请求SAP参数 ===> " + jsonString);

        return jsonString;
    }

    /**
     * @param success S-成功，E-失败
     * @param msg
     * @return
     */
    private Map<Object, Object> genResult(String success, String msg) {

        Map<Object, Object> respMap = new HashMap<>();
        respMap.put("MSGTY", success);
        respMap.put("MSAGE", msg);

        return respMap;
    }
}

package yitouniu.esb;


import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.soa.workflow.request.RequestInfo;
import weaver.soa.workflow.request.RequestService;
import yitouniu.util.SendDDMsgUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class ReturnPaymentStatus {

    private RecordSet recordSet = new RecordSet();

    public Map execute(Map params) {

        Map map = new HashMap();
        try {
            new BaseBean().writeLog("执行");
            // 获取参数
            String worknum = (String) params.get("worknum");
            String notecode = (String) params.get("notecode");
            String paystate = (String) params.get("paystate");
            String payinfo = (String) params.get("payInfo");
            String paymadedate = (String) params.get("paymadedate");
            String banknum = (String) params.get("banknum");
            String tableName = getTableNameByRequestId(worknum);
            new BaseBean().writeLog(tableName);
            if ("0".equals(tableName)) { // 无此编号返回信息
                map.put("result", "error");
                map.put("MSAGE", "无此流程编号");
                return map;
            }
            if ("".equals(banknum)||banknum==null) { // 无此编号返回信息
                map.put("result", "error");
                map.put("MSAGE", "银行科目不能为空");
                return map;
            }
            new  BaseBean().writeLog("资金返盘开始: "+worknum);
            // 更新表单数据
            String updateSql = "update " + tableName + " set NOTECODE = ?,PAYSTATE=?,PAYINFO=?,PAYMADEDATE=?,BANKNUM =? where lcbh = ?";
            boolean b = recordSet.executeUpdate(updateSql, notecode, paystate, payinfo, paymadedate, banknum, worknum);
            new BaseBean().writeLog("sql语句："+updateSql);
            if (b) {
                Map requestMap = getRequestId(worknum); // 获取requestid
                String request_id = (String) requestMap.get("requestid");
                new BaseBean().writeLog("111111");
                new BaseBean().writeLog(requestMap.get("requestid"));
                new BaseBean().writeLog(request_id);
                int requestid = Integer.parseInt(request_id);
                new BaseBean().writeLog(requestid);
                String  currentnodetype = (String) requestMap.get("currentnodetype");
                if (!"3".equals(currentnodetype)) {


                    map.put("result", "success");
                    RequestService requestService = new RequestService();
                    // 更新成功到达下个节点并且已支付
                    RequestInfo request = requestService.getRequest(requestid);

                    boolean b1 = requestService.nextNodeBySubmit(request, requestid, 1, "自动提交");
                    // 流程强制归档
                    // new WfForceOver().doForceOver();
                    if (b1) {
                        map.put("MSAGE", "支付完成,流程提交下个节点");

                        // 提交下个节点推送第二笔凭证
                        String userId = getUserId(request_id);
                        boolean b2 = requestService.nextNodeBySubmit(request, requestid, Integer.parseInt(userId), "自动提交");
                        if (b2) {
                            map.put("MSAGE", "支付完成,流程提交下个节点");
                        } else {
                            // 发送钉钉消息

                            Set<String> userIdList = new HashSet<>(); //接收人id
                            String title = "财务流程提交失败提醒"; //标题
                            String linkUrl = "http://47.114.168.77:8088/spa/workflow/static4form/index.html?_rdm=1581304078723#/main/workflow/req?requestid=" + requestid; //PC端链接 纯文本就传空字符串
                            String linkMobileUrl = "http://47.114.168.77:8088/spa/workflow/static4form/index.html?_rdm=1581304078723#/main/workflow/req?requestid=" + requestid; //移动端链接 纯文本就传空字符串
                            String context = "编号为的" + worknum + "财务流程自动提交失败,请及时处理"; //内容

                            userIdList.add(userId);
                            SendDDMsgUtil.send(569, userIdList, title, context, linkUrl, linkMobileUrl);
                            map.put("MSAGE", "支付完成,流程提交下个节点失败");
                        }

                    } else {
                        map.put("MSAGE", "支付完成,流程提交下个节点失败");
                    }
                }else{
                    map.put("result", "success");
                    map.put("MSAGE", "流程已归档");
                    return map;
                }
                if ("3".equals(paystate)||"6".equals(paystate)){ // 发送钉钉消息
                    map.put("result", "success");
                    map.put("MSAGE", "支付失败");
                    Set<String> allUserId = getAllUserId(requestid + "");
                    sendDDMsg(requestid,worknum,paystate,allUserId);
                }

            } else {
                map.put("result", "false");
                map.put("MSAGE", "更新失败");
            }
            return map;
        } catch (Exception e) {
            map.put("result", "fail");
            map.put("MSAGE", "异常错误信息: " + e);
            return map;
        }


    }

    /**
     * 获取表明
     *
     * @param worknum 流程编号
     * @return
     */
    public String getTableNameByRequestId(String worknum) {
        String selectSql = "select * from WORKFLOW_BILL where id = (" +
                "select formid from WORKFLOW_BASE where id = (" +
                "select WORKFLOWID from WORKFLOW_REQUESTBASE where requestmark = ?))";
        recordSet.executeQuery(selectSql, worknum);
        String tableName = "0";
        while (recordSet.next()) {
            tableName = recordSet.getString("TABLENAME");

        }

        return tableName;
    }

    public void sendDDMsg(int requestid,String lcbh,String status,Set<String> userIdList) {
        String type = "";
        if ("3".equals(status)){
            type = "支付失败";
        }else{
            type = "出现退票";
        }
        // Set<String> userIdList = new HashSet<>(); //接收人id
        String title = "财务流程支付失败或退票提醒"; //标题
        String linkUrl = "http://47.114.168.77:8088/spa/workflow/static4form/index.html?_rdm=1581304078723#/main/workflow/req?requestid="+requestid; //PC端链接 纯文本就传空字符串
        String linkMobileUrl = "http://47.114.168.77:8088/spa/workflow/static4form/index.html?_rdm=1581304078723#/main/workflow/req?requestid="+requestid; //移动端链接 纯文本就传空字符串
        String context = "编号为的"+lcbh+"财务流程"+type+",请到流程查看支付返回信息,如有需要请重新发起流程!"; //内容

        // userIdList.add(createid);
        SendDDMsgUtil.send(569, userIdList, title, context, linkUrl, linkMobileUrl);

    }


    /**
     * 获取requestid 和创建人
     *
     * @param worknum
     * @return
     */
    public Map getRequestId(String worknum) {
        Map map = new HashMap();
        String selectSql = "select * from WORKFLOW_REQUESTBASE where requestmark = ?";
        recordSet.executeQuery(selectSql, worknum);
        while (recordSet.next()) {
            map.put("requestid", recordSet.getString("requestid"));
            map.put("creater", recordSet.getString("creater"));
            map.put("currentnodetype", recordSet.getString("currentnodetype"));

        }

        return map;
    }

    /**
     * 当前节点操作人
     * @param requestid
     * @return
     */
    public String getUserId(String  requestid) {

        RecordSet recordSet = new RecordSet();
        String sql = "select  a.userid  from workflow_currentoperator a INNER JOIN  workflow_requestbase b on a.requestid = b.requestid and a.nodeid = b.CURRENTNODEID  where a.requestid = ? and a.isremark = 0";

        recordSet.executeQuery(sql,requestid);
        String userid = "";
        if (recordSet.next()){
            userid = recordSet.getString("userid");
        }
        return userid;
    }

    /**
     * 所有节点操作人
     * @param requestid
     * @return
     */
    public Set<String> getAllUserId(String  requestid) {

        RecordSet recordSet = new RecordSet();
        String sql = "select  userid  from workflow_currentoperator where  requestid = ?";
        Set<String> userIdList = new HashSet<>(); //接收人id
        recordSet.executeQuery(sql,requestid);
        while (recordSet.next()){
            userIdList.add(recordSet.getString("userid")) ;
        }
        return userIdList;
    }
}

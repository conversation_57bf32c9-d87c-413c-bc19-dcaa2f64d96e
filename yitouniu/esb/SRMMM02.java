package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.CDMSUtil;
import yitouniu.util.SRMUtil;

import java.io.IOException;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

public class SRMMM02{       //OA读取SRM物料分类主数据

    //正式
    final String dataBaseName = "uf_srm_wl_category";
    //测试
//    final String dataBaseName = "srm_wl_category";

    public Map executeParam(Map params) {
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        try {
            JSONObject jsonObject = JSONObject.parseObject(execute);
//            new BaseBean().writeLog("以下为jsonObject："+jsonObject);

            String result = SRMUtil.pushSRM(jsonObject);
//            new BaseBean().writeLog("以下为result："+result);

            JSONObject jsonObject2 = JSONObject.parseObject(result);
            JSONObject outObject = (JSONObject) jsonObject2.get("OUT_JSON");
            JSONObject ctrlObject = (JSONObject) outObject.get("CTRL");
            String successflag = ctrlObject.getString("success");
            String messageflag = ctrlObject.getString("message");

            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();

            if(successflag.equals("true")){
                List<JSONObject> dataList = (List<JSONObject>) outObject.get("DATA");
                for(int i = 0; i <dataList.size(); i++){
                    JSONObject object = dataList.get(i);
                    List insertData =  new ArrayList<>();
                    List updateData =  new ArrayList<>();

                    String cclasscode = object.getString("cclasscode");
                    String cclassname = object.getString("cclassname");
                    String cfclasscode = object.getString("cfclasscode");
                    String cfclassname = object.getString("cfclassname");
                    String cquotation = object.getString("cquotation");
                    String dcreatetime = object.getString("dcreatetime");
                    String cstates = object.getString("cstates");
                    String ilast = object.getString("ilast");
                    String cpurchase = object.getString("cpurchase");
                    String dmodifytime = object.getString("dmodifytime");
                    String cmodifyuser = object.getString("cmodifyuser");

                    boolean b = selectWLFL(cclasscode);
                    if(b){
                        updateData.add(cclassname);
                        updateData.add(cfclasscode);
                        updateData.add(cfclassname);
                        updateData.add(cquotation);
                        updateData.add(dcreatetime);
                        updateData.add(cstates);
                        updateData.add(ilast);
                        updateData.add(cpurchase);
                        updateData.add(dmodifytime);
                        updateData.add(cmodifyuser);
                        updateData.add(formatDate.replaceAll( "-",""));
                        updateData.add(cclasscode);
                        updateAllData.add(updateData);
                    }else{
                        insertData.add(cclasscode);
                        insertData.add(cclassname);
                        insertData.add(cfclasscode);
                        insertData.add(cfclassname);
                        insertData.add(cquotation);
                        insertData.add(dcreatetime);
                        insertData.add(cstates);
                        insertData.add(ilast);
                        insertData.add(cpurchase);
                        insertData.add(dmodifytime);
                        insertData.add(cmodifyuser);
                        insertData.add(formatDate.replaceAll( "-",""));
                        insertAllData.add(insertData);
                    }

                }

                if(insertAllData.size()>0){
                    flag +=  insertWLFL(insertAllData) + ",";

                }

                if(updateAllData.size()>0){
                    flag +=  updateWLFL(updateAllData) + ",";

                }

                if (flag.indexOf("false") != -1) {
                    map.put("MSGTY","F");
                    map.put("MSAGE","处理数据失败");
                    map.put("RESULT",result);
                    map.put("PARAMS",params);
                }else{
                    map.put("MSGTY","S");
                    map.put("MSAGE","处理数据成功");
                    map.put("RESULT",result);
                    map.put("PARAMS",params);
                }
            }else if(successflag.equals("false") && messageflag.equals("Not Found DataTable")){
                map.put("MSGTY","S");
                map.put("MSAGE","成功，无增量数据");
                map.put("RESULT",result);
                map.put("PARAMS",params);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","读取失败");
                map.put("RESULT",result);
                map.put("PARAMS",params);
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
        }
        return map;
    }


    public boolean selectWLFL(String cclasscode) {
        RecordSet rs1 = new RecordSet();
        rs1.executeQuery("select * from "+dataBaseName+" where cclasscode =? ", cclasscode);
        return rs1.next();
    }

    public boolean updateWLFL(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("update "+dataBaseName+" set cclassname =?,cfclasscode=?,cfclassname=?,cquotation=?,dcreatetime= ?,cstates=?,ilast=?,cpurchase=?,dmodifytime=?,cmodifyuser=?,readtime=? where cclasscode =? ",list);

        return b;
    }

    public boolean insertWLFL(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("insert into "+dataBaseName+" (cclasscode, cclassname, cfclasscode, cfclassname,cquotation,dcreatetime,cstates,ilast,cpurchase,dmodifytime,cmodifyuser,readtime) values (?,?,?,?,?,?,?,?,?,?,?,?) ",list);
        return b;
    }

}

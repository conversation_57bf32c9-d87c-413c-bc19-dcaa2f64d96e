package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.util.CDMSUtil;
import yitouniu.util.SRMUtil;

import java.io.IOException;
import java.sql.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

public class SRMMM03 {     //OA读取SRM物料主数据

    public final static String dataBaseName = "uf_srm_wl";

    public final static String dataBaseSapName = "uf_KCXGWLZSJSY";

    RecordSet rs = new RecordSet();

    public Map executeParam(Map params) {
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        String formatDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
        try {
            JSONObject jsonObject = JSONObject.parseObject(execute);
            new BaseBean().writeLog("以下为SRMMM03jsonObject："+jsonObject);
            String result = SRMUtil.pushSRM(jsonObject);
            JSONObject jsonObject2 = JSONObject.parseObject(result);
            JSONObject outObject = (JSONObject) jsonObject2.get("OUT_JSON");
            JSONObject ctrlObject = (JSONObject) outObject.get("CTRL");
            String successflag = ctrlObject.getString("success");
            String messageflag = ctrlObject.getString("message");

            String flag = "";
            List<List> insertAllData = new ArrayList<>();
            List<List> updateAllData = new ArrayList<>();
            List<List> insertAllSapData = new ArrayList<>();
            List<List> updateAllSapData = new ArrayList<>();


            if(successflag.equals("true")){
                List<JSONObject> dataList = (List<JSONObject>) outObject.get("DATA");
                new BaseBean().writeLog("SRMMM03：executeParam：本次拉取的数量为："+dataList.size());
                for(int i = 0; i <dataList.size(); i++){
                    JSONObject object = dataList.get(i);
                    List insertData =  new ArrayList<>();
                    List updateData =  new ArrayList<>();
                    List insertSapData =  new ArrayList<>();
                    List updateSapData =  new ArrayList<>();

                    String cclasscode = object.getString("cclasscode");
                    String cfactorycodedep = object.getString("cfactorycodedep");
                    String cclassname = object.getString("cclassname");
                    String cpurchases = object.getString("cpurchases");
                    String cinvcode = object.getString("cinvcode");
                    String cinvname = object.getString("cinvname");
                    String cinvdescribe = object.getString("cinvdescribe");
                    String cpasturecode = object.getString("cpasturecode");
                    String cpasturename = object.getString("cpasturename");
                    String cpurchaseinadv = object.getString("cpurchaseinadv");
                    String cinvstetas = object.getString("cinvstetas");
                    String cunit = object.getString("cunit");
                    String cunitCode = null;
                    rs.executeQuery("select MEINS from uf_jbdwb where MSEH3 = ?", cunit);
                    while (rs.next()){
                        cunitCode = rs.getString("MEINS");
                    }

                    String dcreatetime = object.getString("dcreatetime");
                    String cremark = object.getString("cremark");
                    String iprice = object.getString("iprice");
                    String csapinvgroup = object.getString("csapinvgroup");
                    String cinvmodel = object.getString("cinvmodel");
                    String cbrand = object.getString("cbrand");
                    String csupinvcode = object.getString("csupinvcode");
                    String dmodifytime = object.getString("dmodifytime");
                    String cmodifyuser = object.getString("cmodifyuser");
                    String readtime = object.getString("readtime");

                    String csrminvcode = object.getString("csrminvcode");
                    String iminbatch = object.getString("iminbatch");
                    String ccategory = object.getString("ccategory");//费用-1，物料-2
                    String cunitcode= object.getString("cunitcode");
                    String cinvtype = object.getString("cinvtype");
                    String cinvtypeId = "";
                    if("物料".equals(cinvtype)){
                        cinvtypeId = "0";
                    } else if("资产".equals(cinvtype)){
                        cinvtypeId = "1";
                    } else if("费用".equals(cinvtype)){
                        cinvtypeId = "2";
                    } else if("服务".equals(cinvtype)){
                        cinvtypeId = "3";
                    } else {
                        cinvtypeId = "";
                    }
                    int id = selectWL(cinvcode,cpasturecode);
                    if(id != -1){
                        updateData.add(cclasscode);
                        updateData.add(cfactorycodedep);
                        updateData.add(cclassname);
                        updateData.add(cpurchases);
                        updateData.add(cinvname);
                        updateData.add(cinvdescribe);
                        updateData.add(cpasturename);
                        updateData.add(cpurchaseinadv);
                        updateData.add(cinvstetas);
                        updateData.add(cunit);
                        updateData.add(dcreatetime);
                        updateData.add(cremark);
                        updateData.add(iprice);
                        updateData.add(csapinvgroup);
                        updateData.add(cinvmodel);
                        updateData.add(cbrand);
                        updateData.add(csupinvcode);
                        updateData.add(dmodifytime);
                        updateData.add(cmodifyuser);
                        updateData.add(csrminvcode);
                        updateData.add(iminbatch);
                        updateData.add(ccategory);
                        updateData.add(cunitcode);
                        updateData.add(cinvtype);
                        updateData.add(cinvtypeId);

                        updateData.add(formatDate.replaceAll( "-",""));

                        updateData.add(id);
                        updateAllData.add(updateData);
                    } else{
                        insertData.add(cclasscode);
                        insertData.add(cfactorycodedep);
                        insertData.add(cclassname);
                        insertData.add(cpurchases);
                        insertData.add(cinvcode);
                        insertData.add(cinvname);
                        insertData.add(cinvdescribe);
                        insertData.add(cpasturecode);
                        insertData.add(cpasturename);
                        insertData.add(cpurchaseinadv);
                        insertData.add(cinvstetas);
                        insertData.add(cunit);
                        insertData.add(dcreatetime);
                        insertData.add(cremark);
                        insertData.add(iprice);
                        insertData.add(csapinvgroup);
                        insertData.add(cinvmodel);
                        insertData.add(cbrand);
                        insertData.add(csupinvcode);
                        insertData.add(dmodifytime);
                        insertData.add(cmodifyuser);
                        insertData.add(csrminvcode);
                        insertData.add(iminbatch);
                        insertData.add(ccategory);
                        insertData.add(cunitcode);
                        insertData.add(cinvtype);
                        insertData.add(cinvtypeId);

                        insertData.add(formatDate.replaceAll( "-",""));
                        insertAllData.add(insertData);
                    }

                    if("3400".equals(cpasturecode) || "4200".equals(cpasturecode)){
                        int idSap = selectWLSap(cinvcode,cpasturecode);
                        if(idSap != -1){
                            updateSapData.add(cclasscode);
                            updateSapData.add(cclassname);
                            updateSapData.add(cinvname);
                            updateSapData.add(cpasturename);

                            updateSapData.add(cunitCode);

                            updateSapData.add(csapinvgroup);
                            updateSapData.add(cinvmodel);

                            updateSapData.add(idSap);
                            updateAllSapData.add(updateSapData);
                        } else {
                            insertSapData.add(cclasscode);
                            insertSapData.add(cclassname);
                            insertSapData.add(cinvcode);
                            insertSapData.add(cinvname);
                            insertSapData.add(cpasturecode);
                            insertSapData.add(cpasturename);

                            insertSapData.add(cunitCode);

                            insertSapData.add(csapinvgroup);
                            insertSapData.add(cinvmodel);
                            insertAllSapData.add(insertSapData);
                        }

                    }

                }

                if(insertAllData.size()>0){
                    List<List<List>> insertDataList =split(insertAllData, 1000);
                    for(List<List> i : insertDataList){
                        flag = insertWL(i) + ",";
                        new BaseBean().writeLog("SRMMM03:flag = {}"+flag);
                        new BaseBean().writeLog("SRMMM03:insertWL:insertDataList.size() = "+insertDataList.size());
                        new BaseBean().writeLog("SRMMM03:insertWL:i.size() = "+i.size());
                        new BaseBean().writeLog("SRMMM03:insertWL:i = "+i);
                    }
                } else {
                    new BaseBean().writeLog("SRMMM03:insertWL 无数据");
                }
                if(updateAllData.size()>0){
                    List<List<List>> updateDataList =split(updateAllData, 1000);
                    for(List<List> i : updateDataList){
                        flag = updateWL(i) + ",";
                        new BaseBean().writeLog("SRMMM03:flag = "+flag);
                        new BaseBean().writeLog("SRMMM03:updateWL:updateDataList.size() = "+updateDataList.size());
                        new BaseBean().writeLog("SRMMM03:updateWL:i.size() = "+i.size());
                        new BaseBean().writeLog("SRMMM03:updateWL:i = "+i);
                    }
                } else {
                    new BaseBean().writeLog("SRMMM03:updateWL 无数据");
                }
                if(insertAllSapData.size()>0){
                    List<List<List>> updateDataList =split(insertAllSapData, 1000);
                    for(List<List> i : updateDataList){
                        flag = insertWLSap(i) + ",";
                        new BaseBean().writeLog("SRMMM03:flag = "+flag);
                        new BaseBean().writeLog("SRMMM03:updateSapWL:updateDataList.size() = "+updateDataList.size());
                        new BaseBean().writeLog("SRMMM03:updateSapWL:i.size() = "+i.size());
                        new BaseBean().writeLog("SRMMM03:updateSapWL:i = "+i);
                    }
                } else {
                    new BaseBean().writeLog("SRMMM03:insertSapWL 无数据");
                }
                if(updateAllData.size()>0){
                    List<List<List>> updateDataList =split(updateAllSapData, 1000);
                    for(List<List> i : updateDataList){
                        flag = updateWLSap(i) + ",";
                        new BaseBean().writeLog("SRMMM03:flag = "+flag);
                        new BaseBean().writeLog("SRMMM03:updateSapWL:updateDataList.size() = "+updateDataList.size());
                        new BaseBean().writeLog("SRMMM03:updateSapWL:i.size() = "+i.size());
                        new BaseBean().writeLog("SRMMM03:updateSapWL:i = "+i);
                    }
                } else {
                    new BaseBean().writeLog("SRMMM03:updateSapWL 无数据");
                }

                if (flag.contains("false")) {
                    map.put("MSGTY","F");
                    map.put("MSAGE","处理数据失败");
                    map.put("RESULT",result);
                    map.put("PARAMS",params);
                }else{
                    map.put("MSGTY","S");
                    map.put("MSAGE","处理数据成功");
                    map.put("RESULT",result);
                    map.put("PARAMS",params);
                }
            }else if("false".equals(successflag) && "Not Found DataTable".equals(messageflag)){
                map.put("MSGTY","S");
                map.put("MSAGE","成功，无增量数据");
                map.put("RESULT",result);
                map.put("PARAMS",params);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","读取失败");
                map.put("RESULT",result);
                map.put("PARAMS",params);
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误失败");
            map.put("RESULT",e);
            map.put("PARAMS",params);
        }
        return map;
    }


    public int selectWL(String cinvcode,String cpasturecode) {
        RecordSet rs1 = new RecordSet();
        int id = -1;
        rs1.executeQuery("select id from "+dataBaseName+" where cinvcode =? and cpasturecode = ?", cinvcode,cpasturecode);
        if(rs1.next()){
            id = rs1.getInt("id");
        }
        return id;
    }

    public int selectWLSap(String cinvcode,String cpasturecode) {
        RecordSet rs1 = new RecordSet();
        int id = -1;
        rs1.executeQuery("select id from "+dataBaseSapName+" where MATNR =? and WERKS = ?", cinvcode,cpasturecode);
        if(rs1.next()){
            id = rs1.getInt("id");
        }
        return id;
    }

    public boolean updateWL(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("update "+dataBaseName+" set cclasscode =?,cfactorycodedep=?,cclassname=?,cpurchases=?,cinvname=?,cinvdescribe=?,cpasturename=?,cpurchaseinadv=?,cinvstetas=?,cunit=?,dcreatetime=?,cremark=?,iprice=?,csapinvgroup=?,cinvmodel=?,cbrand=?,csupinvcode=?,dmodifytime=?,cmodifyuser=?,csrminvcode=?,iminbatch=?,ccategory=?,cunitcode=?,cinvtype=?,cinvtype_id=?,readtime=? where id = ?",list);
        return b;
    }

    public boolean insertWL(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("insert into "+dataBaseName+" (cclasscode, cfactorycodedep, cclassname, cpurchases,cinvcode,cinvname,cinvdescribe,cpasturecode,cpasturename,cpurchaseinadv,cinvstetas,cunit,dcreatetime,cremark,iprice,csapinvgroup,cinvmodel,cbrand,csupinvcode,dmodifytime,cmodifyuser,csrminvcode,iminbatch,ccategory,cunitcode,cinvtype,cinvtype_id,readtime) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ",list);
        return b;
    }

    public boolean insertWLSap(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("insert into "+dataBaseSapName+" (cclasscode, cclassname,MATNR,MAKTX,WERKS,NAME1,MEINS,MATKL,MODEL) values (?,?,?,?,?,?,?,?,?) ",list);
        return b;
    }

    public boolean updateWLSap(List<List> list) {
        RecordSet rs1 = new RecordSet();
        boolean b = rs1.executeBatchSql("update "+dataBaseSapName+" set cclasscode =?,cclassname=?,MAKTX=?,NAME1=?,MEINS=?,MATKL=?,MODEL=? where id = ?",list);
        return b;
    }



    public static List<List<List>> split(List dataList, int splitDataSize) {
        List<List<List>> resultList = new ArrayList();

        int listSize = dataList.size();
        if (listSize <= splitDataSize) {
            resultList.add(dataList);
        } else {
            DecimalFormat df = new DecimalFormat("0.00");
            int splitListCount = (int) Math.ceil(Double.parseDouble(df.format((float) listSize / splitDataSize)));
            for (int i = 0; i < splitListCount; i++) {
                int beginIndex = (i + 1 - 1) * splitDataSize;
                int endIndex = Math.min((i + 1) * splitDataSize, listSize);
                resultList.add(dataList.subList(beginIndex, endIndex));
            }
        }
        return resultList;
    }


//    public String

}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.SAPUtil;
import yitouniu.util.TimeUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: haiyang
 * @Date: 2025-01-15 09:48
 * @Desc:
 */
public class NewRetailCustomerPaymentEsbV4 extends ReturnMsgToSAP{

    BaseBean baseBean = new BaseBean();
    public Map executeParam(Map params) {
        baseBean.writeLog("NewRetailCustomerPaymentEsb.param:", params);
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray jsonArray = jsonObject.getJSONArray("DATA");
        List<JSONObject> dataListObject = JSONObject.parseArray(jsonArray.toJSONString(), JSONObject.class);
        // 流程编号
        String lcbh = ctrlObject.getString("lcbh");
        JSONObject reqJSON = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(lcbh);
        List<JSONObject> dataListReq = Lists.newArrayList();

        for (JSONObject object : dataListObject) {
            JSONObject dataObject = new JSONObject();
            dataObject.put("BUKRS", object.getString("BUKRS"));
            dataObject.put("KUNNR", object.getString("KUNNR"));
            dataObject.put("ZZDJSPC", object.getString("ZZDJSPC"));
            dataObject.put("ZZDGSFS", object.getString("ZZDGSFS"));
            dataObject.put("ZZDGSQJC1", object.getString("ZZDGSQJC1"));
            dataObject.put("ZZDGSQJZ1", object.getString("ZZDGSQJZ1"));
            dataObject.put("ZZDGSQJC2", object.getString("ZZDGSQJC2"));
            dataObject.put("ZZDGSQJZ2", object.getString("ZZDGSQJZ2"));
            dataObject.put("ZZDQDSJ1", object.getString("ZZDQDSJ1"));
            dataObject.put("ZQY1", object.getString("ZQY1"));
            dataObject.put("ZQR1", object.getString("ZQR1"));
            dataObject.put("ZZDQDSJ2", object.getString("ZZDQDSJ2"));
            dataObject.put("ZQY2", object.getString("ZQY2"));
            dataObject.put("ZQR2", object.getString("ZQR2"));
            dataObject.put("ZZDQDSJ3", object.getString("ZZDQDSJ3"));
            dataObject.put("ZQY3", object.getString("ZQY3"));
            dataObject.put("ZQR3", object.getString("ZQR3"));
            dataObject.put("ZJZR", object.getString("ZJZR"));
            dataObject.put("ZGDFKRLX", object.getString("ZGDFKRLX"));
            dataObject.put("ZGDFKR1", object.getString("ZGDFKR1"));
            dataObject.put("ZGDFKR2", object.getString("ZGDFKR2"));
            dataObject.put("ZGDFKR3", object.getString("ZGDFKR3"));
            // 是否工作日 1是 0否
            dataObject.put("ZSFGZR", object.getString("ZSFGZR"));

            dataListReq.add(dataObject);
        }



        reqJSON.put("CTRL", ctrlReq);
        reqJSON.put("DATA", dataListReq);
        baseBean.writeLog("newRetailCustomerPaymentRequestSapParam:" + reqJSON.toJSONString());
        Map executeResult = execute(reqJSON.toJSONString());
        return executeResult;
    }


    private static JSONObject getCtrlReq(String lcbh){
        JSONObject ctrlReq = new JSONObject();
        Date date = new Date();
        ctrlReq.put("SYSID", "OA");
        ctrlReq.put("REVID", "SAP");
        ctrlReq.put("FUNID", "ZOAINF007");
        ctrlReq.put("INFID", lcbh + System.currentTimeMillis());
        ctrlReq.put("UNAME", "sysadmin");
        ctrlReq.put("DATUM", TimeUtils.getTimeStr(date, "yyyy-MM-dd"));
        ctrlReq.put("UZEIT", TimeUtils.getTimeStr(date, "HH:mm:ss"));
        ctrlReq.put("KEYID", lcbh);

        return ctrlReq;
    }

    public Map execute(String params) {
        Map map = new HashMap();
        try {

            String execute = SAPUtil.execute(params); // 调用sap返回结果
            JSONObject jsonObject = JSONObject.parseObject(execute);
            if (jsonObject.containsKey("DATA")) {

                List<JSONObject> dataObject = (List<JSONObject>) jsonObject.get("DATA"); // 获取响应结果

                boolean isResult = true;  // 判断返回结果是否成功
                if (dataObject.size() > 0) {
                    for (JSONObject result : dataObject) {
                        String msgty = result.getString("MSGTY");
                        if ("S".equals(msgty)) {
                            isResult = true;
                        } else {
                            isResult = false;
                            break;
                        }
                    }
                }
                if (isResult) {
                    map.put("MSGTY", "S");
                    map.put("MSAGE", "成功");
                    map.put("RESULT", execute);
                    map.put("PARAMS", params);
                } else {
                    map.put("MSGTY", "F");
                    map.put("MSAGE", dataObject.get(0).getString("MSAGE"));
                    map.put("RESULT", execute);
                    map.put("PARAMS", params);
                }
                return map;
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "sap结构返回异常，无DATA结构");
                map.put("RESULT", execute);
                map.put("PARAMS", params);
                return map;
            }
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "请求sap接口异常");
            map.put("RESULT", e);
            map.put("PARAMS", params);
        }
        return map;
    }


}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.*;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @program: oa
 * @description: 数据支持流程同步tb
 * @author: haiyang
 * @create: 2023-08-29 17:44
 **/
public class DataSupportTaskToTbESBV4 {

    private final String projectId = "645e0091762f31f9b22c0e20";
    // 数据支持tag
    private static final String TAG_ID = "64618bb5733753b4c7949f47";


    Map<String, String> headers = TeambitionHttpToken.returnHeaders(TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID, TeambitionBaseConfig.APP_SECRET));

    public DataSupportTaskToTbESBV4() throws UnsupportedEncodingException {
    }

    public Map executeParam(Map params) {
        new BaseBean().writeLog("ESB-executeParam：请求参数" + JSON.toJSONString(params));

        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        Map map = new HashMap();
        // 标题
        String title = ctrlObject.getString("btwb");
        // 需求对接人id
        String demandDockingPersonId = ctrlObject.getString("oaxqdjr");
        // 需求申请时间
        String taskApplyDate = TimeUtils.getTimeStr(new Date(), "yyyy-MM-dd HH:mm:ss");
        // 期望解决时间
        String expectCompleteTime = ctrlObject.getString("clygwcsj") + " 18:30:00";
        // 申请人id
        String applicantId = ctrlObject.getString("sqr");
        // 自定义字段 - 应对方案
        String solution = ctrlObject.getString("sjmcdyzd");
        // 备注
        String remark = ctrlObject.getString("sqyy");
        new BaseBean().writeLog("ESB-DataSupportTaskToTbESB executor 111");


        // 查询执行者
        String executorId;
        String xqdjrLoginId = null;
        String xqdjrName = null;
        RecordSet rs = new RecordSet();
        String sql = "select lastname,loginid as loginId " +
                "from HrmResource " +
                "where id = ?";
        rs.executeQuery(sql, demandDockingPersonId);
        if (rs.next()) {
            xqdjrLoginId = rs.getString("loginId");
            xqdjrName = rs.getString("lastname");
        }

        String applyPeopleName = null;

        RecordSet rs1 = new RecordSet();
        String sql1 = "select lastname,loginid as loginId " +
                "from HrmResource " +
                "where id = ?";
        rs1.executeQuery(sql1, applicantId);
        if (rs1.next()) {
            applyPeopleName = rs1.getString("lastname");
        }


        executorId = TeambitionUtils.returnUserIdByName(xqdjrName, xqdjrLoginId, headers);
        headers.put("x-operator-id", executorId);
        new BaseBean().writeLog("ESB-DataSupportTaskToTbESB executorId: {}", executorId);

        Map taskTypes = TeambitionUtils.returnTaskTypes(projectId, this.headers);
        Map<String, String> taskTypeMap = (Map<String, String>) taskTypes.get("任务");
        new BaseBean().writeLog("ESB-DataSupportTaskToTbESB taskTypes: {}", JSON.toJSONString(taskTypes));

        JSONObject taskReq = new JSONObject();
        // 项目id
        taskReq.put("projectId", projectId);
        // 标题
        taskReq.put("content", title);
        // 执行人
        taskReq.put("executorId", executorId);
//        taskReq.put("startDate", taskApplyDate);
        taskReq.put("dueDate", expectCompleteTime);
        // 任务分组 - 无
//        taskReq.put("tasklistId", tasklistId);
        // 任务列id - 无
//        taskReq.put("stageId", stageId);
        // 备注
        taskReq.put("note", remark);
        taskReq.put("tagIds", Lists.newArrayList(TAG_ID));
        //自定义字段id
        JSONArray customfieldArr = assembleCustomFields(taskTypeMap, applyPeopleName, solution);
        taskReq.put("customfields", customfieldArr);

        try {
            //  1. 获取参数 + 构建请求
            new BaseBean().writeLog("ESB-DataSupportTaskToTbESB：请求参数" + JSON.toJSONString(taskReq));
            //  2. 接口调用
            String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfig.URL_API_TASK_CREATE_POST_V3, headers, taskReq);
            JSONObject resp = JSONObject.parseObject(httpResponse);
            new BaseBean().writeLog("ESB-DataSupportTaskToTbESB：主任务返回json" + httpResponse);
            String code = resp.getString("code");
            if (CommonConstant.SUCCESS_CODE.equals(code)) {
                map.put("MSGTY", "S");
                map.put("MSAGE", "创建任务成功，请到Teambition查看");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "任务请求失败");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
            }
            return map;
        }catch (Exception e) {
            map.put("MSGTY", "E");
            map.put("MSAGE", "异常错误失败");
            map.put("RESULT", e.getMessage());
            map.put("IN_JSON", JSON.toJSONString(params));
        }
        new BaseBean().writeLog("ESB-DataSupportTaskToTbESB-end");
        return map;

    }

    private JSONArray assembleCustomFields(Map<String, String> taskTypeMap, String applicant, String solution) {
        JSONArray customfieldArr = new JSONArray();
        for (Map.Entry<String, String> m : taskTypeMap.entrySet()) {
            new BaseBean().writeLog("ESB-DataSupportTaskToTbESB-taskType.entrySet()：" + m.getKey() + "。类型字段id为：" + m.getValue());
            JSONObject customfieldJson = new JSONObject();
            JSONArray value = new JSONArray();
            JSONObject valueJson = new JSONObject();
            if (!"templateId-taskflowId".contains(m.getKey())) {
                switch (m.getKey()) {
                    case "申请者":
                        valueJson.put("title", applicant);
                        break;
                    case "应对方案_":
                        valueJson.put("title", solution);
                        break;
                    default:
                        break;
                }
                customfieldJson.put("cfId", m.getValue());
                value.add(valueJson);
                customfieldJson.put("value", value);
            }
            if (!customfieldJson.isEmpty()) {
                customfieldArr.add(customfieldJson);
            }
        }
        return customfieldArr;
    }
}
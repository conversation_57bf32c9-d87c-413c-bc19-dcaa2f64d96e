package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.ActionUtil;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * @program: oa
 * @description: 新增供应商to Sap
 * @author: haiyang
 * @create: 2023-12-14 11:01
 **/
public class SyncSupplierToSAPESBV2 {

    RecordSet rs = new RecordSet();

    BaseBean baseBean = new BaseBean();


//    public static void main(String[] args) {
//        String str = "[{\"NAME_ORG1\":\"?\",\"BU_SORT1\":\"?\",\"NAME_ORG2\":\"?\",\"BU_GROUP\":\"?\",\"STCD5\":\"?\",\"NAME4\":\"?\",\"NAME_ORG4\":\"?\",\"SMTP_ADDR\":\"?\",\"MOB_NUMBER\":\"?\",\"LAND1\":\"?\",\"LANGU\":\"?\",\"REGIO\":\"?\",\"CITY1\":\"?\",\"TEL_NUMBER\":\"?\",\"POST_CODE1\":\"?\",\"FAX_NUMBER\":\"?\",\"STREET\":\"?\",\"TAXNUM\":\"?\",\"BANK_LAND\":\"?\",\"BANK_ID\":\"?\",\"BKREF\":\"?\",\"BANK_NO\":\"?\",\"KOINH\":\"?\",\"BAHNS\":\"?\",\"ZCHANGE\":\"?\",\"BUKRS\":\"?\",\"BUTXT\":\"?\",\"AKONT\":\"?\",\"ZTERM\":\"?\",\"REPRF\":\"?\",\"EKORG\":\"?\",\"EKNAM\":\"?\",\"WAERS\":\"?\",\"ZTERM2\":\"?\",\"KALSK\":\"?\",\"WEBRE\":\"?\",\"lineUniqueKey\":\"********\"}]";
//        List<JSONObject> o = JSONArray.parseObject(str, List.class);
//        System.out.println(o);
//    }

    public Map execute(Map params) {
        String execute = JSON.toJSONString(params);
        baseBean.writeLog("SyncSupplierToSAPESB.enter.params: {}", params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        baseBean.writeLog("SyncSupplierToSAPESB.enter.ctrl: {}", ctrlObject.toJSONString());
        baseBean.writeLog("SyncSupplierToSAPESB.enter.datalist: {}", jsonObject.get("DATA"));

        List<JSONObject> dataListObject =  JSONArray.parseObject(jsonObject.getString("DATA"), List.class);
        String requestId = ctrlObject.getString("requestid");
        RecordSet main = new RecordSet();
        Map tableNameByRequestId = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String)tableNameByRequestId.get("tableName");
        int mainId = -1;
        String lcbh = "";
        String sql = "select * from " + tableName + " where requestid = ?";
        main.executeQuery(sql, requestId);
        if(main.next()) {
            mainId = main.getInt("id");
            lcbh = main.getString("lcbh");
        }
        Map<String, JSONObject> zoainf003ReqJsonMap = getZOAINF003ReqJson(ctrlObject, dataListObject, requestId, lcbh);
        if(zoainf003ReqJsonMap.isEmpty()){
            baseBean.writeLog("SyncSupplierToSAPESB.request.isEmpty");
            Map map = new HashMap();
            map.put("type", "S");
            map.put("message", "无可更新数据");
            map.put("result", "");
            map.put("errMsg", "");
            return map;
        }
        return invockData(zoainf003ReqJsonMap, tableName);

    }

    /**
     * 获取 Z002 的请求json
     *
     * @param ctrlObject
     * @param dataListObject
     * @param requestId
     * @param lcbh
     * @return
     */
    public Map<String, JSONObject> getZOAINF003ReqJson(JSONObject ctrlObject, List<JSONObject> dataListObject, String requestId, String lcbh){
        int line = 1;
        Map<String, JSONObject> objectMap = new HashMap<>();
        for (JSONObject paramData : dataListObject) {
            if (StringUtils.isNotEmpty(paramData.getString("sapgysbh"))) {
                baseBean.writeLog("该行数据已提交并更新，无需更新,paramData: "+ paramData.toJSONString());
                line++;
                continue;
            }
            JSONObject reqJson = new JSONObject();
            JSONObject ctrlReq = SAPUtil.getCtrlReq(requestId, line, lcbh, "ZOAINF003", "1");
            JSONArray dataArr = new JSONArray();
            JSONObject dataReq = new JSONObject();
            dataReq.put("ZCHANGE", paramData.get("ZCHANGE"));
            dataReq.put("ZHBBS", "1");
            dataReq.put("NAME_ORG1", paramData.getString("NAME_ORG1"));
            dataReq.put("BU_SORT1", paramData.getString("BU_SORT1"));
            dataReq.put("BU_GROUP", paramData.getString("BU_GROUP"));
            dataReq.put("STCD5", "");
            dataReq.put("NAME4", "");
            dataReq.put("NAME_ORG4", paramData.getString("NAME_ORG4"));
            dataReq.put("MOB_NUMBER", paramData.getString("MOB_NUMBER"));
            dataReq.put("LAND1", "CN");
            dataReq.put("LANGU", "ZH");
            dataReq.put("REGIO", paramData.getString("REGIO"));
            dataReq.put("CITY1", paramData.getString("CITY1"));
            dataReq.put("TEL_NUMBER", paramData.getString("TEL_NUMBER"));
            dataReq.put("POST_CODE1", paramData.getString("POST_CODE1"));
            dataReq.put("STREET", paramData.getString("STREET"));
            dataReq.put("TAXNUM", paramData.getString("TAXNUM"));
            dataReq.put("BANK_LAND", "CN");
            dataReq.put("BANK_ID", paramData.getString("BANK_ID"));
            dataReq.put("YHMC", paramData.getString("yhmc"));
            dataReq.put("BKREF", paramData.getString("BKREF"));
            dataReq.put("BANK_NO", paramData.getString("BANK_NO"));
            dataReq.put("KOINH", paramData.getString("KOINH"));

            JSONArray companyJsonArr = new JSONArray();
            JSONObject companyJson = new JSONObject();
            companyJson.put("BUKRS", paramData.getString("BUKRS"));
            companyJson.put("AKONT", "**********");
            companyJson.put("ZTERM", paramData.getString("ZTERM"));
            companyJsonArr.add(companyJson);

            JSONArray purchaseOrgJsonArr = new JSONArray();
            JSONObject purchaseOrgJson = new JSONObject();
            purchaseOrgJson.put("EKORG", paramData.getString("EKORG"));
            purchaseOrgJson.put("EKNAM", "");
            purchaseOrgJson.put("WAERS", "CNY");
            purchaseOrgJson.put("ZTERM2", paramData.getString("ZTERM2"));
            purchaseOrgJson.put("WEBRE", "X");
            purchaseOrgJsonArr.add(purchaseOrgJson);

            dataReq.put("BUKRS_I", companyJsonArr);
            dataReq.put("EKORG_I", purchaseOrgJsonArr);
            dataArr.add(dataReq);
            reqJson.put("CTRL", ctrlReq);
            reqJson.put("DATA", dataArr);

            // TODO 行唯一
            objectMap.put(line + "-" +paramData.getString("lineUniqueKey"), reqJson);
            line++;
        }


        return objectMap;
    }


    /**
     * 请求sap
     * @param z003Map
     * @param tableName
     * @return
     */
    public Map invockData(Map<String, JSONObject> z003Map, String tableName){
        JSONObject resp = null;
        Map map = new HashMap();
        int count = z003Map.size();
        int sCount = 0;
        int eCount = 0;
        String errMsg = "";
        //空
        int errCount = 0;
        for (Map.Entry<String, JSONObject> o : z003Map.entrySet()) {
            baseBean.writeLog("SyncSupplierToSAPESB.SAP.Param: {}", o.getValue().toJSONString());
            // 调用sap返回结果
            resp = JSONObject.parseObject(SAPUtil.execute(o.getValue().toJSONString()));
            baseBean.writeLog("lineKeyUnique=" + o.getKey());
            String[] keyArr = o.getKey().split("-");
            String line  = keyArr[0];
            String lineUniqueKey = keyArr[1];

            if(resp != null){
                baseBean.writeLog("SyncSupplierToSAPESB.SAP.resp: {}", JSON.toJSONString(resp));
                JSONObject ctrl = resp.getJSONObject("CTRL");
                JSONArray data = resp.getJSONArray("DATA");
                baseBean.writeLog("SyncSupplierToSAPESB.data=" + data);
                String MSGTY = ctrl.getString("MSGTY");
                String partner = "";
                String MSAGE = ctrl.getString("MSAGE");
                if("S".equals(MSGTY)){
                    JSONObject dataJSONObject = data.getJSONObject(0);
                    partner = dataJSONObject.getString("LIFNR");
                    sCount++;
                    updateItemSql(tableName, lineUniqueKey, "0", partner, MSGTY, MSAGE);
                } else if("E".equals(MSGTY)){
                    if (null != data && data.size() > 0) {
                        JSONObject dataJSONObject = data.getJSONObject(0);
                        if (null != dataJSONObject) {
                            MSAGE = dataJSONObject.getString("MSAGE");
                        }
                    }
                    eCount++;
                    updateItemSql(tableName, lineUniqueKey, "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                } else {
                    if (null != data && data.size() > 0) {
                        JSONObject dataJSONObject = data.getJSONObject(0);
                        if (null != dataJSONObject) {
                            MSAGE = dataJSONObject.getString("MSAGE");
                        }
                    }
                    errCount++;
                    updateItemSql(tableName, lineUniqueKey, "1", null, MSGTY, MSAGE);
                    errMsg += "第{" + line + "}行返回为{" + MSGTY +"}消息为{" + MSAGE + "},";
                }
            } else {
                map.put("type", "F");
                map.put("message", "异常失败");
                map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
                map.put("errMsg", errMsg);
                return map;
            }

        }

//        //更新主表数据
//        if(StringUtils.isNotBlank(finalPartner) ){
//            rs.executeUpdate("update " + tableName + " set htqyfmc = ?,htqyfdm = ? where requestid = ?", finalNAME_ORG1, finalPartner, requestId);
//        }

        if(eCount > 0 || errCount > 0){
            map.put("type", "F");
            map.put("message", "部分失败");
            map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
            map.put("errMsg", errMsg);
            return map;
        }
        map.put("type", "S");
        map.put("message", "全部成功");
        map.put("result", "总数为"+count+"个，成功的为"+sCount+"个，失败的为"+(eCount+errCount)+"个");
        map.put("errMsg", errMsg);
        return map;
    }

    /**
     * 更新明细表的数据
     * @param tableName
     */
    public void updateItemSql(String tableName,String lineUniqueKey, String sfyts, String partner, String MSGTY, String MSAGE){
        String sql = "";
        sql = "update "+tableName+"_dt1  set sfyts = ?, sapgysbh = ?, xxlx = ?, xxnr = ? where lineUniqueKey = ?";
        rs.executeUpdate(sql, sfyts, partner, MSGTY, MSAGE, lineUniqueKey);
    }

}

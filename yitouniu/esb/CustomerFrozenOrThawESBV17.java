package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import weaver.general.BaseBean;
import yitouniu.constant.CommonConstant;
import yitouniu.util.SAPUtil;
import yitouniu.util.TimeUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: oa
 * @description:
 * @author: haiyang
 * @create: 2024-04-19 10:56
 **/
public class CustomerFrozenOrThawESBV17 extends ReturnMsgToSAP {

    BaseBean baseBean = new BaseBean();
    public Map executeParam(Map params) {
        baseBean.writeLog("CustomerFrozenOrThawESB.param:", params);
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = jsonObject.getJSONObject(CommonConstant.CTRL);
        JSONArray dataArr = jsonObject.getJSONArray("DATA");
        JSONArray zjsDataArr = jsonObject.getJSONArray("ZJSDATA");
        JSONArray qdDataArr = jsonObject.getJSONArray("QDDATA");

        List<JSONObject> dataListObject = Lists.newArrayList();
        List<JSONObject> zjsDataListObject = Lists.newArrayList();
        List<JSONObject> qdDataListObject = Lists.newArrayList();

        if (null != dataArr) {
            dataListObject = JSONObject.parseArray(dataArr.toJSONString(), JSONObject.class);
        }
        if (null != zjsDataArr) {
            zjsDataListObject = JSONObject.parseArray(zjsDataArr.toJSONString(), JSONObject.class);
        }
        if (null != qdDataArr) {
            qdDataListObject = JSONObject.parseArray(qdDataArr.toJSONString(), JSONObject.class);
        }
        // 流程编号
        String lcbh = ctrlObject.getString("lcbh");
        // 冻结状态 空 解冻 10冻结
        Integer djzt = ctrlObject.getInteger("djzt");
        // 申请公司
        String sqgs = ctrlObject.getString("sqgs");
        JSONObject reqJSON = new JSONObject();
        JSONObject ctrlReq = getCtrlReq(lcbh);
        List<JSONObject> dataListReq = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dataListObject)) {
            for (JSONObject object : dataListObject) {
                String status = object.getString("status");
                if ("E".equals(status)){
                    JSONObject dataReq = new JSONObject();
                    // 客户编码
                    dataReq.put("PARTNER", object.getString("PARTNER"));
                    // 清算日期 YYYYMMDD
                    dataReq.put("LIQUID_DAT", null == object.getDate("LIQUID_DAT")? "" : TimeUtils.getTimeStr(object.getDate("LIQUID_DAT"), "yyyy-MM-dd"));
                    List<JSONObject> knvvObjects = Lists.newArrayList();
                    JSONObject knvvObject = new JSONObject();
                    knvvObject.put("PARTNER", object.getString("PARTNER"));
                    // 销售组织 取申请公司
                    knvvObject.put("VKORG", sqgs);
                    // 分销渠道
                    knvvObject.put("VTWEG", "00");
                    // 产品组
                    knvvObject.put("SPART", "00");
                    knvvObject.put("AUFSD", djzt);
                    // 解冻/冻结原因
                    knvvObject.put("KVGR5", object.getString("KVGR5"));
                    knvvObjects.add(knvvObject);
                    dataReq.put("KNVV", knvvObjects);
                    dataListReq.add(dataReq);
                }
            }
        }

        List<JSONObject> zjsDataListReq = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(zjsDataListObject)) {
            for (JSONObject object : zjsDataListObject) {
                String status = object.getString("status");
                if ("E".equals(status)) {
                    JSONObject dataReq = new JSONObject();
                    // 客户编码
                    dataReq.put("PARTNER", object.getString("CUST_ID_AR"));
                    // 清算日期 YYYYMMDD
                    dataReq.put("LIQUID_DAT", null == object.getDate("LIQUID_DAT")? "" : TimeUtils.getTimeStr(object.getDate("LIQUID_DAT"), "yyyy-MM-dd"));
                    // 开票客户信息
                    List<JSONObject> zjsObjects = Lists.newArrayList();
                    JSONObject zjsObject = new JSONObject();
                    zjsObject.put("CUST_ID_AR", object.getString("CUST_ID_AR"));
                    zjsObject.put("CUST_NAME_AR", object.getString("CUST_NAME_AR"));
                    zjsObject.put("CUST_TAXCODE", object.getString("CUST_TAXCODE"));
                    zjsObject.put("CUST_ADDRESS", object.getString("CUST_ADDRESS"));
                    zjsObject.put("CUST_TELEPHONE", object.getString("CUST_TELEPHONE"));
                    zjsObject.put("CUST_BANKNAME", object.getString("CUST_BANKNAME"));
                    zjsObject.put("ZCUST_BANKACCOUNT", object.getString("ZCUST_BANKACCOUNT"));
                    zjsObject.put("CUST_MOBILE", object.getString("CUST_MOBILE"));
                    zjsObject.put("ZNAME_SALES", object.getString("ZNAME_SALES"));
                    zjsObject.put("CUST_EMAIL", object.getString("CUST_EMAIL"));
                    zjsObject.put("ZEMAIL_SALES", object.getString("ZEMAIL_SALES"));
                    zjsObjects.add(zjsObject);
                    dataReq.put("ZJS", zjsObjects);
                    zjsDataListReq.add(dataReq);
                }
            }
        }

        List<JSONObject> qdDataListReq = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(qdDataListObject)) {
            for (JSONObject object : qdDataListObject) {
                String status = object.getString("status");
                if ("E".equals(status)){
                    JSONObject dataReq = new JSONObject();
                    // 客户编码
                    dataReq.put("PARTNER", object.getString("PARTNER"));
                    // 渠道大类
                    dataReq.put("KATR5", object.getString("KATR5"));
                    // 归属渠道部门
                    dataReq.put("KATR6", object.getString("KATR6"));
                    // 渠道名称
                    dataReq.put("ZQDMC", object.getString("ZQDMC"));
                    // 门店类型
                    dataReq.put("ZKHQD", object.getString("ZKHQD"));
                    qdDataListReq.add(dataReq);
                }
            }
        }

        reqJSON.put("CTRL", ctrlReq);
        if (!dataListReq.isEmpty()) {
            reqJSON.put("DATA", dataListReq);
        } else if (!zjsDataListReq.isEmpty()) {
            reqJSON.put("DATA", zjsDataListReq);
        } else if (!qdDataListReq.isEmpty()) {
            reqJSON.put("DATA", qdDataListReq);
        } else {
            Map map = new HashMap();
            map.put("MSGTY", "F");
            map.put("MSAGE", "失败");
            map.put("RESULT", "DATA都为空");
            map.put("PARAMS", params);
            return map;
        }

        baseBean.writeLog("ZOAINF005reqJSON: {}", reqJSON.toJSONString());
        Map executeResult = this.execute(reqJSON.toJSONString());
        return executeResult;
    }


    private static JSONObject getCtrlReq(String lcbh){
        JSONObject ctrlReq = new JSONObject();
        Date date = new Date();
        ctrlReq.put("SYSID", "OA");
        ctrlReq.put("REVID", "SAP");
        ctrlReq.put("FUNID", "ZOAINF005");
        ctrlReq.put("INFID", lcbh + System.currentTimeMillis());
        ctrlReq.put("UNAME", "sysadmin");
        ctrlReq.put("DATUM", TimeUtils.getTimeStr(date, "yyyy-MM-dd"));
        ctrlReq.put("UZEIT", TimeUtils.getTimeStr(date, "HH:mm:ss"));
        ctrlReq.put("KEYID", lcbh);

        return ctrlReq;
    }


    public Map execute(String params) {
        Map map = new HashMap();
        try {
            String execute = SAPUtil.execute(params); // 调用sap返回结果
            JSONObject jsonObject = JSONObject.parseObject(execute);
            baseBean.writeLog("executeResult: {}", execute);

            if (jsonObject.containsKey("DATA")) {

                List<JSONObject> dataObject = (List<JSONObject>) jsonObject.get("DATA"); // 获取响应结果
                baseBean.writeLog("executeParam：{}", JSON.toJSONString(dataObject));

                dataObject.forEach(data -> {
                    if ("S".equals(data.getString("MSGTY"))) {
                        data.put("status", "S");
                    } else {
                        data.put("status", "E");
                    }
                });

                Map<Object, Object> respJ = new HashMap<>();
                respJ.put("CTRL", jsonObject.getJSONObject("CTRL"));
                respJ.put("DATA", dataObject);
                baseBean.writeLog("respJ：" + JSON.toJSONString(respJ));
                return respJ;

            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "失败");
                map.put("RESULT", execute);
                map.put("PARAMS", params);
                return map;
            }
        } catch (Exception e) {
            map.put("MSGTY", "F");
            map.put("MSAGE", "失败");
            map.put("RESULT", e);
            map.put("PARAMS", params);
        }
        return map;
    }
}

package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import yitouniu.util.CDMSUtil;
import yitouniu.util.WMSUtils;

import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

//盘点差异调用中间件   by   依依
public class PDToZJJ {

    public Map executeParam(Map params) {
        Map map = new HashMap();
        String result = "";
        String pdbh = "";
        String sfty = "";
        new BaseBean().writeLog("盘点测试");
        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            List<JSONObject> data = (List<JSONObject>) jsonObject.get("DATA");
            JSONObject idata = data.get(0);
            pdbh = idata.getString("PDBH");
            sfty = idata.getString("SFTY");
            new BaseBean().writeLog("单号"+pdbh);
            new BaseBean().writeLog("结果"+sfty);
//            CDMSUtil cdmsUtil = new CDMSUtil();
            JSONObject jsonObject2 =new JSONObject();
            jsonObject2.put("orderNo",pdbh);
            jsonObject2.put("status",sfty);
//            result = CDMSUtil.pushCdms(CDMSUtil.INVENTORY,jsonObject2);
            result = WMSUtils.pushInventoryWms(pdbh, sfty);
            JSONObject resultJson = JSONObject.parseObject(result);
            if("success".equals(resultJson.getString("flag"))){
                map.put("MSGTY","S");
                map.put("RESULT","推送成功");
            } else {
                map.put("MSGTY","F");
                map.put("RESULT","推送失败");
            }
        }catch (Exception e){
            map.put("MSGTY","F");
            map.put("RESULT",result);
        }
        return map;
    }
}

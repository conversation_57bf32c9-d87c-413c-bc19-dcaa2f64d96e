package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.general.BaseBean;
import yitouniu.util.SAPUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * oa审批店铺解冻 sap
 *
 * <AUTHOR>
 * @Date 2023/7/13
 */
public class OAShopThawESB {

    private static final BaseBean LOGGER = new BaseBean();

    public Map<Object, Object> execute(String params) {

        LOGGER.writeLog("oa审批店铺解冻 sap 参数 ===> " + params);

        if (!StringUtils.isNotBlank(params) || !paramsCheck(params)) {
            return genResult("E", "请查看流程参数是否正确");
        }

        String resp; // 调用sap返回结果
        try {
            resp = SAPUtil.execute(modifyParams(params));
            LOGGER.writeLog("调用sap返回结果 ===> " + resp);
        }
        catch (Exception e) {
            LOGGER.writeLog("oa审批店铺解冻 sap执行异常");
            e.printStackTrace();
            return genResult("E", "SAP执行异常");
        }

        if (StringUtils.isBlank(resp)) {
            return genResult("E", "SAP响应失败");
        }

        JSONObject jsonObject = JSON.parseObject(resp);
        JSONObject data = jsonObject.getJSONObject("CTRL");

        return genResult(data.getString("MSGTY"), data.getString("MSAGE"));
    }

    private String modifyParams(String params) {
        JSONObject paramsJsonObject = JSON.parseObject(params);
        JSONObject ctrl = paramsJsonObject.getJSONObject("CTRL");
        JSONObject data = paramsJsonObject.getJSONObject("DATA");
        JSONArray dataArr = new JSONArray();
        dataArr.add(data);
        paramsJsonObject.put("CTRL", ctrl);
        paramsJsonObject.put("DATA", dataArr);
        String jsonString = paramsJsonObject.toJSONString();

        LOGGER.writeLog("oa审批店铺解冻 修改后参数 ===> " + jsonString);

        return jsonString;
    }

    /**
     * 校验必填参数
     *
     * @param params
     * @return
     */
    private boolean paramsCheck(String params) {
        JSONObject CTRL = JSON.parseObject(params).getJSONObject("CTRL");
        JSONObject DATA = JSON.parseObject(params).getJSONObject("DATA");

        if (null == CTRL || null == DATA)
            return Boolean.FALSE;

        return (StringUtils.isNotBlank(CTRL.getString("SYSID")) ||
                StringUtils.isNotBlank(CTRL.getString("REVID")) ||
                StringUtils.isNotBlank(CTRL.getString("FUNID")) ||
                StringUtils.isNotBlank(CTRL.getString("INFID")) ||
                StringUtils.isNotBlank(CTRL.getString("UNAME")) ||
                StringUtils.isNotBlank(CTRL.getString("DATUM")) ||
                StringUtils.isNotBlank(CTRL.getString("UZEIT")) ||
                StringUtils.isNotBlank(CTRL.getString("KEYID")) ||
                StringUtils.isNotBlank(DATA.getString("KUNNR")) ||
                StringUtils.isNotBlank(DATA.getString("ZJDZT")) ||
                StringUtils.isNotBlank(DATA.getString("ZJDZT_Q")) ||
                StringUtils.isNotBlank(DATA.getString("ZJDZT_Z")));
    }

    /**
     * @param success S-成功，E-失败
     * @param msg
     * @return
     */
    private Map<Object, Object> genResult(String success, String msg) {

        Map<Object, Object> respMap = new HashMap<>();
        respMap.put("MSGTY", success);
        respMap.put("MSAGE", msg);

        return respMap;
    }
}

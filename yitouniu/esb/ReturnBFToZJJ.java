package yitouniu.esb;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetData;
import weaver.general.BaseBean;
import yitouniu.util.CDMSUtil;
import yitouniu.util.OMSUtils;

import java.math.BigDecimal;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * 仓库报废返回给中间件表单所有信息
 */
public class ReturnBFToZJJ {

    public Map executeParam(Map params){
        String result = "";
        String lcbh = "";
        String sqr = "";
        String lastname = "";
        String sqrq = "";
        String createDate = "";
        String createTime = "";
        Map map = new HashMap();

        try{
            String execute = JSON.toJSONString(params);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            JSONObject data = jsonObject.getJSONObject("DATA");
            List<JSONObject> itemFileList = (List<JSONObject>) jsonObject.get("ITEM");

            lcbh = data.getString("lcbh");
            sqr = data.getString("sqr");

            JSONObject reqJson = new JSONObject();

            new BaseBean().writeLog("流程编号"+lcbh);
            RecordSet rs3 = new RecordSet();
            String sql3 = "select * from workflow_requestbase  where requestmark = ?";
            rs3.executeQuery(sql3,lcbh);
            while(rs3.next()){
                createDate = rs3.getString("createdate");
                createTime = rs3.getString("createtime");
            }
            sqrq = createDate + " " +createTime;
            RecordSet rs4 = new RecordSet();
            String sql4 = "select * from HrmResource where id = ?";
            rs4.executeQuery(sql4,sqr);
            while(rs4.next()){
                lastname = rs4.getString("lastname");
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            reqJson.put("lcbh",lcbh);
            reqJson.put("billDate",sdf.format(new Date()));
            reqJson.put("sqr",lastname);
            reqJson.put("sqrq",sqrq);

            JSONArray itemArr = new JSONArray();
            for(JSONObject object : itemFileList) {
                JSONObject itemJson = new JSONObject();
                itemJson.put("bfxz",object.getString("bfxz"));
                itemJson.put("werehousecode",object.getString("werehousecode"));
                itemJson.put("wlmc1",object.getString("wlmc1"));
                String scrq = object.getString("scrq");
                if(StringUtils.isNotBlank(scrq)){
                    itemJson.put("scrq",scrq.replaceAll("-",""));
                } else {
                    itemJson.put("scrq","00000000");
                }
                int planqty = Double.valueOf(object.getString("planqty")).intValue();
                if(planqty > 0){
                    planqty = -planqty;
                }
                itemJson.put("planqty",planqty);
                itemArr.add(itemJson);
            }

            reqJson.put("scrapOrderItemModels",itemArr);

            OMSUtils omsUtils = new OMSUtils();
            String token = omsUtils.getToken();
            Map<String, String> header = getHeader(token);
            new BaseBean().writeLog("报废请求request="+reqJson);
            new BaseBean().writeLog("报废请求token="+token);
            result = HttpRequest
                    .post(OMSUtils.OMS_OMS_URL+OMSUtils.OMS_SCRAP_API)
                    .header("r3-api-token",token)
                    .body(reqJson.toJSONString()).execute().body();
            new BaseBean().writeLog("报废请求返回="+result);
            JSONObject resultJson = JSONObject.parseObject(result);

            if(resultJson.getInteger("code") == 0){
                map.put("MSGTY","S");
                map.put("MSAGE","推送成功");
                map.put("RESULT",result);
            }else{
                map.put("MSGTY","F");
                map.put("MSAGE","推送失败");
                map.put("RESULT",result);
            }
        }catch (Exception e){
            new BaseBean().writeLog("异常 = "+e);
            e.printStackTrace();
            map.put("MSGTY","F");
            map.put("MSAGE","异常错误");
            map.put("RESULT",result);
        }
        return map;
    }

    public Map<String,String> getHeader(String token) {
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json;charset=utf-8");
        headers.put("r3-api-token",token);
        new BaseBean().writeLog("报废getHeader :headers " + headers);

        return headers;
    }

}

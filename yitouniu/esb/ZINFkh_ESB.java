package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.ActionUtil;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
//员工主数据推送  by  依依
public class ZINFkh_ESB extends ReturnMsgToSAP{

    public Map executeParam(String params){
        new BaseBean().writeLog("员工主数据测试日志" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));

        Map execute = new HashMap();

        JSONObject jsonObject = JSONObject.parseObject(params);
        JSONObject CTRLObject = (JSONObject) jsonObject.get("CTRL");
        String requestid = CTRLObject.getString("INFID");

        //RecordSet recordSet = new RecordSet();
        //这个表名要改
        //String sql = "SELECT id FROM formtable_main_350_dt1 WHERE mainid = ( SELECT id FROM formtable_main_350 WHERE requestid = ?)";
        //recordSet.executeQuery(sql,requestid);


        if(jsonObject.containsKey("DATA")){
            List<JSONObject> dataList = (List<JSONObject>) jsonObject.get("DATA");
            JSONArray dataArr = new JSONArray();
            for(int i= 0; i<dataList.size();i++){
                JSONObject dataJSON = new JSONObject();
                JSONObject object = dataList.get(i);
                //if(recordSet.next()){
                //    dataJSON.put("ZID",recordSet.getString("id"));
                //}else{
                //    dataJSON.put("ZID","");
                //}

                dataJSON.put("NAME1",object.getString("NAME1"));
                dataJSON.put("NAME2",object.getString("NAME2"));
                dataJSON.put("REGIO",object.getString("REGIO"));
                dataJSON.put("BUKRS",object.getString("BUKRS"));
                dataJSON.put("BANKL",object.getString("BANKL"));
                dataJSON.put("BANKN",object.getString("BANKN"));
                dataJSON.put("BKONT",object.getString("BKONT"));
                dataJSON.put("KOINH",object.getString("KOINH"));
                dataJSON.put("ZTYPE",object.getString("ZTYPE"));

                dataArr.add(dataJSON);
            }
            jsonObject.put("DATA",dataArr);
            execute = super.execute(jsonObject.toString());
            Object msgty = execute.get("MSGTY");
            if(!"S".equals(msgty)){
                return execute;   //失败
            }

        }

        if(execute.isEmpty()){
            execute.put("MSGTY","S");
            execute.put("MSAGE","成功");
            execute.put("RESULT",execute);
            execute.put("PARAMS",params);
        }
        //还需要接收返回信息
        return execute;
    }
}

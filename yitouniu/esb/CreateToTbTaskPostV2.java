package yitouniu.esb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import yitouniu.util.*;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/11/17 8:07
 * @Description TODO
 * @Version 1.0
 */
public class CreateToTbTaskPostV2 {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();

    public Map executeParam(Map params) {
        new BaseBean().writeLog("ESB-CreateToTbTask-start");
        Map map = new HashMap();
        String execute = JSON.toJSONString(params);
        JSONObject jsonObject = JSONObject.parseObject(execute);
        JSONObject ctrlObject = (JSONObject) jsonObject.get("CTRL");
        List<JSONObject> taskList = (List<JSONObject>) jsonObject.get("TASK");

        //创建任务标记
        boolean flag = false;

        //基本数据
        //任务id
        String taskId;
        //项目编号,预计会和父任务id填写到前台流程表单上，
        String projectNumber;
        //CTRL
        //流程id
        String requestId = ctrlObject.getString("requestId");
        //流程编号
        String processNumber = ctrlObject.getString("processNumber");
        //得到任务应放所在处的年月
        //表单上的申请日期
        String applyTime = ctrlObject.getString("applyTime");
        //需求申请时间
        String taskApplyDate = TimeUtils.getTimeStr(new Date(), "yyyy-MM-dd HH:mm:ss");
        //主表 期望解决时间
        String qwjjsj = ctrlObject.getString("qwjjsj")+" 00:00:00";

        String year = applyTime.substring(0, 4);
        String month = applyTime.substring(5, 7);
        String day = applyTime.substring(8, 10);

        //获取表名
        Map tableNameByRequestID = ActionUtil.getTableNameByRequestId(requestId);
        String tableName = (String) tableNameByRequestID.get("tableName");

        //获取token
        String token = null;
        try {
            token = TeambitionHttpToken.genAppToken(TeambitionBaseConfig.APP_ID,TeambitionBaseConfig.APP_SECRET);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        //非创建更新使用的headers
        Map<String, String> headers = TeambitionHttpToken.returnHeaders(token);



        //查找部门，到二级甚至三级部门
        String taskApplyDepId = ctrlObject.getString("taskApplyDep");
        String taskApplyDep = null;
        RecordSet rsTaskApplyDep = new RecordSet();
        String sqlTaskApplyDep = "SELECT departmentname FROM HrmDepartment where id = ?";
        rsTaskApplyDep.executeQuery(sqlTaskApplyDep, taskApplyDepId);
        if (rsTaskApplyDep.next()) {
            taskApplyDep = rsTaskApplyDep.getString("departmentname");
        }
        String applyPeopleID = ctrlObject.getString("applyPeople");
        String applyPeople = null;
        RecordSet rsApplyPeople = new RecordSet();
        String applyPeopleSql = "select lastname from hrmresource where id = ?";
        rsApplyPeople.executeQuery(applyPeopleSql, applyPeopleID);
        if (rsApplyPeople.next()) {
            applyPeople = rsApplyPeople.getString("lastname");
        }


        //Task 任务信息
        //任务名称以及任务需要循环，可能有多个
        for (JSONObject o : taskList) {
            //任务id
            String oaTaskId = o.getString("taskId");
            //已经创建过的不创建
            if(StringUtils.isNotBlank(oaTaskId)){
                continue;
            }
            String taskName = o.getString("taskName");
            String taskDes = o.getString("taskDes");
            //需求分类
            String xqfl = o.getString("xqfl");
            //优先级
            String yxjpd = o.getString("yxjpd");
            String yxjpdName = "P"+yxjpd;
            //所属系统
            String szxt = o.getString("szxt");
            //需求对接人
            String xqdjr = o.getString("xqdjr");
            //必要性说明
            String byxsm = o.getString("bjms");
            //预计解决时间
            String yjjjsj = o.getString("yjjjsj");
            //预估人天
            String ygrt = o.getString("ygrt");
            // 流程编号
            String lcbh = o.getString("lcbh");

            String projectName = "";
            String projectTasklistName = "";
            switch (szxt){
                case "数据中台/BI":
                case "RPA":
                    projectName = "数据中台周任务管理";
                    projectTasklistName = year + "年" + Integer.parseInt(month) + "月";
                    break;
                case "OA/钉钉":
                    projectName = "基础运维项目";
                    projectTasklistName = "OA/钉钉需求清单";
                    break;
                case "业务中台":
                    projectName = "业务中台迭代运维项目";
                    projectTasklistName = "问题清单";
                    break;
                case "SAP":
                    projectName = "SAP项目";
                    projectTasklistName = "SAP需求清单";
                    break;
                case "旺店通":
                    projectName = "旺店通项目";
                    projectTasklistName = "旺店通需求清单";
                    break;
                case "WMS":
                    projectName = "WMS项目";
                    projectTasklistName = "WMS需求清单";
                    break;
                case "防窜货&质量追溯系统":
                    projectName = "溯源项目";
                    projectTasklistName = "溯源系统需求清单";
                    break;
                case "SRM":
                    projectName = "SRM项目";
                    projectTasklistName = "SRM需求清单";
                    break;
                case "中间件":
                    projectName = "中间件项目";
                    projectTasklistName = "中间件需求清单";
                    break;
                case "金蝶":
                    projectName = "金蝶项目";
                    projectTasklistName = "金蝶需求清单";
                    break;
                case "费控":
                    projectName = "数智化财务项目";
                    projectTasklistName = "费控需求清单";
                    break;
                case "资金平台":
                    projectName = "数智化财务项目";
                    projectTasklistName = "资金平台需求清单";
                    break;
                case "金税系统":
                    projectName = "数智化财务项目";
                    projectTasklistName = "金税需求清单";
                    break;
                case "POS系统":
                    projectName = "POS项目";
                    projectTasklistName = "POS需求清单";
                    break;
                case "小程序/奶卡系统":
                    projectName = "会员商城项目";
                    projectTasklistName = "小程序需求清单";
                    break;
                default:
                    break;
            }
            if(StringUtils.isBlank(projectName) || StringUtils.isBlank(projectTasklistName)){
                map.put("MSGTY", "F");
                map.put("MSAGE", "项目名称不匹配，为空");
                map.put("RESULT", "");
                map.put("IN_JSON", "taskReq");
                return map;
            }

            String xqflName = "";
            rs.executeQuery("select * from workflow_SelectItem where fieldid = 26438 and selectvalue = ? ", xqfl);
            if (rs.next()){
                xqflName = rs.getString("selectname");
            }

            //查询执行者（当前节点审批的人）
            String executorId = null;
            String xqdjrLoginId = null;
            String xqdjrName = null;
            //通过requestid和nodeid查到审批人的工号，然后去企业成员列表中匹配得到执行者id
            RecordSet rs = new RecordSet();
            String sql = "select lastname,loginid as loginId " +
                    "from HrmResource " +
                    "where id = ?";
            rs.executeQuery(sql, xqdjr);
            if (rs.next()) {
                xqdjrLoginId = rs.getString("loginId");
                xqdjrName = rs.getString("lastname");
            }

            executorId = TeambitionUtils.returnUserIdByName(xqdjrName, xqdjrLoginId, headers);

            //创建更新使用的headers
            Map<String,String> createHeaderMap = TeambitionHttpToken.getHeaders(executorId, token);

            //region 开始创建任务
            //先查到项目id、创建人id、项目编号前缀
            Map<String, String> projectMap = TeambitionUtils.returnProject(projectName, headers);
            String projectId = projectMap.get("projectId");
            String uniqueIdPrefix = projectMap.get("uniqueIdPrefix");
            //根据项目id以及今年的年份和月份找到相应的任务分组和任务列表
            String tasklistId = TeambitionUtils.returntasklistIdV3(year, projectTasklistName, projectId, headers);
            String stageId = TeambitionUtils.returnStageIdV3(month, day, projectId, tasklistId, headers);
            //查询任务所需要的任务类型
            Map taskTypes = TeambitionUtils.returnTaskTypes(projectId, headers);
            Map<String, String> taskTypeMap = new HashMap<>();
            String scenariofieldconfigId = "";
            String taskflowId = "";
            String taskflowStatusId = "";
            if("业务中台".equals(szxt) || "小程序/奶卡系统".equals(szxt)){
                taskTypeMap = (Map<String, String>) taskTypes.get("需求");
                scenariofieldconfigId = taskTypeMap.get("templateId");
                taskflowId = taskTypeMap.get("taskflowId");
                taskflowStatusId = TeambitionUtils.returnTaskflowStatusIdV3(taskflowId, "需求池", projectId, headers);
            } else {
                taskTypeMap = (Map<String, String>) taskTypes.get("任务");
                scenariofieldconfigId = taskTypeMap.get("templateId");
                taskflowId = taskTypeMap.get("taskflowId");
                taskflowStatusId = TeambitionUtils.returnTaskflowStatusIdV3(taskflowId, "待处理", projectId, headers);
            }
            //创建任务的req数据
            JSONObject taskReq = new JSONObject();
            //项目id
            taskReq.put("projectId", projectId);
            //任务列表id
            taskReq.put("stageId", stageId);
            //任务类型id
            taskReq.put("scenariofieldconfigId", scenariofieldconfigId);
            //任务分组id
            taskReq.put("tasklistId", tasklistId);
            //任务名称
            taskReq.put("content", taskName);
            //执行者id，不传表示待认领
            taskReq.put("executorId", executorId);
            //工作流状态id
            taskReq.put("taskflowstatusId", taskflowStatusId);
            //开始日期，现在不传值
            taskReq.put("startDate", "");
            //截止日期，现在不传值
            if(StringUtils.isBlank(yjjjsj)){
                taskReq.put("dueDate", "");
            } else {
                taskReq.put("dueDate", yjjjsj + " 18:30:00");
            }
            //任务备注,现在不传值
            taskReq.put("note", byxsm);
            //可见性,默认为projectMembers，项目成员可见，   任务参与者可见为participants
            taskReq.put("visible", "members");
            //自定义字段id
            JSONArray customfieldArr = new JSONArray();
            //parentTaskType是任务类型返回的父任务类型Map，其中除了模板id和工作流id其他都是自定义字段。
            for (Map.Entry<String, String> m : taskTypeMap.entrySet()) {
                new BaseBean().writeLog("ESB-CreateToTbTask-taskType.entrySet()：" + m.getKey() + "。类型字段id为：" + m.getValue());
                JSONObject customfieldJson = new JSONObject();
                JSONArray value = new JSONArray();
                JSONObject valueJson = new JSONObject();
                if (!"templateId-taskflowId".contains(m.getKey())) {
                    switch (m.getKey()) {
                        case "需求类型":
                            valueJson.put("title", xqflName);
                            break;
                        case "优先级":
                            valueJson.put("title", yxjpdName);
                            break;
                        case "需求方":
                            valueJson.put("title", applyPeople);
                            break;
                        case "需求部门":
                            valueJson.put("title", taskApplyDep);
                            break;
                        case "需求申请日期":
                            valueJson.put("title", taskApplyDate);
                            break;
                        case "需求对接人":
                            valueJson.put("title", xqdjrName);
                            break;
                        case "OA流程单号":
                            valueJson.put("title", processNumber);
                            break;
                        case "期望解决时间":
                            valueJson.put("title", qwjjsj);
                            break;
                        case "预估人天":
                            valueJson.put("title", ygrt);
                        case "流程编号":
                            valueJson.put("title", lcbh);
                        default:
                            break;
                    }
                    customfieldJson.put("cfId", m.getValue());
                    value.add(valueJson);
                    customfieldJson.put("value", value);
                }
                if (!customfieldJson.isEmpty()) {
                    customfieldArr.add(customfieldJson);
                }
            }
            taskReq.put("customfields", customfieldArr);
            new BaseBean().writeLog("ESB-CreateToTbTask：任务请求header" + createHeaderMap);
            new BaseBean().writeLog("ESB-CreateToTbTask：任务请求json" + taskReq);
            //发起请求
            String httpResponse = HttpClientUtils.doPostJson(TeambitionBaseConfig.URL_API_TASK_CREATE_POST_V3, createHeaderMap, taskReq);
            JSONObject resp = JSONObject.parseObject(httpResponse);
            new BaseBean().writeLog("ESB-CreateToTbTask：任务返回json" + httpResponse);
            int code = resp.getInteger("code");
            JSONObject resultObject = (JSONObject) resp.get("result");
            if (code == 200) {
                taskId = resultObject.getString("taskId");
                projectNumber = uniqueIdPrefix + "-" + resultObject.getString("uniqueId");
                //将这两个值存到流程表单中
                RecordSet rs1 = new RecordSet();
                String sql1 = "SELECT Top 1 * FROM " + tableName + "_dt1 WHERE mainid = ( SELECT id FROM " + tableName + " WHERE requestid = ?) and rwbh = '' and taskId = '' ";
                rs1.executeQuery(sql1, requestId);
                if (rs1.next()) {
                    if ("".equals(rs1.getString("taskId")) && "".equals(rs1.getString("rwbh"))) {
                        String dataBaseid = rs1.getString("id");
                        RecordSet rs2 = new RecordSet();
                        String sql2 = "update " + tableName + "_dt1 set rwbh = ? , taskId = ? where id = ? and mainid = (SELECT id FROM " + tableName + " WHERE requestid = ?)";
                        rs2.executeUpdate(sql2, projectNumber, taskId, dataBaseid, requestId);
                    }
                }




                map.put("MSGTY", "S");
                map.put("MSAGE", "创建任务成功，请到Teambition查看");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
            } else {
                map.put("MSGTY", "F");
                map.put("MSAGE", "任务请求失败");
                map.put("RESULT", httpResponse);
                map.put("IN_JSON", taskReq);
                return map;
            }

            //endregion
        }
        new BaseBean().writeLog("ESB-CreateToTbTask-end");



        return map;
    }



}

package yitouniu.esb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

//资产订单推送SAP   by   依依
public class ZINF038ESB extends ReturnMsgToSAP {

    public Map executeParam(String params) {
        JSONArray dataArr = new JSONArray();
        String requestID = "";
        RecordSet rs = new RecordSet();
        JSONObject jsonObject = JSONObject.parseObject(params);
        if (jsonObject.containsKey("DATA")) {
            List<JSONObject> dataList = (List)jsonObject.get("DATA");
            new BaseBean().writeLog("038测试日志" + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date()) + dataList.toString());
            JSONObject arr = (JSONObject)jsonObject.get("CTRL");
            new BaseBean().writeLog("038测试日志" + arr.toString());
            requestID = arr.getString("INFID");
            new BaseBean().writeLog("038测试日志" + requestID);
            Map<String, List<JSONObject>> collect = (Map)dataList.stream().collect(Collectors.groupingBy((row) -> {
                return row.getString("ANLN1");
            }));
            Iterator iterator = collect.entrySet().iterator();

            while(iterator.hasNext()) {
                Entry<String, List<JSONObject>> next = (Entry)iterator.next();
                List<JSONObject> detailDaoList = (List)next.getValue();

                for(int i = 0; i < detailDaoList.size(); ++i) {
                    JSONObject dataJson = new JSONObject();
                    JSONObject object = (JSONObject) detailDaoList.get(i);
                    if (object.getString("WERKS").equals("")) {
                        System.out.println("君宏君康不推送SAP");
                    } else {
                        dataJson.put("OPERATION", object.getString("OPERATION"));
                        dataJson.put("VBELN_OA", object.getString("VBELN_OA"));
                        dataJson.put("NAME1", object.getString("NAME1"));
                        dataJson.put("TXZ01", object.getString("TXZ01"));
                        dataJson.put("MEINS", object.getString("MEINS"));
                        dataJson.put("MENGE", object.getString("MENGE"));
                        dataJson.put("ANLN1", object.getString("ANLN1"));
                        dataJson.put("AFNAM", object.getString("AFNAM"));
                        dataJson.put("WERKS", object.getString("WERKS"));
                        dataArr.add(dataJson);
                    }
                }
            }
        }

        jsonObject.put("DATA", dataArr);
        if (dataArr.size() > 0) {
            Map execute = super.execute(jsonObject.toString());
            String result = execute.get("RESULT").toString();
            JSONObject jsonObject2 = JSONObject.parseObject(result);
            List<JSONObject> dataObject2 = (List)jsonObject2.get("DATA");
            String cgsqbh = ((JSONObject)dataObject2.get(0)).getString("BANFN");
            new BaseBean().writeLog("038测试日志" + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date()) + cgsqbh);
            List<List> allList = new ArrayList();
            List list = new ArrayList();
            list.add(cgsqbh);
            list.add(requestID);
            allList.add(list);
            new BaseBean().writeLog("038测试日志" + (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date()) + allList.toString());
            String sql = "update formtable_main_228 set cgsqbh = ? where requestId = ?";
            rs.executeBatchSql(sql, allList);
            return execute;
        } else {
            Map execute = new HashMap();
            execute.put("MSGTY", "S");
            execute.put("MSAGE", "不推送");
            execute.put("PARAMS", "");
            execute.put("RESULT", "");
            return execute;
        }
    }
}

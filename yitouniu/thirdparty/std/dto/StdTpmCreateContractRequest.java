package yitouniu.thirdparty.std.dto;

import java.math.BigDecimal;
import java.util.List;

/**
 * TPM合同创建接口入参
 * <p>
 * 前端直接传，这个类其实没用到
 *
 * <AUTHOR>
 * @since 2024-06-24 15:57
 */
@Deprecated
public class StdTpmCreateContractRequest implements StdBaseRequest {
    @Override
    public String apiUrl() {
        return "/crm-tpm/v1/external/tpm/createBatchContract";
    }

    /**
     * 是否必填：是
     * 流程ID
     * OA流程ID
     */
    private String processId;
    /**
     * 是否必填：是
     * 合同编码
     */
    private String contractCode;
    /**
     * 是否必填：是
     * 合同名称
     */
    private String contractName;
    /**
     * 是否必填：否
     * 辅助列
     */
    private String auxiliary;
    /**
     * 是否必填：否
     * 合同类型
     * 配置数据字典：0  采购合同 ；1  租赁合同 ；2  综合管理类合同 ；3  营销投放类合同 ；4  仓储、运输合同； 5  委托合同 ；6  客户新建档维护一体化流程V2.0； 7  销售合同
     */
    private String contractType;
    /**
     * 是否必填：否
     * 原始合同/补充合同
     * 0原始，1补充
     */
    private String isReplenish;
    /**
     * 是否必填：否
     * 原始合同编码
     * 如果合同为补充，则当前字段必填
     */
    private String originalContractCode;
    /**
     * 是否必填：是
     * 公司编码
     */
    private String companyCode;
    /**
     * 是否必填：是
     * ERP编码
     * 合同客户
     */
    private String erpCode;
    /**
     * 是否必填：是
     * 合同开始日期
     * 格式yyyy-MM-dd
     */
    private String startDate;
    /**
     * 是否必填：是
     * 合同结束日期
     * 格式yyyy-MM-dd
     */
    private String endDate;
    /**
     * 是否必填：否
     * 文件路径
     * url地址
     */
    private String fileUrl;
    /**
     * 是否必填：否
     * 明细参数列表
     */
    private List<Detail> details;

    public String getProcessId() {
        return processId;
    }

    public void setProcessId(String processId) {
        this.processId = processId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }

    public String getAuxiliary() {
        return auxiliary;
    }

    public void setAuxiliary(String auxiliary) {
        this.auxiliary = auxiliary;
    }

    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getIsReplenish() {
        return isReplenish;
    }

    public void setIsReplenish(String isReplenish) {
        this.isReplenish = isReplenish;
    }

    public String getOriginalContractCode() {
        return originalContractCode;
    }

    public void setOriginalContractCode(String originalContractCode) {
        this.originalContractCode = originalContractCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getErpCode() {
        return erpCode;
    }

    public void setErpCode(String erpCode) {
        this.erpCode = erpCode;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public List<Detail> getDetails() {
        return details;
    }

    public void setDetails(List<Detail> details) {
        this.details = details;
    }

    /**
     * 明细参数类
     */
    public static class Detail {
        /**
         * 是否必填：是
         * 费用类型
         * 返利/搭赠/固定
         */
        private String caseType;
        /**
         * 是否必填：是
         * 活动细类
         * 数据字典（待产品提供）
         */
        private String detailCode;
        /**
         * 是否必填：是
         * 适用部门
         * 营销组织架构中的部门（单选）
         */
        private String belongDepartmentCode;
        /**
         * 是否必填：否
         * 核算产品
         * 考核产品/本品产品
         */
        private List<Item> productList;
        /**
         * 是否必填：否
         * 核算品项
         * 考核品项
         */
        private List<Item> itemList;
        /**
         * 是否必填：否
         * 核算层级
         * 本品小类
         */
        private List<Item> levelList;
        /**
         * 是否必填：否
         * 费用产品
         * 返利产品/赠品产品
         */
        private List<Item> feeProductList;
        /**
         * 是否必填：否
         * 费用品项
         * 返利品项
         */
        private List<Item> feeItemList;
        /**
         * 是否必填：否
         * 费用层级
         * 赠品小类
         */
        private List<Item> feeLevelList;
        /**
         * 是否必填：否
         * 返利类型
         * 返利周期/扣费周期；数据字典（待产品提供）
         */
        private List<Item> rebateType;
        /**
         * 是否必填：是
         * 开始时间
         * 格式yyyy-MM-dd
         */
        private String startDate;
        /**
         * 是否必填：是
         * 结束时间
         * 格式yyyy-MM-dd
         */
        private String endDate;
        /**
         * 是否必填：否
         * 返利时间
         * 返利计算时间
         */
        private Integer rebateCalDay;
        /**
         * 是否必填：否
         * 兑付方式
         * 数据字典（待产品提供）
         */
        private String cashType;
        /**
         * 是否必填：否
         * 条件公式
         * 政策形式
         */
        private String conditionFormula;
        /**
         * 是否必填：否
         * 结果公示
         * 达成条件
         */
        private String resultFormula;
        /**
         * 是否必填：否
         * 返利标准
         */
        private String rebateStandard;
        /**
         * 是否必填：否
         * 条件数量
         * 多阶梯用顿号隔开，跟赠送数量顺序一致，例如 100、200
         */
        private String conditionNum;
        /**
         * 是否必填：否
         * 赠送数量
         * 多阶梯用顿号隔开，跟条件数量顺序一致，例如：5、10
         */
        private String giveNum;
        /**
         * 是否必填：否
         * 客户优惠数量上限
         */
        private BigDecimal discountQuantity;
        /**
         * 是否必填：否
         * 客户优惠金额上限
         */
        private BigDecimal discountAmount;

        public String getCaseType() {
            return caseType;
        }

        public void setCaseType(String caseType) {
            this.caseType = caseType;
        }

        public String getDetailCode() {
            return detailCode;
        }

        public void setDetailCode(String detailCode) {
            this.detailCode = detailCode;
        }

        public String getBelongDepartmentCode() {
            return belongDepartmentCode;
        }

        public void setBelongDepartmentCode(String belongDepartmentCode) {
            this.belongDepartmentCode = belongDepartmentCode;
        }

        public List<Item> getProductList() {
            return productList;
        }

        public void setProductList(List<Item> productList) {
            this.productList = productList;
        }

        public List<Item> getItemList() {
            return itemList;
        }

        public void setItemList(List<Item> itemList) {
            this.itemList = itemList;
        }

        public List<Item> getLevelList() {
            return levelList;
        }

        public void setLevelList(List<Item> levelList) {
            this.levelList = levelList;
        }

        public List<Item> getFeeProductList() {
            return feeProductList;
        }

        public void setFeeProductList(List<Item> feeProductList) {
            this.feeProductList = feeProductList;
        }

        public List<Item> getFeeItemList() {
            return feeItemList;
        }

        public void setFeeItemList(List<Item> feeItemList) {
            this.feeItemList = feeItemList;
        }

        public List<Item> getFeeLevelList() {
            return feeLevelList;
        }

        public void setFeeLevelList(List<Item> feeLevelList) {
            this.feeLevelList = feeLevelList;
        }

        public List<Item> getRebateType() {
            return rebateType;
        }

        public void setRebateType(List<Item> rebateType) {
            this.rebateType = rebateType;
        }

        public String getStartDate() {
            return startDate;
        }

        public void setStartDate(String startDate) {
            this.startDate = startDate;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public Integer getRebateCalDay() {
            return rebateCalDay;
        }

        public void setRebateCalDay(Integer rebateCalDay) {
            this.rebateCalDay = rebateCalDay;
        }

        public String getCashType() {
            return cashType;
        }

        public void setCashType(String cashType) {
            this.cashType = cashType;
        }

        public String getConditionFormula() {
            return conditionFormula;
        }

        public void setConditionFormula(String conditionFormula) {
            this.conditionFormula = conditionFormula;
        }

        public String getResultFormula() {
            return resultFormula;
        }

        public void setResultFormula(String resultFormula) {
            this.resultFormula = resultFormula;
        }

        public String getRebateStandard() {
            return rebateStandard;
        }

        public void setRebateStandard(String rebateStandard) {
            this.rebateStandard = rebateStandard;
        }

        public String getConditionNum() {
            return conditionNum;
        }

        public void setConditionNum(String conditionNum) {
            this.conditionNum = conditionNum;
        }

        public String getGiveNum() {
            return giveNum;
        }

        public void setGiveNum(String giveNum) {
            this.giveNum = giveNum;
        }

        public BigDecimal getDiscountQuantity() {
            return discountQuantity;
        }

        public void setDiscountQuantity(BigDecimal discountQuantity) {
            this.discountQuantity = discountQuantity;
        }

        public BigDecimal getDiscountAmount() {
            return discountAmount;
        }

        public void setDiscountAmount(BigDecimal discountAmount) {
            this.discountAmount = discountAmount;
        }
    }

    /**
     * 核算、费用对象类
     */
    public static class Item {
        /**
         * 是否必填：否
         * 编码
         * 小类编码
         */
        private String code;
        /**
         * 是否必填：否
         * 名称
         * 品项/小类名称
         */
        private String name;
        /**
         * 是否必填：否
         * 物料编码
         * 物料编码
         */
        private String materialCode;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public void setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
        }
    }
}

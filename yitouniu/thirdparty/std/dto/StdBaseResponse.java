package yitouniu.thirdparty.std.dto;


/**
 * <AUTHOR>
 * @since 2024-06-24 15:46
 */
public class StdBaseResponse<T> {
    private Boolean success;
    private String message;
    private Long timestamp;
    private T result;

    private T data;

    /**
     * 根据这个是否是200来判断是否成功，其他都不用管
     */
    private String code;


    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }



    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public T getResult() {
        return result;
    }

    public void setResult(T result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "StdBaseResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", timestamp=" + timestamp +
                ", result=" + result +
                ", data=" + data +
                ", code=" + code +
                '}';
    }
}

package yitouniu.thirdparty.std.dto;

/**
 * OA回调入参
 * <p>
 * 前端直接传，这个类其实没用到
 *
 * <AUTHOR>
 * @since 2024-06-24 16:13
 */
@Deprecated
public class StdOaCallbackRequest implements StdBaseRequest {
    @Override
    public String apiUrl() {
        return "/crm-mdm/v1/external/std/oaCallback";
    }

    private String businessCode;
    private String processKey;
    private String processStatus;

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getProcessKey() {
        return processKey;
    }

    public void setProcessKey(String processKey) {
        this.processKey = processKey;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }


}

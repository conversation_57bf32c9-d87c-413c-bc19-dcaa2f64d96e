package yitouniu.hec.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.weaver.esb.client.EsbClient;
import com.weaver.esb.spi.EsbService;

import net.sf.json.JSONObject;
import weaver.general.BaseBean;

import java.util.Map;


public class ToHECCmd extends AbstractCommonCommand {
    BaseBean baseBean = new BaseBean();
    public static String eventKey ;
    public static JSONObject jsonAll ;

    public ToHECCmd (Map<String, Object> param){
        this.eventKey = param.get("eventKey").toString();
        this.jsonAll = (JSONObject) param.get("jsonAll");
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Object execute(CommandContext commandContext) {
        String eventKey = this.eventKey; //事件标识
        String params = jsonAll.toString();
        baseBean.writeLog("===toHECCmd   eventKey："+eventKey+"   params:"+params);
        EsbService service = EsbClient.getService();
        String response = service.execute(eventKey,params);
        baseBean.writeLog("======返回的接口数据："+response);
        return response;
    }
}

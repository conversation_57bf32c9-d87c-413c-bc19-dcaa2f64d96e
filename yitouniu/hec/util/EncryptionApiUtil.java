package yitouniu.hec.util;

import com.alibaba.fastjson.JSONObject;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;
import org.apache.commons.codec.binary.Base64;
import weaver.general.BaseBean;


/**
 * <AUTHOR> 2021/09/06
 */
public final class EncryptionApiUtil {

    /**
     * 字符集
     */
    private static final Charset CHARSET = StandardCharsets.UTF_8;
    /**
     * 客户端标识，固定
     */
    public static final String CLIENT_ID = "ry1tn-hec";
    /**
     * 费控登录URL，需根据环境需要做调整
     */
    public static final String HEC_LOGIN_URL = "http://**************:30002/oauth/public/encryption-api/redirect";
            //正式系统配置
//    public static final String HEC_LOGIN_URL = "http://gateway.ryytngroup.com:30002/oauth/public/encryption-api/redirect";
    /**
     * 签名token，需根据环境需要做调整
     */
    private static final String TOKEN = "QDG6eK";
    //正式系统配置
//    private static final String TOKEN = "J7G9NH";
    /**
     * 加密key，需根据环境需要做调整
     */
    private static final String ENCRYPT_KEY = "jWmYm7qr5nMoAUwZRjGtBxmz3KA1tkAj3ykkR6q2B2C=";
    //正式系统配置
//    private static final String ENCRYPT_KEY = "36bSwGwj0OhHUUt6usndEpfWeqVW2w9oIfpr0fG36mk=";

    /**
     * 获取费控单点登录URL
     *
     * @param redirectUrl   重定向页面地址（取OA代办中的URL）
     * @param loginName     登录用户（必须为工号，否则无法登录）
     * @param effectiveTime 链接有效时长，越短越好，防止被盗用，毫秒记，例如：3000 为3秒
     * @return 费控单点登录URL
     */
    public static String getSsoUrl(String redirectUrl, String loginName, Long effectiveTime) {
        removeJceLimit();
        String result;
        try {
            Long timeMillis = System.currentTimeMillis();
            String timeStamp = String.valueOf(timeMillis);
            Long invalidTimeStamp = null;
            if (Objects.nonNull(effectiveTime) && effectiveTime > 0) {
                invalidTimeStamp = timeMillis + effectiveTime;
            }

            Map<String, String> contentMap = new HashMap<>(5);
            // 重定向页面地址
            contentMap.put("redirectUrl", redirectUrl);
            // 登录用户，必须为员工工号
            contentMap.put("loginName", loginName);
            // 客户端，默认
            contentMap.put("clientId", CLIENT_ID);
            // 授权类型，默认
            contentMap.put("grantType", "implicit");
            // 失效时间戳
            contentMap.put("invalidTimeStamp", String.valueOf(invalidTimeStamp));

            String content = JSONObject.toJSONString(contentMap);
            System.out.println("content:\n" + content);

            // 加密
            String encryptStr = Aes.encrypt(ENCRYPT_KEY, content);
            // 签名
            String signature = Signature.signature(TOKEN, timeStamp, encryptStr);

            result = String.format(HEC_LOGIN_URL + "?signature=%s&timeStamp=%s" +
                            "&encryptStr=%s&client_id=%s&grant_type=%s&response_type=%s",
                    signature, timeStamp, encryptStr, CLIENT_ID, "implicit", "token");
            new BaseBean().writeLog("EncryptionApiUtil-getSsoUrl："+result);
        } catch (Exception e) {
            System.out.println("===========:"+e);
            throw new RuntimeException(e);
        }
        return result;
    }

    /**
     * AES加密解密工具
     *
     * <AUTHOR> 2021/09/06
     */
    public static final class Aes {
        private Aes() {
        }

        /**
         * 加密
         *
         * @param aesKey 密钥key
         * @param str    待加密的字符串
         * @return 数据加密结果
         */
        public static String encrypt(String aesKey, String str) {
            BaseBean baseBean = new BaseBean();
            ByteGroup byteCollector = new ByteGroup();
            byte[] randomStrBytes = getRandomStr().getBytes(CHARSET);
            byte[] textBytes = str.getBytes(CHARSET);
            byte[] networkBytesOrder = getNetworkBytesOrder(textBytes.length);

            // randomStr + networkBytesOrder + text
            byteCollector.addBytes(randomStrBytes)
                    .addBytes(networkBytesOrder)
                    .addBytes(textBytes);

            // ... + pad: 使用自定义的填充方式对明文进行补位填充
            byte[] padBytes = Pkcs7Encoder.encode(byteCollector.size());
            byteCollector.addBytes(padBytes);

            // 获得最终的字节流, 未加密
            byte[] unencrypted = byteCollector.toBytes();

            try {
                baseBean.writeLog(" ======aesKey:"+aesKey+"     length:"+aesKey.length());
                byte[] aesKeyBytes = Base64.decodeBase64(aesKey.getBytes());
                baseBean.writeLog(" ======aesKeyBytes:"+aesKeyBytes.length);
                // 设置加密模式为AES的CBC模式
                Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
                SecretKeySpec keySpec = new SecretKeySpec(aesKeyBytes, "AES");
                IvParameterSpec iv = new IvParameterSpec(aesKeyBytes, 0, 16);
                cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);

                // 加密
                byte[] encrypted = cipher.doFinal(unencrypted);
                // 使用BASE64对加密后的字符串进行编码
                return Base64.encodeBase64URLSafeString(encrypted);
            } catch (Exception e) {
                baseBean.writeLog(" ======出错鸟:"+e);
                throw new RuntimeException("aes加密失败", e);
            }
        }

        // 随机生成16位字符串
        private static String getRandomStr() {
            String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            Random random = new Random();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 16; i++) {
                int number = random.nextInt(base.length());
                sb.append(base.charAt(number));
            }
            return sb.toString();
        }

        /**
         * 生成4个字节的网络字节序
         *
         * @param sourceNumber 源数据
         * @return 生成的4个字节的网络字节序
         */
        private static byte[] getNetworkBytesOrder(int sourceNumber) {
            byte[] orderBytes = new byte[4];
            orderBytes[3] = (byte) (sourceNumber & 0xFF);
            orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
            orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
            orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
            return orderBytes;
        }

        /**
         * 字节组
         *
         * <AUTHOR> 2021/09/06
         */
        private static final class ByteGroup {
            ArrayList<Byte> byteContainer = new ArrayList<>();

            public byte[] toBytes() {
                byte[] bytes = new byte[byteContainer.size()];
                for (int i = 0; i < byteContainer.size(); i++) {
                    bytes[i] = byteContainer.get(i);
                }
                return bytes;
            }

            public ByteGroup addBytes(byte[] bytes) {
                for (byte b : bytes) {
                    byteContainer.add(b);
                }
                return this;
            }

            public int size() {
                return byteContainer.size();
            }
        }

        /**
         * 提供基于PKCS7算法的加解密接口
         *
         * <AUTHOR> 2021/09/06
         */
        private static final class Pkcs7Encoder {
            private static final int BLOCK_SIZE = 32;

            /**
             * 获得对明文进行补位填充的字节.
             *
             * @param count 需要进行填充补位操作的明文字节个数
             * @return 补齐用的字节数组
             */
            public static byte[] encode(int count) {
                // 计算需要填充的位数
                int amountToPad = BLOCK_SIZE - (count % BLOCK_SIZE);
                // 获得补位所用的字符
                char padChr = chr(amountToPad);
                StringBuilder tmp = new StringBuilder();
                for (int index = 0; index < amountToPad; index++) {
                    tmp.append(padChr);
                }
                return tmp.toString().getBytes(CHARSET);
            }

            /**
             * 将数字转化成ASCII码对应的字符，用于对明文进行补码
             *
             * @param a 需要转化的数字
             * @return 转化得到的字符
             */
            static char chr(int a) {
                byte target = (byte) (a & 0xFF);
                return (char) target;
            }

        }
    }

    /**
     * 签名工具类
     *
     * <AUTHOR> 2021/09/06
     */
    public static final class Signature {
        private Signature() {
        }

        /**
         * 签名
         *
         * @param token     签名token
         * @param timeStamp 时间戳
         * @param content   待签名的内容
         * @return 签名
         */
        public static String signature(String token, String timeStamp,
                                       String content) throws Exception {
            return Sha1.getSha1(token, timeStamp, content);
        }
    }

    /**
     * 计算消息签名接口
     *
     * <AUTHOR> 2021/09/06
     */
    private static final class Sha1 {
        private Sha1() {
        }

        /**
         * 用SHA1算法生成安全签名
         *
         * @param token     票据
         * @param timestamp 时间戳
         * @param encrypt   密文
         * @return 安全签名
         * @throws Exception 处理异常
         */
        public static String getSha1(String token, String timestamp, String encrypt) throws Exception {
            try {
                String[] array = new String[]{token, timestamp, encrypt};
                StringBuilder sb = new StringBuilder();
                // 字符串排序
                Arrays.sort(array);
                for (int i = 0; i < 3; i++) {
                    sb.append(array[i]);
                }
                String str = sb.toString();
                // SHA1签名生成
                MessageDigest md = MessageDigest.getInstance("SHA-1");
                md.update(str.getBytes());
                byte[] digest = md.digest();

                StringBuilder hexStr = new StringBuilder();
                String shaHex;
                for (byte b : digest) {
                    shaHex = Integer.toHexString(b & 0xFF);
                    if (shaHex.length() < 2) {
                        hexStr.append(0);
                    }
                    hexStr.append(shaHex);
                }
                return hexStr.toString();
            } catch (Exception e) {
                throw new Exception("sha加密生成签名失败", e);
            }
        }
    }
    /**
     * 去除JCE限制
     */
    private static void removeJceLimit() {
        BaseBean baseBean = new BaseBean();
        //去除JCE加密限制，只限于Java1.8
        try {
            Field field = Class.forName("javax.crypto.JceSecurity").getDeclaredField("isRestricted");
            field.setAccessible(true);
            Field modifiersField = Field.class.getDeclaredField("modifiers");
            modifiersField.setAccessible(true);
            modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL);
            field.set(null, false);
            baseBean.writeLog("============= remove the key size restriction Success =============");
        } catch (ClassNotFoundException | NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException ex) {
            baseBean.writeLog(" ======出错鸟:"+ex);
            ex.printStackTrace(System.err);
        }
    }
}

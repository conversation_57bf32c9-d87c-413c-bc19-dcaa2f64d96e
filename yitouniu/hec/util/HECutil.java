package yitouniu.hec.util;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONObject;
import weaver.general.BaseBean;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;

import java.util.HashMap;
import java.util.Map;

public class HECutil {
    BaseBean baseBean = new BaseBean();


    /**
     * 获取token , 为了解决偶发性的接口连接报错，获取到token为空时重新获取一次。
     * @return
     */
    public String getToken(){
        String access_token  = getTokenOld();
        if("".equals(access_token)){
            access_token = getTokenOld();
        }
        return access_token;
    }


    /**
     * 获取token
     * @return
     */
    public String getTokenOld(){
        String access_token = "";
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("client_id","ry1tn-hec");
            jsonAll.put("grant_type","client_credentials");
            jsonAll.put("client_secret","P8fz8QHkvriE53sl");
            param.put("eventKey","getToken");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            if(!"".equals(re)){
                JSONObject rejsonAll = JSONObject.fromObject(re);
                String code = rejsonAll.get("code").toString();
                if("100".equals(code)){
                    JSONObject rejsonData = JSONObject.fromObject(rejsonAll.get("data"));
                    access_token = rejsonData.get("access_token").toString();
                }
            }
            return access_token;
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
        return access_token;
    }

    public String getToken_java(){
        String access_token = "";
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            param.put("eventKey","getTokenNew");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            if(!"".equals(re)){
                JSONObject rejsonAll = JSONObject.fromObject(re);
                String code = rejsonAll.get("code").toString();
                if("100".equals(code)){
                    JSONObject rejsonData = JSONObject.fromObject(rejsonAll.get("data"));
                    access_token = rejsonData.get("token").toString();
                }
            }
            return access_token;
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
        return access_token;
    }

}

package yitouniu.hec.job;

import weaver.conn.RecordSet;
import weaver.interfaces.schedule.BaseCronJob;

/**
 *
 */
public class set<PERSON><PERSON>si extends BaseCronJob{

    @Override
    public void execute() {
        RecordSet recordSet = new RecordSet();
        RecordSet recordSet2 = new RecordSet();
        String sql = "select * from HrmSubCompany where supsubcomid <> 0 and ([canceled] <>1 or canceled is null)";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String gsmc = recordSet.getString("id");
            // 设置建模模块 “公司板块对应”基础数据，若新增公司不在建模数据内，就新增上
            String sql2 = " select * from uf_gsbkdyb where gsmc = '"+gsmc+"' ";
            recordSet2.executeQuery(sql2);
            if(!recordSet2.next()){
                //新增
                sql2 = " insert into  uf_gsbkdyb (formmodeid, modedatacreater   , modedatacreatertype , gsmc) " +
                        " values (69,1,0,'"+gsmc+"') ";
                recordSet2.executeUpdate(sql2);
            }
            // 设置建模模块 “SAP组织代码映射”基础数据，若新增公司不在建模数据内，就新增上
            sql2 = " select * from uf_zzdmys where oagsmc = '"+gsmc+"' ";
            recordSet2.executeQuery(sql2);
            if(!recordSet2.next()){
                //新增
                sql2 = " insert into  uf_zzdmys (formmodeid , modedatacreater , modedatacreatertype , oagsmc) " +
                        " values (24,1,0,'"+gsmc+"') ";
                recordSet2.executeUpdate(sql2);
            }
        }
    }

}

package yitouniu.hec.job;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * 设置 员工工号，和登陆名保持一致
 */
public class SetResourceWorkcode extends BaseCronJob {

    @Override
    public void execute() {
        BaseBean baseBean = new BaseBean();
        baseBean.writeLog("=========设置员工工号，和登陆名保持一致  srart=====");
        RecordSet recordSet = new RecordSet();
        String sql = "select * from hrmresource where  workcode <>  loginid and status < 4";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            baseBean.writeLog("=====lastname:"+recordSet.getString("lastname")+",workcode:"+recordSet.getString("workcode")+",loginid:"+recordSet.getString("loginid"));
        }
        sql = " update hrmresource set workcode =loginid , modified = (select GETDATE()) where workcode <>  loginid and status < 4";
        recordSet.executeUpdate(sql);
        baseBean.writeLog("=========设置员工工号，和登陆名保持一致  end=====");
    }
}

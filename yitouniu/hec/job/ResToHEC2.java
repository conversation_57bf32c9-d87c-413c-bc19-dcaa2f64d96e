package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;

public class ResToHEC2 extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();

        JSONArray employees_arr = new JSONArray();
        JSONObject employees = null ;
        RecordSet recordSet = new RecordSet();
        String sql = " select b.rybh,d.name,a.hrmresource a left join uf_SAPrenyuan b on a.id = b.oary " +
                " left join cus_fielddata c on a.id = c.id and c.scopeid = 1 " +
                " left join hrmjobcall d on c.field3 = d.id " +
                " where loginid in('c010001',\n" +
                "'c010184',\n" +
                "'c010059',\n" +
                "'c020025',\n" +
                "'c020036',\n" +
                "'c030167',\n" +
                "'c030203',\n" +
                "'c060059',\n" +
                "'c040059',\n" +
                "'c040059',\n" +
                "'c110131',\n" +
                "'c110235',\n" +
                "'c080058',\n" +
                "'c080058',\n" +
                "'c010346',\n" +
                "'c070067',\n" +
                "'c110585',\n" +
                "'c110624',\n" +
                "'c010644',\n" +
                "'c110918',\n" +
                "'c040362',\n" +
                "'c050233',\n" +
                "'c010773',\n" +
                "'c111016',\n" +
                "'c040385',\n" +
                "'c020111',\n" +
                "'c020115',\n" +
                "'c050338',\n" +
                "'c020129',\n" +
                "'c010827',\n" +
                "'c111045',\n" +
                "'c010847',\n" +
                "'c010854',\n" +
                "'c010868',\n" +
                "'c010874',\n" +
                "'c080335',\n" +
                "'c080363',\n" +
                "'c080378',\n" +
                "'c010906',\n" +
                "'c010908',\n" +
                "'c010909',\n" +
                "'c030517',\n" +
                "'c010953',\n" +
                "'c160032',\n" +
                "'c010970',\n" +
                "'c080399',\n" +
                "'c040475',\n" +
                "'c011000',\n" +
                "'c011000',\n" +
                "'c011003',\n" +
                "'c011010',\n" +
                "'c011012',\n" +
                "'c011089'); ";
        //String sql = " select b.rybh,a.* from hrmresource a left join uf_SAPrenyuan b on a.id = b.oary   where a.workcode <> '' ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            employees = new JSONObject() ;
            employees.put("userCode",recordSet.getString("workcode"));
            employees.put("employeeId",recordSet.getString("id"));
            employees.put("employeeCode",recordSet.getString("workcode"));
            employees.put("employeeName",recordSet.getString("lastname"));
            employees.put("email",recordSet.getString("email"));
            employees.put("mobil",recordSet.getString("mobile"));
            employees.put("phone",recordSet.getString("telephone"));
            employees.put("status",recordSet.getString("status"));
            employees.put("employeeLevel",recordSet.getString("seclevel"));
            employees.put("companyCode",recordSet.getString("subcompanyid1"));
            employees.put("unitCode",recordSet.getString("departmentid"));
            String rybh = recordSet.getString("rybh");
            if("".equals(rybh)){
                rybh = getCode(recordSet.getString("id"));
            }
            employees.put("customerCode",rybh);
            employees.put("superiorId",recordSet.getString("managerid"));
            employees_arr.add(employees);
        }
        toHEC(token,employees_arr);
    }

    public void toHEC(String token , JSONArray employees_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","EMPLOYEE_SYNC");
            jsonAll.put("token",token);
            jsonAll.put("employees",employees_arr);
            param.put("eventKey","resToHEC");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步人员接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }

    public static String getCode(String userid){
        RecordSet rs = new RecordSet();
        rs.executeQuery("select a.sapkhbh from formtable_main_354 a, uf_SAPrenyuan b where a.sqr = b.oary and a.cbzxbh = b.KOSTL and a.sqr=?",userid);
        if(rs.next()){
            return rs.getString("sapkhbh");
        }
        return "";
    }

}

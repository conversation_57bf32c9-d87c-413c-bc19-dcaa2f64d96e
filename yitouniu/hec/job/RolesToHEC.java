package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;

public class RolesToHEC extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();

        JSONArray roleses_arr = new JSONArray();
        JSONObject roleses = null ;
        RecordSet recordSet = new RecordSet();

        String sql = " select b.id,b.rolesmark,a.resourceid,c.id userid,c.workcode,c.lastname from hrmrolemembers a left join hrmroles b on a.roleid = b.id left join hrmresource c on a.resourceid = c.id " +
                " where a.resourcetype = 1 and c.status < 4 " +
                " union " +
                " select b.id,b.rolesmark,a.resourceid,c.id userid,c.workcode,c.lastname from hrmrolemembers a left join hrmroles b on a.roleid = b.id left join hrmresource c on a.resourceid = c.subcompanyid1 " +
                " where a.resourcetype = 2 and c.status < 4 " +
                " union " +
                " select b.id,b.rolesmark,a.resourceid,c.id userid,c.workcode,c.lastname from hrmrolemembers a left join hrmroles b on a.roleid = b.id left join hrmresource c on a.resourceid = c.departmentid " +
                " where a.resourcetype = 3 and c.status < 4 " +
                " union " +
                " select b.id,b.rolesmark,a.resourceid,c.id userid,c.workcode,c.lastname from hrmrolemembers a left join hrmroles b on a.roleid = b.id left join hrmresource c on a.resourceid = c.jobtitle " +
                " where a.resourcetype = 5 and c.status < 4 ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            roleses = new JSONObject() ;
            roleses.put("roleCode",recordSet.getString("id"));
            roleses.put("roleName",recordSet.getString("rolesmark"));
            roleses.put("employeeId",recordSet.getString("userid"));
            roleses.put("employeeCode",recordSet.getString("workcode"));
            roleses.put("employeeName",recordSet.getString("lastname"));
            roleses.put("enabledFlag","1");
            roleses_arr.add(roleses);
        }
        toHEC(token,roleses_arr);
    }

    private void toHEC(String token , JSONArray roleses_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","OA_EMPLOYEE_ROLE");
            jsonAll.put("token",token);
            jsonAll.put("oaEmployeeRoles",roleses_arr);
            param.put("eventKey","rolesToHEC");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步角色接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }
}

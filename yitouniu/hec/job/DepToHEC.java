package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;

public class DepToHEC extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();
        baseBean.writeLog(" ======token=====:"+token);
        JSONArray units_arr = new JSONArray();
        JSONObject units = null ;
        RecordSet recordSet = new RecordSet();
        String sql = " SELECT * FROM hrmdepartment where modified >= GETDATE()-2 ";
        //String sql = " SELECT * FROM hrmdepartment  where id <>1 and (canceled =0 or canceled is null) and subcompanyid1 not in (7,9,10,12,13,15,16,21) ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            units = new JSONObject() ;
            units.put("companyCode",recordSet.getString("subcompanyid1"));
            units.put("unitCode",recordSet.getString("id"));
            units.put("description",recordSet.getString("departmentname"));
            units.put("parentUnitCode",recordSet.getString("supdepid"));
            units.put("enabledFlag","1".equals(recordSet.getString("canceled"))?"0":"1");
            units_arr.add(units);
        }
        toHEC(token,units_arr);
    }

    public void toHEC(String token , JSONArray units_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","UNIT_SYNC");
            jsonAll.put("token",token);
            jsonAll.put("units",units_arr);
            param.put("eventKey","depToHEC");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步部门接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }
}

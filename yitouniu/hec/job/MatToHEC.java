package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;


public class MatToHEC extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();
        JSONArray approvals_arr = new JSONArray();
        RecordSet recordSet = new RecordSet();

        //region 杭州一头牛部门矩阵
        String sql = " SELECT a.* FROM Matrixtable_1 a left join HrmDepartment b on a.bm = b.id where (b.canceled <> '1' or b.canceled is null) and a.bm is not null and a.bm <> '' ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String bm = recordSet.getString("bm");
            String bmzg = recordSet.getString("bmzg");
            String hrbp = recordSet.getString("hrbp");
            String fgzj = recordSet.getString("fgzj");
            String cwbp = recordSet.getString("cwbp");
            String xsnq = recordSet.getString("xsnq");
            String sjbp = recordSet.getString("sjbp");
            String cnbp = recordSet.getString("cnbp");
            String bpfzr = recordSet.getString("bpfzr");
            String sjbmzg = recordSet.getString("sjbmzg");
            String xxtg = recordSet.getString("xxtg");
            String jtyjbmfg = recordSet.getString("jtyjbmfg");
            String yjbmffzr = recordSet.getString("yjbmffzr");
            String sybzjl = recordSet.getString("sybzjl");
            String sybfzjl = recordSet.getString("sybfzjl");
            String yyzc = recordSet.getString("yyzc");
            String xszcjdhpsy = recordSet.getString("xszcjdhpsy");
            String cpglzxfzr = recordSet.getString("cpglzxfzr");
            String zbxxtg = recordSet.getString("zbxxtg");
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","bmzg","二级部门主管","",bm,bmzg);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","hrbp","人事BP","",bm,hrbp);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","fgzj","一级部门主管","",bm,fgzj);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","cwbp","财务BP","",bm,cwbp);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","xsnq","销售内勤","",bm,xsnq);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","sjbp","设计BP","",bm,sjbp);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","cnbp","出纳BP","",bm,cnbp);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","bpfzr","BP负责人","",bm,bpfzr);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","sjbmzg","三级部门主管","",bm,sjbmzg);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","xxtg","行销推广","",bm,xxtg);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","jtyjbmfg","部门VP(分管副总)","",bm,jtyjbmfg);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","yyzc","运营支持","",bm,yyzc);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","yjbmffzr","一级部门副负责人","",bm,yjbmffzr);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","sybzjl","事业部总经理","",bm,sybzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","sybfzjl","事业部副总经理","",bm,sybfzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","xszcjdhpsy","销售支持(借调货品使用)","",bm,xszcjdhpsy);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","cpglzxfzr","产品管理中心负责人","",bm,cpglzxfzr);
            approvals_arr = getApprovalsJson(approvals_arr,"杭州一头牛部门矩阵","Matrixtable_1","zbxxtg","总部行销推广","",bm,zbxxtg);
        }
        //endregion

        //region 公司矩阵
        sql = " SELECT * FROM Matrixtable_2  ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String gs = recordSet.getString("gs");
            String rszg = recordSet.getString("rszg");
            String cwzg = recordSet.getString("cwzg");
            String xzzg = recordSet.getString("xzzg");
            String xzzy = recordSet.getString("xzzy");
            String xczy = recordSet.getString("xczy");
            String cn = recordSet.getString("cn");
            String wlzy = recordSet.getString("wlzy");
            String xshj = recordSet.getString("xshj");
            String zjl = recordSet.getString("zjl");
            String sjzy = recordSet.getString("sjzy");
            String cwzj = recordSet.getString("cwzj");
            String zchj = recordSet.getString("zchj");
            String gsyxsmry = recordSet.getString("gsyxsmry");

            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","rszg","人事主管",gs,"",rszg);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","cwzg","财务主管",gs,"",cwzg);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","xzzg","行政主管",gs,"",xzzg);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","xzzy","行政专员",gs,"",xzzy);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","xczy","人事专员",gs,"",xczy);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","cn","出纳",gs,"",cn);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","wlzy","物流专员",gs,"",wlzy);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","xshj","销售会计",gs,"",xshj);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","zjl","总经理",gs,"",zjl);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","sjzy","设计专员",gs,"",sjzy);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","cwzj","财务总监",gs,"",cwzj);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","zchj","资产会计",gs,"",zchj);
            approvals_arr = getApprovalsJson(approvals_arr,"公司矩阵","Matrixtable_2","gsyxsmry","公司影像扫描人员",gs,"",gsyxsmry);
        }
        //endregion

        //region 牧场部门矩阵
        sql = " SELECT a.* FROM Matrixtable_3 a left join HrmDepartment b on a.bm = b.id where (b.canceled <> '1' or b.canceled is null) and a.bm is not null and a.bm <> ''  ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String bm = recordSet.getString("bm");
            String bmfzr = recordSet.getString("bmfzr");
            String bmzj = recordSet.getString("bmzj");
            String pkzg = recordSet.getString("pkzg");
            approvals_arr = getApprovalsJson(approvals_arr,"牧场部门矩阵","Matrixtable_3","bmfzr","部门负责人","",bm,bmfzr);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场部门矩阵","Matrixtable_3","bmzj","分管副场","",bm,bmzj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场部门矩阵","Matrixtable_3","pkzg","品控主管","",bm,pkzg);
        }
        //endregion

        //region 牧场公司矩阵
        sql = " SELECT * FROM Matrixtable_4  ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String gs = recordSet.getString("gs");
            String sybzjl = recordSet.getString("sybzjl");
            String zhbhr = recordSet.getString("zhbhr");
            String zhbzg = recordSet.getString("zhbzg");
            String cc = recordSet.getString("cc");
            String sybrlzyzj = recordSet.getString("sybrlzyzj");
            String cwzj = recordSet.getString("cwzj");
            String sybcxzj = recordSet.getString("sybcxzj");
            String fcc = recordSet.getString("fcc");
            String cwzg = recordSet.getString("cwzg");
            String hj = recordSet.getString("hj");
            String jj = recordSet.getString("jj");
            String jszj = recordSet.getString("jszj");
            String xxzg = recordSet.getString("xxzg");
            String cn = recordSet.getString("cn");
            String zhbcgy = recordSet.getString("zhbcgy");
            String rszy = recordSet.getString("rszy");
            String syfgcc = recordSet.getString("syfgcc");
            String syzg = recordSet.getString("syzg");
            String pkzg = recordSet.getString("pkzg");
            String cbhj = recordSet.getString("cbhj");
            String zc = recordSet.getString("zc");
            String kg = recordSet.getString("kg");
            String fyhj = recordSet.getString("fyhj");
            String jkhj = recordSet.getString("jkhj");
            String fgfz = recordSet.getString("fgfz");
            String lzfz = recordSet.getString("lzfz");
            String jsjl = recordSet.getString("jsjl");
            String cwzfrz = recordSet.getString("cwzfrz");
            String htbgy = recordSet.getString("htbgy");
            String xskp = recordSet.getString("xskp");
            String xxb = recordSet.getString("xxb");
            String gsyxsmry = recordSet.getString("gsyxsmry");

            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","sybzjl","事业部总经理",gs,"",sybzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","zhbhr","综合部HR",gs,"",zhbhr);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","zhbzg","综合部主管",gs,"",zhbzg);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cc","场长",gs,"",cc);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","sybrlzyzj","事业部人力资源总监",gs,"",sybrlzyzj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cwzj","财务总监",gs,"",cwzj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","sybcxzj","事业部采销总监",gs,"",sybcxzj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","fcc","副厂长",gs,"",fcc);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cwzg","财务主管",gs,"",cwzg);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","hj","销售会计",gs,"",hj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","jj","基建",gs,"",jj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","jszj","技术总监",gs,"",jszj);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","xxzg","信息主管",gs,"",xxzg);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cn","出纳",gs,"",cn);
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","zhbcgy","综合部采购员",gs,"",zhbcgy  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","rszy","人事专员",gs,"",rszy  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","syfgcc","饲养分管场长",gs,"",syfgcc  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","syzg","饲养主管",gs,"",syzg  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","pkzg","品控主管",gs,"",pkzg  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cbhj","成本会计",gs,"",cbhj  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","zc","资产会计",gs,"",zc  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","kg","库管",gs,"",kg  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","fyhj","费用会计",gs,"",fyhj  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","jkhj","借款会计",gs,"",jkhj  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","fgfz","分管副总",gs,"",fgfz  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","lzfz","轮值副总",gs,"",lzfz  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","jsjl","公章/合同章类",gs,"",jsjl  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","cwzfrz","财务章/法人章",gs,"",cwzfrz  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","htbgy","合同保管员",gs,"",htbgy  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","xskp","销售开票",gs,"",xskp  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","xxb","信息部",gs,"",xxb  );
            approvals_arr = getApprovalsJson(approvals_arr,"牧场公司矩阵","Matrixtable_4","gsyxsmry","公司影像扫描人员",gs,"",gsyxsmry  );
        }
        //endregion

        //region 工厂部门矩阵
        sql = " SELECT a.* FROM Matrixtable_5 a left join HrmDepartment b on a.bm = b.id where (b.canceled <> '1' or b.canceled is null) and a.bm is not null and a.bm <> '' ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String bm = recordSet.getString("bm");
            String bmjl = recordSet.getString("bmjl");
            String fgld = recordSet.getString("fgld");
            String kqtjy = recordSet.getString("kqtjy");

            approvals_arr = getApprovalsJson(approvals_arr,"工厂部门矩阵","Matrixtable_5","bmjl","部门经理","",bm,bmjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂部门矩阵","Matrixtable_5","fgld","分管领导","",bm,fgld);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂部门矩阵","Matrixtable_5","kqtjy","考勤统计员","",bm,kqtjy);
        }
        //endregion

        //region 工厂公司矩阵
        sql = " SELECT * FROM Matrixtable_6  ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String gs = recordSet.getString("gs");
            String rsjl = recordSet.getString("rsjl");
            String cwjl = recordSet.getString("cwjl");
            String gybjl = recordSet.getString("gybjl");
            String jsbjl = recordSet.getString("jsbjl");
            String sbbjl = recordSet.getString("sbbjl");
            String cybjl = recordSet.getString("cybjl");
            String scbjl = recordSet.getString("scbjl");
            String cc = recordSet.getString("cc");
            String fyhj = recordSet.getString("fyhj");
            String zchj = recordSet.getString("zchj");
            String cbhj = recordSet.getString("cbhj");
            String cn = recordSet.getString("cn");
            String cgy = recordSet.getString("cgy");
            String xshj = recordSet.getString("xshj");
            String cgjl = recordSet.getString("cgjl");
            String xxjl = recordSet.getString("xxjl");
            String rszy = recordSet.getString("rszy");
            String xzzg = recordSet.getString("xzzg");
            String yxsmry = recordSet.getString("yxsmry");


            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","rsjl","人事经理",gs,"",rsjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cwjl","财务经理",gs,"",cwjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","gybjl","采购部经理",gs,"",gybjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","jsbjl","技术部经理",gs,"",jsbjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","sbbjl","设备部经理",gs,"",sbbjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cybjl","储运部经理",gs,"",cybjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","scbjl","生产部经理",gs,"",scbjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cc","工厂总经理",gs,"",cc);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","fyhj","费用会计",gs,"",fyhj);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","zchj","资产会计",gs,"",zchj);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cbhj","成本会计",gs,"",cbhj);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cn","出纳",gs,"",cn);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cgy","采购员",gs,"",cgy);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","xshj","销售会计",gs,"",xshj);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","cgjl","采购经理",gs,"",cgjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","xxjl","信息部经理",gs,"",xxjl);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","rszy","人事主管",gs,"",rszy);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","xzzg","行政主管",gs,"",xzzg);
            approvals_arr = getApprovalsJson(approvals_arr,"工厂公司矩阵","Matrixtable_6","yxsmry","影像扫描人员",gs,"",yxsmry);
        }
        //endregion


        //region 肉业屠宰公司矩阵
        sql = " SELECT * FROM Matrixtable_7  ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String gs = recordSet.getString("gs");
            String rszg = recordSet.getString("rszg");
            String cwzg = recordSet.getString("cwzg");
            String xzzg = recordSet.getString("xzzg");
            String zjl = recordSet.getString("zjl");
            String cwbp = recordSet.getString("cwbp");
            String gzbgy = recordSet.getString("gzbgy");
            String cwzj = recordSet.getString("cwzj");
            String frzbgy = recordSet.getString("frzbgy");
            String cwzbgy = recordSet.getString("cwzbgy");
            String htzbgy = recordSet.getString("htzbgy");
            String htgdy = recordSet.getString("htgdy");
            String zchj = recordSet.getString("zchj");
            String xshj = recordSet.getString("xshj");
            String fyhj = recordSet.getString("fyhj");
            String cbhj = recordSet.getString("cbhj");
            String yxsmry = recordSet.getString("yxsmry");
            String sybrlfzr = recordSet.getString("sybrlfzr");

            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","rszg","人事行政经理",gs,"",rszg);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","cwzg","财务经理",gs,"",cwzg);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","xzzg","行政主管",gs,"",xzzg);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","zjl","总经理（废弃）",gs,"",zjl);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","cwbp","财务总监",gs,"",cwbp);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","gzbgy","公章保管员",gs,"",gzbgy);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","cwzj","财务总监",gs,"",cwzj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","frzbgy","法人章保管员",gs,"",frzbgy);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","cwzbgy","财务章保管员",gs,"",cwzbgy);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","htzbgy","合同章-保管员",gs,"",htzbgy);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","htgdy","合同-归档员",gs,"",htgdy);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","zchj","资产会计",gs,"",zchj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","xshj","销售会计",gs,"",xshj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","fyhj","费用会计",gs,"",fyhj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","cbhj","成本会计",gs,"",cbhj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","yxsmry","影像扫描人员",gs,"",yxsmry);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰公司矩阵","Matrixtable_7","sybrlfzr","事业部人力负责人",gs,"",sybrlfzr);
        }
        //endregion

        //region 肉业屠宰部门矩阵
        sql = " SELECT a.* FROM Matrixtable_9 a left join HrmDepartment b on a.bm = b.id where (b.canceled <> '1' or b.canceled is null) and a.bm is not null and a.bm <> '' ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            String bm = recordSet.getString("bm");
            String yjbmzg = recordSet.getString("yjbmzg");
            String fgzj = recordSet.getString("fgzj");
            String bpfzr = recordSet.getString("bpfzr");
            String cwbp = recordSet.getString("cwbp");
            String rsbp = recordSet.getString("rsbp");
            String sjbmzg = recordSet.getString("sjbmzg");
            String ejbmfzr = recordSet.getString("ejbmfzr");
            String sczxfzjl = recordSet.getString("sczxfzjl");
            String sczxzjl = recordSet.getString("sczxzjl");
            String sybfzjl = recordSet.getString("sybfzjl");
            String sybzjl = recordSet.getString("sybzjl");
            String yyzc = recordSet.getString("yyzc");

            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","yjbmzg","一级部门负责人","",bm,yjbmzg);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","fgzj","分管总监","",bm,fgzj);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","bpfzr","BP负责人","",bm,bpfzr);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","cwbp","财务BP","",bm,cwbp);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","rsbp","人事BP","",bm,rsbp);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","sjbmzg","三级部门主管","",bm,sjbmzg);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","ejbmfzr","二级部门负责人","",bm,ejbmfzr);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","sczxfzjl","生产中心副总经理","",bm,sczxfzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","sczxzjl","生产中心总经理","",bm,sczxzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","sybfzjl","事业部副总经理","",bm,sybfzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","sybzjl","事业部总经理","",bm,sybzjl);
            approvals_arr = getApprovalsJson(approvals_arr,"肉业屠宰部门矩阵","Matrixtable_9","yyzc","运营支持","",bm,yyzc);
        }
        //endregion

        baseBean.writeLog("============矩阵数据："+approvals_arr.toString()+"       矩阵条数："+approvals_arr.size());

        toHEC(token,approvals_arr);
    }



    private JSONArray getApprovalsJson(JSONArray approvals_arr, String oaTableName, String oaTableCode, String approvalRoleCode,
                                       String approvalRoleName ,String companyId,  String unitId,String userids) {
        if("".equals(userids) || userids ==null){
            return approvals_arr ;
        }
        String[] _userid = userids.split(",");
        for(int i=0; i<_userid.length;i++){
            String userid = _userid[i];
            JSONObject approvals = new JSONObject() ;
            approvals.put("oaTableCode",oaTableCode);
            approvals.put("oaTableName",oaTableName);
            if("".equals(companyId) || companyId ==null){
                //通过部门找分部id
                approvals = setFbByBmId(approvals,unitId);
            } else {
                approvals = setFbByFmId(approvals,companyId);
            }
            //部门信息
            approvals = setBmByBmId(approvals,unitId);
            //人员信息
            approvals = setUserByUserId(approvals,userid);
            //矩阵角色信息
            approvals.put("approvalRoleCode",approvalRoleCode);
            approvals.put("approvalRoleName",approvalRoleName);
            approvals.put("enabledFlag",1);
            approvals_arr.add(approvals);
        }
        return approvals_arr;
    }

    /**
     * 通过 userid  设置人员id、编码、名称
     * @param approvals
     * @param userid
     * @return
     */
    private JSONObject setUserByUserId(JSONObject approvals, String userid) {
        RecordSet recordSet = new RecordSet() ;
        String sql = " select id,workcode,lastname from hrmresource where id = ? ";
        recordSet.executeQuery(sql,userid);
        if(recordSet.next()){
            approvals.put("employeeId",recordSet.getString("id"));
            approvals.put("employeeCode",recordSet.getString("workcode"));
            approvals.put("employeeName",recordSet.getString("lastname"));
        }
        return approvals;
    }


    /**
     * 通过部门设置  部门id、名称
     * @param approvals
     * @param unitId
     * @return
     */
    private JSONObject setBmByBmId(JSONObject approvals, String unitId) {
        if(!"".equals(unitId) && unitId !=null){
            RecordSet recordSet = new RecordSet() ;
            String sql = "  select id,departmentname from hrmdepartment where id = ? ";
            recordSet.executeQuery(sql,unitId);
            if(recordSet.next()){
                approvals.put("unitId",recordSet.getString("id"));
                approvals.put("unitName",recordSet.getString("departmentname"));
            }
        }
        return approvals ;
    }


    /**
     * 通过分部设置  分部id、名称
     * @param approvals
     * @param unitId
     * @return
     */
    private JSONObject setFbByFmId(JSONObject approvals, String unitId) {
        RecordSet recordSet = new RecordSet() ;
        String sql = " select id,subcompanyname from hrmsubcompany where id = ? ";
        recordSet.executeQuery(sql,unitId);
        if(recordSet.next()){
            approvals.put("companyId",recordSet.getString("id"));
            approvals.put("companyName",recordSet.getString("subcompanyname"));
        }
        return approvals ;
    }

    /**
     * 通过部门设置  分部id、名称
     * @param approvals
     * @param unitId
     * @return
     */
    private JSONObject setFbByBmId(JSONObject approvals, String unitId) {
        RecordSet recordSet = new RecordSet() ;
        String sql = " select b.id,b.subcompanyname from hrmdepartment a left join hrmsubcompany b on a.subcompanyid1 = b.id  where a.id = ? ";
        recordSet.executeQuery(sql,unitId);
        if(recordSet.next()){
            approvals.put("companyId",recordSet.getString("id"));
            approvals.put("companyName",recordSet.getString("subcompanyname"));
        }
        return approvals ;
    }


    private void toHEC(String token , JSONArray approvals_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("approvalMatrix",approvals_arr);
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","APPROVAL_MATRIX");
            jsonAll.put("token",token);
            param.put("eventKey","matToHEC");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步矩阵接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }
}

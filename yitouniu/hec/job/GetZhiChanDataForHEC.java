package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.formmode.setup.ModeRightInfo;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.text.SimpleDateFormat;
import java.util.*;

public class GetZhiChanDataForHEC extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        // 资产：ASSET
        String token = heCutil.getToken();
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","DIMENSION");
            jsonAll.put("token",token);
            jsonAll.put("reqSystem","oa");
            jsonAll.put("reqTime",getDataTime(0));
            jsonAll.put("dimensionCode","ASSET");
            jsonAll.put("dateFrom",getDataTime(-10000));
            jsonAll.put("dateTo",getDataTime(0));
            param.put("eventKey","getHECZCSJ");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         获取资产数据接口返回数据："+re);
            //更新或新增数据到OA
            updateOaData(re);
        } catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }

        // 项目：PROJECT
        token = heCutil.getToken();
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","DIMENSION");
            jsonAll.put("token",token);
            jsonAll.put("reqSystem","oa");
            jsonAll.put("reqTime",getDataTime(0));
            jsonAll.put("dimensionCode","PROJECT");
            jsonAll.put("dateFrom",getDataTime(-10000));
            jsonAll.put("dateTo",getDataTime(0));
            param.put("eventKey","getHECZCSJ");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         获取资产数据接口返回数据："+re);
            //更新或新增数据到OA
            updateOaData(re);
        } catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }

        // 品项：PRODUCT
        token = heCutil.getToken();
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","DIMENSION");
            jsonAll.put("token",token);
            jsonAll.put("reqSystem","oa");
            jsonAll.put("reqTime",getDataTime(0));
            jsonAll.put("dimensionCode","PRODUCT");
            jsonAll.put("dateFrom",getDataTime(-10000));
            jsonAll.put("dateTo",getDataTime(0));
            param.put("eventKey","getHECZCSJ");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         获取资产数据接口返回数据："+re);
            //更新或新增数据到OA
            updateOaData(re);
        } catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }

    private void updateOaData(String re) {
        RecordSet recordSet = new RecordSet() ;
        RecordSet recordSet_up = new RecordSet() ;
        if(!"".equals(re)){
            JSONObject rejsonAll = JSONObject.fromObject(re);
            String code = rejsonAll.get("code").toString();
            if("100".equals(code)){
                JSONObject rejsonData = JSONObject.fromObject(rejsonAll.get("data"));
                JSONArray result = JSONArray.fromObject(rejsonData.get("result").toString()) ;
                for(int i=0;i<result.size();i++) {
                    JSONObject zcmx =  JSONObject.fromObject(result.get(i));
                    String wddm = zcmx.getString("dimensionCode");
                    String wdmc = zcmx.getString("dimensionName");
                    String wzdm = zcmx.getString("dimensionValueCode");
                    String wzmc = zcmx.getString("description");

                    JSONArray coDimensionResDTOS = JSONArray.fromObject(zcmx.getString("coDimensionResDTOS"));
                    for(int j=0;j<coDimensionResDTOS.size();j++) {
                        JSONObject glgs_l =  JSONObject.fromObject(coDimensionResDTOS.get(j));
                        String qybz = "Y".equals(glgs_l.getString("enabledFlag"))?"0":"1";
                        String glgs = glgs_l.getString("accEntity");
                        String sql = " select * from uf_zcsj where wzdm = ?  and glgs = ? ";
                        recordSet.executeQuery(sql,wzdm,glgs);
                        if(recordSet.next()){
                            sql = " update uf_zcsj set wddm = ? , wdmc =? , wzmc = ? , qybz = ?  where wzdm = ?  and  glgs = ?";
                            recordSet_up.executeUpdate(sql,wddm,wdmc,wzmc,qybz,wzdm,glgs);
                        }else {
                            sql = " insert into uf_zcsj ( wddm , wdmc, wzdm, wzmc , qybz ,glgs, formmodeid ,modedatacreater , modedatacreatertype ) values (?,?,?,?,?,?,?,?,?)";
                            recordSet_up.executeUpdate(sql,wddm,wdmc,wzdm,wzmc,qybz,glgs,65,1,0);
                            //recordSet_up.executeUpdate(sql,wddm,wdmc,wzdm,wzmc,qybz,glgs,3070,1,0);
                        }
                    }

                }
                setPermission(65);
            }
        }
    }

    /**
     *  获取当前 前后i天的日期时间数据
     * @param i
     * @return
     */
    private String getDataTime(int i) {
        String data_S = "";
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            Calendar calendar   =   new GregorianCalendar();
            calendar.setTime(date);
            calendar.add(calendar.DATE,i);//把日期往后增加一天.整数往后推,负数往前移动
            date=calendar.getTime();
            data_S = simpleDateFormat.format(date);
        }catch (Exception e){

        }
        return data_S ;
    }

    /**
     *  权限重构   传入FORMMODEID，将没有权限重构的数据重构
     */
    public static void setPermission( int FORMMODEID ) {
        RecordSet rs = new RecordSet();
        String sql = "select * from uf_zcsj where id not in (select sourceid from modeDataShare_"+FORMMODEID+")";
        try {
            rs.executeQuery(sql);
            while (rs.next()) {
                ModeRightInfo ModeRightInfo = new ModeRightInfo();
                ModeRightInfo.rebuildModeDataShareByEdit(1,FORMMODEID, Integer.valueOf(rs.getString("id")));
            }
        } catch (Exception e) {
        }
    }


}

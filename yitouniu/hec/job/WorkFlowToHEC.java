package yitouniu.hec.job;

import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
    import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;

/**
 * 差旅申请；牧场游申请；外出申请；特殊事项
 * 流程归档后推送至HEC
 */
public class WorkFlowToHEC implements Action {

    BaseBean baseBean = new BaseBean();

    @Override
    public String execute(RequestInfo requestInfo) {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();
        JSONArray approvals_arr = new JSONArray();
        RecordSet recordSet = new RecordSet();

        //获取流程信息
        requestInfo.getWorkflowid();



        toHEC(token,approvals_arr);
        return null;
    }




    private void toHEC(String token , JSONArray approvals_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","UNIT_SYNC");
            jsonAll.put("token",token);
            jsonAll.put("units",approvals_arr);
            param.put("eventKey","depToHEC");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步流程接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }


}

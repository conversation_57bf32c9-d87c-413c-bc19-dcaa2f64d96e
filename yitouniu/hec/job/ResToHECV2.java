package yitouniu.hec.job;

import com.alibaba.fastjson.JSON;
import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;
import yitouniu.hec.util.HECutil;

import java.util.HashMap;
import java.util.Map;

public class ResToHECV2 extends BaseCronJob {

    BaseBean baseBean = new BaseBean();

    @Override
    public void execute() {
        //获取token
        HECutil heCutil = new HECutil();
        String token = heCutil.getToken();

        JSONArray employees_arr = new JSONArray();
        JSONObject employees = null ;
        RecordSet recordSet = new RecordSet();
        String sql = " select b.rybh,d.name,a.* from hrmresource a left join uf_SAPrenyuan b on a.id = b.oary " +
                " left join cus_fielddata c on a.id = c.id and c.scopeid = 1 " +
                " left join hrmjobcall d on c.field3 = d.id " +
                " where modified >= GETDATE()-2 or modedatamodifydatetime  >= GETDATE()-2 or modedatacreatedate  >= GETDATE()-2 ";
        //String sql = " select b.rybh,a.* from hrmresource a left join uf_SAPrenyuan b on a.id = b.oary   where a.workcode <> '' ";
        recordSet.executeQuery(sql);
        while (recordSet.next()){
            employees = new JSONObject() ;
            employees.put("userCode",recordSet.getString("workcode"));
            employees.put("employeeId",recordSet.getString("id"));
            employees.put("employeeCode",recordSet.getString("workcode"));
            employees.put("employeeName",recordSet.getString("lastname"));
            employees.put("email",recordSet.getString("email"));
            employees.put("mobil",recordSet.getString("mobile"));
            employees.put("phone",recordSet.getString("telephone"));
            employees.put("status",recordSet.getString("status"));
            employees.put("employeeLevel",recordSet.getString("seclevel"));
            employees.put("companyCode",recordSet.getString("subcompanyid1"));
            employees.put("unitCode",recordSet.getString("departmentid"));
            String rybh = recordSet.getString("rybh");
            if("".equals(rybh)){
                rybh = getCode(recordSet.getString("id"));
            }
            //更新sap人员映射表的数据
            RecordSet rs1 = new RecordSet();
            rs1.executeUpdate("update uf_SAPrenyuan set rybh = ? where oary = ?", rybh, recordSet.getString("id"));

            employees.put("customerCode",rybh);
            employees.put("superiorId",recordSet.getString("managerid"));
            employees_arr.add(employees);
        }
        toHEC(token,employees_arr);
    }

    public void toHEC(String token , JSONArray employees_arr) {
        try {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","EMPLOYEE_SYNC");
            jsonAll.put("token",token);
            jsonAll.put("employees",employees_arr);
            param.put("eventKey","resToHEC");
            param.put("jsonAll",jsonAll);
            baseBean.writeLog("同步人员toHEC：" + JSON.toJSONString(param));
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         同步人员接口："+re);
        }catch (Exception e){
            baseBean.writeLog("         出错了："+e);
        }
    }

    public static String getCode(String userid){
        RecordSet rs = new RecordSet();
        rs.executeQuery("select a.sapkhbh from formtable_main_354 a, uf_SAPrenyuan b where a.sqr = b.oary and a.cbzxbh = b.KOSTL and a.sqr=?",userid);
        if(rs.next()){
            return rs.getString("sapkhbh");
        }
        return "";

    }

}

package yitouniu.hec.workflow;

import cn.hutool.core.collection.CollUtil;
import com.engine.common.util.ServiceUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.*;
import yitouniu.hec.services.ToHECServices;
import yitouniu.hec.services.impl.ToHECServicesImpl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * HEC 预算校验接口
 */
public class YuSuanJiaoYan implements Action {

    BaseBean baseBean = new BaseBean();

    RecordSet rs = new RecordSet();


    @Override
    public String execute(RequestInfo requestInfo) {
        String requestid = requestInfo.getRequestid();
        String tablename = requestInfo.getRequestManager().getBillTableName();
        //流程编号
        String lcbh = "";
        //申请人
        String sqr = "";
        //费用归属部门
        String fygzbm = "";
        //费用归属主体
        String fygzzt = "";
        //申请日期
        String sqrq = "";
        Property[] properties = requestInfo.getMainTableInfo().getProperty();
        for (int i = 0; i < properties.length; ++i) {
            String name = properties[i].getName();
            if ("lcbh".equals(name)) {
                lcbh = Util.null2String(properties[i].getValue());
            }else if ("sqr".equals(name)) {
                sqr = Util.null2String(properties[i].getValue());
            }if ("fygzbm".equals(name)) {
                fygzbm = Util.null2String(properties[i].getValue());
            }if (("fygzzt".equals(name) || "fygzztgs".equals(name)) && StringUtils.isBlank(fygzzt)) {
                fygzzt = Util.null2String(properties[i].getValue());
            }if ("sqrq".equals(name)) {
                sqrq = Util.null2String(properties[i].getValue());
            }
        }

        Map<String, String> map = null;
        List<Map<String, String>> mapList = new ArrayList<>();
        DetailTable dt = requestInfo.getDetailTableInfo().getDetailTable()[0];
        Row[] s = dt.getRow();
        for (int j = 0; j < s.length; j++) {
            Row r = s[j];
            Cell c[] = r.getCell();
            map = new HashMap<>();
            boolean flag = false;
            for (int k = 0; k < c.length; k++) {
                Cell c1 = c[k];
                String name = c1.getName();
                String value = c1.getValue();
                if("yszj".equals(name)){
                    map.put("amount",value);
                }else  if("sl".equals(name)){
                    map.put("quantity",value);
                }else  if("zcbh".equals(name)){
                    map.put("bgtItem",value);
                    map.put("dimension2", value);
                    rs.executeQuery("select wdmc from uf_zcsj where wzdm = ?");
                    if(rs.next()){
                        if("项目".equals(rs.getString("wdmc"))){
                            flag = true;
                            break;
                        }
                    }
                } else if ("xh".equals(name)) {
                    map.put("lineNum", value);
                }
            }
            if(flag){
                continue;
            }
            map.put("unit", fygzbm);
            map.put("bgtPeriod", sqrq);
            map.put("company", fygzzt);
            mapList.add(map);
        }

        if(CollUtil.isEmpty(mapList)){
            requestInfo.getRequestManager().setMessagecontent("明细上无可推送的资产 请确认！ ");
            requestInfo.getRequestManager().setMessageid("error");
            return FAILURE_AND_CONTINUE;
        }

        //根据 dimension2 字段 将相同的dimension2 内容的 amount字段相加
        Map<String, Double> resultMap =
                mapList.stream()
                        .collect(Collectors.groupingBy(
                                m -> m.get("dimension2"),
                                Collectors.summingDouble(m -> Double.parseDouble(m.get("amount")))
                        ));
        Map<String, Double> quantityMap =
                mapList.stream()
                        .collect(Collectors.groupingBy(
                                m -> m.get("dimension2"),
                                Collectors.summingDouble(m -> Double.parseDouble(m.get("quantity")))
                        ));
        //相同dimension2字段的Map对象合并为一个Map<String,List<Map<String,String>>>
        Map<String, List<Map<String, String>>> resultMapList =
                mapList.stream()
                        .collect(Collectors.groupingBy(
                                m -> m.get("dimension2"),
                                Collectors.toList()
                        ));
        List<Map<String, String>> mapResultList = new ArrayList<>();

        // 遍历键值对
        for (Map.Entry<String, List<Map<String, String>>> entry : resultMapList.entrySet()) {
            // 获取键值
            String key = entry.getKey();
            // 获取第一个值
            Map<String, String> value = entry.getValue().get(0);
            value.put("amount", String.valueOf(resultMap.get(key)));
            value.put("quantity", String.valueOf(quantityMap.get(key)));

            mapResultList.add(value);
        }


        JSONArray lines_arr = JSONArray.fromObject(mapResultList);
        baseBean.writeLog("yusuanjiaoyan.line_arr" + lines_arr);
        String re = toHEC(requestid, requestid, sqr, lines_arr);

        if (StringUtils.isBlank(re)) {
            requestInfo.getRequestManager().setMessagecontent("预算校验 请求HEC 异常   ");
            requestInfo.getRequestManager().setMessageid("error");
            return FAILURE_AND_CONTINUE;
        }
        String checkResult_main = "0";
        JSONObject rejsonAll = JSONObject.fromObject(re);
        String code = rejsonAll.get("code").toString();
        if(!"100".equals(code)){
            requestInfo.getRequestManager().setMessagecontent("预算校验 ESB接口 异常   ");
            requestInfo.getRequestManager().setMessageid("error");
            return FAILURE_AND_CONTINUE;
        }
        JSONObject rejsonData = JSONObject.fromObject(rejsonAll.get("data"));
        String status = rejsonData.getString("status");
        JSONObject result =JSONObject.fromObject((JSONArray.fromObject(rejsonData.get("result"))).get(0));
        JSONArray lines =JSONArray.fromObject(result.get("lines"));
//        Set<Integer> errNull = new HashSet<>();
//        Set<Integer> errOverBudget = new HashSet<>();
        String errMessage = "";

        for(int i=0;i<lines.size();i++) {
            try {
                JSONObject fhsj =  JSONObject.fromObject(lines.get(i));
                String lineNum = fhsj.getString("lineNum");
                String totalBudgetAmount = null;
                if(fhsj.has("totalBudgetAmount")){
                    totalBudgetAmount = fhsj.getString("totalBudgetAmount");
                }
                String totalBudgetQuantity = null;
                if(fhsj.has("totalBudgetQuantity")){
                    totalBudgetQuantity = fhsj.getString("totalBudgetQuantity");
                }
                String remainingQuantity = null;
                if(fhsj.has("remainingQuantity")){
                    remainingQuantity = fhsj.getString("remainingQuantity");
                }
                String remainingAmount = null;
                if(fhsj.has("remainingAmount")){
                    remainingAmount = fhsj.getString("remainingAmount");
                }
                String checkResult = null;
                if(fhsj.has("checkResult")){
                    checkResult = fhsj.getString("checkResult");
                    if("1".equals(checkResult)){
//                        errOverBudget.add(i+1);
                        checkResult_main = "1";
                    }
                }
                String msg = fhsj.getString("msg");
                if(!"S".equals(status) && StringUtils.isNotBlank(msg)){
                    errMessage = errMessage + "第" + lineNum + "行返回msg为(" + msg + "), ";
                }


//                if("所给信息未查到对应的预算信息".equals(msg)){
//                    errNull.add(i+1);
//                }
                String sql = " update "+tablename+"_dt1 set ndyszje = ? , ndyszsl = ? , sysl = ? , yskyye = ? , xyjg = ? , ysxyxx = ? " +
                        " where mainid = (select id from "+tablename+" where requestid = ? ) and xh = ?";
                rs.executeUpdate(sql,totalBudgetAmount,totalBudgetQuantity,remainingQuantity,remainingAmount,checkResult,msg,requestid,lineNum);
            }catch (Exception e){
                baseBean.writeLog("      处理返回数据报错了："+e);
            }
        }
        //校验结果回写到主表上，用于流程条件判断
        //规则：只有存在一行明细是超预算的，则主表校验结果就为超预算
        String sql = " update "+tablename+" set  xyjg = ?  where requestid = ?";
        rs.executeUpdate(sql,checkResult_main,requestid);

//        if(errNull.size() != 0){
//            errMessage = errMessage + "亲：你在年初没有提交该资产的预算，请走下预算追加单 为" + errNull + "行。";
//        }
//        if(errOverBudget.size() != 0){
//            errMessage = errMessage + "亲：你在年初提交该资产的预算不够，超出预算，请走下预算追加单 为" + errOverBudget + "行。";
//        }

        if(errMessage.length() != 0 || !"S".equals(status)){
            requestInfo.getRequestManager().setMessagecontent(errMessage);
            requestInfo.getRequestManager().setMessageid(String.valueOf(System.currentTimeMillis()));
            return FAILURE_AND_CONTINUE;
        }


        return SUCCESS;
    }

    private String toHEC(String reqNumber , String reqId , String reqEmployee , JSONArray lines_arr) {
            ToHECServices ToHECService = ServiceUtil.getService(ToHECServicesImpl.class);
            Map<String,Object> param = new HashMap<>();
            JSONObject jsonAll = new JSONObject() ;
            jsonAll.put("sourceSystemCode","OA");
            jsonAll.put("interfaceCode","BUDGET_CHECK");
            jsonAll.put("reqNumber",reqNumber);
            jsonAll.put("reqId",reqId);
            jsonAll.put("reqEmployee",reqEmployee);
            jsonAll.put("token","token");
            jsonAll.put("lines",lines_arr);
            param.put("eventKey","checkYuSuan");
            param.put("jsonAll",jsonAll);
            String re =  ToHECService.toHECData(param);
            baseBean.writeLog("         预算校验接口："+re);
            return re ;
    }

    /**
     * 将校验结果回写
     * @param reqId
     * @param re
     */
    private Boolean updateResult(String tablename,String reqId, String re) {
        Boolean iserror = false ;
        if(!"".equals(re)){
            String checkResult_main = "0";
            JSONObject rejsonAll = JSONObject.fromObject(re);
            String code = rejsonAll.get("code").toString();
            if("100".equals(code)){
                JSONObject rejsonData = JSONObject.fromObject(rejsonAll.get("data"));
                JSONObject result =JSONObject.fromObject((JSONArray.fromObject(rejsonData.get("result"))).get(0));
                JSONArray lines =JSONArray.fromObject(result.get("lines"));
                for(int i=0;i<lines.size();i++) {
                    try {
                        JSONObject fhsj =  JSONObject.fromObject(lines.get(i));
                        String lineNum = fhsj.getString("lineNum");
                        String totalBudgetAmount = null;
                        if(fhsj.has("totalBudgetAmount")){
                            totalBudgetAmount = fhsj.getString("totalBudgetAmount");
                        }
                        String totalBudgetQuantity = null;
                        if(fhsj.has("totalBudgetQuantity")){
                            totalBudgetQuantity = fhsj.getString("totalBudgetQuantity");
                        }
                        String remainingQuantity = null;
                        if(fhsj.has("remainingQuantity")){
                            remainingQuantity = fhsj.getString("remainingQuantity");
                        }
                        String remainingAmount = null;
                        if(fhsj.has("remainingAmount")){
                            remainingAmount = fhsj.getString("remainingAmount");
                        }
                        String checkResult = null;
                        if(fhsj.has("checkResult")){
                            checkResult = fhsj.getString("checkResult");
                            if("1".equals(checkResult)){
                                checkResult_main = "1";
                            }
                        }
                        String msg = fhsj.getString("msg");
                        if("所给信息未查到对应的预算信息".equals(msg)){
                            iserror = true ;
                        }
                        String sql = " update "+tablename+"_dt1 set ndyszje = ? , ndyszsl = ? , sysl = ? , yskyye = ? , xyjg = ? , ysxyxx = ? " +
                                " where mainid = (select id from "+tablename+" where requestid = ? ) and xh = ?";
                        rs.executeUpdate(sql,totalBudgetAmount,totalBudgetQuantity,remainingQuantity,remainingAmount,checkResult,msg,reqId,lineNum);
                    }catch (Exception e){
                        baseBean.writeLog("      处理返回数据报错了："+e);
                    }
                }
                //校验结果回写到主表上，用于流程条件判断
                //规则：只有存在一行明细是超预算的，则主表校验结果就为超预算
                String sql = " update "+tablename+" set  xyjg = ?  where requestid = ?";
                rs.executeUpdate(sql,checkResult_main,reqId);
            }
        }

        return iserror ;
    }

}

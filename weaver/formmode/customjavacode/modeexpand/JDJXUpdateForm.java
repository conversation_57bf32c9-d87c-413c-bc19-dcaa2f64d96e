package weaver.formmode.customjavacode.modeexpand;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 说明
 * 修改时
 * 类名要与文件名保持一致
 * class文件存放位置与路径保持一致。
 * 请把编译后的class文件，放在对应的目录中才能生效
 * 注意 同一路径下java名不能相同。
 * <AUTHOR>
 *
 */
public class JDJXUpdateForm extends AbstractModeExpandJavaCodeNew {
	BaseBean baseBean = new BaseBean();

	RecordSet rs = new RecordSet();

	/**
	 * 执行模块扩展动作
	 * @param param
	 *  param包含(但不限于)以下数据
	 *  user 当前用户
	 *  importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *  导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
	 *  页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
	 * @return 
	 */
	@Override
	public Map<String, String> doModeExpand(Map<String, Object> param) {
		baseBean.writeLog("doModeExpand param = "+param);
		Map<String, String> result = new HashMap<String, String>();
		try {
			User user = (User)param.get("user");
			int billid = -1;//数据id
			int modeid = -1;//模块id
			RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
			if(requestInfo!=null){
				billid = Util.getIntValue(requestInfo.getRequestid());
				modeid = Util.getIntValue(requestInfo.getWorkflowid());
				if(billid>0&&modeid>0){
					//------请在下面编写业务逻辑代码------
					JSONObject jsonStr = JSONObject.parseObject(String.valueOf(param.get("JSONStr")));
					JSONArray detail_1 = jsonStr.getJSONArray("detail_1");
					List<List> updateGrjxxxAllList = new ArrayList<>();
					List<List> updateGRJXPJLCAllList = new ArrayList<>();
					for(int i = 0; i <detail_1.size(); i++){
						JSONObject o = detail_1.getJSONObject(i);
						List grjxxxList = new ArrayList<>();
						List grjxpjlcList = new ArrayList<>();
						//修改绩效结果
						String xgjxjg = o.getString("field22941");
						//制定流程
						String zdlc = o.getString("field22935");
						//评价流程
						String pjlc = o.getString("field22936");
						grjxxxList.add(xgjxjg);
						grjxxxList.add(zdlc);
						grjxpjlcList.add(xgjxjg);
						grjxpjlcList.add(pjlc);
						updateGrjxxxAllList.add(grjxxxList);
						updateGRJXPJLCAllList.add(grjxpjlcList);
					}
					baseBean.writeLog("更新个人绩效信息表的List为 " + updateGrjxxxAllList);
					baseBean.writeLog("更新个人绩效评价流程的List为 " + updateGRJXPJLCAllList);

					boolean flag1 = updateGrjxxx(updateGrjxxxAllList);
					boolean flag2 = updateGRJXPJLC(updateGRJXPJLCAllList);
					baseBean.writeLog("更新个人绩效信息表的结果为 " + flag1);
					baseBean.writeLog("更新个人绩效评价流程的结果为 " + flag2);
					if(!flag1&&!flag2){
						result.put("errmsg","更新失败");
						result.put("flag", "false");
					}

				}
			}
		} catch (Exception e) {
			baseBean.writeLog(e);
			result.put("errmsg","异常错误");
			result.put("flag", "false");
		}
		baseBean.writeLog("result = " + result);
		return result;
	}


	/**
	 * 更新个人绩效信息表
	 * @param list
	 * @return
	 */
	public boolean updateGrjxxx(List<List> list){
		String sql = "update uf_grjxxx set grjxjg = ?  where zdlc = ?";
		return rs.executeBatchSql(sql,list);
	}

	/**
	 * 更新个人绩效评价流程
	 * @param list
	 * @return
	 */
	public boolean updateGRJXPJLC(List<List> list){
		String sql = "update formtable_main_617 set zzjxjg = ?  where requestid = ?";
		return rs.executeBatchSql(sql,list);
	}



}
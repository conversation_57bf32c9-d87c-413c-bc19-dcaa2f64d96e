package weaver.sms;

import com.alibaba.fastjson.JSONObject;
import weaver.general.BaseBean;
import yitouniu.util.SmsUtils;

/**
 * <AUTHOR>
 * @Date 2023/4/7 9:36
 * @Description TODO
 * @Version 1.0
 */
public class YunPianSmsImpl implements SmsService {

    BaseBean baseBean = new BaseBean();

    @Override
    public boolean sendSMS(String s, String s1, String s2) {
        baseBean.writeLog("云片网请求手机号=" + s1 + " 信息为" + s2);
        String response = SmsUtils.singleSend(SmsUtils.YUNPIAN_SMS_APIKEY, s2, s1);
        baseBean.writeLog("云片网返回信息为" + response);
        JSONObject result = JSONObject.parseObject(response);
        if(result.getInteger("code")!=0){
            baseBean.writeLog("code = " + result.getInteger("code"));
            return false;
        }
        return true;
    }
}

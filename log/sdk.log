2024-10-21 14:33:32,027 [main] [A2] [INFO] - rootPath == null
2024-10-21 14:33:32,028 [main] [A2] [INFO] - filePath == nullWEB-INF/prop/isSyncLog4j.properties
2024-10-21 14:33:32,029 [main] [A2] [INFO] - 请求返回={
	"code":0,
	"data":{
		"proSkuList":[
			{
				"AD_CLIENT_ID":null,
				"AD_ORG_ID":null,
				"ATTENTION":null,
				"AVAILABLE_QTY":null,
				"BASE_PRICE_DOWN":354.0000,
				"BASICUNIT":null,
				"BASICUNIT_ECODE":null,
				"BASICUNIT_ENAME":null,
				"BIGCATE":null,
				"BROWSE":null,
				"BUSINESSRANGE":null,
				"BUYER":null,
				"BUYPATTERNER":null,
				"CAPACITY":null,
				"CLOTHCOLLOCATION":null,
				"CLOTHFABRIC":null,
				"CLOTHINGSTYLE":null,
				"CLOTHLINING":null,
				"CLRS":null,
				"CLRSECODE":"RYYTN",
				"CLRSENAME":"-",
				"COLLOCATION":null,
				"COMPONENT":null,
				"COMPOSITION":null,
				"CONCEPT":null,
				"COTTONFEATHER":null,
				"CP_C_DISTRIB_ID":0,
				"CP_C_SHOP_ID":null,
				"CP_C_SHOP_TITLE":null,
				"CP_C_SUPPLIER_ID":null,
				"CP_C_WAREHOUSE_ID":null,
				"CREATIONDATE":null,
				"DATEENDRECYCLE":null,
				"DATEENDRETURN":null,
				"DATEMARKET":null,
				"DATEOFFSHELF":null,
				"DATEONSHELF":null,
				"DATETIMEDIM1":null,
				"DATETIMEDIM2":null,
				"DATETIMEDIM3":null,
				"DATETIMEDIM4":null,
				"DATETIMEDIM5":null,
				"DELIVERY_METHOD":null,
				"DESIGNER":null,
				"DETAILDESC":null,
				"DETAILS":null,
				"DISCENTER":null,
				"DISCONTDATE":null,
				"DISCONTENAME":null,
				"DISCONTID":null,
				"DISCONTNAME":null,
				"DISCOUNT_TYPE":null,
				"DISPALYSTANDARD":0,
				"DISSTORENUM":null,
				"ECODE":"104000000003",
				"EDITIONTYPE":null,
				"ENABLEDATE":null,
				"ENABLEENAME":null,
				"ENABLEID":null,
				"ENABLENAME":null,
				"ENAME":null,
				"EVALUATE":null,
				"EXTENSION":null,
				"FABCOLOR":null,
				"FABDESC":null,
				"FABRIC":null,
				"FACTORYCODE":null,
				"FOLLOWER":null,
				"FORCODE":null,
				"FORCODE1":null,
				"FORCODE2":null,
				"FORCODE3":null,
				"GBCODE":"6970037310561",
				"GROUP_EXTRACT_NUM":null,
				"GROUP_TYPE":null,
				"HEIGHT":0.210000,
				"ID":971,
				"IMAGE":null,
				"IMAGE_SKU":null,
				"ISACTIVE":"Y",
				"ISPROCURED":null,
				"ISSELECTION":null,
				"ISUP":null,
				"ISUPLOADIMG":null,
				"ISWRIDESC":null,
				"IS_AIRFORBIDDEN":null,
				"IS_CERTAINLY":null,
				"IS_CIRCULARS":null,
				"IS_ENABLE_EXPIRY":"Y",
				"IS_GIFT":"N",
				"IS_GROUP":"N",
				"IS_LOVERS":null,
				"IS_MAINPUSH":null,
				"IS_PACKAGE":null,
				"IS_PARENTAGE":null,
				"IS_POP":null,
				"IS_PRINTING":null,
				"IS_PROMOTION":null,
				"IS_SPONSOR":null,
				"IS_SUIT":null,
				"IS_VIRTUAL":"N",
				"LARGECLASS":null,
				"LARGESERIES":null,
				"LEATHERMESH":null,
				"LENGTH":0.412000,
				"MAINCOLOR":null,
				"MATERIELTYPE":null,
				"MATRIXCOLNO":null,
				"MDLARGECLASS":null,
				"MDMIDDLECLASS":null,
				"MINLOTSIZE":null,
				"MODIFIEDDATE":null,
				"MODIFIERENAME":"系统管理员",
				"MODIFIERID":null,
				"MODIFIERNAME":null,
				"M_DIM12_ID":1738,
				"M_DIM1_ID":1242,
				"M_DIM2_ID":1244,
				"M_DIM3_ID":1243,
				"M_DIM4_ID":945,
				"M_DIM5_ID":984,
				"M_DIM6_ID":1160,
				"NEEDLEWOVEN":null,
				"NEWPROER":null,
				"NORTHTIME":null,
				"NUM":null,
				"ORDERNUM":null,
				"OWNERENAME":"系统管理员",
				"OWNERID":null,
				"OWNERNAME":null,
				"PACKAGETYPES":null,
				"POPULAR":null,
				"PREAVGDAILY":0.00,
				"PRICEBAND":null,
				"PRICECOSTLIST":null,
				"PRICELIST":0.0000,
				"PRICELOWER":null,
				"PRICESETTLE":null,
				"PRICE_COST":null,
				"PROBAND":null,
				"PRODFUNCTION":null,
				"PROLINE":null,
				"PROMANGROUP":null,
				"PROMONTH":null,
				"PROMOTIONTYPE":null,
				"PROMOTION_PRICE":null,
				"PRONATURE":0,
				"PROPERT":null,
				"PROSEA":null,
				"PROSOURCE":null,
				"PROSTANDARD":null,
				"PROSTANDARD1":null,
				"PROSTANDARD2":null,
				"PROYEAR":null,
				"PRO_ATTRIBUTE_MAP":{
					"M_DIM12_ID":{
						"ecode":"YN",
						"ename":"液奶",
						"id":1738
					},
					"M_DIM4_ID":{
						"ecode":"基础奶粉",
						"ename":"基础奶粉",
						"id":945
					},
					"M_DIM3_ID":{
						"ecode":"CS",
						"ename":"箱",
						"id":1243
					},
					"M_DIM1_ID":{
						"ecode":"ZERT",
						"ename":"产成品",
						"id":1242
					},
					"M_DIM2_ID":{
						"ecode":"10401",
						"ename":"奶粉-听装800g",
						"id":1244
					},
					"M_DIM5_ID":{
						"ecode":"800G罐",
						"ename":"800G罐",
						"id":984
					},
					"M_DIM6_ID":{
						"ecode":"全脂营养奶粉800G",
						"ename":"全脂营养奶粉800G",
						"id":1160
					}
				},
				"PS_C_BRAND_ID":1,
				"PS_C_PRORULE_ID":null,
				"PS_C_PRO_ECODE":"104000000003",
				"PS_C_PRO_ENAME":"全脂营养奶粉800G-6罐",
				"PS_C_PRO_ID":973,
				"PS_C_PRO_WARETYPE":null,
				"PS_C_SHAPEGROUP_ID":1,
				"PS_C_SIZE_ID":null,
				"PS_C_SKURULE_ID":2,
				"PS_C_SKU_ID":null,
				"PS_C_SPEC1GROUP_ID":1,
				"PS_C_SPEC1OBJ_ID":1,
				"PS_C_SPEC2GROUP_ID":4,
				"PS_C_SPEC2OBJ_ID":4,
				"PS_C_SPEC3GROUP_ID":null,
				"PS_C_SPEC3OBJ_ID":null,
				"PS_C_SPEC4GROUP_ID":null,
				"PS_C_SPEC4OBJ_ID":null,
				"PS_C_SPECOBJ_IDS":null,
				"PS_C_SPEC_IDS":null,
				"PURCHASEMODE":0,
				"PURNATURE":null,
				"QTY_LOWSTOCK":null,
				"QTY_SAFENUM":null,
				"RECYCLE":null,
				"REDISSTORENUM":null,
				"RELIABILITY":null,
				"REMARK":null,
				"SAFETECHCLASS":null,
				"SALEPERIOD":null,
				"SALESVOLUME":null,
				"SERIES":null,
				"SEX":null,
				"SEX_NAME":null,
				"SHELFCODE":null,
				"SHOEEXTEND":null,
				"SHOELABEL":null,
				"SHOEUPPER":null,
				"SIZES":null,
				"SIZESECODE":"RYYTN",
				"SIZESENAME":"-",
				"SKU_TYPE":null,
				"SMALLCATE":null,
				"SMALLSERIES":null,
				"SOLE":null,
				"SOUTHTIME":null,
				"STATUS":1,
				"STATUSERENAME":null,
				"STATUSERID":null,
				"STATUSERNAME":null,
				"STATUSTIME":null,
				"STOPPUR":0,
				"STOPREPLEN":0,
				"STYLE":null,
				"SUBSERIES":null,
				"SUPBRAND":null,
				"SUPPLY_TYPE":null,
				"SUPREMARK":null,
				"SYNC":1,
				"SYNC_STOREPRICE":null,
				"SYNC_SUPPRICE":null,
				"TAGSPEC":null,
				"THEMESTORY":null,
				"THICKNESS":null,
				"TMALL_EXPAND_CARD":null,
				"TRIALDAYS":null,
				"TRIALSTORENUM":null,
				"UNIT":null,
				"VIDEO":null,
				"WARE_TYPE":0,
				"WAVE_BAND":null,
				"WEIGHT":6.800,
				"WEIGHT_UNIT":"KG",
				"WIDTH":0.285000,
				"YEAR_SEASON":null
			}
		]
	},
	"message":null,
	"oK":true
}
2025-08-18 16:03:00,351 [main] [org.apache.http.client.protocol.RequestAddCookies] [DEBUG] - CookieSpec selected: default
2025-08-18 16:03:00,358 [main] [org.apache.http.client.protocol.RequestAuthCache] [DEBUG] - Auth cache not set in the context
2025-08-18 16:03:00,360 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection request: [route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 0 of 2; total allocated: 0 of 20]
2025-08-18 16:03:00,368 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection leased: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:03:00,369 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Opening connection {s}->https://oc-uat.onecontract-cloud.com:443
2025-08-18 16:03:00,880 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connecting to oc-uat.onecontract-cloud.com/*************:443
2025-08-18 16:03:00,880 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Connecting socket to oc-uat.onecontract-cloud.com/*************:443 with timeout 0
2025-08-18 16:03:00,901 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled protocols: [TLSv1.3, TLSv1.2]
2025-08-18 16:03:00,901 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled cipher suites:[TLS_AES_256_GCM_SHA384, TLS_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_256_GCM_SHA384, TLS_DHE_DSS_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_256_CBC_SHA256, TLS_DHE_DSS_WITH_AES_256_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_256_CBC_SHA, TLS_DHE_DSS_WITH_AES_256_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_256_GCM_SHA384, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
2025-08-18 16:03:00,902 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Starting handshake
2025-08-18 16:03:01,002 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Secure session established
2025-08-18 16:03:01,003 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated protocol: TLSv1.2
2025-08-18 16:03:01,003 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
2025-08-18 16:03:01,003 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer principal: CN=*.onecontract-cloud.com, O=上海甄零科技有限公司, ST=上海市, C=CN
2025-08-18 16:03:01,003 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer alternative names: [*.onecontract-cloud.com, onecontract-cloud.com]
2025-08-18 16:03:01,003 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
2025-08-18 16:03:01,008 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connection established 127.0.0.1:61666<->*************:443
2025-08-18 16:03:01,008 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Executing request POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:03:01,008 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Target auth state: UNCHALLENGED
2025-08-18 16:03:01,008 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Proxy auth state: UNCHALLENGED
2025-08-18 16:03:01,009 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:03:01,009 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Type: application/x-www-form-urlencoded;charset=utf-8
2025-08-18 16:03:01,009 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Length: 121
2025-08-18 16:03:01,009 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Host: oc-uat.onecontract-cloud.com
2025-08-18 16:03:01,010 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 16:03:01,010 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)
2025-08-18 16:03:01,010 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Accept-Encoding: gzip,deflate
2025-08-18 16:03:01,010 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "POST /oauth/oauth/token HTTP/1.1[\r][\n]"
2025-08-18 16:03:01,010 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Type: application/x-www-form-urlencoded;charset=utf-8[\r][\n]"
2025-08-18 16:03:01,010 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Length: 121[\r][\n]"
2025-08-18 16:03:01,010 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Host: oc-uat.onecontract-cloud.com[\r][\n]"
2025-08-18 16:03:01,011 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Connection: Keep-Alive[\r][\n]"
2025-08-18 16:03:01,011 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)[\r][\n]"
2025-08-18 16:03:01,011 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Accept-Encoding: gzip,deflate[\r][\n]"
2025-08-18 16:03:01,011 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "[\r][\n]"
2025-08-18 16:03:01,011 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "grant_type=client_credentials&client_id=ryytn-biadmin&client_secret=ryytn-biadminryytn-biadminryytn-biadmin&scope=default"
2025-08-18 16:03:01,050 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "HTTP/1.1 200 OK[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Date: Mon, 18 Aug 2025 08:03:01 GMT[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Content-Type: application/json;charset=UTF-8[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Transfer-Encoding: chunked[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Connection: keep-alive[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Cache-Control: no-store[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-XSS-Protection: 1; mode=block[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Pragma: no-cache[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Frame-Options: DENY[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Content-Type-Options: nosniff[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Strict-Transport-Security: max-age=31536000 ; includeSubDomains[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Server: elb[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:03:01,051 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "71[\r][\n]"
2025-08-18 16:03:01,052 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "{"access_token":"2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf","token_type":"bearer","expires_in":3599,"scope":"default"}[\r][\n]"
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << HTTP/1.1 200 OK
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Date: Mon, 18 Aug 2025 08:03:01 GMT
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Content-Type: application/json;charset=UTF-8
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Transfer-Encoding: chunked
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Connection: keep-alive
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Cache-Control: no-store
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-XSS-Protection: 1; mode=block
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Pragma: no-cache
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Frame-Options: DENY
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Strict-Transport-Security: max-age=31536000 ; includeSubDomains
2025-08-18 16:03:01,054 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Server: elb
2025-08-18 16:03:01,057 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Connection can be kept alive indefinitely
2025-08-18 16:03:01,060 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "0[\r][\n]"
2025-08-18 16:03:01,060 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:03:01,060 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443] can be kept alive indefinitely
2025-08-18 16:03:01,060 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: set socket timeout to 0
2025-08-18 16:03:01,060 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection released: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 1; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:03:01,060 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager is shutting down
2025-08-18 16:03:01,060 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: Close connection
2025-08-18 16:03:01,061 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager shut down
2025-08-18 16:03:01,100 [main] [yitouniu.util.ClmUtil] [INFO] - 成功获取access_token，有效期: 3599秒
2025-08-18 16:03:01,106 [main] [A2] [INFO] - rootPath == null
2025-08-18 16:03:01,106 [main] [A2] [INFO] - filePath == nullWEB-INF/prop/isSyncLog4j.properties
2025-08-18 16:03:01,107 [main] [A2] [INFO] - CLM获取token成功: bearer 2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf
2025-08-18 16:05:29,585 [main] [org.apache.http.client.protocol.RequestAddCookies] [DEBUG] - CookieSpec selected: default
2025-08-18 16:05:29,591 [main] [org.apache.http.client.protocol.RequestAuthCache] [DEBUG] - Auth cache not set in the context
2025-08-18 16:05:29,591 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection request: [route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 0 of 2; total allocated: 0 of 20]
2025-08-18 16:05:29,599 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection leased: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:05:29,600 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Opening connection {s}->https://oc-uat.onecontract-cloud.com:443
2025-08-18 16:05:29,610 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connecting to oc-uat.onecontract-cloud.com/*************:443
2025-08-18 16:05:29,610 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Connecting socket to oc-uat.onecontract-cloud.com/*************:443 with timeout 0
2025-08-18 16:05:29,616 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled protocols: [TLSv1.3, TLSv1.2]
2025-08-18 16:05:29,616 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled cipher suites:[TLS_AES_256_GCM_SHA384, TLS_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_256_GCM_SHA384, TLS_DHE_DSS_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_256_CBC_SHA256, TLS_DHE_DSS_WITH_AES_256_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_256_CBC_SHA, TLS_DHE_DSS_WITH_AES_256_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_256_GCM_SHA384, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
2025-08-18 16:05:29,616 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Starting handshake
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Secure session established
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated protocol: TLSv1.2
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer principal: CN=*.onecontract-cloud.com, O=上海甄零科技有限公司, ST=上海市, C=CN
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer alternative names: [*.onecontract-cloud.com, onecontract-cloud.com]
2025-08-18 16:05:29,707 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
2025-08-18 16:05:29,710 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connection established 127.0.0.1:62189<->*************:443
2025-08-18 16:05:29,710 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Executing request POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:05:29,710 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Target auth state: UNCHALLENGED
2025-08-18 16:05:29,711 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Proxy auth state: UNCHALLENGED
2025-08-18 16:05:29,711 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:05:29,711 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Type: application/x-www-form-urlencoded;charset=utf-8
2025-08-18 16:05:29,711 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Length: 121
2025-08-18 16:05:29,711 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Host: oc-uat.onecontract-cloud.com
2025-08-18 16:05:29,712 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 16:05:29,712 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)
2025-08-18 16:05:29,712 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Accept-Encoding: gzip,deflate
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "POST /oauth/oauth/token HTTP/1.1[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Type: application/x-www-form-urlencoded;charset=utf-8[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Length: 121[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Host: oc-uat.onecontract-cloud.com[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Connection: Keep-Alive[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Accept-Encoding: gzip,deflate[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "[\r][\n]"
2025-08-18 16:05:29,712 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "grant_type=client_credentials&client_id=ryytn-biadmin&client_secret=ryytn-biadminryytn-biadminryytn-biadmin&scope=default"
2025-08-18 16:05:29,784 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "HTTP/1.1 200 OK[\r][\n]"
2025-08-18 16:05:29,784 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Date: Mon, 18 Aug 2025 08:05:29 GMT[\r][\n]"
2025-08-18 16:05:29,784 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Content-Type: application/json;charset=UTF-8[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Transfer-Encoding: chunked[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Connection: keep-alive[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Cache-Control: no-store[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-XSS-Protection: 1; mode=block[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Pragma: no-cache[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Frame-Options: DENY[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Content-Type-Options: nosniff[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Strict-Transport-Security: max-age=31536000 ; includeSubDomains[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Server: elb[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "71[\r][\n]"
2025-08-18 16:05:29,785 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "{"access_token":"2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf","token_type":"bearer","expires_in":3451,"scope":"default"}[\r][\n]"
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << HTTP/1.1 200 OK
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Date: Mon, 18 Aug 2025 08:05:29 GMT
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Content-Type: application/json;charset=UTF-8
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Transfer-Encoding: chunked
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Connection: keep-alive
2025-08-18 16:05:29,787 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Cache-Control: no-store
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-XSS-Protection: 1; mode=block
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Pragma: no-cache
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Frame-Options: DENY
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Strict-Transport-Security: max-age=31536000 ; includeSubDomains
2025-08-18 16:05:29,788 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Server: elb
2025-08-18 16:05:29,791 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Connection can be kept alive indefinitely
2025-08-18 16:05:29,793 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "0[\r][\n]"
2025-08-18 16:05:29,793 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:05:29,793 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443] can be kept alive indefinitely
2025-08-18 16:05:29,793 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: set socket timeout to 0
2025-08-18 16:05:29,794 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection released: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 1; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:05:29,794 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager is shutting down
2025-08-18 16:05:29,794 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: Close connection
2025-08-18 16:05:29,795 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager shut down
2025-08-18 16:05:29,833 [main] [yitouniu.util.ClmUtil] [INFO] - 成功获取access_token，有效期: 3451秒
2025-08-18 16:05:29,838 [main] [A2] [INFO] - rootPath == null
2025-08-18 16:05:29,838 [main] [A2] [INFO] - filePath == nullWEB-INF/prop/isSyncLog4j.properties
2025-08-18 16:05:29,838 [main] [A2] [INFO] - CLM获取token成功: bearer 2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf
2025-08-18 16:07:09,755 [main] [org.apache.http.client.protocol.RequestAddCookies] [DEBUG] - CookieSpec selected: default
2025-08-18 16:07:09,760 [main] [org.apache.http.client.protocol.RequestAuthCache] [DEBUG] - Auth cache not set in the context
2025-08-18 16:07:09,761 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection request: [route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 0 of 2; total allocated: 0 of 20]
2025-08-18 16:07:09,769 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection leased: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:07:09,770 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Opening connection {s}->https://oc-uat.onecontract-cloud.com:443
2025-08-18 16:07:09,912 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connecting to oc-uat.onecontract-cloud.com/*************:443
2025-08-18 16:07:09,912 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Connecting socket to oc-uat.onecontract-cloud.com/*************:443 with timeout 0
2025-08-18 16:07:09,922 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled protocols: [TLSv1.3, TLSv1.2]
2025-08-18 16:07:09,922 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled cipher suites:[TLS_AES_256_GCM_SHA384, TLS_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_256_GCM_SHA384, TLS_DHE_DSS_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_256_CBC_SHA256, TLS_DHE_DSS_WITH_AES_256_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_256_CBC_SHA, TLS_DHE_DSS_WITH_AES_256_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_256_GCM_SHA384, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
2025-08-18 16:07:09,922 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Starting handshake
2025-08-18 16:07:10,020 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Secure session established
2025-08-18 16:07:10,021 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated protocol: TLSv1.2
2025-08-18 16:07:10,021 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
2025-08-18 16:07:10,021 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer principal: CN=*.onecontract-cloud.com, O=上海甄零科技有限公司, ST=上海市, C=CN
2025-08-18 16:07:10,021 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer alternative names: [*.onecontract-cloud.com, onecontract-cloud.com]
2025-08-18 16:07:10,021 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
2025-08-18 16:07:10,024 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connection established 127.0.0.1:62545<->*************:443
2025-08-18 16:07:10,024 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Executing request POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:07:10,024 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Target auth state: UNCHALLENGED
2025-08-18 16:07:10,025 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Proxy auth state: UNCHALLENGED
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Type: application/x-www-form-urlencoded;charset=utf-8
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Length: 121
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Host: oc-uat.onecontract-cloud.com
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)
2025-08-18 16:07:10,026 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Accept-Encoding: gzip,deflate
2025-08-18 16:07:10,026 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "POST /oauth/oauth/token HTTP/1.1[\r][\n]"
2025-08-18 16:07:10,026 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Type: application/x-www-form-urlencoded;charset=utf-8[\r][\n]"
2025-08-18 16:07:10,026 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Length: 121[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Host: oc-uat.onecontract-cloud.com[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Connection: Keep-Alive[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Accept-Encoding: gzip,deflate[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "[\r][\n]"
2025-08-18 16:07:10,027 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "grant_type=client_credentials&client_id=ryytn-biadmin&client_secret=ryytn-biadminryytn-biadminryytn-biadmin&scope=default"
2025-08-18 16:07:10,066 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "HTTP/1.1 200 OK[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Date: Mon, 18 Aug 2025 08:07:10 GMT[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Content-Type: application/json;charset=UTF-8[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Transfer-Encoding: chunked[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Connection: keep-alive[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Cache-Control: no-store[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-XSS-Protection: 1; mode=block[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Pragma: no-cache[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Frame-Options: DENY[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Content-Type-Options: nosniff[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Strict-Transport-Security: max-age=31536000 ; includeSubDomains[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Server: elb[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "71[\r][\n]"
2025-08-18 16:07:10,067 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "{"access_token":"2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf","token_type":"bearer","expires_in":3350,"scope":"default"}[\r][\n]"
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << HTTP/1.1 200 OK
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Date: Mon, 18 Aug 2025 08:07:10 GMT
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Content-Type: application/json;charset=UTF-8
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Transfer-Encoding: chunked
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Connection: keep-alive
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Cache-Control: no-store
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-XSS-Protection: 1; mode=block
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Pragma: no-cache
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Frame-Options: DENY
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Strict-Transport-Security: max-age=31536000 ; includeSubDomains
2025-08-18 16:07:10,070 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Server: elb
2025-08-18 16:07:10,074 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Connection can be kept alive indefinitely
2025-08-18 16:07:10,076 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "0[\r][\n]"
2025-08-18 16:07:10,076 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:07:10,076 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443] can be kept alive indefinitely
2025-08-18 16:07:10,076 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: set socket timeout to 0
2025-08-18 16:07:10,076 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection released: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 1; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:07:10,076 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager is shutting down
2025-08-18 16:07:10,076 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: Close connection
2025-08-18 16:07:10,077 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager shut down
2025-08-18 16:07:10,107 [main] [yitouniu.util.ClmUtil] [INFO] - 成功获取access_token，有效期: 3350秒
2025-08-18 16:07:51,809 [main] [org.apache.http.client.protocol.RequestAddCookies] [DEBUG] - CookieSpec selected: default
2025-08-18 16:07:51,815 [main] [org.apache.http.client.protocol.RequestAuthCache] [DEBUG] - Auth cache not set in the context
2025-08-18 16:07:51,816 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection request: [route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 0 of 2; total allocated: 0 of 20]
2025-08-18 16:07:51,823 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection leased: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 0; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:07:51,824 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Opening connection {s}->https://oc-uat.onecontract-cloud.com:443
2025-08-18 16:07:51,826 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connecting to oc-uat.onecontract-cloud.com/*************:443
2025-08-18 16:07:51,827 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Connecting socket to oc-uat.onecontract-cloud.com/*************:443 with timeout 0
2025-08-18 16:07:51,837 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled protocols: [TLSv1.3, TLSv1.2]
2025-08-18 16:07:51,837 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Enabled cipher suites:[TLS_AES_256_GCM_SHA384, TLS_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_RSA_WITH_AES_256_GCM_SHA384, TLS_DHE_DSS_WITH_AES_256_GCM_SHA384, TLS_DHE_RSA_WITH_AES_128_GCM_SHA256, TLS_DHE_DSS_WITH_AES_128_GCM_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_RSA_WITH_AES_256_CBC_SHA256, TLS_DHE_DSS_WITH_AES_256_CBC_SHA256, TLS_DHE_RSA_WITH_AES_128_CBC_SHA256, TLS_DHE_DSS_WITH_AES_128_CBC_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA384, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA256, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA256, TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA, TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_RSA_WITH_AES_256_CBC_SHA, TLS_DHE_DSS_WITH_AES_256_CBC_SHA, TLS_DHE_RSA_WITH_AES_128_CBC_SHA, TLS_DHE_DSS_WITH_AES_128_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_256_CBC_SHA, TLS_ECDH_RSA_WITH_AES_256_CBC_SHA, TLS_ECDH_ECDSA_WITH_AES_128_CBC_SHA, TLS_ECDH_RSA_WITH_AES_128_CBC_SHA, TLS_RSA_WITH_AES_256_GCM_SHA384, TLS_RSA_WITH_AES_128_GCM_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA256, TLS_RSA_WITH_AES_128_CBC_SHA256, TLS_RSA_WITH_AES_256_CBC_SHA, TLS_RSA_WITH_AES_128_CBC_SHA, TLS_EMPTY_RENEGOTIATION_INFO_SCSV]
2025-08-18 16:07:51,837 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Starting handshake
2025-08-18 16:07:51,927 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] - Secure session established
2025-08-18 16:07:51,928 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated protocol: TLSv1.2
2025-08-18 16:07:51,928 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  negotiated cipher suite: TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
2025-08-18 16:07:51,928 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer principal: CN=*.onecontract-cloud.com, O=上海甄零科技有限公司, ST=上海市, C=CN
2025-08-18 16:07:51,928 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  peer alternative names: [*.onecontract-cloud.com, onecontract-cloud.com]
2025-08-18 16:07:51,928 [main] [org.apache.http.conn.ssl.SSLConnectionSocketFactory] [DEBUG] -  issuer principal: CN=GeoTrust G2 TLS CN RSA4096 SHA256 2022 CA1, O="DigiCert, Inc.", C=US
2025-08-18 16:07:51,931 [main] [org.apache.http.impl.conn.DefaultHttpClientConnectionOperator] [DEBUG] - Connection established 127.0.0.1:62707<->*************:443
2025-08-18 16:07:51,931 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Executing request POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:07:51,932 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Target auth state: UNCHALLENGED
2025-08-18 16:07:51,932 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Proxy auth state: UNCHALLENGED
2025-08-18 16:07:51,932 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> POST /oauth/oauth/token HTTP/1.1
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Type: application/x-www-form-urlencoded;charset=utf-8
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Content-Length: 121
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Host: oc-uat.onecontract-cloud.com
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Connection: Keep-Alive
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)
2025-08-18 16:07:51,933 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 >> Accept-Encoding: gzip,deflate
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "POST /oauth/oauth/token HTTP/1.1[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Type: application/x-www-form-urlencoded;charset=utf-8[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Content-Length: 121[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Host: oc-uat.onecontract-cloud.com[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Connection: Keep-Alive[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "User-Agent: Apache-HttpClient/4.5.6 (Java/1.8.0_421)[\r][\n]"
2025-08-18 16:07:51,933 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "Accept-Encoding: gzip,deflate[\r][\n]"
2025-08-18 16:07:51,934 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "[\r][\n]"
2025-08-18 16:07:51,934 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 >> "grant_type=client_credentials&client_id=ryytn-biadmin&client_secret=ryytn-biadminryytn-biadminryytn-biadmin&scope=default"
2025-08-18 16:07:51,970 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "HTTP/1.1 200 OK[\r][\n]"
2025-08-18 16:07:51,970 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Date: Mon, 18 Aug 2025 08:07:52 GMT[\r][\n]"
2025-08-18 16:07:51,970 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Content-Type: application/json;charset=UTF-8[\r][\n]"
2025-08-18 16:07:51,970 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Transfer-Encoding: chunked[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Connection: keep-alive[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Cache-Control: no-store[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-XSS-Protection: 1; mode=block[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Pragma: no-cache[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Frame-Options: DENY[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "X-Content-Type-Options: nosniff[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Strict-Transport-Security: max-age=31536000 ; includeSubDomains[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "Server: elb[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "71[\r][\n]"
2025-08-18 16:07:51,971 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "{"access_token":"2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf","token_type":"bearer","expires_in":3309,"scope":"default"}[\r][\n]"
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << HTTP/1.1 200 OK
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Date: Mon, 18 Aug 2025 08:07:52 GMT
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Content-Type: application/json;charset=UTF-8
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Transfer-Encoding: chunked
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Connection: keep-alive
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Cache-Control: no-store
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-XSS-Protection: 1; mode=block
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Pragma: no-cache
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Frame-Options: DENY
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << X-Content-Type-Options: nosniff
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Strict-Transport-Security: max-age=31536000 ; includeSubDomains
2025-08-18 16:07:51,973 [main] [org.apache.http.headers] [DEBUG] - http-outgoing-0 << Server: elb
2025-08-18 16:07:51,976 [main] [org.apache.http.impl.execchain.MainClientExec] [DEBUG] - Connection can be kept alive indefinitely
2025-08-18 16:07:51,978 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "0[\r][\n]"
2025-08-18 16:07:51,978 [main] [org.apache.http.wire] [DEBUG] - http-outgoing-0 << "[\r][\n]"
2025-08-18 16:07:51,979 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443] can be kept alive indefinitely
2025-08-18 16:07:51,979 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: set socket timeout to 0
2025-08-18 16:07:51,979 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection released: [id: 0][route: {s}->https://oc-uat.onecontract-cloud.com:443][total kept alive: 1; route allocated: 1 of 2; total allocated: 1 of 20]
2025-08-18 16:07:51,979 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager is shutting down
2025-08-18 16:07:51,979 [main] [org.apache.http.impl.conn.DefaultManagedHttpClientConnection] [DEBUG] - http-outgoing-0: Close connection
2025-08-18 16:07:51,980 [main] [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] [DEBUG] - Connection manager shut down
2025-08-18 16:07:52,012 [main] [yitouniu.util.ClmUtil] [INFO] - 成功获取access_token，有效期: 3309秒
2025-08-18 16:07:52,015 [main] [A2] [INFO] - rootPath == null
2025-08-18 16:07:52,015 [main] [A2] [INFO] - filePath == nullWEB-INF/prop/isSyncLog4j.properties
2025-08-18 16:07:52,015 [main] [A2] [INFO] - CLM获取token成功: bearer 2cd74341-a1fb-4ce2-b18b-ca91b6e0afdf
